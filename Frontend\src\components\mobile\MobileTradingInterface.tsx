/**
 * Mobile Trading Interface Component for DeepTrade
 *
 * Provides mobile-optimized trading interface with swipeable signal cards,
 * touch-friendly position management, and auto-trading controls.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartLine,
  faBriefcase,
  faCog,
  faSync,
  faArrowTrendUp
} from '@fortawesome/free-solid-svg-icons';
import MobileTopBar from '@/components/mobile/MobileTopBar';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { usePaperTrading } from '../../hooks/usePaperTrading';

interface MobileTradingInterfaceProps {
  // Props are optional as we'll fetch real data
}

interface TradingSignal {
  id: string;
  symbol: string;
  side: 'LONG' | 'SHORT';
  entry_price: number;
  target_price: number;
  stop_loss: number;
  confidence: number;
  status: 'ACTIVE' | 'FILLED' | 'CANCELLED';
  timestamp: string;
}

interface Position {
  id: string;
  symbol: string;
  side: 'LONG' | 'SHORT';
  size: number;
  entry_price: number;
  current_price: number;
  unrealized_pnl: number;
  unrealized_pnl_percent: number;
}

const MobileTradingInterface: React.FC<MobileTradingInterfaceProps> = () => {
  const { isMobile } = useMobile();
  const { isPaperMode, balance: paperBalance } = usePaperTrading();
  const [activeTab, setActiveTab] = useState<'signals' | 'positions' | 'settings' | 'marketplace'>('signals');

  // Real data states
  const [signals, setSignals] = useState<TradingSignal[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [autoTradingEnabled, setAutoTradingEnabled] = useState(false);
  const [, setIsSidebarOpen] = useState(false);
  const [balance] = useState<string>('$1,250.75');

  if (!isMobile) return null;

  // Mock data for demonstration
  useEffect(() => {
    setSignals([
      {
        id: '1',
        symbol: 'BTC/USDT',
        side: 'LONG',
        entry_price: 43250.00,
        target_price: 44500.00,
        stop_loss: 42000.00,
        confidence: 85,
        status: 'ACTIVE',
        timestamp: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        symbol: 'ETH/USDT',
        side: 'SHORT',
        entry_price: 2650.00,
        target_price: 2550.00,
        stop_loss: 2750.00,
        confidence: 78,
        status: 'ACTIVE',
        timestamp: '2024-01-15T09:15:00Z'
      }
    ]);

    setPositions([
      {
        id: '1',
        symbol: 'BTC/USDT',
        side: 'LONG',
        size: 0.1,
        entry_price: 43000.00,
        current_price: 43250.00,
        unrealized_pnl: 25.00,
        unrealized_pnl_percent: 0.58
      }
    ]);
  }, []);

  const tabs = [
    { id: 'signals', label: 'Signals', icon: faChartLine, count: signals.length },
    { id: 'marketplace', label: 'Arrow Marketplace', icon: faArrowTrendUp },
    { id: 'positions', label: 'Positions', icon: faBriefcase, count: positions.length },
    { id: 'settings', label: 'Settings', icon: faCog },
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-24 w-full mobile-no-scroll">
      <MobileTopBar
        balance={`Balance: ${isPaperMode ? `Virtual: $${(paperBalance || 0).toLocaleString()}` : balance}`}
        autoTradingEnabled={autoTradingEnabled}
        onMenuClick={() => setIsSidebarOpen(true)}
      />

      {/* Paper Trading Mode Banner */}
      {isPaperMode && (
        <div className="bg-orange-50 dark:bg-orange-900/20 border-b border-orange-200 dark:border-orange-800 px-4 py-2">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse flex-shrink-0"></div>
            <span className="text-xs font-medium text-orange-800 dark:text-orange-200">
              Paper Trading Mode - Practice with virtual funds
            </span>
          </div>
        </div>
      )}

      {/* Tab Navigation - Redesigned for mobile */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-20 w-full">
        <div className="flex h-12 w-full">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex flex-col items-center justify-center py-1 px-0.5 text-xs font-medium transition-colors min-h-[48px] min-w-0 relative ${
                activeTab === tab.id
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <FontAwesomeIcon
                icon={tab.icon}
                className={`w-3 h-3 flex-shrink-0 mb-0.5 ${activeTab === tab.id ? 'opacity-100' : 'opacity-70'}`}
              />
              <span className="text-[10px] font-medium leading-tight text-center truncate max-w-full">
                {tab.label === 'Arrow Marketplace' ? 'Market' : tab.label}
              </span>
              {tab.count !== undefined && tab.count > 0 && (
                <span className="absolute -top-0.5 -right-0.5 bg-blue-500 text-white text-[8px] font-bold px-1 py-0.5 rounded-full min-w-[14px] h-3 flex items-center justify-center">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-2 pb-20 w-full max-w-full overflow-x-hidden">
        {activeTab === 'signals' && (
          <div className="space-y-4 w-full overflow-x-hidden">
            {signals.length > 0 ? (
              signals.map((signal) => (
                <div key={signal.id} className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 w-full overflow-hidden">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className="text-xl font-bold text-gray-900 dark:text-white truncate">
                        {signal.symbol}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
                        signal.side === 'LONG'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}>
                        {signal.side}
                      </span>
                    </div>
                    <div className="text-right flex-shrink-0">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {formatTime(signal.timestamp)}
                      </div>
                      <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                        {signal.confidence}% confidence
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-2 mb-4 w-full">
                    <div className="text-center min-w-0">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Entry</p>
                      <p className="font-semibold text-gray-900 dark:text-white text-sm truncate">
                        {formatPrice(signal.entry_price)}
                      </p>
                    </div>
                    <div className="text-center min-w-0">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Target</p>
                      <p className="font-semibold text-green-600 dark:text-green-400 text-sm truncate">
                        {formatPrice(signal.target_price)}
                      </p>
                    </div>
                    <div className="text-center min-w-0">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Stop Loss</p>
                      <p className="font-semibold text-red-600 dark:text-red-400 text-sm truncate">
                        {formatPrice(signal.stop_loss)}
                      </p>
                    </div>
                  </div>

                  <div className="flex space-x-2 w-full">
                    <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[44px] min-w-0 overflow-hidden">
                      <span className="truncate">Execute Trade</span>
                    </button>
                    <button className="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-white py-2 px-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[44px] min-w-0 overflow-hidden">
                      <span className="truncate">View Details</span>
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <div className="flex items-center justify-center mb-4">
                  <FontAwesomeIcon icon={faChartLine} className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                </div>
                <p className="text-gray-500 dark:text-gray-400 mb-4">No active signals</p>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-colors active:scale-95 min-h-[48px]">
                  <div className="flex items-center justify-center space-x-2">
                    <FontAwesomeIcon icon={faSync} className="w-4 h-4" />
                    <span>Refresh Signals</span>
                  </div>
                </button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'marketplace' && (
          <div className="space-y-4 w-full overflow-x-hidden">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-4 text-white w-full overflow-hidden">
              <h3 className="text-lg font-bold mb-2 truncate">Arrow Marketplace</h3>
              <p className="text-sm opacity-90 truncate">Discover and subscribe to the best trading signals</p>
            </div>

            <div className="grid grid-cols-2 gap-2 w-full">
              {/* Featured Signal Provider Card */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-3 shadow-sm border border-gray-200 dark:border-gray-700 min-w-0 overflow-hidden">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-1.5 min-w-0 flex-1">
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-xs flex-shrink-0">EP</div>
                    <span className="font-medium text-gray-900 dark:text-white text-sm truncate">Elite Pro</span>
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-1.5 py-0.5 rounded-full flex-shrink-0">+85%</span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
                  <span className="truncate">Win: 92%</span>
                  <span className="truncate">Signals: 47</span>
                </div>
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-2 rounded-lg text-xs font-medium transition-colors overflow-hidden">
                  <span className="truncate">Subscribe</span>
                </button>
              </div>

              {/* Second Signal Provider Card */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-3 shadow-sm border border-gray-200 dark:border-gray-700 min-w-0 overflow-hidden">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-1.5 min-w-0 flex-1">
                    <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold text-xs flex-shrink-0">CS</div>
                    <span className="font-medium text-gray-900 dark:text-white text-sm truncate">Crypto Sensei</span>
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-1.5 py-0.5 rounded-full flex-shrink-0">+62%</span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
                  <span className="truncate">Win: 87%</span>
                  <span className="truncate">Signals: 32</span>
                </div>
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-2 rounded-lg text-xs font-medium transition-colors overflow-hidden">
                  <span className="truncate">Subscribe</span>
                </button>
              </div>

              {/* View All Button */}
              <div className="col-span-2">
                <button className="w-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2">
                  <span>View All Signal Providers</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Categories */}
            <div className="mt-6">
              <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Categories</h4>
              <div className="grid grid-cols-4 gap-2">
                {['All', 'High Risk', 'Medium', 'Low Risk', 'Futures', 'Spot', 'DeFi', 'NFTs'].map((category) => (
                  <button 
                    key={category}
                    className={`text-xs py-2 px-3 rounded-full ${
                      category === 'All' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'positions' && (
          <div className="space-y-4">
            {positions.length > 0 ? (
              positions.map((position) => (
                <div key={position.id} className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="text-xl font-bold text-gray-900 dark:text-white">
                        {position.symbol}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        position.side === 'LONG'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      }`}>
                        {position.side}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${
                        position.unrealized_pnl >= 0
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {position.unrealized_pnl >= 0 ? '+' : ''}{formatPrice(position.unrealized_pnl)}
                      </div>
                      <div className={`text-sm ${
                        position.unrealized_pnl_percent >= 0
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {position.unrealized_pnl_percent >= 0 ? '+' : ''}{position.unrealized_pnl_percent.toFixed(2)}%
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Size</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {position.size}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Entry</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {formatPrice(position.entry_price)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current</p>
                      <p className="font-semibold text-blue-600 dark:text-blue-400">
                        {formatPrice(position.current_price)}
                      </p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-medium transition-colors active:scale-95 min-h-[44px]">
                      Close Position
                    </button>
                    <button className="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-white py-2 px-4 rounded-lg font-medium transition-colors active:scale-95 min-h-[44px]">
                      Modify
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <div className="flex items-center justify-center mb-4">
                  <FontAwesomeIcon icon={faBriefcase} className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                </div>
                <p className="text-gray-500 dark:text-gray-400 mb-4">No open positions</p>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-colors active:scale-95 min-h-[48px]">
                  <div className="flex items-center justify-center space-x-2">
                    <FontAwesomeIcon icon={faChartLine} className="w-4 h-4" />
                    <span>View Signals</span>
                  </div>
                </button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            {/* Auto Trading Toggle */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Auto Trading
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Automatically execute trading signals
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer ml-4">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={autoTradingEnabled}
                    onChange={(e) => setAutoTradingEnabled(e.target.checked)}
                  />
                  <div className="w-14 h-8 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>

            {/* Risk Settings */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Risk Settings
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Position Size (% of balance)
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="10"
                    defaultValue="5"
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>1%</span>
                    <span>5%</span>
                    <span>10%</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Max Leverage
                  </label>
                  <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="1">1x (No Leverage)</option>
                    <option value="2">2x</option>
                    <option value="3">3x</option>
                    <option value="5">5x</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-3">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-xl font-medium transition-colors active:scale-95">
                Save Settings
              </button>
              <button className="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-white p-4 rounded-xl font-medium transition-colors active:scale-95">
                Reset to Defaults
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileTradingInterface;
