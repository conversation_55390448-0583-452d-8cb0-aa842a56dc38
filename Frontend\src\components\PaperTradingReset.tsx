/**
 * Paper Trading Reset Component
 * 
 * Provides functionality to reset paper trading account with confirmation modal
 */

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { 
  RotateCcw, 
  AlertTriangle, 
  X, 
  DollarSign,
  Info
} from 'lucide-react';
import { usePaperTrading } from '../hooks/usePaperTrading';
import { useTranslation } from '@/hooks/useTranslation';
import { useMobile } from '../hooks/useResponsiveDesign';
import { toast } from '@/components/ui/use-toast';

// Simple Input component
const Input: React.FC<React.InputHTMLAttributes<HTMLInputElement>> = ({ className = '', ...props }) => (
  <input
    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${className}`}
    {...props}
  />
);

interface PaperTradingResetProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PaperTradingReset: React.FC<PaperTradingResetProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const { account, resetPaperAccount } = usePaperTrading();
  
  const [customBalance, setCustomBalance] = useState<string>('10000');
  const [useCustomBalance, setUseCustomBalance] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [confirmText, setConfirmText] = useState('');

  const handleReset = async () => {
    // Validate confirmation text
    if (confirmText.toLowerCase() !== 'reset') {
      toast({
        title: t('paperTradingHelp.resetModal.confirmationRequired'),
        description: t('paperTradingHelp.resetModal.confirmationRequiredError'),
        variant: "destructive"
      });
      return;
    }

    try {
      setIsResetting(true);
      
      const newBalance = useCustomBalance ? parseFloat(customBalance) : undefined;
      
      // Validate custom balance
      if (useCustomBalance && (isNaN(newBalance!) || newBalance! <= 0)) {
        toast({
          title: t('paperTradingHelp.resetModal.invalidBalance'),
          description: t('paperTradingHelp.resetModal.invalidBalanceDesc'),
          variant: "destructive"
        });
        return;
      }

      await resetPaperAccount(newBalance);
      
      // Reset form
      setConfirmText('');
      setUseCustomBalance(false);
      setCustomBalance('10000');
      
      onClose();
      
    } catch (error) {
      console.error('Error resetting paper account:', error);
    } finally {
      setIsResetting(false);
    }
  };

  const handleClose = () => {
    if (!isResetting) {
      setConfirmText('');
      setUseCustomBalance(false);
      setCustomBalance('10000');
      onClose();
    }
  };

  if (!isOpen) return null;

  const canResetToday = account?.reset_count < 3; // Assuming 3 resets per day limit
  const resetsRemaining = Math.max(0, 3 - (account?.reset_count || 0));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-xl ${isMobile ? 'w-full max-w-sm' : 'w-full max-w-md'}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold flex items-center text-orange-600`}>
            <RotateCcw className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2`} />
            Reset Paper Account
          </h2>
          <Button variant="ghost" size="sm" onClick={handleClose} disabled={isResetting}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {/* Warning */}
          <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                  Warning: This action cannot be undone
                </h3>
                <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                  This will permanently delete all your paper trading history and reset your virtual balance.
                </p>
              </div>
            </div>
          </div>

          {/* Reset Limit Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Reset Limit
                </h3>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  You have {resetsRemaining} reset{resetsRemaining !== 1 ? 's' : ''} remaining today.
                  {account?.last_reset_at && (
                    <span className="block mt-1">
                      Last reset: {new Date(account.last_reset_at).toLocaleDateString()}
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Current Account Info */}
          {account && (
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
              <h3 className="text-sm font-medium mb-2">Current Account</h3>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Balance:</span>
                  <span className="ml-1 font-medium">
                    ${account.virtual_balance?.toLocaleString()}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Total P&L:</span>
                  <span className={`ml-1 font-medium ${account.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    ${account.total_pnl?.toLocaleString()}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Trades:</span>
                  <span className="ml-1 font-medium">{account.total_trades_count}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Win Rate:</span>
                  <span className="ml-1 font-medium">{account.win_rate?.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          )}

          {/* Balance Options */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">New Balance</h3>
            
            {/* Default Balance Option */}
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="balanceOption"
                checked={!useCustomBalance}
                onChange={() => setUseCustomBalance(false)}
                className="text-orange-600 focus:ring-orange-500"
                disabled={isResetting}
              />
              <span className="text-sm">Default ($10,000)</span>
            </label>

            {/* Custom Balance Option */}
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="balanceOption"
                checked={useCustomBalance}
                onChange={() => setUseCustomBalance(true)}
                className="text-orange-600 focus:ring-orange-500"
                disabled={isResetting}
              />
              <span className="text-sm">Custom Amount</span>
            </label>

            {/* Custom Balance Input */}
            {useCustomBalance && (
              <div className="ml-6">
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    type="number"
                    value={customBalance}
                    onChange={(e) => setCustomBalance(e.target.value)}
                    placeholder="Enter amount"
                    className="pl-10"
                    min="1"
                    step="0.01"
                    disabled={isResetting}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Minimum: $1.00, Maximum: $1,000,000
                </p>
              </div>
            )}
          </div>

          {/* Confirmation Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Type "RESET" to confirm:
            </label>
            <Input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder="Type RESET here"
              className="uppercase"
              disabled={isResetting}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex space-x-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isResetting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleReset}
            disabled={isResetting || !canResetToday || confirmText.toLowerCase() !== 'reset'}
            className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
          >
            {isResetting ? (
              <>
                <RotateCcw className="w-4 h-4 mr-2 animate-spin" />
                Resetting...
              </>
            ) : (
              <>
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset Account
              </>
            )}
          </Button>
        </div>

        {/* Reset Limit Reached */}
        {!canResetToday && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
            <p className="text-sm text-red-800 dark:text-red-200 text-center">
              Daily reset limit reached. Try again tomorrow.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaperTradingReset;
