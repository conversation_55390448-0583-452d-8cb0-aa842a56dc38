from flask import Blueprint, request, jsonify, url_for, current_app
import json
import re
import jwt
import time
from flask_jwt_extended import (
    get_jwt_identity,
    jwt_required,
    get_jwt,
    create_access_token,
    create_refresh_token,
    set_access_cookies,
    set_refresh_cookies,
    unset_jwt_cookies,
    verify_jwt_in_request,
    get_jwt_header,
    decode_token
)
from datetime import datetime, timedelta
import uuid

from app.auth.google_oauth import GoogleOAuth
from app.auth.jwt_manager import jwt_manager
from app.auth.two_factor import TwoFactorAuth
from app.auth.security import SecurityManager
from app.auth.decorators import rate_limit_required
from app.auth.password_validator import validate_password_for_registration
from app.models.user import User
from app.models.password_reset import PasswordResetToken, TwoFAResetRequest
from app.services.ip_tracking_service import IPTrackingService
from app import db

auth_bp = Blueprint('auth', __name__)
from flask_cors import cross_origin

@auth_bp.route('/tier', methods=['GET', 'OPTIONS'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["GET", "OPTIONS"], allow_headers=["Content-Type", "Authorization", "Accept"])
def get_tier():
    if request.method == "OPTIONS":
        return jsonify({'status': 'ok'}), 200
    # TODO: Replace with real logic for user tier
    return jsonify({'tier': 'Basic', 'description': 'Basic Tier'}), 200


def get_request_data(key, default=None):
    """Safely get data from request JSON or form."""
    if request.is_json and request.json:
        return request.json.get(key, default)
    return request.form.get(key, default)

def log_request_details():
    """Log details about the incoming request."""
    current_app.logger.info(f"Incoming request: {request.method} {request.path}")
    current_app.logger.info(f"Headers: {dict(request.headers)}")
    current_app.logger.info(f"Content-Type: {request.content_type}")
    
    try:
        if request.content_type == 'application/json':
            data = request.get_json() or {}
            current_app.logger.info(f"JSON data: {data}")
        elif request.content_type == 'application/x-www-form-urlencoded':
            current_app.logger.info(f"Form data: {request.form.to_dict()}")
    except Exception as e:
        current_app.logger.error(f"Error parsing request data: {str(e)}")
    
    if request.files:
        current_app.logger.info(f"Files: {list(request.files.keys())}")

@auth_bp.route('/debug', methods=['GET'])
def debug_auth():
    """Debug authentication setup."""
    try:
        from flask import current_app
        
        return jsonify({
            'google_client_id': current_app.config.get('GOOGLE_CLIENT_ID', 'Not set')[:20] + '...',
            'google_client_secret': 'Set' if current_app.config.get('GOOGLE_CLIENT_SECRET') else 'Not set',
            'discovery_url': current_app.config.get('GOOGLE_DISCOVERY_URL', 'Not set'),
            'app_config_keys': list(current_app.config.keys()),
            'request_url': request.url,
            'request_base_url': request.base_url,
            'request_host': request.host
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/login/google', methods=['POST'])
# @rate_limit_required("10 per minute")  # Temporarily disabled for testing
def google_login():
    """Initiate Google OAuth login."""
    current_app.logger.info("\n" + "="*80)
    current_app.logger.info("LOGIN ENDPOINT - START")
    current_app.logger.info(f"Request method: {request.method}")
    current_app.logger.info(f"Request URL: {request.url}")
    current_app.logger.info(f"Request headers: {dict(request.headers)}")
    current_app.logger.info(f"Content-Type: {request.content_type}")
    
    try:
        # Log request data
        if request.content_type == 'application/json':
            request_data = request.get_json() or {}
            current_app.logger.info(f"JSON request data: {request_data}")
        else:
            request_data = request.form.to_dict()
            current_app.logger.info(f"Form data: {request_data}")
        
        # Get redirect URI from request or use default
        redirect_uri = get_request_data('redirect_uri')
        if not redirect_uri:
            redirect_uri = url_for('auth.callback', _external=True)
            current_app.logger.info(f"Using default redirect_uri: {redirect_uri}")
        else:
            current_app.logger.info(f"Using provided redirect_uri: {redirect_uri}")
        
        # Log the full callback URL that will be used
        callback_url = url_for('auth.callback', _external=True)
        current_app.logger.info(f"Generated callback URL: {callback_url}")
        
        # Allow frontend redirect URIs
        expected_redirect_uri = url_for('auth.callback', _external=True)
        frontend_redirect_uri = redirect_uri
        
        # Check if it's a valid frontend redirect (localhost:5173)
        if frontend_redirect_uri.startswith('http://localhost:5173'):
            current_app.logger.info(f"Accepting frontend redirect URI: {frontend_redirect_uri}")
        elif redirect_uri != expected_redirect_uri:
            current_app.logger.warning(f"Redirect URI mismatch. Expected: {expected_redirect_uri}, Got: {redirect_uri}")
        else:
            current_app.logger.info("Redirect URI validation successful")
        
        # Generate state for CSRF protection
        state = str(uuid.uuid4())
        
        # Store state in session or Redis (implement as needed)
        
        # Get Google OAuth authorization URL
        google_oauth = GoogleOAuth()
        auth_url = google_oauth.get_authorization_url(redirect_uri, state)
        
        current_app.logger.info(f"Generated auth_url: {auth_url}")
        
        return jsonify({
            'authorization_url': auth_url,
            'state': state
        })
        
    except Exception as e:
        current_app.logger.error(f"Login initiation failed: {str(e)}")
        return jsonify({'message': 'Login initiation failed', 'error': str(e)}), 500


@auth_bp.route('/callback', methods=['GET', 'POST'])
# @rate_limit_required("5 per minute")  # Temporarily disabled for testing
def callback():
    """Handle Google OAuth callback."""
    request_id = str(uuid.uuid4())[:8]  # Short ID for request tracking
    current_app.logger.info(f"\n{'='*80}\n=== OAUTH CALLBACK STARTED [{request_id}] ===\n{'='*80}")
    
    try:
        # Log request details
        current_app.logger.info(f"[{request_id}] === REQUEST DETAILS ===")
        current_app.logger.info(f"[{request_id}] Method: {request.method}")
        current_app.logger.info(f"[{request_id}] URL: {request.url}")
        current_app.logger.info(f"[{request_id}] Content-Type: {request.content_type}")
        current_app.logger.info(f"[{request_id}] Headers: {dict(request.headers)}")
        current_app.logger.info(f"[{request_id}] Remote Addr: {request.remote_addr}")
        current_app.logger.info(f"[{request_id}] User Agent: {request.user_agent}")
        
        # Parse request data
        request_data = {}
        try:
            if request.is_json:
                request_data = request.get_json(silent=True) or {}
                current_app.logger.info(f"[{request_id}] Received JSON data: {request_data}")
            elif request.form:
                request_data = request.form.to_dict()
                current_app.logger.info(f"[{request_id}] Received form data: {request_data}")
            else:
                # Try to get raw data for debugging
                raw_data = request.get_data(as_text=True)
                if raw_data:
                    current_app.logger.info(f"[{request_id}] Raw request data: {raw_data}")
                    try:
                        request_data = json.loads(raw_data)
                        current_app.logger.info(f"[{request_id}] Parsed raw data as JSON: {request_data}")
                    except json.JSONDecodeError as je:
                        current_app.logger.error(f"[{request_id}] JSON decode error: {str(je)}", exc_info=True)
                        return jsonify({
                            'message': 'Invalid JSON data',
                            'error': 'invalid_json',
                            'details': str(je),
                            'request_id': request_id
                        }), 400
        except Exception as e:
            current_app.logger.error(f"[{request_id}] Error parsing request data: {str(e)}", exc_info=True)
            return jsonify({
                'message': 'Invalid request data',
                'error': 'invalid_request',
                'details': str(e),
                'request_id': request_id
            }), 400
        
        # Get required parameters with detailed logging
        code = request_data.get('code')
        state = request_data.get('state')
        redirect_uri = request_data.get('redirect_uri')
        error = request_data.get('error')
        
        # Log all received parameters for debugging
        current_app.logger.info(f"[{request_id}] === REQUEST PARAMETERS ===")
        current_app.logger.info(f"[{request_id}] Code: {'PRESENT' if code else 'MISSING'}")
        current_app.logger.info(f"[{request_id}] State: {state or 'Not provided'}")
        current_app.logger.info(f"[{request_id}] Redirect URI: {redirect_uri or 'Not provided'}")
        current_app.logger.info(f"[{request_id}] Error: {error or 'None'}")
        current_app.logger.info(f"[{request_id}] All request data: {request_data}")
        
        # Log parameter details
        current_app.logger.info(f"\n[{request_id}] === OAUTH PARAMETERS ===")
        current_app.logger.info(f"[{request_id}] Code: {'PRESENT' if code else 'MISSING'}")
        current_app.logger.info(f"[{request_id}] State: {state or 'Not provided'}")
        current_app.logger.info(f"[{request_id}] Redirect URI: {redirect_uri or 'Not provided'}")
        current_app.logger.info(f"[{request_id}] Error: {error or 'None'}")
        
        # Check for OAuth errors
        if error:
            error_desc = request_data.get('error_description', 'No error description')
            current_app.logger.error(f"[{request_id}] OAuth provider error: {error} - {error_desc}")
            return jsonify({
                'message': 'OAuth authentication failed',
                'error': error,
                'error_description': error_desc,
                'request_id': request_id
            }), 401
            
        # Validate required parameters
        if not code:
            current_app.logger.error(f"[{request_id}] No authorization code provided")
            return jsonify({
                'message': 'Authorization code is required',
                'error': 'missing_code',
                'request_id': request_id
            }), 400
            
        if not redirect_uri:
            redirect_uri = url_for('auth.callback', _external=True)
            current_app.logger.info(f"[{request_id}] Using default redirect_uri: {redirect_uri}")
        
        # Check if this code has already been used (prevent replay attacks)
        try:
            redis_client = getattr(current_app, "redis", None)
            if not redis_client:
                current_app.logger.warning(f"[{request_id}] Redis client not available, skipping code reuse check")
            else:
                cache_key = f'oauth_code_used:{code}'
                if redis_client.get(cache_key):
                    current_app.logger.warning(f"[{request_id}] Authorization code already used: {code}")
                    return jsonify({
                        'message': 'Authorization code has already been used',
                        'error': 'code_reused',
                        'request_id': request_id
                    }), 400
                # Mark this code as used (expires in 5 minutes to handle race conditions)
                redis_client.setex(cache_key, 300, '1')
        except Exception as redis_err:
            current_app.logger.error(f"[{request_id}] Redis error: {str(redis_err)}")
            # Continue without Redis protection if there's an error
            pass
            
        # Initialize Google OAuth
        google_oauth = GoogleOAuth()
            
        # Authenticate user with Google
        current_app.logger.info(f"[{request_id}] Starting Google OAuth authentication")
        try:
            # Log before authentication
            current_app.logger.info(f"[{request_id}] Calling authenticate_user with code: {code[:10]}..., redirect_uri: {redirect_uri}")
            
            # Get authentication result which contains user and tokens
            auth_result = google_oauth.authenticate_user(code, redirect_uri)
            
            # Log the authentication result structure
            current_app.logger.info(f"[{request_id}] Authentication result keys: {list(auth_result.keys())}")
            
            user_data = auth_result.get('user', {})  # This is already a dictionary from to_dict()
            tokens = auth_result.get('tokens', {})
            requires_2fa = auth_result.get('requires_2fa', False)
            
            # Log the user data structure and subscription status
            if user_data:
                current_app.logger.info(f"[{request_id}] User data keys: {list(user_data.keys())}")
                if 'subscription_status' in user_data:
                    current_app.logger.info(f"[{request_id}] Subscription status in user_data: {user_data['subscription_status']} (type: {type(user_data['subscription_status']).__name__})")
            else:
                current_app.logger.warning(f"[{request_id}] No user data in authentication result")
            
            if not user_data or 'id' not in user_data:
                raise ValueError("Invalid user data received from authentication")
            
            user_id = user_data['id']
            user_email = user_data.get('email', '<EMAIL>')
            
            current_app.logger.info(f"[{request_id}] User authenticated successfully: {user_email}")
            
            # Check if user is active
            if not user_data.get('is_active', True):
                current_app.logger.warning(f"[{request_id}] Login attempt for deactivated account: {user_email}")
                return jsonify({
                    'message': 'This account has been deactivated',
                    'error': 'account_deactivated',
                    'request_id': request_id
                }), 403
                
            # Check if 2FA is required
            if user_data.get('two_fa_enabled', False):
                current_app.logger.info(f"[{request_id}] 2FA required for user: {user_email}")
                temp_token = jwt_manager.create_2fa_pending_token(user_id)

                # Automatically send 2FA code via email
                current_app.logger.info(f"[{request_id}] Sending 2FA code to user email")
                try:
                    from app.auth.two_factor import TwoFactorAuth
                    success = TwoFactorAuth.send_email_2fa_code(user_id)
                    if not success:
                        current_app.logger.error(f"[{request_id}] Failed to send 2FA code to user: {user_email}")
                        return jsonify({
                            'message': 'Failed to send 2FA verification code',
                            'error': 'email_send_failed',
                            'request_id': request_id
                        }), 500
                    else:
                        current_app.logger.info(f"[{request_id}] 2FA code sent successfully to: {user_email}")
                except Exception as e:
                    current_app.logger.error(f"[{request_id}] Error sending 2FA code: {str(e)}")
                    return jsonify({
                        'message': 'Failed to send 2FA verification code',
                        'error': 'email_send_error',
                        'request_id': request_id
                    }), 500

                return jsonify({
                    'message': '2FA verification required',
                    'requires_2fa': True,
                    'temp_token': temp_token,
                    'user': user_data,
                    'request_id': request_id
                })
            else:
                # Log successful login attempt
                IPTrackingService.log_login_attempt(
                    user_id=user_id,
                    login_successful=True,
                    request_obj=request
                )

                # Generate access and refresh tokens
                current_app.logger.info(f"[{request_id}] Creating access and refresh tokens")
                tokens = jwt_manager.create_tokens_for_user(user_id)

                # Clear any previous revocation so new tokens are valid
                jwt_manager.clear_user_revocation(user_id)

                current_app.logger.info(f"[{request_id}] Authentication successful for user: {user_email}")
                return jsonify({
                    'message': 'Authentication successful',
                    'access_token': tokens['access_token'],
                    'refresh_token': tokens['refresh_token'],
                    'user': user_data,
                    'request_id': request_id
                })
                
        except ValueError as ve:
            # Handle validation errors
            current_app.logger.error(f"[{request_id}] Validation error: {str(ve)}", exc_info=True)
            return jsonify({
                'message': 'Authentication validation failed',
                'error': 'validation_error',
                'details': str(ve),
                'request_id': request_id
            }), 400
            
        except Exception as auth_error:
            # Handle authentication errors
            current_app.logger.error(
                f"[{request_id}] Authentication error: {str(auth_error)}", 
                exc_info=True
            )
            return jsonify({
                'message': 'Authentication failed',
                'error': 'authentication_error',
                'details': str(auth_error),
                'request_id': request_id
            }), 401
            
    except Exception as e:
        # Handle unexpected errors
        current_app.logger.error(
            f"[{request_id}] Unexpected error: {str(e)}", 
            exc_info=True
        )
        return jsonify({
            'message': 'An unexpected error occurred',
            'error': 'server_error',
            'details': 'Internal server error',
            'request_id': request_id
        }), 500
        
    finally:
        current_app.logger.info(f"=== OAUTH CALLBACK COMPLETED [{request_id}] ===\n")


@auth_bp.route('/2fa/setup', methods=['POST'])
@jwt_required()
def setup_2fa():
    """Setup 2FA for authenticated user."""
    try:
        user_id = get_jwt_identity()
        
        # Setup 2FA
        setup_result = TwoFactorAuth.setup_2fa_for_user(user_id)
        
        return jsonify({
            'message': '2FA setup initiated',
            'qr_code': setup_result['qr_code'],
            'backup_codes': setup_result['backup_codes'],
            'secret': setup_result['secret']  # Only for initial setup
        })
        
    except Exception as e:
        return jsonify({'message': '2FA setup failed', 'error': str(e)}), 500


@auth_bp.route('/2fa/verify', methods=['POST'])
@jwt_required()
def verify_2fa_setup():
    """Verify and enable 2FA for user."""
    try:
        user_id = get_jwt_identity()
        
        # Get verification code
        verification_code = get_request_data('code')
        
        if not verification_code:
            return jsonify({'message': 'Verification code required', 'error': 'missing_code'}), 400
        
        # Enable 2FA
        TwoFactorAuth.enable_2fa_for_user(user_id, verification_code)
        
        return jsonify({'message': '2FA enabled successfully'})
        
    except ValueError as e:
        return jsonify({'message': str(e), 'error': 'verification_failed'}), 400
    except Exception as e:
        return jsonify({'message': '2FA verification failed', 'error': str(e)}), 500


@auth_bp.route('/2fa/resend', methods=['POST'])
@rate_limit_required("10 per minute")
def resend_2fa_code():
    """Resend 2FA code during login."""
    try:
        # Get temporary token
        temp_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        if not temp_token:
            return jsonify({'message': 'Temporary token required', 'error': 'missing_temp_token'}), 400

        # Verify temporary token and extract user ID
        try:
            decoded_token = jwt.decode(temp_token, current_app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
            user_id = decoded_token.get('sub')

            if not user_id:
                return jsonify({'message': 'Invalid temporary token', 'error': 'invalid_temp_token'}), 400

        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'Temporary token expired', 'error': 'temp_token_expired'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': 'Invalid temporary token', 'error': 'invalid_temp_token'}), 401

        # Send new 2FA code
        from app.auth.two_factor import TwoFactorAuth
        success = TwoFactorAuth.send_email_2fa_code(user_id)

        if not success:
            return jsonify({'message': 'Failed to send 2FA code', 'error': 'send_failed'}), 500

        return jsonify({'message': '2FA code sent successfully'})

    except Exception as e:
        current_app.logger.error(f"2FA resend failed: {str(e)}")
        return jsonify({'message': '2FA resend failed', 'error': str(e)}), 500


@auth_bp.route('/2fa/login', methods=['POST'])
@rate_limit_required("20 per minute")
def verify_2fa_login():
    """Verify 2FA code during login."""
    try:
        # Get temporary token
        temp_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        if not temp_token:
            return jsonify({'message': 'Temporary token required', 'error': 'missing_temp_token'}), 400

        # Verify temporary token and extract user ID
        try:
            decoded_token = jwt.decode(temp_token, current_app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
            user_id = decoded_token.get('sub')

            if not user_id:
                return jsonify({'message': 'Invalid temporary token', 'error': 'invalid_temp_token'}), 400

        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'Temporary token expired', 'error': 'temp_token_expired'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': 'Invalid temporary token', 'error': 'invalid_temp_token'}), 401

        # Get 2FA code
        two_fa_code = get_request_data('code')

        if not two_fa_code:
            return jsonify({'message': '2FA code required', 'error': 'missing_2fa_code'}), 400

        # user_id is already extracted from the decoded token above

        # Verify 2FA code (try both email-based and TOTP)
        verification_success = False

        # First try email-based 2FA
        if TwoFactorAuth.verify_email_2fa_code(user_id, two_fa_code):
            verification_success = True
        # Then try TOTP-based 2FA
        elif TwoFactorAuth.verify_user_2fa_code(user_id, two_fa_code):
            verification_success = True

        if verification_success:
            # Clear any existing revocation timestamps for this user before creating new tokens
            current_app.logger.info(f"[2FA Debug] Clearing revocation timestamps for user {user_id}")
            jwt_manager.clear_user_revocation(user_id)

            # Create full access tokens
            tokens = jwt_manager.create_tokens_for_user(user_id)

            user = User.query.get(user_id)

            if not user:
                return jsonify({'message': 'User not found', 'error': 'user_not_found'}), 404

            # Log successful login attempt after 2FA
            IPTrackingService.log_login_attempt(
                user_id=user_id,
                login_successful=True,
                request_obj=request
            )

            return jsonify({
                'message': '2FA verification successful',
                'user': user.to_dict(),
                **tokens
            })
        else:
            return jsonify({'message': 'Invalid 2FA code', 'error': 'invalid_2fa_code'}), 401
            
    except Exception as e:
        return jsonify({'message': '2FA verification failed', 'error': str(e)}), 401


@auth_bp.route('/2fa/disable', methods=['POST'])
@jwt_required()
def disable_2fa():
    """Disable 2FA for user."""
    try:
        user_id = get_jwt_identity()
        
        # Get verification code
        verification_code = get_request_data('code')
        
        if not verification_code:
            return jsonify({'message': 'Verification code required', 'error': 'missing_code'}), 400
        
        # Disable 2FA
        TwoFactorAuth.disable_2fa_for_user(user_id, verification_code)
        
        return jsonify({'message': '2FA disabled successfully'})
        
    except ValueError as e:
        return jsonify({'message': str(e), 'error': 'verification_failed'}), 400
    except Exception as e:
        return jsonify({'message': '2FA disable failed', 'error': str(e)}), 500


@auth_bp.route('/2fa/backup-codes', methods=['POST'])
@jwt_required()
def regenerate_backup_codes():
    """Regenerate backup codes for user."""
    try:
        user_id = get_jwt_identity()
        
        # Get verification code
        verification_code = get_request_data('code')
        
        if not verification_code:
            return jsonify({'message': 'Verification code required', 'error': 'missing_code'}), 400
        
        # Regenerate backup codes
        backup_codes = TwoFactorAuth.regenerate_backup_codes(user_id, verification_code)
        
        return jsonify({
            'message': 'Backup codes regenerated successfully',
            'backup_codes': backup_codes
        })
        
    except ValueError as e:
        return jsonify({'message': str(e), 'error': 'verification_failed'}), 400
    except Exception as e:
        return jsonify({'message': 'Backup code regeneration failed', 'error': str(e)}), 500


@auth_bp.route('/2fa/status', methods=['GET'])
@jwt_required()
def get_2fa_status():
    """Get 2FA status for user."""
    try:
        user_id = get_jwt_identity()
        
        status = TwoFactorAuth.get_user_2fa_status(user_id)
        
        return jsonify(status)
        
    except Exception as e:
        return jsonify({'message': 'Failed to get 2FA status', 'error': str(e)}), 500


@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """Get current user information."""
    try:
        print("=== /me Endpoint Called ===")
        
        # Get the JWT token from the request
        try:
            jwt_data = get_jwt()
            print(f"JWT Data: {jwt_data}")
            current_user_id = get_jwt_identity()
            print(f"Extracted User ID: {current_user_id}")
        except Exception as jwt_error:
            print(f"JWT Error: {str(jwt_error)}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'message': 'Invalid or expired token',
                'error': str(jwt_error)
            }), 401
        
        print(f"Database URI: {current_app.config.get('SQLALCHEMY_DATABASE_URI')}")
        print("Attempting to query database...")
        
        # Query the database for the user
        try:
            from app import db
            print("Database session:", db.session)
            user = User.query.get(current_user_id)
            print(f"User query result: {user}")
        except Exception as db_error:
            print(f"Database Error: {str(db_error)}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'message': 'Database error',
                'error': str(db_error)
            }), 500
        
        if not user:
            print(f"User not found with ID: {current_user_id}")
            return jsonify({
                'success': False,
                'message': 'User not found',
                'error': 'user_not_found',
                'user_id': current_user_id
            }), 404
        
        print(f"Successfully fetched user: {user.email}")
        
        # Use the user's to_dict method to get complete user data
        user_data = user.to_dict()
        print(f"User data to return: {user_data}")

        return jsonify({
            'success': True,
            'user': user_data
        })
        
    except Exception as e:
        print(f"Unexpected error in /me endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': str(e)
        }), 500


@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh_token():
    """Refresh access token using refresh token."""
    try:
        # Get the identity from the refresh token
        current_user = get_jwt_identity()
        
        # Get the original token to extract claims
        jwt_data = get_jwt()
        
        # Get the user from the database
        user = User.query.get(current_user)
        if not user:
            return jsonify({'message': 'User not found'}), 404
        
        # Create a new access token with the same identity
        access_token = create_access_token(
            identity=current_user,
            additional_claims={
                'tier': user.get_tier(),
                'two_fa_enabled': user.two_fa_enabled,
                'is_active': user.is_active
            }
        )
        
        return jsonify({
            'access_token': access_token,
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Token refresh failed: {str(e)}')
        return jsonify({
            'message': 'Token refresh failed', 
            'error': str(e)
        }), 401


@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """Logout user and immediately blacklist current token and revoke all user tokens."""
    user_id = None
    try:
        from flask_jwt_extended import get_jwt
        
        user_id = get_jwt_identity()
        current_jwt = get_jwt()
        
        current_app.logger.info(f"[LOGOUT] Starting logout for user {user_id}")
        
        # 1. Immediately blacklist the current token being used
        current_jti = current_jwt.get('jti')
        if current_jti:
            current_app.logger.info(f"[LOGOUT] Blacklisting current token JTI: {current_jti}")
            jwt_manager.blacklist_token(current_jti)
        
        # 2. Revoke all user tokens (sets revocation timestamp)
        current_app.logger.info(f"[LOGOUT] Revoking all tokens for user {user_id}")
        jwt_manager.revoke_all_user_tokens(user_id)
        
        # 3. Delete user session data
        current_app.logger.info(f"[LOGOUT] Deleting user session")
        jwt_manager.delete_user_session(user_id)
        
        # 4. Clear any active sessions for this user
        if hasattr(jwt_manager, 'active_sessions') and user_id in jwt_manager.active_sessions:
            for session in jwt_manager.active_sessions[user_id]:
                session_jti = session.get('jti')
                if session_jti:
                    jwt_manager.blacklist_token(session_jti)
            jwt_manager.active_sessions[user_id] = []
        
        current_app.logger.info(f"[LOGOUT] Logout completed successfully for user {user_id}")
        
        return jsonify({
            'message': 'Logout successful',
            'success': True,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        current_app.logger.error(f"[LOGOUT] Logout failed for user {user_id or 'unknown'}: {str(e)}")
        return jsonify({
            'message': 'Logout failed',
            'error': str(e),
            'success': False
        }), 500


@auth_bp.route('/sessions', methods=['GET'])
@jwt_required()
def get_active_sessions():
    """Get active sessions for user."""
    try:
        user_id = get_jwt_identity()
        
        
        sessions = jwt_manager.get_active_sessions(user_id)
        
        return jsonify({
            'sessions': sessions,
            'count': len(sessions)
        })
        
    except Exception as e:
        return jsonify({'message': 'Failed to get sessions', 'error': str(e)}), 500


@auth_bp.route('/sessions/<session_jti>', methods=['DELETE'])
@jwt_required()
def revoke_session(session_jti):
    """Revoke specific session."""
    try:
        user_id = get_jwt_identity()
        
        
        
        # Blacklist the specific token
        jwt_manager.blacklist_token(session_jti)
        
        # Remove from active sessions
        jwt_manager.remove_session(user_id, session_jti)
        
        return jsonify({'message': 'Session revoked successfully'})
        
    except Exception as e:
        return jsonify({'message': 'Session revocation failed', 'error': str(e)}), 500


@auth_bp.route('/verify-email', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
def verify_email():
    """Verify user email with token."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        token = data.get('token', '').strip()
        if not token:
            return jsonify({'message': 'Verification token is required'}), 400

        current_app.logger.info(f"Email verification attempt with token: {token[:8]}...")

        # Find user by verification token
        user = User.query.filter_by(verification_token=token).first()
        if not user:
            current_app.logger.warning(f"Invalid verification token: {token[:8]}...")
            return jsonify({'message': 'Invalid verification token'}), 400

        # Verify email
        success, message = user.verify_email(token)
        if not success:
            current_app.logger.warning(f"Email verification failed for user {user.email}: {message}")
            return jsonify({'message': message}), 400

        # Save changes
        db.session.commit()
        current_app.logger.info(f"Email verified successfully for user: {user.email}")

        # Log security event
        SecurityManager.log_security_event(
            user_id=user.id,
            event_type='email_verified',
            ip_address=request.remote_addr,
            details={'verification_method': 'email_token'},
            risk_level='low'
        )

        return jsonify({
            'message': 'Email verified successfully',
            'user': user.to_dict()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Email verification failed: {str(e)}")
        return jsonify({'message': 'Email verification failed', 'error': str(e)}), 500


@auth_bp.route('/resend-verification', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
def resend_verification():
    """Resend verification email."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        email = data.get('email', '').strip().lower()
        if not email:
            return jsonify({'message': 'Email is required'}), 400

        current_app.logger.info(f"Resend verification request for: {email}")

        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            # Don't reveal if email exists or not for security
            return jsonify({'message': 'If the email exists, a verification email has been sent'}), 200

        # Check if user can resend verification
        if not user.can_resend_verification():
            if user.email_verified:
                return jsonify({'message': 'Email is already verified'}), 400
            elif user.google_id:
                return jsonify({'message': 'Google OAuth users do not need email verification'}), 400
            else:
                return jsonify({'message': 'Cannot resend verification email'}), 400

        # Send verification email
        from app.auth.email_verification import EmailVerification
        EmailVerification.send_verification_email(user)

        current_app.logger.info(f"Verification email resent to: {email}")

        return jsonify({'message': 'Verification email sent successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Resend verification failed: {str(e)}")
        return jsonify({'message': 'Failed to resend verification email', 'error': str(e)}), 500


@auth_bp.route('/forgot-password', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
@rate_limit_required("10 per 15 minutes")
def forgot_password():
    """Request password reset for traditional users."""
    try:
        # Check IP restrictions first
        ip_check = IPTrackingService.check_ip_restrictions(request)
        if not ip_check['allowed']:
            return jsonify({
                'message': ip_check['message'],
                'error': ip_check['reason']
            }), 403

        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        email = data.get('email', '').strip().lower()
        if not email:
            return jsonify({'message': 'Email is required'}), 400

        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            # Don't reveal if email exists or not for security
            return jsonify({'message': 'If an account with this email exists, you will receive a password reset link.'}), 200

        # Check if user is OAuth user (no password reset for OAuth users)
        if user.google_id and not user.password_hash:
            return jsonify({'message': 'OAuth users cannot reset password. Please use Google login.'}), 400

        # Check if user is active
        if not user.is_active:
            return jsonify({'message': 'Account has been deactivated'}), 403

        # Check if user has verified email
        if not user.email_verified:
            return jsonify({'message': 'Please verify your email address first'}), 403

        # Clean up old unused tokens for this user
        old_tokens = PasswordResetToken.query.filter_by(
            user_id=user.id,
            is_used=False
        ).filter(PasswordResetToken.expires_at < datetime.utcnow()).all()

        for token in old_tokens:
            db.session.delete(token)

        # Check for existing valid token
        existing_token = PasswordResetToken.query.filter_by(
            user_id=user.id,
            is_used=False
        ).filter(PasswordResetToken.expires_at > datetime.utcnow()).first()

        if existing_token:
            return jsonify({'message': 'A password reset link has already been sent. Please check your email or wait before requesting another.'}), 429

        # Generate reset token
        reset_token = str(uuid.uuid4())

        # Create password reset token record
        password_reset = PasswordResetToken(
            user_id=user.id,
            token=reset_token,
            ip_address=IPTrackingService.get_client_ip(request),
            user_agent=request.headers.get('User-Agent', '')
        )

        db.session.add(password_reset)
        db.session.commit()

        # Send password reset email
        from app.services.email_service import EmailService
        try:
            email_service = EmailService()
            email_service.send_password_reset_email(user.email, user.full_name, reset_token)

            # Log security event
            SecurityManager.log_security_event(
                user_id=user.id,
                event_type='password_reset_requested',
                ip_address=request.remote_addr,
                details={
                    'email': user.email,
                    'ip_address': IPTrackingService.get_client_ip(request),
                    'user_agent': request.headers.get('User-Agent', '')[:100]
                },
                risk_level='medium'
            )

            current_app.logger.info(f"Password reset email sent to: {user.email}")

        except Exception as email_error:
            current_app.logger.error(f"Failed to send password reset email: {str(email_error)}")
            # Delete the token if email failed
            db.session.delete(password_reset)
            db.session.commit()
            return jsonify({'message': 'Failed to send password reset email. Please try again.'}), 500

        return jsonify({'message': 'If an account with this email exists, you will receive a password reset link.'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Forgot password failed: {str(e)}")
        return jsonify({'message': 'Password reset request failed', 'error': str(e)}), 500


@auth_bp.route('/reset-password', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
@rate_limit_required("15 per 15 minutes")
def reset_password():
    """Reset password using valid token."""
    try:
        # Check IP restrictions first
        ip_check = IPTrackingService.check_ip_restrictions(request)
        if not ip_check['allowed']:
            return jsonify({
                'message': ip_check['message'],
                'error': ip_check['reason']
            }), 403

        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        token = data.get('token', '').strip()
        new_password = data.get('password', '')

        if not token or not new_password:
            return jsonify({'message': 'Token and new password are required'}), 400

        # Validate password strength
        password_validation = validate_password_for_registration(new_password)
        if not password_validation['is_valid']:
            return jsonify({
                'message': 'Password does not meet requirements',
                'errors': password_validation['error_messages'],
                'requirements_met': password_validation['requirements_met'],
                'recommendations': password_validation['recommendations']
            }), 400

        # Find and validate token
        reset_token = PasswordResetToken.query.filter_by(token=token).first()
        if not reset_token:
            return jsonify({'message': 'Invalid or expired reset token'}), 400

        if not reset_token.is_valid():
            return jsonify({'message': 'Invalid or expired reset token'}), 400

        # Get user
        user = User.query.get(reset_token.user_id)
        if not user:
            return jsonify({'message': 'User not found'}), 404

        # Check if user is active
        if not user.is_active:
            return jsonify({'message': 'Account has been deactivated'}), 403

        # Update password
        user.set_password(new_password)
        user.updated_at = datetime.utcnow()

        # Mark token as used
        reset_token.mark_as_used()

        # Invalidate all other reset tokens for this user
        other_tokens = PasswordResetToken.query.filter(
            PasswordResetToken.user_id == user.id,
            PasswordResetToken.id != reset_token.id,
            PasswordResetToken.is_used == False
        ).all()

        for other_token in other_tokens:
            other_token.mark_as_used()

        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user.id,
            event_type='password_reset_completed',
            ip_address=request.remote_addr,
            details={'email': user.email},
            risk_level='high'
        )

        # Send security notification email
        from app.services.email_service import EmailService
        try:
            email_service = EmailService()
            email_service.send_password_changed_notification(user.email, user.full_name)
        except Exception as email_error:
            current_app.logger.error(f"Failed to send password change notification: {str(email_error)}")

        current_app.logger.info(f"Password reset completed for user: {user.email}")

        return jsonify({'message': 'Password reset successfully. You can now log in with your new password.'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Reset password failed: {str(e)}")
        return jsonify({'message': 'Password reset failed', 'error': str(e)}), 500


@auth_bp.route('/validate-reset-token', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
def validate_reset_token():
    """Validate password reset token."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        token = data.get('token', '').strip()
        if not token:
            return jsonify({'message': 'Token is required'}), 400

        # Find and validate token
        reset_token = PasswordResetToken.query.filter_by(token=token).first()
        if not reset_token:
            return jsonify({'valid': False, 'message': 'Invalid token'}), 200

        if not reset_token.is_valid():
            return jsonify({'valid': False, 'message': 'Token has expired'}), 200

        # Get user info
        user = User.query.get(reset_token.user_id)
        if not user or not user.is_active:
            return jsonify({'valid': False, 'message': 'Invalid token'}), 200

        return jsonify({
            'valid': True,
            'email': user.email,
            'expires_at': reset_token.expires_at.isoformat()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Validate reset token failed: {str(e)}")
        return jsonify({'valid': False, 'message': 'Token validation failed'}), 500


@auth_bp.route('/2fa/reset-request', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
@rate_limit_required("5 per hour")
def request_2fa_reset():
    """Submit 2FA reset request with security verification."""
    try:
        # Check IP restrictions first
        ip_check = IPTrackingService.check_ip_restrictions(request)
        if not ip_check['allowed']:
            return jsonify({
                'message': ip_check['message'],
                'error': ip_check['reason']
            }), 403

        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        # Required fields
        email = data.get('email', '').strip().lower()
        full_name = data.get('full_name', '').strip()
        reason = data.get('reason', '').strip()

        if not all([email, full_name, reason]):
            return jsonify({'message': 'Email, full name, and reason are required'}), 400

        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            # Don't reveal if email exists or not for security
            return jsonify({'message': 'If an account with this email exists and has 2FA enabled, your request will be processed.'}), 200

        # Check if user has 2FA enabled
        if not user.two_fa_enabled:
            return jsonify({'message': 'If an account with this email exists and has 2FA enabled, your request will be processed.'}), 200

        # Check if user is active
        if not user.is_active:
            return jsonify({'message': 'Account has been deactivated'}), 403

        # Check for existing pending request
        existing_request = TwoFAResetRequest.query.filter_by(
            user_id=user.id,
            status='pending'
        ).first()

        if existing_request:
            return jsonify({'message': 'You already have a pending 2FA reset request. Please wait for admin review.'}), 429

        # Optional security verification fields
        last_login_date = data.get('last_login_date', '').strip()
        account_creation_date = data.get('account_creation_date', '').strip()
        recent_activity = data.get('recent_activity_description', '').strip()

        # Security questions
        security_answers = {
            'question_1': data.get('security_question_1', '').strip(),
            'answer_1': data.get('security_answer_1', '').strip(),
            'question_2': data.get('security_question_2', '').strip(),
            'answer_2': data.get('security_answer_2', '').strip(),
            'question_3': data.get('security_question_3', '').strip(),
            'answer_3': data.get('security_answer_3', '').strip()
        }

        # Create 2FA reset request
        reset_request = TwoFAResetRequest(
            user_id=user.id,
            reason=reason,
            full_name_provided=full_name,
            email_provided=email,
            ip_address=IPTrackingService.get_client_ip(request),
            user_agent=request.headers.get('User-Agent', '')
        )

        # Set optional fields
        if last_login_date:
            reset_request.last_login_date_provided = last_login_date
        if account_creation_date:
            reset_request.account_creation_date_provided = account_creation_date
        if recent_activity:
            reset_request.recent_activity_description = recent_activity

        # Set security questions and answers
        for key, value in security_answers.items():
            if value:
                setattr(reset_request, f'security_{key}', value)

        # Calculate risk level
        reset_request.calculate_risk_level(user)

        db.session.add(reset_request)
        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user.id,
            event_type='2fa_reset_requested',
            ip_address=request.remote_addr,
            details={
                'email': user.email,
                'risk_level': reset_request.risk_level,
                'request_id': reset_request.id
            },
            risk_level='high'
        )

        # Send notification emails
        from app.services.email_service import EmailService
        try:
            email_service = EmailService()
            # Notify user
            email_service.send_2fa_reset_request_confirmation(user.email, user.full_name, reset_request.id)

            # Notify admins
            email_service.send_2fa_reset_admin_notification(reset_request)

        except Exception as email_error:
            current_app.logger.error(f"Failed to send 2FA reset notification emails: {str(email_error)}")

        current_app.logger.info(f"2FA reset request submitted for user: {user.email} (Request ID: {reset_request.id})")

        return jsonify({
            'message': 'Your 2FA reset request has been submitted and will be reviewed by our security team. You will receive an email notification once it has been processed.',
            'request_id': reset_request.id
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"2FA reset request failed: {str(e)}")
        return jsonify({'message': '2FA reset request failed', 'error': str(e)}), 500


@auth_bp.route('/2fa/reset-request/status/<string:request_id>', methods=['GET'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["GET"], allow_headers=["Content-Type", "Authorization", "Accept"])
def get_2fa_reset_status(request_id):
    """Get status of 2FA reset request."""
    try:
        reset_request = TwoFAResetRequest.query.get_or_404(request_id)

        # Basic status information (no sensitive data)
        return jsonify({
            'request_id': reset_request.id,
            'status': reset_request.status,
            'created_at': reset_request.created_at.isoformat(),
            'reviewed_at': reset_request.reviewed_at.isoformat() if reset_request.reviewed_at else None,
            'completed_at': reset_request.completed_at.isoformat() if reset_request.completed_at else None,
            'admin_notes': reset_request.admin_notes if reset_request.status in ['approved', 'rejected'] else None
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get 2FA reset status failed: {str(e)}")
        return jsonify({'message': 'Failed to get request status', 'error': str(e)}), 500


@auth_bp.route('/register', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
def register():
    """Traditional user registration endpoint."""
    try:
        data = request.get_json()
        current_app.logger.info(f"=== REGISTRATION START ===")
        current_app.logger.info(f"Registration request data: {data}")
        current_app.logger.info(f"Request headers: {dict(request.headers)}")
        
        if not data:
            current_app.logger.error("No data provided in registration request")
            return jsonify({'message': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['firstName', 'lastName', 'email', 'password']
        for field in required_fields:
            if not data.get(field) or not str(data.get(field)).strip():
                current_app.logger.warning(f"Missing field: {field}")
                return jsonify({'message': f'{field} is required'}), 400
        
        first_name = data['firstName'].strip()
        last_name = data['lastName'].strip()
        email = data['email'].strip().lower()
        password = data['password']
        
        current_app.logger.info(f"Processing registration: {first_name} {last_name} ({email})")
        
        # Validate email format
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, email):
            current_app.logger.warning(f"Invalid email format: {email}")
            return jsonify({'message': 'Invalid email format'}), 400
        
        # Enhanced password validation
        password_validation = validate_password_for_registration(password)
        if not password_validation['is_valid']:
            current_app.logger.warning(f"Password validation failed: {password_validation['error_messages']}")
            return jsonify({
                'message': 'Password does not meet security requirements',
                'password_errors': password_validation['error_messages'],
                'password_requirements': password_validation['requirements_met'],
                'recommendations': password_validation['recommendations']
            }), 400
        
        # Check if user already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            current_app.logger.warning(f"User already exists: {email}")
            return jsonify({'message': 'User with this email already exists'}), 409
        
        # Create full name
        full_name = f"{first_name} {last_name}"
        
        # Create new user
        try:
            user = User(email, full_name, password=password)
            current_app.logger.info(f"Created user object: {user}")
            db.session.add(user)
            db.session.flush()  # Get user ID
            current_app.logger.info(f"User added to session with ID: {user.id}")
        except Exception as e:
            current_app.logger.error(f"Error creating user: {str(e)}")
            raise e
        
        # Initialize tier status for new user (Tier 1 by default)
        try:
            from app.models.user_tier_status import UserTierStatus
            from app.models.subscription import Subscription
            
            tier_status = UserTierStatus(
                user_id=user.id,
                tier_1=True,
                tier_2=False,
                tier_3=False
            )
            db.session.add(tier_status)
            current_app.logger.info(f"Added tier status for user {user.id}")
            
            # Create default Tier 1 subscription
            subscription = Subscription(user_id=user.id)
            db.session.add(subscription)
            current_app.logger.info(f"Added subscription for user {user.id}")
            
        except Exception as e:
            current_app.logger.error(f"Error adding tier status/subscription: {str(e)}")
            raise e
        
        try:
            db.session.commit()
            current_app.logger.info(f"Registration completed for user {user.id}")
        except Exception as e:
            current_app.logger.error(f"Error committing registration: {str(e)}")
            raise e
        
        # Log registration for security
        SecurityManager.log_security_event(
            user_id=user.id,
            event_type='user_registered',
            ip_address=request.remote_addr,
            details={'registration_method': 'traditional'},
            risk_level='low'
        )

        # Send verification email instead of auto-login
        from app.auth.email_verification import EmailVerification
        try:
            EmailVerification.send_verification_email(user)
            current_app.logger.info(f"Verification email sent to: {user.email}")
        except Exception as e:
            current_app.logger.error(f"Failed to send verification email: {str(e)}")
            # Don't fail registration if email sending fails

        return jsonify({
            'message': 'Registration successful! Please check your email to verify your account.',
            'email': user.email,
            'requires_verification': True
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Registration failed: {str(e)}")
        return jsonify({'message': 'Registration failed', 'error': str(e)}), 500


@auth_bp.route('/login', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
def traditional_login():
    """Traditional email/password login."""
    try:
        # Check IP restrictions first
        ip_check = IPTrackingService.check_ip_restrictions(request)
        if not ip_check['allowed']:
            return jsonify({
                'message': ip_check['message'],
                'error': ip_check['reason']
            }), 403

        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        email = data.get('email', '').strip().lower()
        password = data.get('password', '')

        if not email or not password:
            return jsonify({'message': 'Email and password are required'}), 400
        
        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            # Log failed login attempt
            IPTrackingService.log_login_attempt(
                user_id=None,
                login_successful=False,
                failure_reason='User not found',
                request_obj=request
            )
            IPTrackingService.record_failed_attempt(request)
            return jsonify({'message': 'Invalid credentials'}), 401

        # Check if user has password (traditional registration)
        if not user.password_hash:
            # Log failed login attempt
            IPTrackingService.log_login_attempt(
                user_id=user.id,
                login_successful=False,
                failure_reason='OAuth user attempted traditional login',
                request_obj=request
            )
            return jsonify({'message': 'Please use Google OAuth to login'}), 401

        # Verify password
        if not user.check_password(password):
            # Log failed login attempt
            IPTrackingService.log_login_attempt(
                user_id=user.id,
                login_successful=False,
                failure_reason='Invalid password',
                request_obj=request
            )
            IPTrackingService.record_failed_attempt(request)
            return jsonify({'message': 'Invalid credentials'}), 401
        
        # Check if user is active
        if not user.is_active:
            return jsonify({'message': 'Account has been deactivated'}), 403

        # Check email verification for traditional users (skip for Google OAuth users)
        if not user.google_id and not user.email_verified:
            return jsonify({
                'message': 'Please verify your email address before logging in',
                'requires_verification': True,
                'email': user.email
            }), 403

        # Check if 2FA is required
        if user.two_fa_enabled:
            current_app.logger.info(f"2FA required for user: {user.email}")
            temp_token = jwt_manager.create_2fa_pending_token(user.id)

            # Automatically send 2FA code via email
            current_app.logger.info(f"Sending 2FA code to user email")
            try:
                from app.auth.two_factor import TwoFactorAuth
                success = TwoFactorAuth.send_email_2fa_code(user.id)
                if not success:
                    current_app.logger.error(f"Failed to send 2FA code to user: {user.email}")
                    return jsonify({
                        'message': 'Failed to send 2FA verification code',
                        'error': 'email_send_failed'
                    }), 500
                else:
                    current_app.logger.info(f"2FA code sent successfully to: {user.email}")
            except Exception as e:
                current_app.logger.error(f"Error sending 2FA code: {str(e)}")
                return jsonify({
                    'message': 'Failed to send 2FA verification code',
                    'error': 'email_send_error'
                }), 500

            return jsonify({
                'message': '2FA verification required',
                'requires_2fa': True,
                'temp_token': temp_token,
                'user': user.to_dict()
            })
        else:
            # Log successful login attempt
            IPTrackingService.log_login_attempt(
                user_id=user.id,
                login_successful=True,
                request_obj=request
            )

            # Clear any previous revocation so new tokens are valid
            jwt_manager.clear_user_revocation(user.id)

            # Generate tokens
            tokens = jwt_manager.create_tokens_for_user(user.id)

            return jsonify({
                'message': 'Login successful',
                'user': user.to_dict(),
                'access_token': tokens['access_token'],
                'refresh_token': tokens['refresh_token']
            }), 200
        
    except Exception as e:
        current_app.logger.error(f"Login failed: {str(e)}")
        return jsonify({'message': 'Login failed', 'error': str(e)}), 500


@auth_bp.route('/server-time', methods=['GET'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["GET"], allow_headers=["Content-Type", "Authorization", "Accept"])
def get_server_time():
    """Get server timestamp for clock synchronization."""
    try:
        server_timestamp = int(time.time() * 1000)  # Milliseconds
        server_utc = datetime.utcnow().isoformat() + 'Z'

        current_app.logger.info(f"[CLOCK_SYNC] Server time requested: {server_timestamp}")

        return jsonify({
            'timestamp': server_timestamp,
            'utc_time': server_utc,
            'timezone': 'UTC'
        }), 200

    except Exception as e:
        current_app.logger.error(f"Server time request failed: {str(e)}")
        return jsonify({'message': 'Failed to get server time', 'error': str(e)}), 500


@auth_bp.route('/validate-password', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
def validate_password():
    """Validate password strength in real-time."""
    try:
        data = request.get_json()
        if not data or 'password' not in data:
            return jsonify({'message': 'Password is required'}), 400

        password = data['password']
        validation_result = validate_password_for_registration(password)

        return jsonify({
            'valid': validation_result['is_valid'],
            'requirements_met': validation_result['requirements_met'],
            'strength_score': validation_result['strength_score'],
            'strength_label': validation_result['strength_label'],
            'error_messages': validation_result['error_messages'],
            'recommendations': validation_result['recommendations'],
            'is_common_password': validation_result['is_common_password']
        }), 200

    except Exception as e:
        current_app.logger.error(f"Password validation failed: {str(e)}")
        return jsonify({'message': 'Password validation failed', 'error': str(e)}), 500