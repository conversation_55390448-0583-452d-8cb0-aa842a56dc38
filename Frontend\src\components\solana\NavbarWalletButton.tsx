import React, { useState } from "react";
import { createPortal } from "react-dom";
import { useWallet } from "@solana/wallet-adapter-react";
import { useWalletModal } from "@solana/wallet-adapter-react-ui";
import { Loader2, Wallet, ChevronDown, AlertCircle } from "lucide-react";
import { toast, toastError } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/Button";
import { cn } from "@/lib/utils";
import { useNFTVerificationContext } from "@/contexts/NFTVerificationContext";
import { useTranslation } from '@/hooks/useTranslation';

const NavbarWalletButton: React.FC = () => {
  const { t } = useTranslation();
  const { connected, connecting, publicKey, disconnect } = useWallet();
  const { setVisible } = useWalletModal();
  const { refreshTierStatus } = useNFTVerificationContext();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showDisconnectModal, setShowDisconnectModal] = useState(false);

  const handleConnect = async () => {
    try {
      if (!connected) {
        setVisible(true);
      }
    } catch (err) {
      const error = err as Error;
      console.error('Wallet connection error:', error);

      let errorMessage = t('wallet.connectionFailed');
      if (error.message.includes('User rejected the request')) {
        errorMessage = t('wallet.connectionRejected');
      } else if (error.message.includes('Wallet not found')) {
        errorMessage = t('wallet.walletNotFound');
      } else if (error.message.includes('WalletNotSelectedError')) {
        return; // Don't show toast for this case
      }

      toastError({
        title: t('wallet.connectionError'),
        description: errorMessage,
      });
    }
  };

  const handleDisconnectClick = () => {
    setShowDropdown(false);
    setShowDisconnectModal(true);
  };

  const handleConfirmDisconnect = async () => {
    try {
      // First disconnect the wallet
      await disconnect();
      setShowDisconnectModal(false);

      // Handle tier updates based on wallet disconnection
      const tierHandled = await handleTierOnWalletDisconnect();

      // Refresh tier status in dashboard
      await refreshTierStatus();

      // Only show generic disconnect toast if no specific tier message was shown
      if (!tierHandled) {
        toast({
          title: t('wallet.disconnected'),
          description: t('wallet.disconnectedSuccess'),
        });
      }
    } catch (err) {
      console.error('Disconnect error:', err);
      toastError({
        title: t('wallet.disconnectError'),
        description: t('wallet.disconnectFailed'),
      });
    }
  };

  const handleCancelDisconnect = () => {
    setShowDisconnectModal(false);
  };

  // Handle tier updates when wallet is disconnected
  const handleTierOnWalletDisconnect = async (): Promise<boolean> => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) return false;

      // Call backend endpoint to handle wallet disconnection and tier updates
      const response = await fetch('/api/trading/wallet/disconnect', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Wallet disconnect result:', result);

        // Show appropriate toast based on the action taken
        if (result.action === 'downgraded') {
          toastError({
            title: 'Tier Downgraded',
            description: result.message,
          });
          return true; // Tier message was shown
        } else if (result.action === 'preserved') {
          toast({
            title: 'Wallet Disconnected',
            description: result.message,
          });
          return true; // Tier message was shown
        }
        // For 'no_change' action, return false so main disconnect toast will be shown
        return false;
      } else {
        console.error('Failed to process wallet disconnect on backend');
        return false;
      }
    } catch (error) {
      console.error('Error handling tier on wallet disconnect:', error);
      // Don't show error toast to user, just log it
      return false;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.substring(0, 4)}...${address.slice(-4)}`;
  };

  if (!connected) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={handleConnect}
        disabled={connecting}
        className="relative px-2 max-w-[120px]"
      >
        {connecting ? (
          <>
            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
            <span className="text-xs truncate">{t('wallet.connecting')}</span>
          </>
        ) : (
          <>
            <Wallet className="mr-1 h-3 w-3" />
            <span className="text-xs truncate">{t('wallet.connect')}</span>
          </>
        )}
      </Button>
    );
  }

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowDropdown(!showDropdown)}
        className="relative flex items-center space-x-1 min-w-0 max-w-[120px] px-2"
      >
        <div className="h-2 w-2 rounded-full bg-green-500 flex-shrink-0"></div>
        <span className="text-xs font-medium truncate min-w-0">
          {formatAddress(publicKey?.toBase58() || '')}
        </span>
        <ChevronDown className={cn(
          "h-3 w-3 transition-transform flex-shrink-0",
          showDropdown && "rotate-180"
        )} />
      </Button>

      {showDropdown && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setShowDropdown(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute left-0 sm:left-auto sm:right-0 mt-2 w-64 sm:w-72 max-w-[calc(100vw-1rem)] bg-popover border rounded-md shadow-lg z-20 origin-top-left sm:origin-top-right">
            <div className="p-3 sm:p-4 border-b">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="h-3 w-3 rounded-full bg-green-500 flex-shrink-0"></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">{t('wallet.connected')}</p>
                  <p className="text-xs text-muted-foreground font-mono break-all">
                    {publicKey?.toBase58()}
                  </p>
                </div>
              </div>
            </div>

            <div className="p-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDisconnectClick}
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
              >
                {t('wallet.disconnect')}
              </Button>
            </div>
          </div>
        </>
      )}

      {/* Disconnect Confirmation Modal */}
      {showDisconnectModal && createPortal(
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[9999]">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
                <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-gray-900 dark:text-white">{t('wallet.confirmDisconnect')}</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.confirmDisconnectMessage')}</p>
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  <p className="font-medium mb-1">Important:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>If you are on the NFT Elite tier, you will be reverted to the Starter tier</li>
                    <li>You'll need to reconnect to access higher tiers</li>
                    <li>Any active memberships will remain valid</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex gap-3 justify-end">
              <button
                onClick={handleCancelDisconnect}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={handleConfirmDisconnect}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              >
                {t('wallet.disconnect')}
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default NavbarWalletButton;
