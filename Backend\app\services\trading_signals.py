import numpy as np
import pandas as pd
from sklearn.ensemble import <PERSON>ForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime
import logging
import os

# Import Elite ML Service
try:
    from app.services.elite_ml_service import Elite96PercentPredictor
    ELITE_ML_AVAILABLE = True
except ImportError:
    ELITE_ML_AVAILABLE = False
    logging.warning("Elite ML Service not available, falling back to legacy system")

# Import SL/TP ML System
try:
    from app.services.sl_tp_ml_predictors import SLTPMLManager
    SL_TP_ML_AVAILABLE = True
except ImportError:
    SL_TP_ML_AVAILABLE = False

# Import Hybrid Deep Learning System
try:
    from app.services.hybrid_deep_ml import HybridDeepMLEnhancer
    HYBRID_DEEP_ML_AVAILABLE = True
except ImportError:
    HYBRID_DEEP_ML_AVAILABLE = False
    logging.warning("SL/TP ML System not available, using fallback methods")

class TradingSignalGenerator:
    """Core trading signal generation logic extracted from trade.py"""
    
    def __init__(self, user_id: str, exchange_service, admin_monitoring_mode: bool = False):
        self.user_id = user_id
        self.exchange_service = exchange_service
        self.admin_monitoring_mode = admin_monitoring_mode
        self.logger = logging.getLogger(__name__)

        # Initialize Elite ML System
        self.elite_ml_enabled = os.getenv('ELITE_ML_ENABLED', 'true').lower() == 'true'
        self.elite_predictor = None

        if ELITE_ML_AVAILABLE and self.elite_ml_enabled:
            try:
                self.elite_predictor = Elite96PercentPredictor()
                # Try to load existing models
                if self.elite_predictor.load_models():
                    self.logger.info(f"Elite ML models loaded successfully for user {user_id}")
                else:
                    self.logger.warning(f"No elite ML models found for user {user_id}, will use legacy system")
            except Exception as e:
                self.logger.error(f"Failed to initialize Elite ML system: {str(e)}")
                self.elite_predictor = None

        # Initialize SL/TP ML System
        self.sl_tp_ml_enabled = os.getenv('SL_TP_ML_ENABLED', 'true').lower() == 'true'
        self.sl_tp_manager = None

        if SL_TP_ML_AVAILABLE and self.sl_tp_ml_enabled:
            try:
                self.sl_tp_manager = SLTPMLManager()
                self.logger.info(f"SL/TP ML system initialized for user {user_id}")
            except Exception as e:
                self.logger.error(f"Failed to initialize SL/TP ML system: {str(e)}")

        # Initialize Hybrid Deep Learning System
        self.hybrid_deep_ml_enabled = os.getenv('HYBRID_DEEP_ML_ENABLED', 'true').lower() == 'true'
        self.hybrid_deep_ml = None

        if HYBRID_DEEP_ML_AVAILABLE and self.hybrid_deep_ml_enabled:
            try:
                self.hybrid_deep_ml = HybridDeepMLEnhancer()
                # Try to load pre-trained models
                if self.hybrid_deep_ml.load_models():
                    self.logger.info(f"🚀 Hybrid Deep Learning system initialized with pre-trained models for user {user_id}")
                else:
                    self.logger.info(f"🤖 Hybrid Deep Learning system initialized (models need training) for user {user_id}")
            except Exception as e:
                self.logger.error(f"Failed to initialize Hybrid Deep Learning system: {str(e)}")
                self.hybrid_deep_ml = None
                self.sl_tp_manager = None
    
    def generate_signals(self, symbol: str = 'BTCUSDT', timeframe: str = '1h') -> Dict:
        """Generate trading signals for a symbol"""
        from app.models.user_tier_status import UserTierStatus
        from app.models.user import User
        try:
            # Check if user has auto-trading enabled
            user = User.query.get(self.user_id)
            if not user:
                self.logger.error(f"[SIGNAL_GEN_ERROR] User {self.user_id} not found")
                return {
                    'error': 'User not found',
                    'blocked': True
                }

            # Allow signal generation for paper trading mode even if auto-trading is disabled
            # Also allow in admin monitoring mode for system monitoring purposes
            if not self.admin_monitoring_mode and not user.auto_trading_enabled and not user.paper_trading_mode:
                return {
                    'message': 'Auto-trading is disabled. Enable it in your settings to receive trading signals.',
                    'auto_trading_enabled': False,
                    'blocked': False
                }



            # Log if paper trading mode is generating signals
            if user.paper_trading_mode:
                self.logger.info(f"[SIGNAL_GEN_PAPER] Generating signals for paper trading mode for user {self.user_id}")

            # Enforce tier payment status
            user_tier = UserTierStatus.query.filter_by(user_id=self.user_id).first()
            if user_tier and user_tier.payment_status == 'unpaid' and user_tier.profit_share_owed > 0:
                self.logger.warning(f"[SIGNAL_GEN_BLOCKED] User {self.user_id} blocked due to unpaid profit share.")
                return {
                    'error': 'Trading signals blocked: Unpaid profit share for your tier. Please settle your balance to continue.',
                    'blocked': True,
                    'owed': str(user_tier.profit_share_owed),
                    'tier': user_tier.get_current_tier()
                }

            # Fetch market data
            market_data = self._fetch_market_data(symbol, timeframe)

            if market_data is None:
                return {'error': 'Market data fetch failed - service unavailable'}

            data_length = len(market_data) if market_data is not None else 0

            if data_length < 100:
                return {'error': f'Insufficient market data: {data_length} records (need 100+)'}

            # Use Enhanced Confirmation Logic (Elite ML + Chart Agreement)
            return self._generate_confirmed_signals(market_data, symbol, timeframe)

            forecast, highest_price, lowest_price, forecast_result = forecast_data

            # Get swing points from the forecast result if available, otherwise calculate them
            swing_points = {
                'swing_high': forecast_result.get('swing_high'),
                'swing_low': forecast_result.get('swing_low')
            }

            # If swing points weren't in the forecast result, calculate them
            if swing_points['swing_high'] is None or swing_points['swing_low'] is None:
                calculated_swings = self._find_swing_points(market_data)
                swing_points['swing_high'] = swing_points['swing_high'] or calculated_swings.get('swing_high')
                swing_points['swing_low'] = swing_points['swing_low'] or calculated_swings.get('swing_low')

            # Calculate Heikin-Ashi
            heikin_ashi = self._calculate_heikin_ashi(market_data)

            # Generate trading signal
            signal_data = self._analyze_trading_conditions(
                market_data, forecast, swing_points, heikin_ashi,
                highest_price, lowest_price
            )

            if isinstance(signal_data, dict) and 'error' in signal_data:
                return signal_data

            return signal_data
            
        except Exception as e:
            return {'error': f'Signal generation exception: {str(e)}'}

    def _generate_confirmed_signals(self, market_data: pd.DataFrame, symbol: str, timeframe: str) -> Dict:
        """
        Enhanced signal generation with Elite ML + Chart confirmation
        Both systems must agree on direction for high-confidence trades
        """
        try:
            data_length = len(market_data) if market_data is not None else 0

            # 1. GET ELITE ML SIGNAL
            elite_signal = None
            elite_direction = 'HOLD'
            elite_confidence = 0.0

            if self.elite_predictor is not None and data_length >= 500:
                self.logger.info(f"[CONFIRMATION] Getting Elite ML signal for user {self.user_id}")
                elite_result = self.elite_predictor.generate_elite_signal(market_data)

                if 'error' not in elite_result:
                    # 🚀 HYBRID DEEP LEARNING ENHANCEMENT
                    if self.hybrid_deep_ml and self.hybrid_deep_ml.is_trained:
                        self.logger.info(f"[HYBRID_ML] Enhancing Elite ML with Deep Learning for user {self.user_id}")
                        elite_result = self.hybrid_deep_ml.enhance_elite_ml_prediction(market_data, elite_result)

                    elite_signal = elite_result
                    elite_direction = elite_result.get('signal', 'HOLD')
                    elite_confidence = elite_result.get('confidence', 0.0)
                    self.logger.info(f"[CONFIRMATION] Elite ML: {elite_direction} ({elite_confidence:.4f})")
                else:
                    self.logger.info(f"[CONFIRMATION] Elite ML error: {elite_result.get('error', 'Unknown')}")

            # 2. GET CHART FORECAST DIRECTION
            self.logger.info(f"[CONFIRMATION] Getting Chart forecast for user {self.user_id}")
            forecast_data = self._get_ml_forecast(market_data, symbol)
            chart_direction = 'HOLD'
            chart_confidence = 0.5  # Default to neutral confidence for HOLD

            if forecast_data:
                forecast, highest_price, lowest_price, forecast_result = forecast_data
                current_price = float(market_data['close'].iloc[-1])

                # Determine chart direction based on forecast
                if len(forecast) > 0:
                    # Look at next 6-12 hours of forecast
                    short_term_forecast = forecast[:12]  # Next 12 hours
                    avg_forecast_price = np.mean(short_term_forecast)

                    price_change_pct = (avg_forecast_price - current_price) / current_price

                    if price_change_pct > 0.005:  # +0.5% threshold
                        chart_direction = 'BUY'
                        chart_confidence = min(abs(price_change_pct) * 20, 0.95)  # Scale confidence
                    elif price_change_pct < -0.005:  # -0.5% threshold
                        chart_direction = 'SELL'
                        chart_confidence = min(abs(price_change_pct) * 20, 0.95)
                    else:
                        # HOLD case: price change is between -0.5% and +0.5% (neutral)
                        chart_direction = 'HOLD'
                        chart_confidence = 0.5  # Neutral confidence for HOLD signals

                    self.logger.info(f"[CONFIRMATION] Chart: {chart_direction} ({chart_confidence:.4f}) - "
                                   f"Price change: {price_change_pct:.4f}")

            # 3. APPLY CONFIRMATION LOGIC
            return self._apply_confirmation_logic(
                elite_direction, elite_confidence, elite_signal,
                chart_direction, chart_confidence,
                market_data, symbol, forecast_data
            )

        except Exception as e:
            self.logger.error(f"[CONFIRMATION] Error in confirmed signals: {str(e)}")
            return {'error': f'Confirmed signal generation failed: {str(e)}'}

    def _apply_confirmation_logic(self, elite_dir: str, elite_conf: float, elite_signal: Dict,
                                chart_dir: str, chart_conf: float, market_data: pd.DataFrame,
                                symbol: str, forecast_data: Optional[Tuple]) -> Dict:
        """Apply confirmation logic between Elite ML and Chart systems"""

        self.logger.info(f"[CONFIRMATION] Applying logic - Elite: {elite_dir} ({elite_conf:.4f}), "
                        f"Chart: {chart_dir} ({chart_conf:.4f})")

        # CASE 1: BOTH SYSTEMS AGREE (HIGH CONFIDENCE)
        if elite_dir == chart_dir and elite_dir != 'HOLD':
            self.logger.info(f"[CONFIRMATION] ✅ BOTH AGREE on {elite_dir} - HIGH CONFIDENCE")

            result = self._convert_elite_signal_to_trading_signal(elite_signal, market_data, symbol, forecast_data)
            result.update({
                'signal': elite_dir,
                'confidence': min(elite_conf + 0.1, 0.99),  # Boost confidence when both agree
                'confirmation': 'BOTH_AGREE',
                'elite_confidence': elite_conf,
                'chart_confidence': chart_conf,
                'reason': f'Both Elite ML and Chart agree on {elite_dir}',
                'risk_level': 'LOW',  # Both systems agree = lower risk
            })
            return result

        # CASE 2: SYSTEMS DISAGREE (REQUIRE HIGHER CONFIDENCE)
        elif elite_dir != chart_dir and elite_dir != 'HOLD' and chart_dir != 'HOLD':
            self.logger.info(f"[CONFIRMATION] ❌ DISAGREEMENT - Elite: {elite_dir}, Chart: {chart_dir}")

            # Only trade if Elite ML has VERY high confidence (92%+ optimized)
            if elite_conf >= 0.92:
                self.logger.info(f"[CONFIRMATION] ⚠️ ELITE OVERRIDE - Very high confidence: {elite_conf:.4f}")

                result = self._convert_elite_signal_to_trading_signal(elite_signal, market_data, symbol, forecast_data)
                result.update({
                    'signal': elite_dir,
                    'confidence': elite_conf * 0.9,  # Reduce confidence due to disagreement
                    'confirmation': 'ELITE_OVERRIDE',
                    'elite_confidence': elite_conf,
                    'chart_confidence': chart_conf,
                    'reason': f'Elite ML override: {elite_dir} (95%+ confidence) vs Chart: {chart_dir}',
                    'risk_level': 'HIGH',  # Disagreement = higher risk
                    'warning': 'Systems disagree - Elite ML overriding with very high confidence',
                })
                return result
            else:
                self.logger.info(f"[CONFIRMATION] 🛑 HOLD - Disagreement with Elite confidence < 95%")
                return {
                    'signal': 'HOLD',
                    'confidence': 0.5,
                    'confirmation': 'DISAGREEMENT_HOLD',
                    'elite_confidence': elite_conf,
                    'chart_confidence': chart_conf,
                    'reason': f'Systems disagree: Elite ML: {elite_dir} ({elite_conf:.2f}) vs Chart: {chart_dir} ({chart_conf:.2f})',
                    'risk_level': 'HIGH',
                    'warning': 'Systems disagree and Elite ML confidence < 95% - Holding position'
                }

        # CASE 3: ONLY ELITE ML HAS SIGNAL
        elif elite_dir != 'HOLD' and chart_dir == 'HOLD':
            self.logger.info(f"[CONFIRMATION] 🎯 ELITE ONLY - {elite_dir}")

            result = self._convert_elite_signal_to_trading_signal(elite_signal, market_data, symbol, forecast_data)
            result.update({
                'signal': elite_dir,
                'confidence': elite_conf,
                'confirmation': 'ELITE_ONLY',
                'elite_confidence': elite_conf,
                'chart_confidence': chart_conf,
                'reason': f'Elite ML signal: {elite_dir}, Chart neutral',
                'risk_level': 'MEDIUM',
            })
            return result

        # CASE 4: ONLY CHART HAS SIGNAL
        elif elite_dir == 'HOLD' and chart_dir != 'HOLD':
            self.logger.info(f"[CONFIRMATION] 📊 CHART ONLY - {chart_dir}")

            if forecast_data:
                forecast, highest_price, lowest_price, forecast_result = forecast_data

                # Get swing points from the forecast result if available, otherwise calculate them
                swing_points = {
                    'swing_high': forecast_result.get('swing_high'),
                    'swing_low': forecast_result.get('swing_low')
                }

                # If swing points weren't in the forecast result, calculate them
                if swing_points['swing_high'] is None or swing_points['swing_low'] is None:
                    calculated_swings = self._find_swing_points(market_data)
                    swing_points['swing_high'] = swing_points['swing_high'] or calculated_swings.get('swing_high')
                    swing_points['swing_low'] = swing_points['swing_low'] or calculated_swings.get('swing_low')

                # Calculate Heikin-Ashi
                heikin_ashi = self._calculate_heikin_ashi(market_data)

                # Generate trading signal using legacy system
                signal_data = self._analyze_trading_conditions(
                    market_data, forecast, swing_points, heikin_ashi,
                    highest_price, lowest_price
                )

                if isinstance(signal_data, dict) and 'error' not in signal_data:
                    signal_data.update({
                        'confirmation': 'CHART_ONLY',
                        'elite_confidence': elite_conf,
                        'chart_confidence': chart_conf,
                        'reason': f'Chart-based signal: {chart_dir}, Elite ML neutral',
                        'risk_level': 'MEDIUM'
                    })
                    return signal_data

            # Calculate potential moves for admin dashboard (even with chart error)
            potential_up_move = 0.0
            potential_down_move = 0.0
            current_price = float(market_data['close'].iloc[-1]) if len(market_data) > 0 else 0.0

            return {
                'signal': 'HOLD',
                'confidence': 0.5,
                'confirmation': 'CHART_ERROR',
                'elite_confidence': elite_conf,
                'chart_confidence': chart_conf,
                'reason': 'Chart system error - unable to generate signal',
                'risk_level': 'HIGH',
                'potential_up_move': round(potential_up_move, 4),
                'potential_down_move': round(potential_down_move, 4),
                'current_price': current_price
            }

        # CASE 5: BOTH SYSTEMS NEUTRAL
        else:
            self.logger.info(f"[CONFIRMATION] 😴 BOTH NEUTRAL - No signals")

            # Calculate potential moves for admin dashboard
            potential_up_move = 0.0
            potential_down_move = 0.0
            current_price = 0.0

            if forecast_data:
                forecast, highest_price, lowest_price, forecast_result = forecast_data
                current_price = float(market_data['close'].iloc[-1])

                if current_price > 0:
                    potential_up_move = (highest_price - current_price) / current_price * 100
                    potential_down_move = (current_price - lowest_price) / current_price * 100

            return {
                'signal': 'HOLD',
                'confidence': 0.5,
                'confirmation': 'BOTH_NEUTRAL',
                'elite_confidence': elite_conf,
                'chart_confidence': chart_conf,
                'reason': 'Both systems neutral - no clear signal',
                'risk_level': 'LOW',
                'potential_up_move': round(potential_up_move, 4),
                'potential_down_move': round(potential_down_move, 4),
                'current_price': current_price
            }

    def _fetch_market_data(self, symbol: str, timeframe: str, lookback: int = 1000) -> Optional[pd.DataFrame]:
        """Fetch historical market data (extracted from trade.py fetch_binance_futures_data)"""
        try:
            try:
                from app.services.market_data import ml_service
            except (ImportError, Exception):
                return None

            # Check if ml_service has the expected attributes
            if not hasattr(ml_service, 'market_data'):
                return None

            # Use futures klines to match terminal bot data source
            if hasattr(ml_service.market_data, 'get_futures_klines'):
                # Use futures API to match terminal bot
                klines = ml_service.market_data.get_futures_klines(symbol, timeframe, lookback)
            elif hasattr(ml_service.market_data, 'get_klines'):
                # Fallback to spot API if futures not available
                klines = ml_service.market_data.get_klines(symbol, timeframe, lookback)
            else:
                return None

            if not klines:
                return None
            
            # Convert to pandas DataFrame similar to trade.py format
            data = []
            for kline in klines:
                try:
                    data.append({
                        'timestamp': kline['timestamp'],
                        'open': float(kline['open']),
                        'high': float(kline['high']),
                        'low': float(kline['low']),
                        'close': float(kline['close']),
                        'volume': float(kline['volume'])
                    })
                except (KeyError, ValueError, TypeError):
                    continue

            if not data:
                return None

            df = pd.DataFrame(data)

            # Convert timestamp to datetime index
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            return df

        except Exception:
            return None
    
    def _get_ml_forecast(self, market_data: pd.DataFrame, symbol: str) -> Optional[Tuple]:
        """Generate ML forecast using the ensemble model from ml_service"""
        try:
            if market_data is None or len(market_data) == 0:
                return None

            if 'close' not in market_data.columns:
                return None

            # Get the current price
            current_price = float(market_data['close'].iloc[-1])

            # Find swing points from the market data
            swing_points = self._find_swing_points(market_data)

            # Use the same forecast model as the chart plotly
            from app.services.market_data import ml_service
            forecast_result = ml_service.generate_ensemble_forecast(symbol, '1h', 72)

            if not forecast_result or 'forecast' not in forecast_result:
                return None

            # Extract forecast values from the response
            forecast_values = forecast_result['forecast']
            if not isinstance(forecast_values, (list, np.ndarray)) or len(forecast_values) == 0:
                return None
                
            # Convert to numpy array if it's a list
            if isinstance(forecast_values, list):
                forecast_values = np.array(forecast_values)
                
            # Get highest and lowest prices
            highest_price = float(np.max(forecast_values))
            lowest_price = float(np.min(forecast_values))
            
            # Add swing points to the forecast result if they exist
            if swing_points and 'swing_high' in swing_points and 'swing_low' in swing_points:
                forecast_result['swing_high'] = swing_points['swing_high']
                forecast_result['swing_low'] = swing_points['swing_low']

            return forecast_values, highest_price, lowest_price, forecast_result

        except Exception:
            return None
    
    def _find_swing_points(self, data: pd.DataFrame, max_look_back: int = 24) -> Dict:
        """Find swing high and low points (extracted from trade.py)"""
        last_swing_high = None
        last_swing_low = None
        
        try:
            for i in range(1, len(data) - 1):
                look_back_range = min(i, max_look_back)
                
                # Check for swing high
                prev_highs = data['high'].iloc[i - look_back_range:i]
                next_high = data['high'].iloc[i + 1]
                
                if data['high'].iloc[i] > np.max(prev_highs) and data['high'].iloc[i] > next_high:
                    last_swing_high = float(data['high'].iloc[i])
                
                # Check for swing low
                prev_lows = data['low'].iloc[i - look_back_range:i]
                next_low = data['low'].iloc[i + 1]
                
                if data['low'].iloc[i] < np.min(prev_lows) and data['low'].iloc[i] < next_low:
                    last_swing_low = float(data['low'].iloc[i])
            
            return {
                'swing_high': last_swing_high,
                'swing_low': last_swing_low
            }
            
        except Exception as e:
            self.logger.error(f"Error finding swing points: {str(e)}")
            return {'swing_high': None, 'swing_low': None}
    
    def _calculate_heikin_ashi(self, data: pd.DataFrame) -> Dict:
        """Calculate Heikin-Ashi values (extracted from trade.py)"""
        try:
            ha_data = data.copy()
            
            # Calculate HA Close
            ha_data['HA_Close'] = (
                ha_data['open'].astype(float) + 
                ha_data['high'].astype(float) + 
                ha_data['low'].astype(float) + 
                ha_data['close'].astype(float)
            ) / 4
            
            # Initialize HA Open
            ha_data['HA_Open'] = ha_data['open'].astype(float)
            
            # Calculate HA Open
            for i in range(1, len(ha_data)):
                ha_data.loc[ha_data.index[i], 'HA_Open'] = float(
                    (float(ha_data['HA_Open'].iloc[i-1]) + float(ha_data['HA_Close'].iloc[i-1])) / 2
                )
            
            # Calculate HA High and Low
            ha_data['HA_High'] = ha_data[['high', 'HA_Open', 'HA_Close']].astype(float).max(axis=1)
            ha_data['HA_Low'] = ha_data[['low', 'HA_Open', 'HA_Close']].astype(float).min(axis=1)
            
            # Get current and previous values
            current_ha_close = float(ha_data['HA_Close'].iloc[-1])
            current_ha_open = float(ha_data['HA_Open'].iloc[-1])
            previous_ha_close = float(ha_data['HA_Close'].iloc[-2])
            previous_ha_open = float(ha_data['HA_Open'].iloc[-2])
            
            ha_color = "green" if current_ha_close > current_ha_open else "red"
            prev_ha_color = "green" if previous_ha_close > previous_ha_open else "red"
            
            return {
                'current_ha_close': current_ha_close,
                'current_ha_open': current_ha_open,
                'previous_ha_close': previous_ha_close,
                'previous_ha_open': previous_ha_open,
                'ha_color': ha_color,
                'prev_ha_color': prev_ha_color
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating Heikin-Ashi: {str(e)}")
            return {}

    def _calculate_ml_sl_tp(self, market_data: pd.DataFrame, entry_price: float, signal: str) -> Dict:
        """
        Calculate Stop Loss and Take Profit using ML predictors
        Returns optimized SL/TP levels with confidence scores
        """
        try:
            if self.sl_tp_manager and SL_TP_ML_AVAILABLE:
                # Use ML-based SL/TP prediction
                ml_result = self.sl_tp_manager.get_optimal_sl_tp(market_data, entry_price, signal)

                if ml_result and ml_result['system_status'] in ['OPTIMAL', 'MODERATE']:
                    # 🚀 HYBRID DEEP LEARNING ENHANCEMENT FOR SL/TP
                    if self.hybrid_deep_ml and self.hybrid_deep_ml.is_trained:
                        self.logger.info(f"[HYBRID_ML] Enhancing SL/TP ML with Deep Learning for user {self.user_id}")
                        ml_result = self.hybrid_deep_ml.enhance_sl_tp_prediction(market_data, entry_price, signal, ml_result)

                    sl_result = ml_result['sl_result']
                    tp_result = ml_result['tp_result']

                    self.logger.info(f"ML SL/TP: SL=${sl_result['sl_price']:.2f} ({sl_result['confidence']:.1f}%), "
                                   f"TP=${tp_result['tp_price']:.2f} ({tp_result['confidence']:.1f}%), "
                                   f"RR=1:{ml_result['final_risk_reward']:.2f}")

                    return {
                        'stop_loss': sl_result['sl_price'],
                        'take_profit': tp_result['tp_price'],
                        'sl_confidence': sl_result['confidence'],
                        'tp_confidence': tp_result['confidence'],
                        'risk_reward_ratio': ml_result['final_risk_reward'],
                        'method': 'ML_OPTIMIZED_+_DEEP_LEARNING' if self.hybrid_deep_ml and self.hybrid_deep_ml.is_trained else 'ML_OPTIMIZED',
                        'system_status': ml_result['system_status'],
                        'deep_learning_enhanced': self.hybrid_deep_ml and self.hybrid_deep_ml.is_trained
                    }
                else:
                    self.logger.warning("ML SL/TP system returned poor results, using fallback")

            # Fallback to ATR-based calculation
            return self._calculate_fallback_sl_tp(market_data, entry_price, signal)

        except Exception as e:
            self.logger.error(f"Error in ML SL/TP calculation: {e}")
            return self._calculate_fallback_sl_tp(market_data, entry_price, signal)

    def _calculate_fallback_sl_tp(self, market_data: pd.DataFrame, entry_price: float, signal: str) -> Dict:
        """
        Fallback SL/TP calculation using ATR method
        """
        try:
            # Calculate ATR for dynamic SL/TP
            atr = self._calculate_atr(market_data, period=14)

            if signal == 'BUY':
                stop_loss = entry_price - (atr * 2.0)  # 2 ATR stop loss
                take_profit = entry_price + (atr * 4.0)  # 4 ATR take profit (1:2 ratio)

                # Ensure minimum distances
                min_stop_distance = entry_price * 0.005  # 0.5%
                min_tp_distance = entry_price * 0.010    # 1.0%

                if (entry_price - stop_loss) < min_stop_distance:
                    stop_loss = entry_price - min_stop_distance

                if (take_profit - entry_price) < min_tp_distance:
                    take_profit = entry_price + min_tp_distance

            else:  # SELL
                stop_loss = entry_price + (atr * 2.0)  # 2 ATR stop loss
                take_profit = entry_price - (atr * 4.0)  # 4 ATR take profit (1:2 ratio)

                # Ensure minimum distances
                min_stop_distance = entry_price * 0.005  # 0.5%
                min_tp_distance = entry_price * 0.010    # 1.0%

                if (stop_loss - entry_price) < min_stop_distance:
                    stop_loss = entry_price + min_stop_distance

                if (entry_price - take_profit) < min_tp_distance:
                    take_profit = entry_price - min_tp_distance

            # Calculate risk-reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 2.0

            return {
                'stop_loss': round(stop_loss, 2),
                'take_profit': round(take_profit, 2),
                'sl_confidence': 65.0,  # Default confidence for ATR method
                'tp_confidence': 65.0,
                'risk_reward_ratio': round(risk_reward_ratio, 2),
                'method': 'ATR_FALLBACK',
                'system_status': 'FALLBACK'
            }

        except Exception as e:
            self.logger.error(f"Error in fallback SL/TP calculation: {e}")
            # Emergency fallback
            sl_distance = entry_price * 0.008  # 0.8%
            tp_distance = entry_price * 0.016  # 1.6% (1:2 ratio)

            if signal == 'BUY':
                stop_loss = entry_price - sl_distance
                take_profit = entry_price + tp_distance
            else:
                stop_loss = entry_price + sl_distance
                take_profit = entry_price - tp_distance

            return {
                'stop_loss': round(stop_loss, 2),
                'take_profit': round(take_profit, 2),
                'sl_confidence': 50.0,
                'tp_confidence': 50.0,
                'risk_reward_ratio': 2.0,
                'method': 'EMERGENCY_FALLBACK',
                'system_status': 'EMERGENCY'
            }


    
    def _analyze_trading_conditions(self, market_data: pd.DataFrame, forecast, swing_points: Dict,
                                  heikin_ashi: Dict, highest_price: float, lowest_price: float) -> Dict:
        """
        OPTIMIZED Legacy Trading Conditions - Enhanced Multi-Indicator Analysis
        Used only when Elite ML is neutral (HOLD) as fallback system
        """
        try:
            if market_data is None or len(market_data) == 0:
                return {'error': 'Market data is None or empty'}

            if 'close' not in market_data.columns:
                return {'error': 'Close price data not available'}

            current_price = float(market_data['close'].iloc[-1])

            # ENHANCED TECHNICAL INDICATORS CALCULATION
            self.logger.info(f"[LEGACY_OPT] Calculating optimized technical indicators...")

            # 1. MULTIPLE MOVING AVERAGES (instead of just SMA12)
            market_data['SMA12'] = market_data['close'].astype(float).rolling(window=12).mean()
            market_data['SMA20'] = market_data['close'].astype(float).rolling(window=20).mean()
            market_data['SMA50'] = market_data['close'].astype(float).rolling(window=50).mean()
            market_data['EMA12'] = market_data['close'].astype(float).ewm(span=12).mean()
            market_data['EMA20'] = market_data['close'].astype(float).ewm(span=20).mean()

            current_sma12 = float(market_data['SMA12'].iloc[-1])
            current_sma20 = float(market_data['SMA20'].iloc[-1])
            current_sma50 = float(market_data['SMA50'].iloc[-1])
            current_ema12 = float(market_data['EMA12'].iloc[-1])
            current_ema20 = float(market_data['EMA20'].iloc[-1])

            # 2. RSI CALCULATION
            delta = market_data['close'].astype(float).diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            market_data['RSI'] = 100 - (100 / (1 + rs))
            current_rsi = float(market_data['RSI'].iloc[-1])

            # 3. MACD CALCULATION
            ema_12 = market_data['close'].astype(float).ewm(span=12).mean()
            ema_26 = market_data['close'].astype(float).ewm(span=26).mean()
            market_data['MACD'] = ema_12 - ema_26
            market_data['MACD_Signal'] = market_data['MACD'].ewm(span=9).mean()
            market_data['MACD_Histogram'] = market_data['MACD'] - market_data['MACD_Signal']

            current_macd = float(market_data['MACD'].iloc[-1])
            current_macd_signal = float(market_data['MACD_Signal'].iloc[-1])
            current_macd_hist = float(market_data['MACD_Histogram'].iloc[-1])
            prev_macd_hist = float(market_data['MACD_Histogram'].iloc[-2])

            # 4. BOLLINGER BANDS
            bb_period = 20
            bb_std = 2
            market_data['BB_Middle'] = market_data['close'].astype(float).rolling(window=bb_period).mean()
            bb_std_dev = market_data['close'].astype(float).rolling(window=bb_period).std()
            market_data['BB_Upper'] = market_data['BB_Middle'] + (bb_std_dev * bb_std)
            market_data['BB_Lower'] = market_data['BB_Middle'] - (bb_std_dev * bb_std)

            current_bb_upper = float(market_data['BB_Upper'].iloc[-1])
            current_bb_lower = float(market_data['BB_Lower'].iloc[-1])
            current_bb_middle = float(market_data['BB_Middle'].iloc[-1])

            # 5. VOLUME ANALYSIS
            market_data['Volume_SMA20'] = market_data['volume'].astype(float).rolling(window=20).mean()
            current_volume = float(market_data['volume'].iloc[-1])
            avg_volume = float(market_data['Volume_SMA20'].iloc[-1])
            volume_surge = current_volume > (avg_volume * 1.5)  # 50% above average

            # 6. PRICE MOMENTUM
            price_change_1h = (current_price - float(market_data['close'].iloc[-2])) / float(market_data['close'].iloc[-2])
            price_change_4h = (current_price - float(market_data['close'].iloc[-5])) / float(market_data['close'].iloc[-5]) if len(market_data) >= 5 else 0
            price_change_12h = (current_price - float(market_data['close'].iloc[-13])) / float(market_data['close'].iloc[-13]) if len(market_data) >= 13 else 0

            # Calculate potential moves
            potential_up_move = (highest_price - current_price) / current_price if current_price > 0 else 0
            potential_down_move = (current_price - lowest_price) / current_price if current_price > 0 else 0

            # Extract values
            last_swing_high = swing_points.get('swing_high')
            last_swing_low = swing_points.get('swing_low')
            ha_color = heikin_ashi.get('ha_color')
            prev_ha_color = heikin_ashi.get('prev_ha_color')
            previous_ha_close = heikin_ashi.get('previous_ha_close')

            # OPTIMIZED TREND ANALYSIS
            # 1. Moving Average Alignment (Trend Strength)
            ma_bullish_alignment = (current_price > current_ema12 > current_ema20 > current_sma20 > current_sma50)
            ma_bearish_alignment = (current_price < current_ema12 < current_ema20 < current_sma20 < current_sma50)
            ma_mixed = not ma_bullish_alignment and not ma_bearish_alignment

            # 2. RSI Conditions (Momentum + Overbought/Oversold)
            rsi_oversold = current_rsi < 35  # More conservative than 30
            rsi_overbought = current_rsi > 65  # More conservative than 70
            rsi_neutral = 40 <= current_rsi <= 60
            rsi_bullish_momentum = 35 < current_rsi < 65 and current_rsi > float(market_data['RSI'].iloc[-2])
            rsi_bearish_momentum = 35 < current_rsi < 65 and current_rsi < float(market_data['RSI'].iloc[-2])

            # 3. MACD Conditions (Trend Confirmation)
            macd_bullish_cross = current_macd > current_macd_signal and float(market_data['MACD'].iloc[-2]) <= float(market_data['MACD_Signal'].iloc[-2])
            macd_bearish_cross = current_macd < current_macd_signal and float(market_data['MACD'].iloc[-2]) >= float(market_data['MACD_Signal'].iloc[-2])
            macd_histogram_rising = current_macd_hist > prev_macd_hist
            macd_histogram_falling = current_macd_hist < prev_macd_hist

            # 4. Bollinger Bands (Volatility + Mean Reversion)
            bb_squeeze = (current_bb_upper - current_bb_lower) / current_bb_middle < 0.04  # Tight bands
            bb_expansion = (current_bb_upper - current_bb_lower) / current_bb_middle > 0.08  # Wide bands
            price_near_bb_lower = current_price < (current_bb_lower * 1.02)  # Within 2% of lower band
            price_near_bb_upper = current_price > (current_bb_upper * 0.98)  # Within 2% of upper band

            # 5. Enhanced Heikin-Ashi Analysis
            ha_strong_bullish = ha_color == "green" and prev_ha_color == "green" and previous_ha_close > current_sma12
            ha_strong_bearish = ha_color == "red" and prev_ha_color == "red" and previous_ha_close < current_sma12
            ha_reversal_bullish = ha_color == "green" and prev_ha_color == "red"
            ha_reversal_bearish = ha_color == "red" and prev_ha_color == "green"

            signal = 'HOLD'
            confidence = 50.0  # Start with neutral confidence
            entry_price = current_price
            stop_loss = None
            take_profit = None
            side = None
            order_type = 'MARKET'
            quantity = None
            
            # Check if user has open positions (equivalent to final_check in trade.py)
            has_open_position = False
            if not self.admin_monitoring_mode:
                # Only check positions for real trading, not admin monitoring
                try:
                    from app.models.trade import Trade
                    open_trades = Trade.query.filter_by(
                        user_id=self.user_id,
                        status='open'
                    ).all()
                    has_open_position = len(open_trades) > 0
                except Exception as e:
                    self.logger.warning(f"Could not check open positions: {e}")

            # Calculate price levels (equivalent to trade.py)
            lower_price_levels = np.array(forecast) * 0.99  # 1% below forecast prices
            upper_price_levels = np.array(forecast) * 1.01  # 1% above forecast prices
            avg_lower_bound = np.mean(lower_price_levels)
            avg_upper_bound = np.mean(upper_price_levels)

            # ===================================================================
            # OPTIMIZED LEGACY TRADING CONDITIONS - MULTI-INDICATOR ANALYSIS
            # ===================================================================

            # ORIGINAL LEGACY SELL CONDITIONS (Restored from trade.py)
            sell_conditions = [
                not has_open_position,  # No existing position
                last_swing_high is not None and last_swing_high > current_price,  # Resistance above
                potential_down_move > 0.008,  # At least 0.8% potential down move (optimized)
                ha_color == "red",  # Current HA red
                prev_ha_color == "red",  # Previous HA red
                previous_ha_close < current_sma12,  # Previous HA close below SMA12
                current_price > avg_lower_bound  # Price above average lower bound
            ]

            # ORIGINAL LEGACY BUY CONDITIONS (Restored from trade.py)
            buy_conditions = [
                not has_open_position,  # No existing position
                last_swing_low is not None and last_swing_low < current_price,  # Support below
                potential_up_move > 0.008,  # At least 0.8% potential up move (optimized)
                ha_color == "green",  # Current HA green
                prev_ha_color == "green",  # Previous HA green
                previous_ha_close > current_sma12,  # Previous HA close above SMA12
                current_price < avg_upper_bound  # Price below average upper bound
            ]

            # EXECUTE OPTIMIZED SELL SIGNAL
            if all(sell_conditions):
                signal = 'SELL'
                side = 'SELL'

                # ENHANCED CONFIDENCE CALCULATION
                base_confidence = min(potential_down_move * 100, 75)  # Base from potential move

                # Confidence boosts for multiple confirmations
                if ma_bearish_alignment: base_confidence += 5
                if rsi_overbought: base_confidence += 5
                if macd_bearish_cross: base_confidence += 5
                if volume_surge: base_confidence += 3
                if price_change_4h < -0.01: base_confidence += 3  # Strong recent bearish momentum

                confidence = min(base_confidence, 85)  # Cap at 85% for legacy system

                # Use ML-based SL/TP calculation
                sl_tp_result = self._calculate_ml_sl_tp(market_data, current_price, 'SELL')
                stop_loss = sl_tp_result['stop_loss']
                take_profit = sl_tp_result['take_profit']

                # Log ML SL/TP details
                self.logger.info(f"Legacy SELL - ML SL/TP: SL=${stop_loss:.2f}, TP=${take_profit:.2f}, "
                               f"RR=1:{sl_tp_result['risk_reward_ratio']:.2f}, Method={sl_tp_result['method']}")
                quantity = max(round((1000 / current_price), 4), 0.002)  # Min 0.002 BTC

                self.logger.info(f"[LEGACY_OPT] OPTIMIZED SELL Signal - Confidence: {confidence:.1f}%")
                self.logger.info(f"[LEGACY_OPT] Confirmations - MA: {ma_bearish_alignment}, RSI: {current_rsi:.1f}, MACD Cross: {macd_bearish_cross}")
                self.logger.info(f"[LEGACY_OPT] Volume Surge: {volume_surge}, Price Momentum 4h: {price_change_4h*100:.2f}%")

            # EXECUTE OPTIMIZED BUY SIGNAL
            elif all(buy_conditions):
                signal = 'BUY'
                side = 'BUY'

                # ENHANCED CONFIDENCE CALCULATION
                base_confidence = min(potential_up_move * 100, 75)  # Base from potential move

                # Confidence boosts for multiple confirmations
                if ma_bullish_alignment: base_confidence += 5
                if rsi_oversold: base_confidence += 5
                if macd_bullish_cross: base_confidence += 5
                if volume_surge: base_confidence += 3
                if price_change_4h > 0.01: base_confidence += 3  # Strong recent bullish momentum

                confidence = min(base_confidence, 85)  # Cap at 85% for legacy system

                # Use ML-based SL/TP calculation
                sl_tp_result = self._calculate_ml_sl_tp(market_data, current_price, 'BUY')
                stop_loss = sl_tp_result['stop_loss']
                take_profit = sl_tp_result['take_profit']

                # Log ML SL/TP details
                self.logger.info(f"Legacy BUY - ML SL/TP: SL=${stop_loss:.2f}, TP=${take_profit:.2f}, "
                               f"RR=1:{sl_tp_result['risk_reward_ratio']:.2f}, Method={sl_tp_result['method']}")
                quantity = max(round((1000 / current_price), 4), 0.002)  # Min 0.002 BTC

                self.logger.info(f"[LEGACY_OPT] OPTIMIZED BUY Signal - Confidence: {confidence:.1f}%")
                self.logger.info(f"[LEGACY_OPT] Confirmations - MA: {ma_bullish_alignment}, RSI: {current_rsi:.1f}, MACD Cross: {macd_bullish_cross}")
                self.logger.info(f"[LEGACY_OPT] Volume Surge: {volume_surge}, Price Momentum 4h: {price_change_4h*100:.2f}%")

            else:
                # ENHANCED DEBUG - Show why optimized conditions weren't met
                self.logger.info(f"[LEGACY_OPT] NO SIGNAL - Analyzing optimized conditions:")
                self.logger.info(f"  Position Status: Open={has_open_position}")
                self.logger.info(f"  Swing Levels: High={last_swing_high}, Low={last_swing_low}, Current={current_price}")
                self.logger.info(f"  Potential Moves: Up={potential_up_move*100:.4f}%, Down={potential_down_move*100:.4f}% (need >0.8%)")

                self.logger.info(f"  TREND ANALYSIS:")
                self.logger.info(f"    MA Bullish Alignment: {ma_bullish_alignment}")
                self.logger.info(f"    MA Bearish Alignment: {ma_bearish_alignment}")
                self.logger.info(f"    HA Strong Bull: {ha_strong_bullish}, HA Strong Bear: {ha_strong_bearish}")

                self.logger.info(f"  MOMENTUM INDICATORS:")
                self.logger.info(f"    RSI: {current_rsi:.1f} (Oversold: {rsi_oversold}, Overbought: {rsi_overbought})")
                self.logger.info(f"    MACD: {current_macd:.2f} vs Signal: {current_macd_signal:.2f}")
                self.logger.info(f"    MACD Crosses: Bull={macd_bullish_cross}, Bear={macd_bearish_cross}")

                self.logger.info(f"  VOLUME & VOLATILITY:")
                self.logger.info(f"    Volume Surge: {volume_surge} (Current: {current_volume:.0f}, Avg: {avg_volume:.0f})")
                self.logger.info(f"    BB Expansion: {bb_expansion}, BB Squeeze: {bb_squeeze}")

                self.logger.info(f"  PRICE MOMENTUM:")
                self.logger.info(f"    1H: {price_change_1h*100:.2f}%, 4H: {price_change_4h*100:.2f}%, 12H: {price_change_12h*100:.2f}%")

                self.logger.info(f"  PRICE BOUNDS:")
                self.logger.info(f"    Avg Lower: {avg_lower_bound:.2f}, Avg Upper: {avg_upper_bound:.2f}")
                self.logger.info(f"    BB Lower: {current_bb_lower:.2f}, BB Upper: {current_bb_upper:.2f}")
            
            # Legacy TP strategy removed - now using ML-based SL/TP system
            tp_strategy = {
                'strategy': 'ml_based',
                'note': 'Using independent ML predictors for SL/TP optimization'
            }

            # Calculate risk-reward ratio
            risk_reward_ratio = 0
            risk = 0
            reward = 0

            if stop_loss and take_profit:
                if signal == 'BUY':
                    risk = abs(entry_price - stop_loss)
                    reward = abs(take_profit - entry_price)
                elif signal == 'SELL':
                    risk = abs(stop_loss - entry_price)
                    reward = abs(entry_price - take_profit)

                if risk > 0:
                    risk_reward_ratio = reward / risk
            
            # Return comprehensive trading signal with OPTIMIZED indicators
            return {
                'signal': signal,
                'side': side,
                'action': 'OPEN' if signal != 'HOLD' else 'HOLD',
                'confidence': round(confidence, 2),
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'quantity': quantity,
                'order_type': order_type,
                'risk_reward_ratio': round(risk_reward_ratio, 2) if risk_reward_ratio else None,
                'potential_up_move': round(potential_up_move * 100, 4),
                'potential_down_move': round(potential_down_move * 100, 4),
                'current_price': current_price,
                'swing_high': last_swing_high,
                'swing_low': last_swing_low,

                # LEGACY INDICATORS (for backward compatibility)
                'ha_color': ha_color,
                'prev_ha_color': prev_ha_color,
                'sma12': current_sma12,

                # OPTIMIZED INDICATORS (new enhanced analysis)
                'optimized_indicators': {
                    'moving_averages': {
                        'sma12': round(current_sma12, 2),
                        'sma20': round(current_sma20, 2),
                        'sma50': round(current_sma50, 2),
                        'ema12': round(current_ema12, 2),
                        'ema20': round(current_ema20, 2),
                        'bullish_alignment': ma_bullish_alignment,
                        'bearish_alignment': ma_bearish_alignment
                    },
                    'momentum': {
                        'rsi': round(current_rsi, 2),
                        'rsi_oversold': rsi_oversold,
                        'rsi_overbought': rsi_overbought,
                        'rsi_bullish_momentum': rsi_bullish_momentum,
                        'rsi_bearish_momentum': rsi_bearish_momentum
                    },
                    'macd': {
                        'macd': round(current_macd, 4),
                        'signal': round(current_macd_signal, 4),
                        'histogram': round(current_macd_hist, 4),
                        'bullish_cross': macd_bullish_cross,
                        'bearish_cross': macd_bearish_cross,
                        'histogram_rising': macd_histogram_rising
                    },
                    'bollinger_bands': {
                        'upper': round(current_bb_upper, 2),
                        'middle': round(current_bb_middle, 2),
                        'lower': round(current_bb_lower, 2),
                        'squeeze': bb_squeeze,
                        'expansion': bb_expansion,
                        'near_upper': price_near_bb_upper,
                        'near_lower': price_near_bb_lower
                    },
                    'volume': {
                        'current': round(current_volume, 0),
                        'average': round(avg_volume, 0),
                        'surge': volume_surge,
                        'ratio': round(current_volume / avg_volume, 2) if avg_volume > 0 else 0
                    },
                    'price_momentum': {
                        '1h_change': round(price_change_1h * 100, 2),
                        '4h_change': round(price_change_4h * 100, 2),
                        '12h_change': round(price_change_12h * 100, 2)
                    }
                },

                'market_conditions': {
                    'trend': 'BULLISH' if ma_bullish_alignment else 'BEARISH' if ma_bearish_alignment else 'MIXED',
                    'momentum': 'BULLISH' if rsi_bullish_momentum else 'BEARISH' if rsi_bearish_momentum else 'NEUTRAL',
                    'volatility': 'HIGH' if bb_expansion else 'LOW' if bb_squeeze else 'NORMAL',
                    'volume': 'HIGH' if volume_surge else 'NORMAL',
                    'swing_analysis': {
                        'resistance': last_swing_high,
                        'support': last_swing_low
                    }
                },
                'forecast_data': {
                    'highest_predicted': highest_price,
                    'lowest_predicted': lowest_price
                },
                'profit_taking_strategy': tp_strategy,
                'timestamp': datetime.utcnow().isoformat(),
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing trading conditions: {str(e)}")
            return {'error': str(e)}

    def _convert_elite_signal_to_trading_signal(self, elite_signal: Dict, market_data: pd.DataFrame, symbol: str, forecast_data: Optional[Tuple] = None) -> Dict:
        """Convert elite ML signal to standard trading signal format"""
        try:
            current_price = float(market_data['close'].iloc[-1])
            signal = elite_signal['signal']
            confidence = elite_signal['confidence']

            # Use ML-based SL/TP calculation for Elite signals
            sl_tp_result = self._calculate_ml_sl_tp(market_data, current_price, signal)
            stop_loss = sl_tp_result['stop_loss']
            take_profit = sl_tp_result['take_profit']

            # Log Elite ML SL/TP details
            self.logger.info(f"Elite {signal} - ML SL/TP: SL=${stop_loss:.2f}, TP=${take_profit:.2f}, "
                           f"RR=1:{sl_tp_result['risk_reward_ratio']:.2f}, Method={sl_tp_result['method']}, "
                           f"Elite Conf={confidence:.1f}%, SL Conf={sl_tp_result['sl_confidence']:.1f}%, "
                           f"TP Conf={sl_tp_result['tp_confidence']:.1f}%")

            # Validate signal type
            if signal not in ['BUY', 'SELL']:
                return {'error': 'Invalid elite signal type'}

            # Calculate risk/reward ratio
            if signal == 'BUY':
                risk_amount = current_price - stop_loss
                reward_amount = take_profit - current_price
            else:
                risk_amount = stop_loss - current_price
                reward_amount = current_price - take_profit

            risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0

            # Calculate position size based on elite system (conservative approach)
            # Elite system uses smaller position sizes due to higher confidence
            base_quantity = 0.001  # Conservative base size for elite signals

            # Calculate potential moves from forecast data for admin dashboard
            potential_up_move = 0.0
            potential_down_move = 0.0

            if forecast_data:
                forecast, highest_price, lowest_price, forecast_result = forecast_data
                if current_price > 0:
                    potential_up_move = (highest_price - current_price) / current_price * 100
                    potential_down_move = (current_price - lowest_price) / current_price * 100

            return {
                'signal': signal,
                'confidence': confidence,
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'quantity': base_quantity,
                'order_type': 'market',  # Elite signals use market orders for immediate execution
                'risk_reward_ratio': round(risk_reward_ratio, 2),
                'current_price': current_price,
                'potential_up_move': round(potential_up_move, 4),
                'potential_down_move': round(potential_down_move, 4),
                'elite_signal': True,  # Flag to identify elite signals
                'regime': elite_signal.get('regime', 'Unknown'),
                'regime_id': elite_signal.get('regime_id', -1),
                'model_type': elite_signal.get('model_type', 'Unknown'),
                'threshold_used': elite_signal.get('threshold_used', 0.0),
                'raw_probability': elite_signal.get('raw_probability', 0.0),
                'atr_used': atr,
                'market_conditions': {
                    'elite_ml_active': True,
                    'confidence_threshold': elite_signal.get('threshold_used', 0.0),
                    'market_regime': elite_signal.get('regime', 'Unknown')
                },
                'profit_taking_strategy': {
                    'type': 'elite_1_to_2',
                    'description': 'Elite ML 1:2 risk/reward strategy',
                    'levels': [
                        {'percentage': 100, 'price': take_profit, 'description': 'Full position at 1:2 target'}
                    ]
                },
                'timestamp': elite_signal.get('timestamp', datetime.utcnow().isoformat()),
                'generated_at': datetime.utcnow().isoformat(),
                'system_type': 'ELITE_ML_96_PERCENT'
            }

        except Exception as e:
            self.logger.error(f"Error converting elite signal: {str(e)}")
            return {'error': f'Elite signal conversion failed: {str(e)}'}

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            high = df['high']
            low = df['low']
            close = df['close']

            # Calculate True Range
            prev_close = close.shift(1)
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)

            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            atr = true_range.rolling(window=period).mean().iloc[-1]

            return float(atr) if not np.isnan(atr) else 0.01

        except Exception as e:
            self.logger.error(f"Error calculating ATR: {str(e)}")
            return 0.01  # Default ATR if calculation fails