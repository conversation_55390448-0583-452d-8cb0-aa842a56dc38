"""
Subscription Service

Handles subscription management, including:
- Subscription creation and management
- Tier upgrades/downgrades
"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from app import db
from app.models.subscription import Subscription, SubscriptionTier, SubscriptionStatus
from app.models.user_tier_status import UserTierStatus

class SubscriptionService:
    """Service for managing subscriptions and payments."""

    def create_subscription(self, user_id: int, tier: SubscriptionTier) -> Dict[str, Any]:
        """
        Create a new subscription for a user.

        Args:
            user_id: ID of the user
            tier: Subscription tier (TIER_1, TIER_2, or TIER_3)

        Returns:
            Dict containing subscription details and status
        """
        # Check if user already has an active subscription
        existing_sub = Subscription.query.filter_by(
            user_id=user_id,
            status=SubscriptionStatus.ACTIVE
        ).first()

        if existing_sub:
            return {
                'success': False,
                'message': 'User already has an active subscription',
                'subscription_id': existing_sub.id
            }

        # Calculate subscription period
        now = datetime.utcnow()
        period_end = self._calculate_period_end(now, tier)

        # Create subscription record (local only)
        subscription = Subscription(
            user_id=user_id,
            tier=tier
        )

        db.session.add(subscription)

        # Update user's tier status
        self._update_user_tier_status(user_id, tier, subscription.id)

        db.session.commit()

        return {
            'success': True,
            'subscription_id': subscription.id,
            'tier': tier.name,
            'status': SubscriptionStatus.ACTIVE.value,
            'current_period_end': period_end.isoformat()
        }
    
    def cancel_subscription(self, user_id: int, subscription_id: int) -> Dict[str, Any]:
        """
        Cancel a user's subscription.

        Args:
            user_id: ID of the user
            subscription_id: ID of the subscription to cancel

        Returns:
            Dict containing cancellation status
        """
        subscription = Subscription.query.filter_by(
            id=subscription_id,
            user_id=user_id
        ).first()

        if not subscription:
            return {
                'success': False,
                'message': 'Subscription not found'
            }

        # Update subscription status
        subscription.status = SubscriptionStatus.CANCELED
        subscription.canceled_at = datetime.utcnow()

        # Downgrade user to TIER_1
        self._update_user_tier_status(user_id, SubscriptionTier.TIER_1, None)

        db.session.commit()

        return {
            'success': True,
            'message': 'Subscription canceled successfully',
            'subscription_id': subscription.id
        }
    
    def _update_user_tier_status(self, user_id: int, tier: SubscriptionTier, subscription_id: Optional[int]) -> None:
        """Update user's tier status using booleans."""
        user_tier = UserTierStatus.query.filter_by(user_id=user_id).first()
    
        if not user_tier:
            user_tier = UserTierStatus(
                user_id=user_id,
                tier_1=(tier == SubscriptionTier.TIER_1),
                tier_2=(tier == SubscriptionTier.TIER_2),
                tier_3=(tier == SubscriptionTier.TIER_3)
            )
            db.session.add(user_tier)
        else:
            # Set only one tier boolean to True
            user_tier.tier_1 = (tier == SubscriptionTier.TIER_1)
            user_tier.tier_2 = (tier == SubscriptionTier.TIER_2)
            user_tier.tier_3 = (tier == SubscriptionTier.TIER_3)
    
        user_tier.subscription_id = subscription_id
        user_tier.updated_at = datetime.utcnow()
    
        db.session.commit()

    @staticmethod
    def _calculate_period_end(start_date: datetime, tier: SubscriptionTier) -> datetime:
        """Calculate subscription period end date based on tier."""
        if tier == SubscriptionTier.TIER_1:
            # TIER_1 is free, no expiration
            return start_date + timedelta(days=365 * 10)  # 10 years
        elif tier == SubscriptionTier.TIER_2:
            # TIER_2 is monthly
            return start_date + timedelta(days=30)
        else:  # TIER_3
            # TIER_3 is premium, no expiration
            return start_date + timedelta(days=365 * 10)  # 10 years

# Singleton instance
subscription_service = SubscriptionService()
