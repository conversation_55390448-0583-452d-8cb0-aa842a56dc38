"""
Support Routes for DeepTrade Platform
Handles contact form submissions and support-related functionality.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_cors import cross_origin
import os
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

support_bp = Blueprint('support', __name__)

@support_bp.route('/contact', methods=['POST', 'OPTIONS'])
@cross_origin(origins=[
    os.getenv('FRONTEND_URL', 'http://localhost:5173'),
    'https://deep-trade-frontend.vercel.app',
    'https://deeptrade.capitolchilax.com',
    'http://localhost:5173',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173'
],
supports_credentials=True,
methods=['POST', 'OPTIONS'],
allow_headers=['Content-Type', 'Authorization', 'Accept'],
expose_headers=['Content-Type'])
def contact_form():
    """Handle contact form submissions."""
    if request.method == 'OPTIONS':
        return jsonify({'status': 'ok'}), 200
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        name = data.get('name', '').strip()
        email = data.get('email', '').strip()
        subject = data.get('subject', '').strip()
        message = data.get('message', '').strip()
        
        if not all([name, email, subject, message]):
            return jsonify({'error': 'All fields are required'}), 400
        
        # Basic email validation
        if '@' not in email or '.' not in email:
            return jsonify({'error': 'Invalid email address'}), 400
        
        # Get SMTP configuration from environment variables
        smtp_server = os.getenv('SMTP_SERVER', 'smtp-relay.brevo.com')
        smtp_port = int(os.getenv('SMTP_PORT', '587'))
        smtp_username = os.getenv('SMTP_USERNAME')
        smtp_password = os.getenv('SMTP_PASSWORD')
        from_email = email  # Use the user's email as the sender
        support_email = os.getenv('FROM_EMAIL3', '<EMAIL>')

        # Debug logging
        current_app.logger.info(f"SMTP Config - Server: {smtp_server}, Port: {smtp_port}")
        current_app.logger.info(f"SMTP Config - Username: {smtp_username}, From: {from_email}, To: {support_email}")

        if not smtp_username or not smtp_password:
            logger.error("SMTP credentials not found in environment variables")
            current_app.logger.error(f"Missing SMTP credentials - Username: {smtp_username}, Password: {'***' if smtp_password else None}")
            return jsonify({'error': 'Email service not configured'}), 500

        # Create email message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f"[DeepTrade Support] {subject}"
        msg['From'] = from_email
        msg['To'] = support_email
        msg['Reply-To'] = email

        # Create text content
        text_content = f"""
New contact form submission from DeepTrade website:

Name: {name}
Email: {email}
Subject: {subject}

Message:
{message}

---
Submitted at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC
IP Address: {request.remote_addr}
User Agent: {request.headers.get('User-Agent', 'Unknown')}
        """

        # Create HTML content
        html_content = f"""
<html>
<body>
<h2>New Contact Form Submission</h2>
<p><strong>From:</strong> {name} &lt;{email}&gt;</p>
<p><strong>Subject:</strong> {subject}</p>
<p><strong>Message:</strong></p>
<div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0;">
    {message.replace('\n', '<br>')}
</div>
<hr>
<p><small>
    <strong>Submitted:</strong> {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC<br>
    <strong>IP Address:</strong> {request.remote_addr}<br>
    <strong>User Agent:</strong> {request.headers.get('User-Agent', 'Unknown')}
</small></p>
</body>
</html>
        """

        # Attach parts
        part1 = MIMEText(text_content, 'plain')
        part2 = MIMEText(html_content, 'html')
        msg.attach(part1)
        msg.attach(part2)

        # Send email via SMTP
        try:
            context = ssl.create_default_context()
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls(context=context)
                server.login(smtp_username, smtp_password)
                server.send_message(msg)

            logger.info(f"Contact form email sent successfully to {support_email} from {email}")

        except Exception as email_error:
            logger.error(f"Failed to send contact form email via SMTP: {str(email_error)}")
            current_app.logger.error(f"SMTP Error Details: {str(email_error)}")
            import traceback
            current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
            return jsonify({'error': 'Failed to send email. Please try again later.'}), 500
        
        return jsonify({
            'message': 'Contact form submitted successfully',
            'status': 'sent'
        }), 200
        
    except Exception as e:
        logger.error(f"Error processing contact form: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@support_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for support service."""
    return jsonify({
        'status': 'ok',
        'service': 'support',
        'timestamp': datetime.utcnow().isoformat()
    }), 200
