"""
Admin Models for DeepTrade Administrative Dashboard

This module contains models for admin users, coupon codes, and related
administrative functionality for the DeepTrade application.
"""

from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
from app import db


class AdminUser(db.Model):
    """Admin user model for administrative access"""
    
    __tablename__ = 'admin_user'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    is_super_admin = db.Column(db.<PERSON>olean, default=False, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)
    created_by = db.<PERSON>umn(db.Integer, db.<PERSON>('admin_user.id'), nullable=True)

    # IP tracking fields
    last_ip_address = db.Column(db.String(45), nullable=True)
    last_login_ip = db.Column(db.String(45), nullable=True)
    ip_login_count = db.Column(db.Integer, default=0)
    
    # Relationships
    created_coupons = db.relationship('CouponCode', backref='creator', lazy='dynamic')
    created_admins = db.relationship('AdminUser', backref='creator', remote_side=[id])
    
    def __init__(self, username, password, is_super_admin=False, created_by=None):
        self.username = username
        self.set_password(password)
        self.is_super_admin = is_super_admin
        self.created_by = created_by
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """Convert admin user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'is_super_admin': self.is_super_admin,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_by': self.created_by,
            'last_ip_address': self.last_ip_address,
            'last_login_ip': self.last_login_ip,
            'ip_login_count': self.ip_login_count or 0
        }
    
    @staticmethod
    def create_default_admin():
        """Create default super admin if none exists"""
        existing_admin = AdminUser.query.filter_by(is_super_admin=True).first()
        if not existing_admin:
            default_admin = AdminUser(
                username='admin',
                password='12345',
                is_super_admin=True
            )
            db.session.add(default_admin)
            db.session.commit()
            return default_admin
        return existing_admin
    
    def __repr__(self):
        return f'<AdminUser {self.username}>'


class CouponCode(db.Model):
    """Coupon code model for tier upgrades"""
    
    __tablename__ = 'coupon_code'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)
    tier_level = db.Column(db.Integer, nullable=False)  # 2 or 3
    expiration_date = db.Column(db.DateTime, nullable=False)
    usage_count = db.Column(db.Integer, default=0, nullable=False)
    max_uses = db.Column(db.Integer, default=1, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # Relationships
    usages = db.relationship('CouponUsage', backref='coupon', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, code, tier_level, expiration_date, created_by, max_uses=1, description=None):
        self.code = code.upper()  # Store codes in uppercase
        self.tier_level = tier_level
        self.expiration_date = expiration_date
        self.created_by = created_by
        self.max_uses = max_uses
        self.description = description
    
    def is_valid(self):
        """Check if coupon is valid for use"""
        return (
            self.is_active and
            self.expiration_date > datetime.utcnow() and
            self.usage_count < self.max_uses
        )
    
    def can_be_used_by(self, user_id):
        """Check if coupon can be used by specific user"""
        if not self.is_valid():
            return False
        
        # Check if user has already used this coupon
        existing_usage = CouponUsage.query.filter_by(
            coupon_id=self.id,
            user_id=user_id
        ).first()
        
        return existing_usage is None
    
    def use_coupon(self, user_id):
        """Use the coupon for a user"""
        if not self.can_be_used_by(user_id):
            raise ValueError("Coupon cannot be used by this user")
        
        # Create usage record
        usage = CouponUsage(
            coupon_id=self.id,
            user_id=user_id
        )
        db.session.add(usage)
        
        # Increment usage count
        self.usage_count += 1
        
        db.session.commit()
        return usage
    
    def deactivate(self):
        """Deactivate the coupon"""
        self.is_active = False
        db.session.commit()
    
    def to_dict(self):
        """Convert coupon to dictionary"""
        return {
            'id': self.id,
            'code': self.code,
            'tier_level': self.tier_level,
            'expiration_date': self.expiration_date.isoformat(),
            'usage_count': self.usage_count,
            'max_uses': self.max_uses,
            'is_active': self.is_active,
            'is_valid': self.is_valid(),
            'created_at': self.created_at.isoformat(),
            'created_by': self.created_by,
            'description': self.description,
            'remaining_uses': self.max_uses - self.usage_count
        }
    
    @staticmethod
    def generate_code(prefix='DT', length=8):
        """Generate a random coupon code"""
        import random
        import string
        
        suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
        return f"{prefix}{suffix}"
    
    def __repr__(self):
        return f'<CouponCode {self.code}>'


class CouponUsage(db.Model):
    """Track coupon usage by users"""
    
    __tablename__ = 'coupon_usage'
    
    id = db.Column(db.Integer, primary_key=True)
    coupon_id = db.Column(db.Integer, db.ForeignKey('coupon_code.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    used_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='coupon_usages')
    
    def __init__(self, coupon_id, user_id):
        self.coupon_id = coupon_id
        self.user_id = user_id
    
    def to_dict(self):
        """Convert usage to dictionary"""
        return {
            'id': self.id,
            'coupon_id': self.coupon_id,
            'user_id': self.user_id,
            'used_at': self.used_at.isoformat(),
            'user_email': self.user.email if self.user else None
        }
    
    def __repr__(self):
        return f'<CouponUsage {self.coupon_id} by {self.user_id}>'


class AdminAction(db.Model):
    """Log admin actions for audit trail"""
    
    __tablename__ = 'admin_action'
    
    id = db.Column(db.Integer, primary_key=True)
    admin_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=False)
    action_type = db.Column(db.String(50), nullable=False)  # e.g., 'user_disabled', 'coupon_created'
    target_type = db.Column(db.String(50), nullable=False)  # e.g., 'user', 'coupon'
    target_id = db.Column(db.Integer, nullable=True)  # ID of the target object
    description = db.Column(db.Text, nullable=False)
    action_metadata = db.Column(db.JSON, nullable=True)  # Additional data about the action
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    ip_address = db.Column(db.String(45), nullable=True)  # Support IPv6
    
    # Relationships
    admin = db.relationship('AdminUser', backref='actions')
    
    def __init__(self, admin_id, action_type, target_type, description, target_id=None, action_metadata=None, ip_address=None):
        self.admin_id = admin_id
        self.action_type = action_type
        self.target_type = target_type
        self.target_id = target_id
        self.description = description
        self.action_metadata = action_metadata
        self.ip_address = ip_address
    
    def to_dict(self):
        """Convert action to dictionary"""
        return {
            'id': self.id,
            'admin_id': self.admin_id,
            'admin_username': self.admin.username if self.admin else None,
            'action_type': self.action_type,
            'target_type': self.target_type,
            'target_id': self.target_id,
            'description': self.description,
            'action_metadata': self.action_metadata,
            'created_at': self.created_at.isoformat(),
            'ip_address': self.ip_address
        }
    
    @staticmethod
    def log_action(admin_id, action_type, target_type, description, target_id=None, action_metadata=None, ip_address=None):
        """Log an admin action"""
        action = AdminAction(
            admin_id=admin_id,
            action_type=action_type,
            target_type=target_type,
            description=description,
            target_id=target_id,
            action_metadata=action_metadata,
            ip_address=ip_address
        )
        db.session.add(action)
        db.session.commit()
        return action
    
    def __repr__(self):
        return f'<AdminAction {self.action_type} by {self.admin_id}>'
