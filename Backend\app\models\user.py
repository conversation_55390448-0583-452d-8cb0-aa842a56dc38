import uuid
from datetime import datetime, timed<PERSON>ta
from flask_sqlalchemy import SQLAlchemy
from app import db
import bcrypt
from cryptography.fernet import Fernet
import base64
import os

from app.models.user_tier_status import UserTierStatus

class User(db.Model):
    """User model with Google OAuth and 2FA support."""
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    google_id = db.Column(db.String(255), unique=True, nullable=True)  # Made optional for traditional registration
    email = db.Column(db.String(255), unique=True, nullable=False)
    full_name = db.Column(db.String(255), nullable=False)
    profile_picture = db.Column(db.Text)
    password_hash = db.Column(db.String(255), nullable=True)  # Added for traditional registration
    
    # 2FA fields
    two_fa_enabled = db.Column(db.<PERSON><PERSON>, default=False, nullable=False)
    two_fa_secret = db.Column(db.Text)  # Encrypted TOTP secret
    
    # Email verification fields
    email_verified = db.Column(db.Boolean, default=False, nullable=False)
    verification_token = db.Column(db.String(255), nullable=True)
    verification_token_expires_at = db.Column(db.DateTime, nullable=True)

    # Email change verification fields
    email_verification_token = db.Column(db.String(255), nullable=True)
    email_verification_token_expires_at = db.Column(db.DateTime, nullable=True)
    pending_email = db.Column(db.String(255), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Trading settings
    auto_trading_enabled = db.Column(db.Boolean, default=False, nullable=False)
    paper_trading_mode = db.Column(db.Boolean, default=False, nullable=False)

    # Risk management settings
    investment_percentage = db.Column(db.Numeric(5, 2), default=0.00, nullable=False)  # 0-10%
    leverage_multiplier = db.Column(db.Numeric(3, 1), default=1.0, nullable=False)  # 1x-10x
    risk_settings_configured = db.Column(db.Boolean, default=False, nullable=False)
    selected_exchange = db.Column(db.String(50), default='binance', nullable=False)
    account_type = db.Column(db.String(20), default='FUTURES', nullable=False)  # SPOT, FUTURES, SPOT_MARGIN

    # Language preference for email notifications
    language_preference = db.Column(db.String(5), default='en', nullable=False)  # ISO 639-1 language codes

    # Admin fields
    is_admin = db.Column(db.Boolean, default=False, nullable=False)
    is_super_admin = db.Column(db.Boolean, default=False, nullable=False)

    # IP tracking fields
    last_ip_address = db.Column(db.String(45), nullable=True)
    last_login_ip = db.Column(db.String(45), nullable=True)
    ip_login_count = db.Column(db.Integer, default=0)
    
    # Relationships
    backup_codes = db.relationship('User2FABackupCode', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    email_2fa_codes = db.relationship('User2FAEmailCode', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    subscriptions = db.relationship('Subscription', backref='user', lazy='dynamic')
    payments = db.relationship('Payment', backref='user', lazy='dynamic')
    api_credentials = db.relationship('APICredential', backref='user', lazy='dynamic')
    trading_sessions = db.relationship('TradingSession', backref='user', lazy='dynamic')
    trades = db.relationship('Trade', backref='user', lazy='dynamic')
    fee_calculations = db.relationship('FeeCalculation', backref='user', lazy='dynamic')
    login_attempts = db.relationship('LoginAttempt', backref='user', lazy='dynamic')

    # Paper trading relationships
    paper_trades = db.relationship('PaperTrade', backref='user', lazy='dynamic')
    paper_trading_sessions = db.relationship('PaperTradingSession', backref='user', lazy='dynamic')

    # Tier status relationship
    tier_status = db.relationship('UserTierStatus', uselist=False, back_populates='user')

    def __init__(self, email, full_name, google_id=None, profile_picture=None, password=None):
        self.google_id = google_id
        self.email = email
        self.full_name = full_name
        self.profile_picture = profile_picture
        # Google OAuth users are automatically verified
        self.email_verified = bool(google_id)
        if password:
            self.set_password(password)
    
    def __repr__(self):
        return f'<User {self.email}>'
    
    def get_encryption_key(self):
        """Generate user-specific encryption key from user ID and secret."""
        from flask import current_app
        user_salt = self.id.encode('utf-8')
        app_secret = current_app.config['SECRET_KEY'].encode('utf-8')
        combined = user_salt + app_secret
        # Use first 32 bytes of hash for Fernet key
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=user_salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(app_secret))
        return key
    
    def encrypt_2fa_secret(self, secret):
        """Encrypt 2FA secret for storage."""
        if not secret:
            return None
        key = self.get_encryption_key()
        f = Fernet(key)
        encrypted_secret = f.encrypt(secret.encode('utf-8'))
        return base64.b64encode(encrypted_secret).decode('utf-8')
    
    def decrypt_2fa_secret(self):
        """Decrypt 2FA secret for use."""
        if not self.two_fa_secret:
            return None
        try:
            key = self.get_encryption_key()
            f = Fernet(key)
            encrypted_secret = base64.b64decode(self.two_fa_secret.encode('utf-8'))
            decrypted_secret = f.decrypt(encrypted_secret)
            return decrypted_secret.decode('utf-8')
        except Exception as e:
            # Log error and return None
            return None
    
    def enable_2fa(self, secret):
        """Enable 2FA for user."""
        self.two_fa_secret = self.encrypt_2fa_secret(secret)
        self.two_fa_enabled = True
        self.updated_at = datetime.utcnow()
    
    def disable_2fa(self):
        """Disable 2FA for user."""
        self.two_fa_enabled = False
        self.two_fa_secret = None
        self.updated_at = datetime.utcnow()
        # Remove all backup codes
        for backup_code in self.backup_codes.all():
            db.session.delete(backup_code)

    def set_password(self, password):
        """Hash and set user password for traditional registration."""
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def check_password(self, password):
        """Check if provided password matches hash."""
        if not self.password_hash:
            return False
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

    def generate_verification_token(self):
        """Generate a unique verification token."""
        self.verification_token = str(uuid.uuid4())
        self.verification_token_expires_at = datetime.utcnow() + timedelta(hours=24)
        return self.verification_token

    def verify_email(self, token):
        """Verify email with provided token."""
        if not self.verification_token or self.verification_token != token:
            return False, "Invalid verification token"

        if not self.verification_token_expires_at or self.verification_token_expires_at < datetime.utcnow():
            return False, "Verification token has expired"

        self.email_verified = True
        self.verification_token = None
        self.verification_token_expires_at = None
        self.updated_at = datetime.utcnow()
        return True, "Email verified successfully"

    def is_verification_token_expired(self):
        """Check if verification token is expired."""
        if not self.verification_token_expires_at:
            return True
        return self.verification_token_expires_at < datetime.utcnow()

    def can_resend_verification(self):
        """Check if user can resend verification email (not already verified and has email)."""
        return not self.email_verified and self.email and not self.google_id

    def generate_email_verification_token(self, new_email):
        """Generate a unique token for email change verification."""
        self.email_verification_token = str(uuid.uuid4())
        self.email_verification_token_expires_at = datetime.utcnow() + timedelta(hours=24)
        self.pending_email = new_email
        return self.email_verification_token

    def verify_email_change(self, token):
        """Verify email change with provided token."""
        if not self.email_verification_token or self.email_verification_token != token:
            return False, "Invalid email verification token"

        if not self.email_verification_token_expires_at or self.email_verification_token_expires_at < datetime.utcnow():
            return False, "Email verification token has expired"

        if not self.pending_email:
            return False, "No pending email change found"

        # Update email
        old_email = self.email
        self.email = self.pending_email
        self.email_verification_token = None
        self.email_verification_token_expires_at = None
        self.pending_email = None
        self.updated_at = datetime.utcnow()

        return True, f"Email successfully changed from {old_email} to {self.email}"

    def deactivate_account(self):
        """Soft delete user account."""
        self.is_active = False
        self.deleted_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

        # Clear sensitive data but keep for audit trail
        self.verification_token = None
        self.verification_token_expires_at = None
        self.email_verification_token = None
        self.email_verification_token_expires_at = None
        self.pending_email = None
    
    def get_current_subscription(self):
        """Get user's current active subscription."""
        from app.models.subscription import SubscriptionStatus
        return self.subscriptions.filter_by(status=SubscriptionStatus.ACTIVE).first()
    
    def get_tier(self):
        """Get user's current tier from UserTierStatus booleans."""
        tier_status = getattr(self, 'tier_status', None)
        if tier_status and hasattr(tier_status, 'get_current_tier'):
            current_tier = tier_status.get_current_tier()
            return f'tier_{current_tier}'
        return 'tier_1'  # Default to free tier
    
    def get_profit_share_rate(self):
        """Get user's profit sharing rate based on tier booleans."""
        from flask import current_app
        tier_status = getattr(self, 'tier_status', None)
        if tier_status and hasattr(tier_status, 'get_current_tier'):
            tier = tier_status.get_current_tier()
            if tier == 2:
                return current_app.config['TIER_2_PROFIT_SHARE']
            if tier == 3:
                return current_app.config.get('TIER_3_PROFIT_SHARE', 0.10)
        return current_app.config['TIER_1_PROFIT_SHARE']

    def get_max_leverage_for_exchange(self, exchange: str, account_type: str = 'SPOT') -> float:
        """Calculate max leverage based on user tier and exchange limits."""
        # Get user's current tier
        tier_status = getattr(self, 'tier_status', None)
        if tier_status and hasattr(tier_status, 'get_current_tier'):
            user_tier = tier_status.get_current_tier()
        else:
            user_tier = 1  # Default to tier 1

        # Tier-based leverage limits
        tier_limits = {1: 3.0, 2: 5.0, 3: 10.0}
        tier_max = tier_limits.get(user_tier, 1.0)

        # Exchange-specific leverage limits (corrected market types)
        exchange_limits = {
            'binance': {'FUTURES': 125},  # Binance is Futures market
            'binance_us': {'SPOT_MARGIN': 3, 'SPOT': 1},  # Binance US is Spot market with margin
            'kraken': {'FUTURES': 100},  # Kraken is Futures market
            'bitso': {'SPOT': 1}  # Bitso is Spot market only
        }

        exchange_max = exchange_limits.get(exchange.lower(), {}).get(account_type, 1)

        # Return the minimum of tier limit and exchange limit
        return min(tier_max, exchange_max)

    def validate_leverage_setting(self, leverage: float, exchange: str, account_type: str = 'SPOT') -> bool:
        """Validate if the requested leverage is allowed for user's tier and exchange."""
        max_allowed = self.get_max_leverage_for_exchange(exchange, account_type)
        return leverage <= max_allowed

    def to_dict(self):
        """Convert user to dictionary."""
        from flask import current_app
        from app.models.subscription import SubscriptionStatus
        
        subscription = self.get_current_subscription()
        
        # Handle both enum and string cases for subscription status
        if subscription:
            if hasattr(subscription.status, 'value'):
                # It's an enum, get its value
                status_value = subscription.status.value
            else:
                # It's already a string or something else
                status_value = str(subscription.status)
            
            # Ensure status is always uppercase
            status_value = status_value.upper()
        else:
            current_app.logger.info("[DEBUG] to_dict - No subscription found")
            status_value = 'ACTIVE'  # Default to uppercase ACTIVE
        
        current_app.logger.info(f"[DEBUG] to_dict - Final status value: {status_value}")
        
        return {
            'id': self.id,
            'email': self.email,
            'full_name': self.full_name,
            'profile_picture': self.profile_picture,
            'two_fa_enabled': self.two_fa_enabled,
            'email_verified': self.email_verified,
            'tier': self.get_tier(),
            'subscription_status': status_value,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'is_active': self.is_active,
            'registration_type': 'google' if self.google_id else 'traditional'
        }
    
    def calculate_trading_stats(self):
        trades = self.trades.all()
        total_trades = len(trades)
        if total_trades == 0:
            return {
                "total_pnl": 0,
                "monthly_rate": 0,
                "win_rate": 0
            }
        total_pnl = sum([t.pnl or 0 for t in trades])
        wins = sum([1 for t in trades if t.pnl and t.pnl > 0])
        win_rate = (wins / total_trades) * 100
        # Monthly rate: sum PnL for trades in last 30 days
        from datetime import datetime, timedelta
        now = datetime.utcnow()
        month_ago = now - timedelta(days=30)
        monthly_trades = [t for t in trades if getattr(t, "exit_time", None) and t.exit_time >= month_ago]
        monthly_pnl = sum([t.pnl or 0 for t in monthly_trades])
        monthly_rate = (monthly_pnl / total_trades) * 100 if total_trades else 0
        return {
            "total_pnl": round(total_pnl, 2),
            "monthly_rate": round(monthly_rate, 2),
            "win_rate": round(win_rate, 2)
        }


class User2FABackupCode(db.Model):
    """Backup codes for 2FA recovery."""
    __tablename__ = 'user_2fa_backup_codes'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    code_hash = db.Column(db.String(255), nullable=False)  # bcrypt hash of the code
    is_used = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    used_at = db.Column(db.DateTime)
    
    def __init__(self, user_id, code):
        self.user_id = user_id
        self.set_code(code)
    
    def set_code(self, code):
        """Hash and store the backup code."""
        salt = bcrypt.gensalt()
        self.code_hash = bcrypt.hashpw(code.encode('utf-8'), salt).decode('utf-8')
    
    def verify_code(self, code):
        """Verify a backup code."""
        if self.is_used:
            return False
        return bcrypt.checkpw(code.encode('utf-8'), self.code_hash.encode('utf-8'))
    
    def use_code(self):
        """Mark code as used."""
        self.is_used = True
        self.used_at = datetime.utcnow()
    
    def __repr__(self):
        return f'<User2FABackupCode {self.id} - Used: {self.is_used}>'


class User2FAEmailCode(db.Model):
    """Email-based 2FA codes for verification."""
    __tablename__ = 'user_2fa_email_codes'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    code_hash = db.Column(db.String(255), nullable=False)  # bcrypt hash of the code
    is_used = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    used_at = db.Column(db.DateTime)

    def __init__(self, user_id, code, expires_in_minutes=10):
        self.user_id = user_id
        self.expires_at = datetime.utcnow() + timedelta(minutes=expires_in_minutes)
        self.set_code(code)

    def set_code(self, code):
        """Hash and store the 2FA code."""
        salt = bcrypt.gensalt()
        self.code_hash = bcrypt.hashpw(code.encode('utf-8'), salt).decode('utf-8')

    def verify_code(self, code):
        """Verify a 2FA code."""
        if self.is_used:
            return False
        if self.expires_at < datetime.utcnow():
            return False
        return bcrypt.checkpw(code.encode('utf-8'), self.code_hash.encode('utf-8'))

    def use_code(self):
        """Mark code as used."""
        self.is_used = True
        self.used_at = datetime.utcnow()

    def is_expired(self):
        """Check if code is expired."""
        return self.expires_at < datetime.utcnow()

    def __repr__(self):
        return f'<User2FAEmailCode {self.id} - Used: {self.is_used} - Expired: {self.is_expired()}>'