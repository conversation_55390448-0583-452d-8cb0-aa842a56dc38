#!/bin/bash

# Deploy Complete CORS Fix Script
# This script fixes both backend CORS and frontend API URL, then deploys both

echo "🚀 Deploying Complete CORS Fix..."

# Server details
SERVER_IP="************"
SERVER_USER="ubuntu"
BACKEND_PATH="/var/www/deeptrade"

echo "📁 Uploading updated backend files..."

# Upload the updated backend files
scp Backend/app/api/auth_routes.py $SERVER_USER@$SERVER_IP:$BACKEND_PATH/app/api/
scp Backend/app/api/admin_routes.py $SERVER_USER@$SERVER_IP:$BACKEND_PATH/app/api/
scp Backend/app/api/forecast_routes.py $SERVER_USER@$SERVER_IP:$BACKEND_PATH/app/api/
scp Backend/app/__init__.py $SERVER_USER@$SERVER_IP:$BACKEND_PATH/app/

echo "🛑 Stopping backend service..."

# Stop the backend service first
ssh $SERVER_USER@$SERVER_IP << 'EOF'
sudo systemctl stop deeptrade
echo "✅ Backend service stopped"
EOF

echo "🔄 Starting backend service with updated files..."

# Start the backend service
ssh $SERVER_USER@$SERVER_IP << 'EOF'
cd /var/www/deeptrade
sudo systemctl start deeptrade
sudo systemctl status deeptrade
EOF

echo "✅ Backend CORS fix deployed successfully!"

echo "🌐 Now deploying frontend with correct API URL..."

# Build and deploy frontend to Vercel
cd Frontend
echo "📦 Building frontend with updated API URL..."
npm run build

echo "🚀 Deploying to Vercel..."
npx vercel --prod

echo "✅ Complete deployment finished!"
echo "🌐 Frontend: https://deeptrade.capitolchilax.com"
echo "🔧 Backend: https://************:5000"
echo "🎯 CORS should now be working properly!"
