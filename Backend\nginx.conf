server {
    listen 80;
    DeepTrade your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # Proxy to your Flask app
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Update static files path to your actual location
    location /static/ {
        alias /var/www/deeptrade/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}