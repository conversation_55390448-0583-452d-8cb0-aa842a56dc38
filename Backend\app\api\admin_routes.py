"""
Admin API Routes for DeepTrade Administrative Dashboard

This module provides API endpoints for administrative functions including
user management, coupon code management, and system administration.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_cors import cross_origin
from flask_jwt_extended import get_jwt_identity, get_jwt, create_access_token
from datetime import datetime, timedelta
import re
import psutil

from app.auth.decorators import jwt_required
from app.auth.security import SecurityManager
from app.models.admin import AdminUser, CouponCode, CouponUsage, AdminAction
from app.services.ip_tracking_service import IPTrackingService
from app.models.user import User
from app.models.password_reset import TwoFAResetRequest
from app.models.user_tier_status import UserTierStatus
from app.models.trade import Trade, TradingSession, TradingSessionStatus
from app.models.payment import Payment
from app.models.security_log import APICredential, SecurityLog, LoginAttempt
from app.models.subscription import Subscription
from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
from app.models.user_balance_tracker import UserBalance<PERSON>racker, Ba<PERSON>Snapshot
from app.models.fee_calculation import FeeCalculation
from app.services.trading_container import UserTradingContainer
from app import db

# Import IP tracking models for admin deletion
try:
    from app.models.ip_tracking import IPAccessLog, IPBlacklist
except ImportError:
    IPAccessLog = None
    IPBlacklist = None

# Simple Trading Container Manager for admin monitoring
class TradingContainerManager:
    _containers = {}

    @classmethod
    def get_all_active_containers(cls):
        """Get all active trading containers"""
        # In a real implementation, this would track active containers
        # For now, we'll return containers for users with auto-trading enabled
        active_users = User.query.filter_by(auto_trading_enabled=True).all()
        containers = {}
        for user in active_users:
            if user.id not in cls._containers:
                cls._containers[user.id] = UserTradingContainer(user.id)
            containers[user.id] = cls._containers[user.id]
        return containers

    @classmethod
    def get_container(cls, user_id):
        """Get container for specific user"""
        if user_id not in cls._containers:
            cls._containers[user_id] = UserTradingContainer(user_id)
        return cls._containers[user_id]

    @classmethod
    def get_or_create_container(cls, user_id):
        """Get or create container for user"""
        return cls.get_container(user_id)

admin_bp = Blueprint('admin', __name__)


def get_current_admin():
    """Get current admin user from JWT token"""
    try:
        claims = get_jwt()
        identity = get_jwt_identity()

        # Extract admin ID from identity (format: "admin_123")
        if not identity or not identity.startswith('admin_'):
            raise ValueError("Invalid admin identity format")

        admin_id = int(identity.replace('admin_', ''))
        admin = AdminUser.query.get(admin_id)

        if not admin or not admin.is_active:
            raise ValueError("Admin not found or inactive")

        return admin

    except Exception as e:
        current_app.logger.error(f"Error getting current admin: {str(e)}")
        raise


def admin_required(f):
    """Decorator to require admin privileges"""
    from functools import wraps
    from flask_jwt_extended import verify_jwt_in_request

    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Verify JWT token
            verify_jwt_in_request()
            claims = get_jwt()
            identity = get_jwt_identity()

            # Check for admin claim
            if not claims.get('is_admin', False):
                return jsonify({'message': 'Admin privileges required', 'error': 'admin_required'}), 403

            # Verify admin identity format
            if not identity or not identity.startswith('admin_'):
                return jsonify({'message': 'Invalid admin token', 'error': 'invalid_admin_token'}), 403

            return f(*args, **kwargs)

        except Exception as e:
            current_app.logger.error(f"Admin auth error: {str(e)}")
            import traceback
            current_app.logger.error(f"Admin auth traceback: {traceback.format_exc()}")
            return jsonify({'message': 'Authentication failed', 'error': 'auth_failed', 'details': str(e)}), 401

    return decorated_function


def super_admin_required(f):
    """Decorator to require super admin privileges"""
    from functools import wraps
    from flask_jwt_extended import verify_jwt_in_request

    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Verify JWT token
            verify_jwt_in_request()
            claims = get_jwt()
            identity = get_jwt_identity()

            # Check for super admin claim
            if not claims.get('is_super_admin', False):
                return jsonify({'message': 'Super admin privileges required', 'error': 'super_admin_required'}), 403

            # Verify admin identity format
            if not identity or not identity.startswith('admin_'):
                return jsonify({'message': 'Invalid admin token', 'error': 'invalid_admin_token'}), 403

            return f(*args, **kwargs)

        except Exception as e:
            current_app.logger.error(f"Super admin auth error: {str(e)}")
            return jsonify({'message': 'Authentication failed', 'error': 'auth_failed'}), 401

    return decorated_function


@admin_bp.route('/login', methods=['POST'])
@cross_origin(origins=[
    "https://deeptrade.capitolchilax.com",
    "https://deep-trade-frontend.vercel.app",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5173"
], supports_credentials=True, methods=["POST"], allow_headers=["Content-Type", "Authorization", "Accept"])
def admin_login():
    """Admin authentication endpoint"""
    try:
        # Check IP restrictions first
        ip_check = IPTrackingService.check_ip_restrictions(request)
        if not ip_check['allowed']:
            return jsonify({
                'message': ip_check['message'],
                'error': ip_check['reason']
            }), 403

        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '')

        if not username or not password:
            return jsonify({'message': 'Username and password are required'}), 400
        
        # Find admin user
        admin = AdminUser.query.filter_by(username=username, is_active=True).first()
        if not admin or not admin.check_password(password):
            # Log failed login attempt with IP tracking
            IPTrackingService.log_login_attempt(
                admin_id=admin.id if admin else None,
                login_successful=False,
                failure_reason='Invalid credentials',
                request_obj=request
            )

            # Record failed attempt for rate limiting
            IPTrackingService.record_failed_attempt(request)

            # Legacy security logging
            SecurityManager.log_security_event(
                user_id=None,
                event_type='admin_login_failed',
                ip_address=request.remote_addr,
                details={'username': username},
                risk_level='high'
            )
            return jsonify({'message': 'Invalid credentials'}), 401
        
        # Update last login
        admin.update_last_login()

        # Log successful login with IP tracking
        IPTrackingService.log_login_attempt(
            admin_id=admin.id,
            login_successful=True,
            request_obj=request
        )

        # Create JWT token with admin claims
        additional_claims = {
            'is_admin': True,
            'is_super_admin': admin.is_super_admin,
            'admin_id': admin.id,
            'admin_username': admin.username
        }

        access_token = create_access_token(
            identity=f"admin_{admin.id}",
            additional_claims=additional_claims
        )

        # Legacy admin action logging
        AdminAction.log_action(
            admin_id=admin.id,
            action_type='admin_login',
            target_type='admin',
            description=f'Admin {admin.username} logged in',
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'message': 'Admin login successful',
            'access_token': access_token,
            'admin': admin.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Admin login failed: {str(e)}")
        return jsonify({'message': 'Login failed', 'error': str(e)}), 500


@admin_bp.route('/users', methods=['GET'])
@admin_required
def list_users():
    """List all users with pagination and filtering"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)  # Max 100 per page
        tier_filter = request.args.get('tier', type=int)
        status_filter = request.args.get('status')  # 'active', 'inactive'
        debt_filter = request.args.get('debt_status')  # 'has_debt', 'no_debt'
        search = request.args.get('search', '').strip()
        
        # Build query
        query = User.query
        
        # Apply filters
        if tier_filter:
            query = query.join(UserTierStatus).filter(
                (UserTierStatus.tier_1 == True if tier_filter == 1 else False) |
                (UserTierStatus.tier_2 == True if tier_filter == 2 else False) |
                (UserTierStatus.tier_3 == True if tier_filter == 3 else False)
            )
        
        if status_filter == 'active':
            query = query.filter(User.is_active == True)
        elif status_filter == 'inactive':
            query = query.filter(User.is_active == False)
        
        if debt_filter == 'has_debt':
            query = query.join(UserTierStatus).filter(UserTierStatus.profit_share_owed > 0)
        elif debt_filter == 'no_debt':
            query = query.join(UserTierStatus).filter(UserTierStatus.profit_share_owed == 0)
        
        if search:
            query = query.filter(
                (User.email.contains(search)) |
                (User.full_name.contains(search))
            )
        
        # Order by creation date (newest first)
        query = query.order_by(User.created_at.desc())
        
        # Paginate
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        users = []
        for user in pagination.items:
            user_dict = user.to_dict()

            # Add proper user status logic
            if hasattr(user, 'deletion_requested') and user.deletion_requested:
                user_dict['status'] = 'deletion_requested'
            elif not user.is_active:
                user_dict['status'] = 'inactive'
            elif not user.email_verified:
                user_dict['status'] = 'unverified'
            else:
                user_dict['status'] = 'active'

            # Add tier status
            tier_status = UserTierStatus.query.filter_by(user_id=user.id).first()
            if tier_status:
                user_dict['tier_status'] = tier_status.to_dict()
            else:
                user_dict['tier_status'] = None

            users.append(user_dict)
        
        return jsonify({
            'users': users,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'filters': {
                'tier': tier_filter,
                'status': status_filter,
                'debt_status': debt_filter,
                'search': search
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"List users failed: {str(e)}")
        return jsonify({'message': 'Failed to list users', 'error': str(e)}), 500


@admin_bp.route('/users/<string:user_id>/disable', methods=['POST'])
@admin_required
def disable_user(user_id):
    """Disable user account"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        
        user = User.query.get_or_404(user_id)
        
        if not user.is_active:
            return jsonify({'message': 'User is already disabled'}), 400
        
        data = request.get_json() or {}
        reason = data.get('reason', 'No reason provided')
        
        # Disable user
        user.is_active = False
        db.session.commit()
        
        # Log action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='user_disabled',
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f'User {user.email} (ID: {user_id}) disabled. Reason: {reason}',
            action_metadata={'reason': reason, 'user_id': str(user_id)},
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'message': 'User disabled successfully',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Disable user failed: {str(e)}")
        return jsonify({'message': 'Failed to disable user', 'error': str(e)}), 500


@admin_bp.route('/users/<string:user_id>/enable', methods=['POST'])
@admin_required
def enable_user(user_id):
    """Enable user account"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        
        user = User.query.get_or_404(user_id)
        
        if user.is_active:
            return jsonify({'message': 'User is already active'}), 400
        
        data = request.get_json() or {}
        reason = data.get('reason', 'No reason provided')
        
        # Enable user
        user.is_active = True
        db.session.commit()
        
        # Log action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='user_enabled',
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f'User {user.email} (ID: {user_id}) enabled. Reason: {reason}',
            action_metadata={'reason': reason, 'user_id': str(user_id)},
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'message': 'User enabled successfully',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Enable user failed: {str(e)}")
        return jsonify({'message': 'Failed to enable user', 'error': str(e)}), 500


@admin_bp.route('/users/<string:user_id>/toggle-status', methods=['POST'])
@super_admin_required
def toggle_user_status(user_id):
    """Toggle user active/inactive status"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        user = User.query.get_or_404(user_id)

        data = request.get_json() or {}
        reason = data.get('reason', 'Admin action via toggle')

        # Toggle user status
        new_status = not user.is_active
        user.is_active = new_status

        # Log action
        action_type = 'user_enabled' if new_status else 'user_disabled'
        action_desc = 'enabled' if new_status else 'disabled'

        AdminAction.log_action(
            admin_id=admin_id,
            action_type=action_type,
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f'User {user.email} (ID: {user_id}) {action_desc}. Reason: {reason}',
            action_metadata={'reason': reason, 'user_id': str(user_id), 'new_status': new_status},
            ip_address=request.remote_addr
        )

        db.session.commit()

        return jsonify({
            'message': f'User {action_desc} successfully',
            'user': user.to_dict(),
            'new_status': new_status
        }), 200

    except Exception as e:
        current_app.logger.error(f"Toggle user status failed: {str(e)}")
        return jsonify({'message': 'Failed to toggle user status', 'error': str(e)}), 500


@admin_bp.route('/users/<string:user_id>/reset-2fa', methods=['POST'])
@super_admin_required
def reset_user_2fa(user_id):
    """Reset 2FA for a user (super admin only)"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        user = User.query.get_or_404(user_id)

        data = request.get_json() or {}
        reason = data.get('reason', 'Admin reset 2FA')

        # Check if user has 2FA enabled
        if not user.two_fa_enabled:
            return jsonify({'message': 'User does not have 2FA enabled'}), 400

        # Disable 2FA for the user
        user.disable_2fa()
        db.session.commit()

        # Log admin action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='user_2fa_reset',
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f'Reset 2FA for user {user.email} (ID: {user_id}). Reason: {reason}',
            action_metadata={'reason': reason, 'user_id': str(user_id)},
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'User 2FA reset successfully',
            'user': user.to_dict()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Reset user 2FA failed: {str(e)}")
        return jsonify({'message': 'Failed to reset user 2FA', 'error': str(e)}), 500


@admin_bp.route('/2fa-reset-requests', methods=['GET'])
@admin_required
def list_2fa_reset_requests():
    """List all 2FA reset requests with filtering."""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        status_filter = request.args.get('status', 'all')  # all, pending, approved, rejected, completed
        risk_filter = request.args.get('risk_level', 'all')  # all, low, medium, high

        # Build query
        query = TwoFAResetRequest.query

        # Apply filters
        if status_filter != 'all':
            query = query.filter(TwoFAResetRequest.status == status_filter)

        if risk_filter != 'all':
            query = query.filter(TwoFAResetRequest.risk_level == risk_filter)

        # Order by creation date (newest first)
        query = query.order_by(TwoFAResetRequest.created_at.desc())

        # Paginate
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        requests_list = []
        for req in pagination.items:
            req_dict = req.to_dict()
            requests_list.append(req_dict)

        return jsonify({
            'requests': requests_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'filters': {
                'status': status_filter,
                'risk_level': risk_filter
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"List 2FA reset requests failed: {str(e)}")
        return jsonify({'message': 'Failed to list 2FA reset requests', 'error': str(e)}), 500


@admin_bp.route('/2fa-reset-requests/<string:request_id>', methods=['GET'])
@admin_required
def get_2fa_reset_request(request_id):
    """Get detailed information about a specific 2FA reset request."""
    try:
        reset_request = TwoFAResetRequest.query.get_or_404(request_id)

        # Get additional user information for verification
        user = reset_request.user

        # Get user's recent login history
        from app.models.security_log import LoginAttempt
        recent_logins = LoginAttempt.query.filter(
            LoginAttempt.user_id == user.id,
            LoginAttempt.success == True
        ).order_by(LoginAttempt.attempted_at.desc()).limit(5).all()

        # Get user's tier information
        tier_info = None
        if hasattr(user, 'tier_status') and user.tier_status:
            tier_info = {
                'current_tier': user.tier_status.get_current_tier(),
                'tier_updated_at': user.tier_status.updated_at.isoformat() if user.tier_status.updated_at else None
            }

        # Get user's trading activity summary
        from app.models.trade import Trade
        recent_trades = Trade.query.filter_by(user_id=user.id).order_by(Trade.entry_time.desc()).limit(3).all()

        request_details = reset_request.to_dict()
        request_details['verification_data'] = {
            'user_actual_info': {
                'full_name': user.full_name,
                'email': user.email,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': recent_logins[0].attempted_at.isoformat() if recent_logins else None,
                'email_verified': user.email_verified,
                'is_active': user.is_active
            },
            'recent_logins': [
                {
                    'attempted_at': login.attempted_at.isoformat(),
                    'ip_address': login.ip_address,
                    'user_agent': login.user_agent[:100] if login.user_agent else None
                } for login in recent_logins
            ],
            'tier_info': tier_info,
            'recent_trades_count': len(recent_trades),
            'last_trade_date': recent_trades[0].entry_time.isoformat() if recent_trades else None
        }

        return jsonify(request_details), 200

    except Exception as e:
        current_app.logger.error(f"Get 2FA reset request failed: {str(e)}")
        return jsonify({'message': 'Failed to get 2FA reset request', 'error': str(e)}), 500


@admin_bp.route('/2fa-reset-requests/<string:request_id>/approve', methods=['POST'])
@admin_required
def approve_2fa_reset_request(request_id):
    """Approve a 2FA reset request and reset user's 2FA."""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        reset_request = TwoFAResetRequest.query.get_or_404(request_id)

        if reset_request.status != 'pending':
            return jsonify({'message': 'Request is not in pending status'}), 400

        data = request.get_json() or {}
        admin_notes = data.get('admin_notes', '').strip()

        # Approve the request
        reset_request.approve(admin_id, admin_notes)

        # Reset user's 2FA
        user = reset_request.user
        if user.two_fa_enabled:
            user.disable_2fa()

        # Mark request as completed
        reset_request.complete()

        db.session.commit()

        # Log admin action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='2fa_reset_approved',
            target_type='user',
            target_id=None,
            description=f'Approved 2FA reset request for user {user.email} (Request ID: {request_id})',
            action_metadata={
                'request_id': request_id,
                'user_id': str(user.id),
                'admin_notes': admin_notes
            },
            ip_address=request.remote_addr
        )

        # Send notification emails
        from app.services.email_service import EmailService
        try:
            email_service = EmailService()
            email_service.send_2fa_reset_approved_notification(user.email, user.full_name)
        except Exception as email_error:
            current_app.logger.error(f"Failed to send 2FA reset approval notification: {str(email_error)}")

        current_app.logger.info(f"2FA reset request approved and completed for user: {user.email} (Request ID: {request_id})")

        return jsonify({
            'message': '2FA reset request approved and user 2FA has been disabled',
            'request': reset_request.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Approve 2FA reset request failed: {str(e)}")
        return jsonify({'message': 'Failed to approve 2FA reset request', 'error': str(e)}), 500


@admin_bp.route('/2fa-reset-requests/<string:request_id>/reject', methods=['POST'])
@admin_required
def reject_2fa_reset_request(request_id):
    """Reject a 2FA reset request."""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        reset_request = TwoFAResetRequest.query.get_or_404(request_id)

        if reset_request.status != 'pending':
            return jsonify({'message': 'Request is not in pending status'}), 400

        data = request.get_json() or {}
        admin_notes = data.get('admin_notes', '').strip()

        if not admin_notes:
            return jsonify({'message': 'Admin notes are required for rejection'}), 400

        # Reject the request
        reset_request.reject(admin_id, admin_notes)

        db.session.commit()

        # Log admin action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='2fa_reset_rejected',
            target_type='user',
            target_id=None,
            description=f'Rejected 2FA reset request for user {reset_request.user.email} (Request ID: {request_id})',
            action_metadata={
                'request_id': request_id,
                'user_id': str(reset_request.user_id),
                'admin_notes': admin_notes
            },
            ip_address=request.remote_addr
        )

        # Send notification email
        from app.services.email_service import EmailService
        try:
            email_service = EmailService()
            email_service.send_2fa_reset_rejected_notification(
                reset_request.user.email,
                reset_request.user.full_name,
                admin_notes
            )
        except Exception as email_error:
            current_app.logger.error(f"Failed to send 2FA reset rejection notification: {str(email_error)}")

        current_app.logger.info(f"2FA reset request rejected for user: {reset_request.user.email} (Request ID: {request_id})")

        return jsonify({
            'message': '2FA reset request rejected',
            'request': reset_request.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Reject 2FA reset request failed: {str(e)}")
        return jsonify({'message': 'Failed to reject 2FA reset request', 'error': str(e)}), 500


@admin_bp.route('/users/<string:user_id>/delete', methods=['DELETE'])
@super_admin_required
def soft_delete_user(user_id):
    """Soft delete user account (preserve data)"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        user = User.query.get_or_404(user_id)

        data = request.get_json() or {}
        reason = data.get('reason', 'No reason provided')

        # Soft delete - set inactive and add deletion timestamp
        user.is_active = False
        user.deleted_at = datetime.utcnow()
        db.session.commit()
        
        # Log action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='user_deleted',
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f'User {user.email} (ID: {user_id}) soft deleted. Reason: {reason}',
            action_metadata={'reason': reason, 'soft_delete': True, 'user_id': str(user_id)},
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'message': 'User deleted successfully (soft delete)',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete user failed: {str(e)}")
        return jsonify({'message': 'Failed to delete user', 'error': str(e)}), 500


@admin_bp.route('/coupons', methods=['GET'])
@admin_required
def list_coupons():
    """List all coupon codes with usage statistics"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        tier_filter = request.args.get('tier', type=int)
        status_filter = request.args.get('status')  # 'active', 'expired', 'used_up'

        # Build query
        query = CouponCode.query

        # Apply filters
        if tier_filter:
            query = query.filter(CouponCode.tier_level == tier_filter)

        if status_filter == 'active':
            query = query.filter(
                CouponCode.is_active == True,
                CouponCode.expiration_date > datetime.utcnow(),
                CouponCode.usage_count < CouponCode.max_uses
            )
        elif status_filter == 'expired':
            query = query.filter(CouponCode.expiration_date <= datetime.utcnow())
        elif status_filter == 'used_up':
            query = query.filter(CouponCode.usage_count >= CouponCode.max_uses)

        # Order by creation date (newest first)
        query = query.order_by(CouponCode.created_at.desc())

        # Paginate
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        coupons = []
        for coupon in pagination.items:
            coupon_dict = coupon.to_dict()

            # Add creator info
            creator = AdminUser.query.get(coupon.created_by)
            coupon_dict['creator_username'] = creator.username if creator else 'Unknown'

            coupons.append(coupon_dict)

        return jsonify({
            'coupons': coupons,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'filters': {
                'tier': tier_filter,
                'status': status_filter
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"List coupons failed: {str(e)}")
        return jsonify({'message': 'Failed to list coupons', 'error': str(e)}), 500


@admin_bp.route('/coupons', methods=['POST'])
@super_admin_required
def create_coupon():
    """Create new coupon code"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        # Validate required fields
        tier_level = data.get('tier_level')
        expiration_days = data.get('expiration_days', 30)
        max_uses = data.get('max_uses', 1)
        description = data.get('description', '').strip()
        custom_code = data.get('custom_code', '').strip().upper()

        if not tier_level or tier_level not in [2, 3]:
            return jsonify({'message': 'Valid tier_level (2 or 3) is required'}), 400

        if expiration_days < 1 or expiration_days > 365:
            return jsonify({'message': 'Expiration days must be between 1 and 365'}), 400

        if max_uses < 1 or max_uses > 999999:
            return jsonify({'message': 'Max uses must be between 1 and 999999 (unlimited)'}), 400

        # Generate or validate code
        if custom_code:
            # Validate custom code format
            if not re.match(r'^[A-Z0-9]{4,20}$', custom_code):
                return jsonify({'message': 'Custom code must be 4-20 characters, letters and numbers only'}), 400

            # Check if code already exists
            existing = CouponCode.query.filter_by(code=custom_code).first()
            if existing:
                return jsonify({'message': 'Coupon code already exists'}), 409

            code = custom_code
        else:
            # Generate unique code
            attempts = 0
            while attempts < 10:
                code = CouponCode.generate_code()
                existing = CouponCode.query.filter_by(code=code).first()
                if not existing:
                    break
                attempts += 1
            else:
                return jsonify({'message': 'Failed to generate unique code'}), 500

        # Calculate expiration date
        expiration_date = datetime.utcnow() + timedelta(days=expiration_days)

        # Create coupon
        coupon = CouponCode(
            code=code,
            tier_level=tier_level,
            expiration_date=expiration_date,
            created_by=admin_id,
            max_uses=max_uses,
            description=description
        )

        db.session.add(coupon)
        db.session.commit()

        # Log action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='coupon_created',
            target_type='coupon',
            target_id=coupon.id,
            description=f'Coupon {coupon.code} created for tier {tier_level}',
            action_metadata={
                'tier_level': tier_level,
                'max_uses': max_uses,
                'expiration_days': expiration_days
            },
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Coupon created successfully',
            'coupon': coupon.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Create coupon failed: {str(e)}")
        return jsonify({'message': 'Failed to create coupon', 'error': str(e)}), 500


@admin_bp.route('/coupons/<int:coupon_id>/deactivate', methods=['POST'])
@super_admin_required
def deactivate_coupon(coupon_id):
    """Deactivate a coupon code"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        coupon = CouponCode.query.get_or_404(coupon_id)

        if not coupon.is_active:
            return jsonify({'message': 'Coupon is already deactivated'}), 400

        data = request.get_json() or {}
        reason = data.get('reason', 'No reason provided')

        # Deactivate coupon
        coupon.deactivate()

        # Log action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='coupon_deactivated',
            target_type='coupon',
            target_id=coupon_id,
            description=f'Coupon {coupon.code} deactivated. Reason: {reason}',
            action_metadata={'reason': reason},
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Coupon deactivated successfully',
            'coupon': coupon.to_dict()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Deactivate coupon failed: {str(e)}")
        return jsonify({'message': 'Failed to deactivate coupon', 'error': str(e)}), 500


@admin_bp.route('/coupons/<string:coupon_code>', methods=['DELETE'])
@super_admin_required
def delete_coupon(coupon_code):
    """Delete a coupon code by code"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        # Find coupon by code
        coupon = CouponCode.query.filter_by(code=coupon_code.upper()).first()
        if not coupon:
            return jsonify({'message': 'Coupon not found'}), 404

        # Store coupon info for logging
        coupon_info = {
            'code': coupon.code,
            'tier_level': coupon.tier_level,
            'usage_count': coupon.usage_count,
            'max_uses': coupon.max_uses
        }

        # Delete associated usage records first
        CouponUsage.query.filter_by(coupon_id=coupon.id).delete()

        # Delete the coupon
        db.session.delete(coupon)
        db.session.commit()

        # Log action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='coupon_deleted',
            target_type='coupon',
            target_id=coupon.id,
            description=f'Coupon {coupon_info["code"]} deleted (Tier {coupon_info["tier_level"]}, {coupon_info["usage_count"]}/{coupon_info["max_uses"]} uses)',
            action_metadata=coupon_info,
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Coupon deleted successfully',
            'deleted_coupon': coupon_info
        }), 200

    except Exception as e:
        current_app.logger.error(f"Delete coupon failed: {str(e)}")
        return jsonify({'message': 'Failed to delete coupon', 'error': str(e)}), 500


@admin_bp.route('/coupons/<string:coupon_code>/details', methods=['GET'])
@admin_required
def get_coupon_details(coupon_code):
    """Get detailed information about a coupon including usage statistics"""
    try:
        # Find the coupon
        coupon = CouponCode.query.filter_by(code=coupon_code).first()
        if not coupon:
            return jsonify({'message': 'Coupon not found'}), 404

        # Get usage details with user information
        usage_details = db.session.query(
            CouponUsage,
            User.full_name,
            User.email
        ).join(
            User, CouponUsage.user_id == User.id
        ).filter(
            CouponUsage.coupon_id == coupon.id
        ).order_by(CouponUsage.used_at.desc()).all()

        # Format usage details
        formatted_usage = []
        ip_usage_count = {}

        for usage, full_name, email in usage_details:
            formatted_usage.append({
                'username': full_name,  # Using full_name as username for display
                'email': email,
                'ip_address': 'N/A',  # IP address not tracked in current model
                'used_at': usage.used_at.isoformat() if usage.used_at else None
            })

        # IP usage summary (not available in current model)
        ip_usage_summary = []

        # Calculate statistics
        unique_ips = 0  # IP tracking not available
        unique_users = len(set(email for _, _, email in usage_details))
        total_uses = len(usage_details)

        return jsonify({
            'coupon': {
                'code': coupon.code,
                'tier_level': coupon.tier_level,
                'usage_count': total_uses,
                'max_uses': coupon.max_uses,
                'is_active': coupon.is_active,
                'expires_at': coupon.expiration_date.isoformat() if coupon.expiration_date else None,
                'created_at': coupon.created_at.isoformat() if coupon.created_at else None
            },
            'usage_stats': {
                'total_uses': total_uses,
                'unique_ips': unique_ips,
                'unique_users': unique_users
            },
            'usage_details': formatted_usage,
            'ip_usage': ip_usage_summary
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting coupon details: {str(e)}")
        return jsonify({'message': 'Failed to get coupon details', 'error': str(e)}), 500


@admin_bp.route('/verify-token', methods=['GET'])
@admin_required
def verify_admin_token():
    """Simple endpoint to verify admin token validity"""
    try:
        # Get current admin info from token
        current_admin = get_current_admin()
        return jsonify({
            'valid': True,
            'admin': {
                'id': current_admin.id,
                'username': current_admin.username,
                'is_super_admin': current_admin.is_super_admin
            }
        }), 200
    except Exception as e:
        current_app.logger.error(f"Token verification failed: {str(e)}")
        return jsonify({'valid': False, 'error': str(e)}), 401

@admin_bp.route('/analytics/dashboard', methods=['GET'])
@admin_required
def admin_analytics():
    """Get admin dashboard analytics"""
    try:
        # User statistics with error handling
        try:
            total_users = User.query.count()
            active_users = User.query.filter_by(is_active=True).count()
            inactive_users = total_users - active_users
        except Exception as e:
            current_app.logger.warning(f"Error getting user stats: {str(e)}")
            total_users = active_users = inactive_users = 0

        # Tier statistics with error handling
        tier_stats = {}
        try:
            for tier in [1, 2, 3]:
                try:
                    if tier == 1:
                        count = UserTierStatus.query.filter_by(tier_1=True).count()
                    elif tier == 2:
                        count = UserTierStatus.query.filter_by(tier_2=True).count()
                    else:
                        count = UserTierStatus.query.filter_by(tier_3=True).count()
                    tier_stats[f'tier_{tier}'] = count
                except Exception as e:
                    current_app.logger.warning(f"Error getting tier {tier} stats: {str(e)}")
                    tier_stats[f'tier_{tier}'] = 0
        except Exception as e:
            current_app.logger.warning(f"Error getting tier stats: {str(e)}")
            tier_stats = {'tier_1': 0, 'tier_2': 0, 'tier_3': 0}

        # Debt statistics
        users_with_debt = UserTierStatus.query.filter(UserTierStatus.profit_share_owed > 0).count()
        total_debt = db.session.query(db.func.sum(UserTierStatus.profit_share_owed)).scalar() or 0

        # Coupon statistics
        total_coupons = CouponCode.query.count()
        active_coupons = CouponCode.query.filter(
            CouponCode.is_active == True,
            CouponCode.expiration_date > datetime.utcnow(),
            CouponCode.usage_count < CouponCode.max_uses
        ).count()
        expired_coupons = CouponCode.query.filter(CouponCode.expiration_date <= datetime.utcnow()).count()
        used_up_coupons = CouponCode.query.filter(CouponCode.usage_count >= CouponCode.max_uses).count()

        # Recent activity (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        new_users_week = User.query.filter(User.created_at >= week_ago).count()
        recent_actions = AdminAction.query.filter(AdminAction.created_at >= week_ago).count()

        return jsonify({
            'user_stats': {
                'total': total_users,
                'active': active_users,
                'inactive': inactive_users,
                'new_this_week': new_users_week
            },
            'tier_stats': tier_stats,
            'debt_stats': {
                'users_with_debt': users_with_debt,
                'total_debt_amount': float(total_debt)
            },
            'coupon_stats': {
                'total': total_coupons,
                'active': active_coupons,
                'expired': expired_coupons,
                'used_up': used_up_coupons
            },
            'activity_stats': {
                'recent_admin_actions': recent_actions
            },
            'generated_at': datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Admin analytics failed: {str(e)}")
        return jsonify({'message': 'Failed to get analytics', 'error': str(e)}), 500


@admin_bp.route('/actions', methods=['GET'])
@admin_required
def list_admin_actions():
    """List admin actions for audit trail"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 50, type=int), 100)
        action_type = request.args.get('action_type')
        admin_id = request.args.get('admin_id', type=int)
        days = request.args.get('days', 30, type=int)  # Default last 30 days

        # Build query
        query = AdminAction.query

        # Filter by date range
        if days > 0:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            query = query.filter(AdminAction.created_at >= cutoff_date)

        # Apply filters
        if action_type:
            query = query.filter(AdminAction.action_type == action_type)

        if admin_id:
            query = query.filter(AdminAction.admin_id == admin_id)

        # Order by creation date (newest first)
        query = query.order_by(AdminAction.created_at.desc())

        # Paginate
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        actions = [action.to_dict() for action in pagination.items]

        return jsonify({
            'actions': actions,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            },
            'filters': {
                'action_type': action_type,
                'admin_id': admin_id,
                'days': days
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"List admin actions failed: {str(e)}")
        return jsonify({'message': 'Failed to list admin actions', 'error': str(e)}), 500


@admin_bp.route('/admins', methods=['GET'])
@super_admin_required
def list_admins():
    """List all admin users (super admin only)"""
    try:
        # Filter out deleted admins (those with DELETED_ prefix and inactive status)
        admins = AdminUser.query.filter(
            ~AdminUser.username.like('DELETED_%')
        ).order_by(AdminUser.created_at.desc()).all()
        admin_list = []

        for admin in admins:
            admin_dict = admin.to_dict()

            # Add recent activity count
            week_ago = datetime.utcnow() - timedelta(days=7)
            recent_actions = AdminAction.query.filter(
                AdminAction.admin_id == admin.id,
                AdminAction.created_at >= week_ago
            ).count()
            admin_dict['recent_actions_count'] = recent_actions

            admin_list.append(admin_dict)

        return jsonify({
            'admins': admin_list
        }), 200

    except Exception as e:
        current_app.logger.error(f"List admins failed: {str(e)}")
        return jsonify({'message': 'Failed to list admins', 'error': str(e)}), 500


@admin_bp.route('/admins', methods=['POST'])
@super_admin_required
def create_admin():
    """Create new admin user (super admin only)"""
    try:
        claims = get_jwt()
        creator_admin_id = claims.get('admin_id')

        data = request.get_json()
        if not data:
            return jsonify({'message': 'No data provided'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '')
        is_super_admin = data.get('is_super_admin', False)

        if not username or not password:
            return jsonify({'message': 'Username and password are required'}), 400

        if len(username) < 3 or len(username) > 50:
            return jsonify({'message': 'Username must be 3-50 characters'}), 400

        if len(password) < 8:
            return jsonify({'message': 'Password must be at least 8 characters'}), 400

        # Check if username already exists
        existing = AdminUser.query.filter_by(username=username).first()
        if existing:
            return jsonify({'message': 'Username already exists'}), 409

        # Create admin user
        admin = AdminUser(
            username=username,
            password=password,
            is_super_admin=is_super_admin,
            created_by=creator_admin_id
        )

        db.session.add(admin)
        db.session.commit()

        # Log action
        AdminAction.log_action(
            admin_id=creator_admin_id,
            action_type='admin_created',
            target_type='admin',
            target_id=admin.id,
            description=f'Admin user {username} created (super_admin: {is_super_admin})',
            action_metadata={'is_super_admin': is_super_admin},
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Admin created successfully',
            'admin': admin.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Create admin failed: {str(e)}")
        return jsonify({'message': 'Failed to create admin', 'error': str(e)}), 500


# Trading Bot Monitoring Endpoints

@admin_bp.route('/trading-bot/containers', methods=['GET'])
@admin_required
def get_trading_containers():
    """Get active trading containers for admin monitoring"""
    try:
        # Get all users with auto-trading enabled
        active_users = User.query.filter_by(auto_trading_enabled=True).all()

        containers_list = []
        for user in active_users:
            # Get user's tier status
            tier_status = UserTierStatus.query.filter_by(user_id=user.id).first()

            # Get current active trading session
            active_session = TradingSession.query.filter_by(
                user_id=user.id,
                status=TradingSessionStatus.ACTIVE
            ).first()

            containers_list.append({
                'user_id': user.id,
                'user_email': user.email,
                'tier': user.tier if hasattr(user, 'tier') else 1,
                'status': 'active' if user.auto_trading_enabled else 'inactive',
                'current_trade': 'BTC/USDT' if active_session else 'None',
                'risk_percentage': user.investment_percentage if hasattr(user, 'investment_percentage') else 0,
                'leverage': user.leverage_multiplier if hasattr(user, 'leverage_multiplier') else 1
            })

        return jsonify({
            'containers': containers_list,
            'total_active': len(containers_list)
        }), 200

    except Exception as e:
        import traceback
        current_app.logger.error(f"Get trading containers failed: {str(e)}")
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")

        # Return empty data if error occurs
        return jsonify({
            'containers': [],
            'total_active': 0,
            'error': 'Failed to load containers'
        }), 200

@admin_bp.route('/trading-bot/status', methods=['GET'])
@admin_required
def get_trading_bot_status():
    """Get comprehensive trading bot status for all users"""
    try:
        # Get all users with trading containers
        container_manager = TradingContainerManager()
        active_containers = container_manager.get_all_active_containers()

        # Get trading statistics
        total_users = User.query.count()
        users_with_auto_trading = User.query.filter_by(auto_trading_enabled=True).count()

        # Get recent trading activity (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_trades = Trade.query.filter(Trade.entry_time >= yesterday).count()

        # Get active trading sessions
        from app.models.trade import TradingSessionStatus
        active_sessions = TradingSession.query.filter_by(status=TradingSessionStatus.ACTIVE).count()

        # Calculate success metrics
        completed_trades = Trade.query.filter(Trade.status.in_(['CLOSED', 'FILLED'])).all()
        profitable_trades = [t for t in completed_trades if t.pnl and t.pnl > 0]

        win_rate = (len(profitable_trades) / len(completed_trades) * 100) if completed_trades else 0
        total_pnl = sum(t.pnl for t in completed_trades if t.pnl) if completed_trades else 0

        status_data = {
            'overview': {
                'total_users': total_users,
                'users_with_auto_trading': users_with_auto_trading,
                'active_containers': len(active_containers),
                'active_sessions': active_sessions,
                'auto_trading_adoption_rate': (users_with_auto_trading / total_users * 100) if total_users > 0 else 0
            },
            'performance': {
                'total_trades_24h': recent_trades,
                'total_completed_trades': len(completed_trades),
                'profitable_trades': len(profitable_trades),
                'win_rate': round(win_rate, 2),
                'total_pnl': round(total_pnl, 2)
            },
            'containers': []
        }

        # Add individual container status
        for user_id, container in active_containers.items():
            user = User.query.get(user_id)
            if user:
                container_info = {
                    'user_id': user_id,
                    'user_email': user.email,
                    'user_tier': user.get_tier(),
                    'active': container.active,
                    'risk_params': container.risk_params,
                    'current_trade': {
                        'symbol': container.current_trade.symbol if container.current_trade else None,
                        'side': container.current_trade.side if container.current_trade else None,
                        'entry_price': float(container.current_trade.entry_price) if container.current_trade and container.current_trade.entry_price else None,
                        'status': container.current_trade.status if container.current_trade else None
                    } if container.current_trade else None,
                    'last_signal': container.last_signal,
                    'session_active': container.session is not None
                }
                status_data['containers'].append(container_info)

        return jsonify(status_data), 200

    except Exception as e:
        current_app.logger.error(f"Error getting trading bot status: {str(e)}")
        return jsonify({'error': 'Failed to get trading bot status', 'message': str(e)}), 500


@admin_bp.route('/trading-bot/signals', methods=['GET'])
@admin_required
def get_trading_signals_status():
    """Get current trading signals and conditions for all monitored symbols"""
    try:
        from app.services.trading_signals import TradingSignalGenerator
        from app.services.market_data import BinanceMarketData

        # Initialize market data service
        market_service = BinanceMarketData()

        # Get signals for BTC/USDT (primary trading pair)
        symbol = 'BTCUSDT'
        timeframe = '1h'

        # Find a valid user for signal generation (preferably an admin user)
        admin_user = User.query.filter_by(is_active=True).first()
        if not admin_user:
            return jsonify({
                'signals': [],
                'message': 'No active users found for signal generation',
                'timestamp': datetime.utcnow().isoformat()
            }), 200

        # Create a temporary signal generator
        signal_generator = TradingSignalGenerator(user_id=admin_user.id, exchange_service=market_service)

        try:
            signals = signal_generator.generate_signals(symbol, timeframe)

            # Get current market conditions
            current_price = market_service.get_current_price(symbol)

            # Get ML forecast data
            from app.services.market_data import ml_service
            forecast_data = ml_service.generate_ensemble_forecast(symbol, timeframe, 24)

            signals_data = {
                'symbol': symbol,
                'timeframe': timeframe,
                'current_price': current_price,
                'signals': signals,
                'market_conditions': {
                    'forecast_direction': forecast_data.get('direction') if forecast_data else 'unknown',
                    'confidence': forecast_data.get('confidence') if forecast_data else 0,
                    'support_level': forecast_data.get('support_level') if forecast_data else None,
                    'resistance_level': forecast_data.get('resistance_level') if forecast_data else None,
                    'highest_predicted': forecast_data.get('highest_price') if forecast_data else None,
                    'lowest_predicted': forecast_data.get('lowest_price') if forecast_data else None
                },
                'generated_at': datetime.utcnow().isoformat()
            }

            return jsonify(signals_data), 200

        except Exception as signal_error:
            current_app.logger.error(f"Error generating signals: {str(signal_error)}")
            return jsonify({
                'symbol': symbol,
                'timeframe': timeframe,
                'error': 'Failed to generate signals',
                'message': str(signal_error),
                'generated_at': datetime.utcnow().isoformat()
            }), 200  # Return 200 but with error info

    except Exception as e:
        current_app.logger.error(f"Error getting trading signals: {str(e)}")
        return jsonify({'error': 'Failed to get trading signals', 'message': str(e)}), 500


@admin_bp.route('/trading-bot/current-signal', methods=['GET'])
@admin_required
def get_current_trading_signal():
    """Get current trading signal in format expected by frontend"""
    try:
        from app.services.trading_signals import TradingSignalGenerator
        from app.services.market_data import BinanceMarketData

        # Initialize market data service
        market_service = BinanceMarketData()

        # Get current price
        current_price = market_service.get_current_price('BTCUSDT')

        # Find a valid user for signal generation
        admin_user = User.query.filter_by(is_active=True).first()
        if not admin_user:
            return jsonify({
                'signal': 'HOLD',
                'confidence': 0,
                'current_price': current_price,
                'timestamp': datetime.utcnow().isoformat(),
                'message': 'No active users found for signal generation'
            }), 200

        try:
            # Create signal generator in admin monitoring mode (bypasses user trading settings)
            signal_generator = TradingSignalGenerator(user_id=admin_user.id, exchange_service=market_service, admin_monitoring_mode=True)
            signal_result = signal_generator.generate_signals('BTCUSDT', '1h')

            # Get the signal from the result dictionary
            current_signal = 'HOLD'
            confidence = 0

            if signal_result and not signal_result.get('error'):
                current_signal = signal_result.get('signal', 'HOLD').upper()
                confidence = signal_result.get('confidence', 0)

                # Log the signal generation for debugging
                current_app.logger.info(f"Signal generated: {current_signal} with confidence {confidence}%")
            elif signal_result and signal_result.get('error'):
                current_app.logger.error(f"Signal generation error: {signal_result.get('error')}")
                current_signal = 'ERROR'

            # Get ML forecast for additional confidence
            from app.services.market_data import ml_service
            forecast_data = ml_service.generate_ensemble_forecast('BTCUSDT', '1h', 24)

            if forecast_data and forecast_data.get('confidence'):
                confidence = max(confidence, forecast_data.get('confidence', 0))

            return jsonify({
                'signal': current_signal,
                'confidence': round(confidence, 1) if confidence else 0,
                'current_price': current_price,
                'timestamp': datetime.utcnow().isoformat()
            }), 200

        except Exception as signal_error:
            current_app.logger.error(f"Error generating current signal: {str(signal_error)}")
            return jsonify({
                'signal': 'HOLD',
                'confidence': 0,
                'current_price': current_price,
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(signal_error)
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting current trading signal: {str(e)}")
        return jsonify({
            'signal': 'HOLD',
            'confidence': 0,
            'current_price': 0,
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }), 200


@admin_bp.route('/trading-bot/performance', methods=['GET'])
@admin_required
def get_trading_performance():
    """Get detailed trading performance metrics"""
    try:
        # Get query parameters for time range
        days = request.args.get('days', 7, type=int)  # Default to last 7 days
        cutoff_date = datetime.utcnow() - timedelta(days=days)

        # Get trades within the time range
        trades = Trade.query.filter(Trade.entry_time >= cutoff_date).all()

        # Calculate performance metrics
        total_trades = len(trades)
        completed_trades = [t for t in trades if t.status in ['CLOSED', 'FILLED']]
        open_trades = [t for t in trades if t.status == 'OPEN']

        # PnL calculations
        profitable_trades = [t for t in completed_trades if t.pnl and t.pnl > 0]
        losing_trades = [t for t in completed_trades if t.pnl and t.pnl < 0]

        total_pnl = sum(t.pnl for t in completed_trades if t.pnl) if completed_trades else 0
        avg_profit = sum(t.pnl for t in profitable_trades) / len(profitable_trades) if profitable_trades else 0
        avg_loss = sum(t.pnl for t in losing_trades) / len(losing_trades) if losing_trades else 0

        # Win rate and other metrics
        win_rate = (len(profitable_trades) / len(completed_trades) * 100) if completed_trades else 0
        profit_factor = abs(avg_profit / avg_loss) if avg_loss != 0 else 0

        # Trading frequency
        avg_trades_per_day = total_trades / days if days > 0 else 0

        # Group by symbol
        symbol_performance = {}
        for trade in completed_trades:
            symbol = trade.symbol
            if symbol not in symbol_performance:
                symbol_performance[symbol] = {
                    'total_trades': 0,
                    'profitable_trades': 0,
                    'total_pnl': 0,
                    'win_rate': 0
                }

            symbol_performance[symbol]['total_trades'] += 1
            if trade.pnl and trade.pnl > 0:
                symbol_performance[symbol]['profitable_trades'] += 1
            if trade.pnl:
                symbol_performance[symbol]['total_pnl'] += trade.pnl

        # Calculate win rates for each symbol
        for symbol_data in symbol_performance.values():
            symbol_data['win_rate'] = (symbol_data['profitable_trades'] / symbol_data['total_trades'] * 100) if symbol_data['total_trades'] > 0 else 0

        # Recent activity (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_trades = Trade.query.filter(Trade.entry_time >= yesterday).count()

        performance_data = {
            'time_range': {
                'days': days,
                'from_date': cutoff_date.isoformat(),
                'to_date': datetime.utcnow().isoformat()
            },
            'overview': {
                'total_trades': total_trades,
                'completed_trades': len(completed_trades),
                'open_trades': len(open_trades),
                'profitable_trades': len(profitable_trades),
                'losing_trades': len(losing_trades),
                'recent_trades_24h': recent_trades
            },
            'performance_metrics': {
                'total_pnl': round(total_pnl, 2),
                'win_rate': round(win_rate, 2),
                'avg_profit': round(avg_profit, 2),
                'avg_loss': round(avg_loss, 2),
                'profit_factor': round(profit_factor, 2),
                'avg_trades_per_day': round(avg_trades_per_day, 2)
            },
            'symbol_breakdown': symbol_performance,
            'generated_at': datetime.utcnow().isoformat()
        }

        # Also return simplified format for frontend compatibility
        simplified_data = {
            'total_trades': total_trades,
            'win_rate': round(win_rate, 1),
            'total_pnl': f"${round(total_pnl, 2)}",
            'avg_trades_per_day': round(avg_trades_per_day, 1),
            'active_alerts': 0  # Placeholder
        }

        return jsonify(simplified_data), 200

    except Exception as e:
        import traceback
        current_app.logger.error(f"Error getting trading performance: {str(e)}")
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")

        # Return empty data if error occurs
        return jsonify({
            'total_trades': 0,
            'win_rate': 0,
            'total_pnl': '$0.00',
            'avg_trades_per_day': 0,
            'active_alerts': 0,
            'error': 'Failed to load performance data'
        }), 200


@admin_bp.route('/trading-bot/alerts', methods=['GET'])
@admin_required
def get_trading_alerts():
    """Get current trading alerts and critical conditions"""
    try:
        alerts = []

        # Check for users with insufficient balance
        min_balance = 100  # Minimum required balance
        users_low_balance = User.query.join(UserTierStatus).filter(
            UserTierStatus.current_balance < min_balance,
            User.auto_trading_enabled == True
        ).all()

        for user in users_low_balance:
            alerts.append({
                'type': 'low_balance',
                'severity': 'warning',
                'user_id': user.id,
                'user_email': user.email,
                'message': f'User balance (${user.get_current_balance():.2f}) below minimum required (${min_balance})',
                'timestamp': datetime.utcnow().isoformat()
            })

        # Check for stuck trades (open for more than 24 hours)
        day_ago = datetime.utcnow() - timedelta(days=1)
        stuck_trades = Trade.query.filter(
            Trade.status == 'OPEN',
            Trade.created_at < day_ago
        ).all()

        for trade in stuck_trades:
            user = User.query.get(trade.user_id)
            alerts.append({
                'type': 'stuck_trade',
                'severity': 'critical',
                'user_id': trade.user_id,
                'user_email': user.email if user else 'Unknown',
                'trade_id': trade.id,
                'symbol': trade.symbol,
                'message': f'Trade {trade.id} ({trade.symbol}) has been open for more than 24 hours',
                'timestamp': datetime.utcnow().isoformat()
            })

        # Check for users with failed API credentials
        container_manager = TradingContainerManager()
        active_containers = container_manager.get_all_active_containers()

        for user_id, container in active_containers.items():
            if not container._get_exchange_service():
                user = User.query.get(user_id)
                alerts.append({
                    'type': 'api_credentials_failed',
                    'severity': 'critical',
                    'user_id': user_id,
                    'user_email': user.email if user else 'Unknown',
                    'message': f'Failed to connect to exchange - invalid API credentials',
                    'timestamp': datetime.utcnow().isoformat()
                })

        # Check for high loss trades (more than 10% loss)
        high_loss_threshold = -10.0
        recent_high_losses = Trade.query.filter(
            Trade.pnl < high_loss_threshold,
            Trade.entry_time >= datetime.utcnow() - timedelta(hours=6)
        ).all()

        for trade in recent_high_losses:
            user = User.query.get(trade.user_id)
            alerts.append({
                'type': 'high_loss',
                'severity': 'warning',
                'user_id': trade.user_id,
                'user_email': user.email if user else 'Unknown',
                'trade_id': trade.id,
                'symbol': trade.symbol,
                'pnl': trade.pnl,
                'message': f'High loss trade: ${trade.pnl:.2f} on {trade.symbol}',
                'timestamp': datetime.utcnow().isoformat()
            })

        # Sort alerts by severity and timestamp
        severity_order = {'critical': 0, 'warning': 1, 'info': 2}
        alerts.sort(key=lambda x: (severity_order.get(x['severity'], 3), x['timestamp']), reverse=True)

        alerts_data = {
            'total_alerts': len(alerts),
            'critical_alerts': len([a for a in alerts if a['severity'] == 'critical']),
            'warning_alerts': len([a for a in alerts if a['severity'] == 'warning']),
            'alerts': alerts,
            'generated_at': datetime.utcnow().isoformat()
        }

        return jsonify(alerts_data), 200

    except Exception as e:
        import traceback
        current_app.logger.error(f"Error getting trading alerts: {str(e)}")
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")

        # Return empty data if error occurs
        return jsonify({
            'alerts': [],
            'error': 'Failed to load alerts'
        }), 200


@admin_bp.route('/trading-bot/stop/<int:user_id>', methods=['POST'])
@admin_required
def stop_trading_container(user_id):
    """Stop trading for a specific user"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Disable auto trading for the user
        user.auto_trading_enabled = False
        db.session.commit()

        # Log admin action
        AdminAction.log_action(
            admin_id=get_jwt_identity().replace('admin_', ''),
            action_type='stop_trading',
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f'Stopped trading for user {user.email} (ID: {user_id})',
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': f'Trading stopped for user {user.email}',
            'user_id': user_id,
            'status': 'stopped'
        }), 200

    except Exception as e:
        current_app.logger.error(f"Stop trading container failed: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/trading-bot/control/<int:user_id>', methods=['POST'])
@super_admin_required
def control_trading_bot(user_id):
    """Control trading bot for specific user (super admin only)"""
    try:
        data = request.get_json()
        action = data.get('action')  # 'start', 'stop', 'force_close'

        if not action:
            return jsonify({'error': 'Action is required'}), 400

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        container_manager = TradingContainerManager()
        admin_id = get_jwt_identity()

        if action == 'start':
            # Enable auto-trading for user
            user.auto_trading_enabled = True
            container = container_manager.get_or_create_container(user_id)
            container.set_active(True)
            db.session.commit()

            message = f'Trading bot started for user {user.email}'

        elif action == 'stop':
            # Disable auto-trading for user
            user.auto_trading_enabled = False
            container = container_manager.get_container(user_id)
            if container:
                container.set_active(False)
            db.session.commit()

            message = f'Trading bot stopped for user {user.email}'

        elif action == 'force_close':
            # Force close current trade
            container = container_manager.get_container(user_id)
            if container and container.current_trade:
                container.close_trade(reason="admin_force_close")
                message = f'Forced close of trade for user {user.email}'
            else:
                message = f'No active trade to close for user {user.email}'

        else:
            return jsonify({'error': 'Invalid action'}), 400

        # Log admin action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='trading_bot_control',
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f"{message} (User ID: {user_id})",
            action_metadata={'action': action, 'user_id': str(user_id)},
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': message,
            'user_id': user_id,
            'action': action,
            'timestamp': datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error controlling trading bot: {str(e)}")
        return jsonify({'error': 'Failed to control trading bot', 'message': str(e)}), 500


@admin_bp.route('/trading-bot/health', methods=['GET'])
@admin_required
def get_trading_bot_health():
    """Get trading bot system health status"""
    return get_system_health_data()

@admin_bp.route('/trading-bot/system-health', methods=['GET'])
@admin_required
def get_system_health():
    """Get overall system health for trading operations"""
    return get_system_health_data()

def get_system_health_data():
    """Get overall system health for trading operations"""
    try:
        # Check database connectivity
        db_healthy = True
        db_error = None
        try:
            from sqlalchemy import text
            db.session.execute(text('SELECT 1'))
            current_app.logger.info("Health Check - Database: PASS")
        except Exception as e:
            db_healthy = False
            db_error = str(e)
            current_app.logger.error(f"Health Check - Database: FAIL - {str(e)}")

        # Check trading container manager
        container_manager = TradingContainerManager()
        active_containers = container_manager.get_all_active_containers()

        # Check market data connectivity
        market_data_healthy = True
        market_data_error = None
        try:
            from app.services.market_data import BinanceMarketData
            market_service = BinanceMarketData()
            current_price = market_service.get_current_price('BTCUSDT')
            if not current_price:
                market_data_healthy = False
                market_data_error = "Failed to fetch BTC/USDT price"
                current_app.logger.error("Health Check - Market Data: FAIL - No price data")
            else:
                current_app.logger.info(f"Health Check - Market Data: PASS - BTC/USDT: ${current_price}")
        except Exception as e:
            market_data_healthy = False
            market_data_error = str(e)
            current_app.logger.error(f"Health Check - Market Data: FAIL - {str(e)}")

        # Check ML service
        ml_service_healthy = True
        ml_service_error = None
        try:
            from app.services.market_data import ml_service
            current_app.logger.info("Health Check - ML Service: Import successful")

            # Check if ml_service is available and has basic functionality
            if hasattr(ml_service, 'generate_ensemble_forecast'):
                current_app.logger.info("Health Check - ML Service: Has generate_ensemble_forecast method")
                # Try to generate a small forecast
                try:
                    forecast = ml_service.generate_ensemble_forecast('BTCUSDT', '1h', 1)
                    if not forecast:
                        ml_service_healthy = False
                        ml_service_error = "ML forecast generation returned empty result"
                        current_app.logger.error("Health Check - ML Service: FAIL - Empty forecast result")
                    else:
                        current_app.logger.info("Health Check - ML Service: PASS - Forecast generated successfully")
                except Exception as forecast_e:
                    ml_service_healthy = False
                    ml_service_error = f"Forecast generation failed: {str(forecast_e)}"
                    current_app.logger.error(f"Health Check - ML Service: FAIL - Forecast error: {str(forecast_e)}")
            else:
                ml_service_healthy = False
                ml_service_error = "ML service missing generate_ensemble_forecast method"
                current_app.logger.error("Health Check - ML Service: FAIL - Missing forecast method")
        except Exception as e:
            ml_service_healthy = False
            ml_service_error = f"ML service import/initialization failed: {str(e)}"
            current_app.logger.error(f"Health Check - ML Service: FAIL - Import error: {str(e)}")

        # System resource usage (basic)
        cpu_usage = psutil.cpu_percent(interval=1)
        memory_usage = psutil.virtual_memory().percent

        # Log health check results
        current_app.logger.info(f"Health Check - DB: {db_healthy}, Market: {market_data_healthy}, ML: {ml_service_healthy}")

        # Overall health status
        all_systems_healthy = all([
            db_healthy,
            market_data_healthy,
            ml_service_healthy
        ])

        # Format for frontend compatibility with detailed error information
        health_data = {
            'overall_status': 'Online' if all_systems_healthy else 'Degraded',
            'active_containers': str(len(active_containers)),
            'market_data_status': 'Connected' if market_data_healthy else 'Disconnected',
            'ml_service_status': 'Online' if ml_service_healthy else 'Offline',
            'database_status': 'Connected' if db_healthy else 'Disconnected',
            'system_resources': {
                'cpu_usage_percent': cpu_usage,
                'memory_usage_percent': memory_usage
            },
            'detailed_status': {
                'database': {
                    'healthy': db_healthy,
                    'error': db_error
                },
                'market_data': {
                    'healthy': market_data_healthy,
                    'error': market_data_error
                },
                'ml_service': {
                    'healthy': ml_service_healthy,
                    'error': ml_service_error
                }
            },
            'failed_services': [
                service for service, healthy in [
                    ('Database', db_healthy),
                    ('Market Data', market_data_healthy),
                    ('ML Service', ml_service_healthy)
                ] if not healthy
            ],
            'timestamp': datetime.utcnow().isoformat()
        }

        return jsonify(health_data), 200

    except Exception as e:
        current_app.logger.error(f"Error getting system health: {str(e)}")
        return jsonify({'error': 'Failed to get system health', 'message': str(e)}), 500


@admin_bp.route('/trading-bot/conditions', methods=['GET'])
@admin_required
def get_trading_conditions():
    """Get real-time trading conditions analysis for terminal display"""
    try:
        from app.services.trading_signals import TradingSignalGenerator
        from app.services.market_data import BinanceMarketData

        # Initialize market data service
        market_service = BinanceMarketData()

        # Find a valid user for signal generation
        admin_user = User.query.filter_by(is_active=True).first()
        if not admin_user:
            return jsonify({
                'conditions': [],
                'message': 'No active users found for condition analysis'
            }), 200

        # Create signal generator in admin monitoring mode (bypasses user trading settings)
        signal_generator = TradingSignalGenerator(user_id=admin_user.id, exchange_service=market_service, admin_monitoring_mode=True)

        # Get current market data
        current_price = market_service.get_current_price('BTCUSDT')

        conditions = []

        # Check 1: Market Data Availability
        conditions.append({
            'check': 'Market Data Connection',
            'status': 'PASS' if current_price else 'FAIL',
            'details': f'BTC/USDT Price: ${current_price}' if current_price else 'Failed to fetch price',
            'timestamp': datetime.utcnow().strftime('%H:%M:%S')
        })

        # Check 2: Generate trading signals to test conditions
        try:
            signals = signal_generator.generate_signals('BTCUSDT', '1h')

            if 'error' in signals:
                conditions.append({
                    'check': 'Signal Generation',
                    'status': 'FAIL',
                    'details': signals['error'],
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })
            else:
                # Extract technical analysis data
                signal_type = signals.get('signal', 'HOLD')
                confidence = signals.get('confidence', 0)
                # Don't overwrite current_price - keep the working market service price
                signals_current_price = signals.get('current_price', 0)
                # Use signals_current_price for signal calculations, current_price for display
                signal_price = signals_current_price if signals_current_price > 0 else current_price
                swing_high = signals.get('swing_high')
                swing_low = signals.get('swing_low')
                ha_color = signals.get('ha_color')
                prev_ha_color = signals.get('prev_ha_color')
                sma12 = signals.get('sma12')
                potential_up_move = signals.get('potential_up_move', 0)
                potential_down_move = signals.get('potential_down_move', 0)

                # Technical Condition Validations (from trading_signals.py lines 480-601)
                conditions.append({
                    'check': 'Current Market Price',
                    'status': 'PASS',
                    'details': f'BTC/USDT: ${current_price}',
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })

                # Add debug info for signal price vs market price
                if signals_current_price != current_price:
                    conditions.append({
                        'check': 'Price Comparison Debug',
                        'status': 'INFO',
                        'details': f'Market: ${current_price} | Signals: ${signals_current_price} | Using: ${signal_price}',
                        'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                    })

                # Swing Points Validation
                if swing_high is not None and swing_low is not None:
                    conditions.append({
                        'check': 'Swing Points Analysis',
                        'status': 'PASS',
                        'details': f'High: ${swing_high} | Low: ${swing_low}',
                        'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                    })

                # Potential Move Calculations
                conditions.append({
                    'check': 'Potential Move Analysis',
                    'status': 'PASS',
                    'details': f'Up: {potential_up_move:.4f}% | Down: {potential_down_move:.4f}%',
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })

                # Heikin-Ashi Trend Analysis
                if ha_color and prev_ha_color:
                    conditions.append({
                        'check': 'Heikin-Ashi Trend',
                        'status': 'PASS',
                        'details': f'Current: {ha_color} | Previous: {prev_ha_color}',
                        'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                    })

                # SMA12 Technical Indicator
                if sma12:
                    conditions.append({
                        'check': 'SMA12 Technical Level',
                        'status': 'PASS',
                        'details': f'SMA12: ${sma12}',
                        'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                    })

                # SELL Signal Conditions Validation (lines 481-485)
                sell_conditions_met = (
                    swing_high is not None and swing_high > signal_price and
                    potential_down_move > 1.0 and  # At least 1% potential down move
                    ha_color == "red" and
                    prev_ha_color == "red"
                )

                conditions.append({
                    'check': 'SELL Signal Conditions',
                    'status': 'PASS' if sell_conditions_met else 'FAIL',
                    'details': f'Swing High > Price: {swing_high > signal_price if swing_high else False} | Down Move > 1%: {potential_down_move > 1.0} | Red Trend: {ha_color == "red" and prev_ha_color == "red"}',
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })

                # BUY Signal Conditions Validation (lines 507-511)
                buy_conditions_met = (
                    swing_low is not None and swing_low < signal_price and
                    potential_up_move > 1.0 and  # At least 1% potential up move
                    ha_color == "green" and
                    prev_ha_color == "green"
                )

                conditions.append({
                    'check': 'BUY Signal Conditions',
                    'status': 'PASS' if buy_conditions_met else 'FAIL',
                    'details': f'Swing Low < Price: {swing_low < signal_price if swing_low else False} | Up Move > 1%: {potential_up_move > 1.0} | Green Trend: {ha_color == "green" and prev_ha_color == "green"}',
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })

                # Final Signal Decision
                conditions.append({
                    'check': 'Trading Signal Decision',
                    'status': 'PASS',
                    'details': f'Signal: {signal_type} | Confidence: {confidence}%',
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })

                # Risk Management (if signal is not HOLD)
                if signal_type != 'HOLD':
                    stop_loss = signals.get('stop_loss')
                    take_profit = signals.get('take_profit')
                    risk_reward = signals.get('risk_reward_ratio')
                    conditions.append({
                        'check': 'Risk Management Setup',
                        'status': 'PASS',
                        'details': f'SL: ${stop_loss} | TP: ${take_profit} | R:R: {risk_reward}',
                        'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                    })

        except Exception as e:
            conditions.append({
                'check': 'Signal Generation',
                'status': 'FAIL',
                'details': f'Exception: {str(e)}',
                'timestamp': datetime.utcnow().strftime('%H:%M:%S')
            })

        # Check 7: ML Forecast Service
        try:
            from app.services.market_data import ml_service
            if hasattr(ml_service, 'generate_ensemble_forecast'):
                conditions.append({
                    'check': 'ML Forecast Service',
                    'status': 'PASS',
                    'details': 'ML service available and functional',
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })
            else:
                conditions.append({
                    'check': 'ML Forecast Service',
                    'status': 'FAIL',
                    'details': 'ML service missing forecast method',
                    'timestamp': datetime.utcnow().strftime('%H:%M:%S')
                })
        except Exception as e:
            conditions.append({
                'check': 'ML Forecast Service',
                'status': 'FAIL',
                'details': f'ML service error: {str(e)}',
                'timestamp': datetime.utcnow().strftime('%H:%M:%S')
            })

        # Reorder conditions to prioritize most important checks first (for reverse chronological display)
        # The frontend will reverse this order, so we want the most important checks to appear at the top
        priority_order = [
            'ML Forecast Service',
            'Trading Signal Decision',
            'BUY Signal Conditions',
            'SELL Signal Conditions',
            'Potential Move Analysis',
            'Current Market Price',
            'Market Data Connection',
            'Price Comparison Debug',
            'Swing Points Analysis',
            'Heikin-Ashi Trend',
            'SMA12 Technical Level',
            'Risk Management Setup',
            'Signal Generation'
        ]

        # Sort conditions based on priority order
        def get_priority(condition):
            check_name = condition['check']
            try:
                return priority_order.index(check_name)
            except ValueError:
                return len(priority_order)  # Put unknown checks at the end

        sorted_conditions = sorted(conditions, key=get_priority)

        return jsonify({
            'conditions': sorted_conditions,
            'total_checks': len(sorted_conditions),
            'passed_checks': len([c for c in sorted_conditions if c['status'] == 'PASS']),
            'timestamp': datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting trading conditions: {str(e)}")
        return jsonify({
            'conditions': [{
                'check': 'System Error',
                'status': 'FAIL',
                'details': str(e),
                'timestamp': datetime.utcnow().strftime('%H:%M:%S')
            }],
            'error': str(e)
        }), 200


@admin_bp.route('/users/<string:user_id>/profile', methods=['GET'])
@admin_required
def get_user_profile(user_id):
    """Get comprehensive user profile for admin"""
    try:
        user = User.query.get_or_404(user_id)

        # Get user's tier status
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()

        # Get user's trades
        trades = Trade.query.filter_by(user_id=user_id).order_by(Trade.entry_time.desc()).limit(50).all()

        # Get current active trading session
        active_session = TradingSession.query.filter_by(
            user_id=user_id,
            status=TradingSessionStatus.ACTIVE
        ).first()

        # Get payment history
        payments = Payment.query.filter_by(user_id=user_id).order_by(Payment.created_at.desc()).all()

        # Calculate profit share owed
        total_profit = sum([float(trade.pnl) for trade in trades if trade.pnl and float(trade.pnl) > 0])
        profit_share_owed = total_profit * 0.20  # 20% profit share

        # Get user's API credentials
        api_credentials = APICredential.query.filter_by(user_id=user_id).all()

        # Format trades data
        trades_data = []
        for trade in trades:
            trades_data.append({
                'id': trade.id,
                'symbol': trade.symbol,
                'side': trade.side,
                'entry_price': float(trade.entry_price) if trade.entry_price else None,
                'exit_price': float(trade.exit_price) if trade.exit_price else None,
                'quantity': float(trade.quantity) if trade.quantity else None,
                'pnl': float(trade.pnl) if trade.pnl else None,
                'status': trade.status.value if hasattr(trade.status, 'value') else str(trade.status),
                'entry_time': trade.entry_time.isoformat() if trade.entry_time else None,
                'exit_time': trade.exit_time.isoformat() if trade.exit_time else None
            })

        # Format payments data
        payments_data = []
        for payment in payments:
            payments_data.append({
                'id': payment.id,
                'amount': float(payment.amount) if payment.amount else None,
                'currency': payment.currency,
                'status': payment.status.value if hasattr(payment.status, 'value') else str(payment.status),
                'payment_method': payment.payment_method.value if hasattr(payment.payment_method, 'value') else str(payment.payment_method),
                'created_at': payment.created_at.isoformat() if payment.created_at else None,
                'transaction_hash': payment.transaction_hash
            })

        # Format API credentials
        credentials_data = []
        for cred in api_credentials:
            credentials_data.append({
                'id': cred.id,
                'exchange': cred.exchange.value if hasattr(cred.exchange, 'value') else str(cred.exchange),
                'is_active': cred.is_active,
                'created_at': cred.created_at.isoformat() if cred.created_at else None
            })

        # Determine user status - prioritize email verification
        user_status = 'active'
        if hasattr(user, 'deletion_requested') and user.deletion_requested:
            user_status = 'deletion_requested'
        elif not user.is_active:
            user_status = 'inactive'
        elif not user.email_verified:
            user_status = 'unverified'

        profile_data = {
            'user_info': {
                'id': user.id,
                'email': user.email,
                'full_name': user.full_name,
                'tier': user.tier if hasattr(user, 'tier') else 1,
                'status': user_status,
                'email_verified': user.email_verified,
                'is_active': user.is_active,
                'auto_trading_enabled': user.auto_trading_enabled if hasattr(user, 'auto_trading_enabled') else False,
                'two_fa_enabled': user.two_fa_enabled if hasattr(user, 'two_fa_enabled') else False,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if hasattr(user, 'last_login') and user.last_login else None
            },
            'tier_status': {
                'current_tier': tier_status.get_current_tier() if tier_status else 1,
                'profit_share_owed': float(tier_status.profit_share_owed) if tier_status else 0.0,
                'payment_status': tier_status.payment_status if tier_status else 'unpaid',
                'last_payment_date': tier_status.last_payment_date.isoformat() if tier_status and tier_status.last_payment_date else None,
                'next_payment_date': tier_status.next_payment_date.isoformat() if tier_status and tier_status.next_payment_date else None
            },
            'trading_info': {
                'total_trades': len(trades_data),
                'active_session': {
                    'id': active_session.id if active_session else None,
                    'symbol': active_session.symbol if active_session else None,
                    'status': active_session.status.value if active_session and hasattr(active_session.status, 'value') else None
                } if active_session else None,
                'auto_trading_enabled': user.auto_trading_enabled if hasattr(user, 'auto_trading_enabled') else False
            },
            'financial_info': {
                'total_profit': round(total_profit, 2),
                'profit_share_owed': round(profit_share_owed, 2),
                'total_payments': len(payments_data)
            },
            'trades': trades_data,
            'payments': payments_data,
            'api_credentials': credentials_data
        }

        return jsonify(profile_data), 200

    except Exception as e:
        current_app.logger.error(f"Error getting user profile: {str(e)}")
        return jsonify({'error': 'Failed to get user profile', 'message': str(e)}), 500


@admin_bp.route('/users/<string:user_id>', methods=['DELETE'])
@super_admin_required
def delete_user_completely(user_id):
    """Completely delete a user and all associated data using comprehensive deletion"""
    try:
        user = User.query.get_or_404(user_id)
        user_email = user.email

        current_app.logger.info(f"Starting comprehensive user deletion for user {user_id}")

        # COMPREHENSIVE DELETION OF ALL FOREIGN KEY REFERENCES
        # This handles ALL models that reference users.id to prevent foreign key constraint errors

        # Import all models that have foreign keys to users (with error handling)
        try:
            from app.models.security_log import LoginAttempt, SecurityLog, APICredential
            from app.models.subscription import Subscription
            from app.models.payment import Payment
            from app.models.trade import Trade, TradingSession
            from app.models.fee_calculation import FeeCalculation
            from app.models.user import User2FABackupCode, User2FAEmailCode
            from app.models.admin import CouponUsage
            from app.models.referral import Referral, ReferralEarning, ReferrerProfile
            from app.models.solana_payment import SolanaPayment, MembershipBilling
            from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
            from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
            from app.models.user_tier_status import UserTierStatus
        except ImportError as e:
            current_app.logger.error(f"Import error in comprehensive deletion: {e}")
            return jsonify({'success': False, 'message': f'Import error: {str(e)}'}), 500

        # Delete in order to respect foreign key dependencies

        # 1. Delete 2FA related data
        backup_codes_count = User2FABackupCode.query.filter_by(user_id=user_id).count()
        if backup_codes_count > 0:
            User2FABackupCode.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {backup_codes_count} 2FA backup codes for user {user_id}")

        email_codes_count = User2FAEmailCode.query.filter_by(user_id=user_id).count()
        if email_codes_count > 0:
            User2FAEmailCode.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {email_codes_count} 2FA email codes for user {user_id}")

        # 2. Delete security and login data
        login_attempts_count = LoginAttempt.query.filter_by(user_id=user_id).count()
        if login_attempts_count > 0:
            LoginAttempt.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {login_attempts_count} login attempts for user {user_id}")

        security_logs_count = SecurityLog.query.filter_by(user_id=user_id).count()
        if security_logs_count > 0:
            SecurityLog.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {security_logs_count} security logs for user {user_id}")

        # 3. Delete API credentials
        api_creds_count = APICredential.query.filter_by(user_id=user_id).count()
        if api_creds_count > 0:
            APICredential.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {api_creds_count} API credentials for user {user_id}")

        # 4. Delete trading data
        trades_count = Trade.query.filter_by(user_id=user_id).count()
        if trades_count > 0:
            Trade.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {trades_count} trades for user {user_id}")

        sessions_count = TradingSession.query.filter_by(user_id=user_id).count()
        if sessions_count > 0:
            TradingSession.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {sessions_count} trading sessions for user {user_id}")

        # 5. Delete fee calculations
        fees_count = FeeCalculation.query.filter_by(user_id=user_id).count()
        if fees_count > 0:
            FeeCalculation.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {fees_count} fee calculations for user {user_id}")

        # 6. Delete coupon usage
        coupon_usage_count = CouponUsage.query.filter_by(user_id=user_id).count()
        if coupon_usage_count > 0:
            CouponUsage.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {coupon_usage_count} coupon usages for user {user_id}")

        # 7. Delete referral data (handle complex relationships)
        user_referrals = Referral.query.filter_by(referrer_id=user_id).all()
        earnings_count = 0
        for referral in user_referrals:
            earnings = ReferralEarning.query.filter_by(referral_id=referral.id).all()
            for earning in earnings:
                db.session.delete(earning)
                earnings_count += 1
        if earnings_count > 0:
            current_app.logger.info(f"Deleted {earnings_count} referral earnings for user {user_id}")

        referrals_as_referrer = Referral.query.filter_by(referrer_id=user_id).count()
        if referrals_as_referrer > 0:
            Referral.query.filter_by(referrer_id=user_id).delete()
            current_app.logger.info(f"Deleted {referrals_as_referrer} referrals where user is referrer")

        referrals_as_referee = Referral.query.filter_by(referee_id=user_id).count()
        if referrals_as_referee > 0:
            Referral.query.filter_by(referee_id=user_id).delete()
            current_app.logger.info(f"Deleted {referrals_as_referee} referrals where user is referee")

        profile_count = ReferrerProfile.query.filter_by(user_id=user_id).count()
        if profile_count > 0:
            ReferrerProfile.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {profile_count} referrer profiles for user {user_id}")

        # 8. Delete Solana payment data
        solana_payments_count = SolanaPayment.query.filter_by(user_id=user_id).count()
        if solana_payments_count > 0:
            SolanaPayment.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {solana_payments_count} Solana payments for user {user_id}")

        billing_count = MembershipBilling.query.filter_by(user_id=user_id).count()
        if billing_count > 0:
            MembershipBilling.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {billing_count} membership billings for user {user_id}")

        # 9. Delete payments (must be after Solana payments due to potential references)
        payments_count = Payment.query.filter_by(user_id=user_id).count()
        if payments_count > 0:
            Payment.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {payments_count} payments for user {user_id}")

        # 10. Delete subscriptions (must be after payments due to foreign key)
        subscriptions_count = Subscription.query.filter_by(user_id=user_id).count()
        if subscriptions_count > 0:
            Subscription.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {subscriptions_count} subscriptions for user {user_id}")
        # Handle remaining models (paper trading, balance tracking, tier status)
        # Paper trading cleanup
        paper_accounts = PaperTradingAccount.query.filter_by(user_id=user_id).all()
        for account in paper_accounts:
            PaperBalanceSnapshot.query.filter_by(paper_account_id=account.id).delete()
        PaperTrade.query.filter_by(user_id=user_id).delete()
        PaperTradingSession.query.filter_by(user_id=user_id).delete()
        PaperTradingAccount.query.filter_by(user_id=user_id).delete()

        # Balance tracking cleanup
        balance_trackers = UserBalanceTracker.query.filter_by(user_id=user_id).all()
        for tracker in balance_trackers:
            BalanceSnapshot.query.filter_by(tracker_id=tracker.id).delete()
        UserBalanceTracker.query.filter_by(user_id=user_id).delete()

        # Tier status cleanup
        tier_status_count = UserTierStatus.query.filter_by(user_id=user_id).count()
        if tier_status_count > 0:
            UserTierStatus.query.filter_by(user_id=user_id).delete()
            current_app.logger.info(f"Deleted {tier_status_count} tier status records for user {user_id}")

        # Finally delete the user using raw SQL to avoid SQLAlchemy relationship issues
        from sqlalchemy import text
        result = db.session.execute(text(
            "DELETE FROM users WHERE id = :user_id"
        ), {"user_id": user_id})
        current_app.logger.info(f"Deleted user {user_id}")

        # Commit all changes
        db.session.commit()
        current_app.logger.info(f"Successfully completed comprehensive deletion for user {user_id}")

        # Log admin action
        AdminAction.log_action(
            admin_id=get_jwt_identity().replace('admin_', ''),
            action_type='delete_user',
            target_type='user',
            target_id=None,  # User IDs are UUIDs, not integers
            description=f'Completely deleted user {user_email} (ID: {user_id}) and all associated data',
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': f'User {user_email} and all associated data deleted successfully',
            'deleted_user_id': user_id
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting user: {str(e)}")
        return jsonify({'error': 'Failed to delete user', 'message': str(e)}), 500


@admin_bp.route('/users/prune', methods=['POST'])
@super_admin_required
def prune_unverified_users():
    """Prune (delete) unverified users that have been inactive for specified days"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        data = request.get_json()
        inactivity_days = data.get('inactivity_days', 30)

        # Validate inactivity_days parameter
        if not isinstance(inactivity_days, int) or inactivity_days < 1:
            return jsonify({'message': 'Invalid inactivity_days parameter'}), 400

        # Calculate cutoff date
        cutoff_date = datetime.utcnow() - timedelta(days=inactivity_days)

        # Find unverified users that haven't been active since cutoff date
        unverified_users = User.query.filter(
            User.email_verified == False,
            User.created_at < cutoff_date
        ).all()

        if not unverified_users:
            return jsonify({
                'message': f'No unverified users found that have been inactive for {inactivity_days} days',
                'deleted_count': 0
            }), 200

        deleted_count = 0
        deleted_emails = []

        for user in unverified_users:
            try:
                # Store email for logging
                user_email = user.email
                user_id = user.id

                # Delete associated data first (comprehensive cleanup)
                # 1. Delete IP access logs (if available)
                if IPAccessLog:
                    IPAccessLog.query.filter_by(user_id=user_id).delete()

                # 2. Delete security logs and login attempts
                SecurityLog.query.filter_by(user_id=user_id).delete()
                LoginAttempt.query.filter_by(user_id=user_id).delete()

                # 3. Delete fee calculations
                FeeCalculation.query.filter_by(user_id=user_id).delete()

                # 4. Delete trades and trading sessions
                Trade.query.filter_by(user_id=user_id).delete()
                TradingSession.query.filter_by(user_id=user_id).delete()

                # 5. Delete user tier status
                UserTierStatus.query.filter_by(user_id=user_id).delete()

                # 6. Delete API credentials
                APICredential.query.filter_by(user_id=user_id).delete()

                # 7. Delete any coupon usage
                CouponUsage.query.filter_by(user_id=user_id).delete()

                # 8. Delete payments
                Payment.query.filter_by(user_id=user_id).delete()

                # 9. Delete subscriptions
                Subscription.query.filter_by(user_id=user_id).delete()

                # 10. Delete paper trading data
                paper_accounts = PaperTradingAccount.query.filter_by(user_id=user_id).all()
                for account in paper_accounts:
                    PaperBalanceSnapshot.query.filter_by(paper_account_id=account.id).delete()
                PaperTrade.query.filter_by(user_id=user_id).delete()
                PaperTradingSession.query.filter_by(user_id=user_id).delete()
                PaperTradingAccount.query.filter_by(user_id=user_id).delete()

                # 11. Delete balance tracking data
                balance_trackers = UserBalanceTracker.query.filter_by(user_id=user_id).all()
                for tracker in balance_trackers:
                    BalanceSnapshot.query.filter_by(tracker_id=tracker.id).delete()
                UserBalanceTracker.query.filter_by(user_id=user_id).delete()

                # 12. Delete the user
                db.session.delete(user)

                deleted_count += 1
                deleted_emails.append(user_email)

                current_app.logger.info(f"Pruned unverified user: {user_email} (inactive for {inactivity_days} days)")

            except Exception as user_error:
                current_app.logger.error(f"Error deleting user {user.email}: {str(user_error)}")
                continue

        # Commit all changes
        db.session.commit()

        # Log admin action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='prune_users',
            target_type='users',
            target_id=None,
            description=f'Pruned {deleted_count} unverified users inactive for {inactivity_days} days',
            action_metadata={
                'inactivity_days': inactivity_days,
                'deleted_count': deleted_count,
                'deleted_emails': deleted_emails[:10]  # Store first 10 emails for reference
            },
            ip_address=request.remote_addr
        )

        current_app.logger.info(f"Successfully pruned {deleted_count} unverified users")

        return jsonify({
            'message': f'Successfully pruned {deleted_count} unverified users',
            'deleted_count': deleted_count,
            'inactivity_days': inactivity_days
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error pruning users: {str(e)}")
        return jsonify({'message': 'Failed to prune users', 'error': str(e)}), 500


@admin_bp.route('/change-password', methods=['POST'])
@admin_required
def change_admin_password():
    """Change admin password with validation"""
    try:
        data = request.get_json()
        current_password = data.get('current_password')
        new_password = data.get('new_password')

        if not current_password or not new_password:
            return jsonify({'message': 'Current password and new password are required'}), 400

        # Get current admin user from JWT claims
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        if not admin_id:
            current_app.logger.error("Admin ID not found in JWT token")
            return jsonify({'message': 'Admin ID not found in token'}), 400

        admin_user = AdminUser.query.get(admin_id)
        if not admin_user:
            current_app.logger.error(f"Admin user not found for ID: {admin_id}")
            return jsonify({'message': 'Admin user not found'}), 404

        # Verify current password
        if not admin_user.check_password(current_password):
            current_app.logger.warning(f"Incorrect current password for admin: {admin_user.username}")
            return jsonify({'message': 'Current password is incorrect'}), 400

        # Validate new password strength
        if len(new_password) < 8:
            return jsonify({'message': 'Password must be at least 8 characters long'}), 400

        # Check for password complexity
        import re
        if not re.search(r'[A-Z]', new_password):
            return jsonify({'message': 'Password must contain at least one uppercase letter'}), 400
        if not re.search(r'[a-z]', new_password):
            return jsonify({'message': 'Password must contain at least one lowercase letter'}), 400
        if not re.search(r'\d', new_password):
            return jsonify({'message': 'Password must contain at least one number'}), 400
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', new_password):
            return jsonify({'message': 'Password must contain at least one special character'}), 400

        # Update password
        admin_user.set_password(new_password)
        db.session.commit()

        # Log the password change (skip if SecurityManager fails)
        try:
            SecurityManager.log_api_access(
                user_id=admin_user.id,
                endpoint='POST /api/admin/change-password',
                ip_address=request.remote_addr,
                details='Admin password changed successfully'
            )
        except Exception as log_error:
            current_app.logger.warning(f"Failed to log admin password change: {str(log_error)}")

        return jsonify({'message': 'Password changed successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error changing admin password: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({'message': 'Internal server error'}), 500


@admin_bp.route('/request-password-change', methods=['POST'])
@admin_required
def request_admin_password_change():
    """Request admin password change with email verification"""
    try:
        data = request.get_json()
        new_password = data.get('new_password')

        if not new_password:
            return jsonify({'message': 'New password is required'}), 400

        # Get current admin user from JWT claims
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        if not admin_id:
            current_app.logger.error("Admin ID not found in JWT token")
            return jsonify({'message': 'Admin ID not found in token'}), 400

        admin_user = AdminUser.query.get(admin_id)
        if not admin_user:
            current_app.logger.error(f"Admin user not found for ID: {admin_id}")
            return jsonify({'message': 'Admin user not found'}), 404

        # Validate new password strength
        if len(new_password) < 8:
            return jsonify({'message': 'Password must be at least 8 characters long'}), 400

        # Check for password complexity
        import re
        if not re.search(r'[A-Z]', new_password):
            return jsonify({'message': 'Password must contain at least one uppercase letter'}), 400
        if not re.search(r'[a-z]', new_password):
            return jsonify({'message': 'Password must contain at least one lowercase letter'}), 400
        if not re.search(r'\d', new_password):
            return jsonify({'message': 'Password must contain at least one number'}), 400
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', new_password):
            return jsonify({'message': 'Password must contain at least one special character'}), 400

        # Generate verification code
        import random
        verification_code = str(random.randint(100000, 999999))

        # Store the verification code and new password temporarily in Redis
        from app.services.redis_service import RedisService
        redis_key = f"admin_password_change:{admin_id}"
        redis_data = {
            'verification_code': verification_code,
            'new_password': new_password,
            'admin_id': admin_id,
            'timestamp': datetime.utcnow().isoformat()
        }

        # Store for 10 minutes
        RedisService.set_with_expiry(redis_key, redis_data, 600)

        # Send verification email
        from app.services.email_service import EmailService
        try:
            EmailService.send_admin_verification_email(
                admin_user.username,  # This is now the email address
                verification_code,
                'password change'
            )

            return jsonify({
                'message': 'Verification code sent to your email address',
                'email': admin_user.username
            }), 200

        except Exception as email_error:
            current_app.logger.error(f"Failed to send verification email: {str(email_error)}")
            return jsonify({'message': 'Failed to send verification email. Please try again.'}), 500

    except Exception as e:
        current_app.logger.error(f"Error requesting admin password change: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({'message': 'Internal server error'}), 500


@admin_bp.route('/verify-password-change', methods=['POST'])
@admin_required
def verify_admin_password_change():
    """Verify admin password change with email code"""
    try:
        data = request.get_json()
        verification_code = data.get('verification_code')

        if not verification_code:
            return jsonify({'message': 'Verification code is required'}), 400

        # Get current admin user from JWT claims
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        if not admin_id:
            return jsonify({'message': 'Admin ID not found in token'}), 400

        # Get verification data from Redis
        from app.services.redis_service import RedisService
        redis_key = f"admin_password_change:{admin_id}"
        redis_data = RedisService.get(redis_key)

        if not redis_data:
            return jsonify({'message': 'Verification code expired or not found. Please request a new one.'}), 400

        # Verify the code
        if redis_data.get('verification_code') != verification_code:
            return jsonify({'message': 'Invalid verification code'}), 400

        # Get admin user and update password
        admin_user = AdminUser.query.get(admin_id)
        if not admin_user:
            return jsonify({'message': 'Admin user not found'}), 404

        # Update password
        new_password = redis_data.get('new_password')
        admin_user.set_password(new_password)
        db.session.commit()

        # Clear the verification data
        RedisService.delete(redis_key)

        # Log the password change
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='password_changed',
            target_type='admin',
            target_id=admin_id,
            description='Admin password changed via email verification',
            ip_address=request.remote_addr
        )

        return jsonify({'message': 'Password changed successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error verifying admin password change: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({'message': 'Internal server error'}), 500


@admin_bp.route('/request-email-change', methods=['POST'])
@admin_required
def request_admin_email_change():
    """Request admin email change with dual email verification"""
    try:
        data = request.get_json()
        new_email = data.get('new_email')

        if not new_email:
            return jsonify({'message': 'New email is required'}), 400

        # Basic email validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, new_email):
            return jsonify({'message': 'Invalid email format'}), 400

        # Get current admin user from JWT claims
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        if not admin_id:
            return jsonify({'message': 'Admin ID not found in token'}), 400

        admin_user = AdminUser.query.get(admin_id)
        if not admin_user:
            return jsonify({'message': 'Admin user not found'}), 404

        # Check if new email already exists
        existing_admin = AdminUser.query.filter(
            AdminUser.username == new_email,
            AdminUser.id != admin_id
        ).first()
        if existing_admin:
            return jsonify({'message': 'Email address already in use by another admin'}), 409

        # Generate verification codes
        import random
        old_email_code = str(random.randint(100000, 999999))
        new_email_code = str(random.randint(100000, 999999))

        # Store the verification codes and new email temporarily in Redis
        from app.services.redis_service import RedisService
        redis_key = f"admin_email_change:{admin_id}"
        redis_data = {
            'old_email_code': old_email_code,
            'new_email_code': new_email_code,
            'new_email': new_email,
            'old_email': admin_user.username,
            'admin_id': admin_id,
            'timestamp': datetime.utcnow().isoformat()
        }

        # Store for 15 minutes
        RedisService.set_with_expiry(redis_key, redis_data, 900)

        # Send verification emails
        from app.services.email_service import EmailService
        try:
            # Send code to old email
            EmailService.send_admin_verification_email(
                admin_user.username,
                old_email_code,
                'email change (current email verification)'
            )

            # Send code to new email
            EmailService.send_admin_verification_email(
                new_email,
                new_email_code,
                'email change (new email verification)'
            )

            return jsonify({
                'message': 'Verification codes sent to both email addresses',
                'old_email': admin_user.username,
                'new_email': new_email
            }), 200

        except Exception as email_error:
            current_app.logger.error(f"Failed to send verification emails: {str(email_error)}")
            return jsonify({'message': 'Failed to send verification emails. Please try again.'}), 500

    except Exception as e:
        current_app.logger.error(f"Error requesting admin email change: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({'message': 'Internal server error'}), 500


@admin_bp.route('/verify-email-change', methods=['POST'])
@admin_required
def verify_admin_email_change():
    """Verify admin email change with dual email codes"""
    try:
        data = request.get_json()
        old_email_code = data.get('old_email_code')
        new_email_code = data.get('new_email_code')

        if not old_email_code or not new_email_code:
            return jsonify({'message': 'Both verification codes are required'}), 400

        # Get current admin user from JWT claims
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        if not admin_id:
            return jsonify({'message': 'Admin ID not found in token'}), 400

        # Get verification data from Redis
        from app.services.redis_service import RedisService
        redis_key = f"admin_email_change:{admin_id}"
        redis_data = RedisService.get(redis_key)

        if not redis_data:
            return jsonify({'message': 'Verification codes expired or not found. Please request new ones.'}), 400

        # Verify both codes
        if (redis_data.get('old_email_code') != old_email_code or
            redis_data.get('new_email_code') != new_email_code):
            return jsonify({'message': 'Invalid verification codes'}), 400

        # Get admin user and update email
        admin_user = AdminUser.query.get(admin_id)
        if not admin_user:
            return jsonify({'message': 'Admin user not found'}), 404

        # Update email
        old_email = admin_user.username
        new_email = redis_data.get('new_email')
        admin_user.username = new_email
        db.session.commit()

        # Clear the verification data
        RedisService.delete(redis_key)

        # Log the email change
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='email_changed',
            target_type='admin',
            target_id=admin_id,
            description=f'Admin email changed from {old_email} to {new_email}',
            action_metadata={'old_email': old_email, 'new_email': new_email},
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Email address updated successfully',
            'new_email': new_email
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error verifying admin email change: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({'message': 'Internal server error'}), 500


@admin_bp.route('/admins/<int:admin_id>', methods=['PUT'])
@super_admin_required
def update_admin(admin_id):
    """Update admin account details (super admin only)"""
    try:
        claims = get_jwt()
        current_admin_id = claims.get('admin_id')

        # Prevent self-modification of super admin status
        if admin_id == current_admin_id:
            return jsonify({'message': 'Cannot modify your own admin privileges'}), 400

        admin = AdminUser.query.get_or_404(admin_id)
        data = request.get_json()

        # Update username if provided
        if 'username' in data:
            new_username = data['username'].strip()
            if len(new_username) < 3 or len(new_username) > 50:
                return jsonify({'message': 'Username must be 3-50 characters'}), 400

            # Check if username already exists (excluding current admin)
            existing = AdminUser.query.filter(
                AdminUser.username == new_username,
                AdminUser.id != admin_id
            ).first()
            if existing:
                return jsonify({'message': 'Username already exists'}), 409

            admin.username = new_username

        # Update super admin status if provided
        if 'is_super_admin' in data:
            admin.is_super_admin = bool(data['is_super_admin'])

        # Update active status if provided
        if 'is_active' in data:
            admin.is_active = bool(data['is_active'])

        db.session.commit()

        # Log the admin update
        AdminAction.log_action(
            admin_id=current_admin_id,
            action_type='admin_updated',
            target_type='admin',
            target_id=admin_id,
            description=f'Admin {admin.username} updated',
            action_metadata=data,
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Admin updated successfully',
            'admin': admin.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Update admin failed: {str(e)}")
        return jsonify({'message': 'Failed to update admin', 'error': str(e)}), 500


@admin_bp.route('/admins/<int:admin_id>', methods=['DELETE'])
@super_admin_required
def delete_admin(admin_id):
    """Delete admin account (super admin only)"""
    try:
        claims = get_jwt()
        current_admin_id = claims.get('admin_id')

        # Prevent self-deletion
        if admin_id == current_admin_id:
            return jsonify({'message': 'Cannot delete your own admin account'}), 400

        # Prevent deletion of the first admin (system primary admin)
        if admin_id == 1:
            return jsonify({
                'message': 'Cannot delete the primary system admin account',
                'error': 'primary_admin_protected',
                'details': 'The first admin account (ID: 1) is protected from deletion for security reasons'
            }), 403

        admin = AdminUser.query.get_or_404(admin_id)
        admin_username = admin.username

        # For now, use soft delete to avoid foreign key issues
        # Set admin as inactive instead of hard delete
        admin.is_active = False
        admin.username = f"DELETED_{admin.username}_{admin_id}"  # Mark as deleted
        db.session.commit()

        # Log the admin deletion
        AdminAction.log_action(
            admin_id=current_admin_id,
            action_type='admin_deleted',
            target_type='admin',
            target_id=admin_id,
            description=f'Admin {admin_username} marked as deleted (soft delete)',
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Admin deleted successfully',
            'deleted_admin': admin_username
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Delete admin failed: {str(e)}")
        import traceback
        current_app.logger.error(f"Delete admin traceback: {traceback.format_exc()}")
        return jsonify({
            'message': 'Failed to delete admin',
            'error': str(e),
            'details': 'Check server logs for more information'
        }), 500


@admin_bp.route('/admins/<int:admin_id>/reset-password', methods=['POST'])
@super_admin_required
def reset_admin_password(admin_id):
    """Reset admin password (super admin only)"""
    try:
        claims = get_jwt()
        current_admin_id = claims.get('admin_id')

        admin = AdminUser.query.get_or_404(admin_id)
        data = request.get_json()
        new_password = data.get('new_password')

        if not new_password:
            return jsonify({'message': 'New password is required'}), 400

        # Validate password strength
        if len(new_password) < 8:
            return jsonify({'message': 'Password must be at least 8 characters long'}), 400

        # Check for password complexity
        import re
        if not re.search(r'[A-Z]', new_password):
            return jsonify({'message': 'Password must contain at least one uppercase letter'}), 400
        if not re.search(r'[a-z]', new_password):
            return jsonify({'message': 'Password must contain at least one lowercase letter'}), 400
        if not re.search(r'\d', new_password):
            return jsonify({'message': 'Password must contain at least one number'}), 400
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', new_password):
            return jsonify({'message': 'Password must contain at least one special character'}), 400

        # Update password
        admin.set_password(new_password)
        db.session.commit()

        # Log the password reset
        AdminAction.log_action(
            admin_id=current_admin_id,
            action_type='admin_password_reset',
            target_type='admin',
            target_id=admin_id,
            description=f'Password reset for admin {admin.username}',
            ip_address=request.remote_addr
        )

        return jsonify({'message': 'Admin password reset successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Reset admin password failed: {str(e)}")
        return jsonify({'message': 'Failed to reset admin password', 'error': str(e)}), 500


@admin_bp.route('/admins/<int:admin_id>/sessions', methods=['GET'])
@super_admin_required
def get_admin_sessions(admin_id):
    """Get active sessions for an admin account (super admin only)"""
    try:
        admin = AdminUser.query.get_or_404(admin_id)

        # Get recent login sessions from IP access logs
        recent_sessions = IPAccessLog.query.filter_by(
            admin_id=admin_id,
            login_successful=True
        ).order_by(IPAccessLog.login_timestamp.desc()).limit(10).all()

        sessions_data = []
        for session in recent_sessions:
            sessions_data.append({
                'id': session.id,
                'ip_address': session.ip_address,
                'login_timestamp': session.login_timestamp.isoformat() if session.login_timestamp else None,
                'user_agent': session.user_agent,
                'geolocation': session.geolocation_data,
                'is_current': session.ip_address == request.remote_addr
            })

        return jsonify({
            'admin_id': admin_id,
            'admin_username': admin.username,
            'sessions': sessions_data
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get admin sessions failed: {str(e)}")
        return jsonify({'message': 'Failed to get admin sessions', 'error': str(e)}), 500


@admin_bp.route('/settings/admins', methods=['GET'])
@super_admin_required
def get_admin_settings():
    """Get admin management settings and statistics (super admin only)"""
    try:
        # Get all admins with their statistics
        admins = AdminUser.query.order_by(AdminUser.created_at.desc()).all()
        admin_list = []

        for admin in admins:
            # Get recent activity count
            week_ago = datetime.utcnow() - timedelta(days=7)
            recent_actions = AdminAction.query.filter(
                AdminAction.admin_id == admin.id,
                AdminAction.created_at >= week_ago
            ).count()

            # Get last login info
            last_login_log = IPAccessLog.query.filter_by(
                admin_id=admin.id,
                login_successful=True
            ).order_by(IPAccessLog.login_timestamp.desc()).first()

            admin_data = admin.to_dict()
            admin_data.update({
                'recent_actions_count': recent_actions,
                'last_login_ip': last_login_log.ip_address if last_login_log else None,
                'last_login_timestamp': last_login_log.login_timestamp.isoformat() if last_login_log and last_login_log.login_timestamp else None,
                'total_logins': IPAccessLog.query.filter_by(admin_id=admin.id, login_successful=True).count()
            })

            admin_list.append(admin_data)

        # Get system statistics
        total_admins = len(admin_list)
        active_admins = len([a for a in admin_list if a['is_active']])
        super_admins = len([a for a in admin_list if a['is_super_admin']])

        return jsonify({
            'admins': admin_list,
            'statistics': {
                'total_admins': total_admins,
                'active_admins': active_admins,
                'super_admins': super_admins,
                'limited_admins': active_admins - super_admins
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get admin settings failed: {str(e)}")
        return jsonify({'message': 'Failed to get admin settings', 'error': str(e)}), 500


@admin_bp.route('/create-admin', methods=['POST'])
@admin_required
def create_admin_account():
    """Create new admin account (super admin only)"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        admin_level = data.get('admin_level', 'limited_admin')

        if not email or not password:
            return jsonify({'message': 'Email and password are required'}), 400

        # Check if current admin is super admin
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        is_super_admin = claims.get('is_super_admin', False)

        if not is_super_admin:
            return jsonify({'message': 'Only super admins can create new admin accounts'}), 403

        current_admin = AdminUser.query.get(admin_id)

        # Check if admin already exists
        existing_admin = AdminUser.query.filter_by(username=email).first()
        if existing_admin:
            return jsonify({'message': 'Admin with this email already exists'}), 400

        # Validate password strength
        if len(password) < 8:
            return jsonify({'message': 'Password must be at least 8 characters long'}), 400

        # Create new admin user
        new_admin = AdminUser(
            username=email,
            password=password,
            is_super_admin=(admin_level == 'super_admin'),
            created_by=admin_id
        )

        db.session.add(new_admin)
        db.session.commit()

        # Log the admin creation
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='create_admin',
            target_type='admin',
            description=f'Created new {admin_level} account for {email}',
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Admin account created successfully',
            'admin_id': new_admin.id,
            'username': new_admin.username,
            'admin_level': admin_level
        }), 201

    except Exception as e:
        current_app.logger.error(f"Error creating admin account: {str(e)}")
        return jsonify({'message': 'Internal server error'}), 500


@admin_bp.route('/ip/clear-logs', methods=['POST'])
@super_admin_required
def clear_ip_access_logs():
    """Clear IP access logs older than specified days"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        data = request.get_json()
        days_to_keep = data.get('days_to_keep', 30)

        # Validate days_to_keep parameter
        if not isinstance(days_to_keep, int) or days_to_keep < 0:
            return jsonify({'message': 'Invalid days_to_keep parameter'}), 400

        if days_to_keep == 0:
            # Clear all logs
            deleted_count = IPAccessLog.query.count()
            IPAccessLog.query.delete()
            current_app.logger.info(f"Cleared all {deleted_count} IP access logs")
        else:
            # Calculate cutoff date
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)

            # Count logs to be deleted
            logs_to_delete = IPAccessLog.query.filter(
                IPAccessLog.login_timestamp < cutoff_date
            )
            deleted_count = logs_to_delete.count()

            # Delete old logs
            logs_to_delete.delete()
            current_app.logger.info(f"Cleared {deleted_count} IP access logs older than {days_to_keep} days")

        # Commit changes
        db.session.commit()

        # Log admin action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='clear_ip_logs',
            target_type='ip_logs',
            target_id=None,
            description=f'Cleared {deleted_count} IP access logs ({"all logs" if days_to_keep == 0 else f"older than {days_to_keep} days"})',
            action_metadata={
                'days_to_keep': days_to_keep,
                'deleted_count': deleted_count
            },
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': f'Successfully cleared {deleted_count} IP access logs',
            'deleted_count': deleted_count,
            'days_to_keep': days_to_keep
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error clearing IP logs: {str(e)}")
        return jsonify({'message': 'Failed to clear IP logs', 'error': str(e)}), 500


@admin_bp.route('/ip/ban', methods=['POST'])
@super_admin_required
def ban_ip_address():
    """Ban an IP address"""
    try:
        claims = get_jwt()
        admin_id = claims.get('admin_id')

        data = request.get_json()
        ip_address = data.get('ip_address', '').strip()
        reason = data.get('reason', '').strip()
        expires_at = data.get('expires_at')

        # Validate required fields
        if not ip_address or not reason:
            return jsonify({'message': 'IP address and reason are required'}), 400

        # Validate IP address format (basic validation)
        import ipaddress
        try:
            ipaddress.ip_address(ip_address)
        except ValueError:
            return jsonify({'message': 'Invalid IP address format'}), 400

        # Check if IP is already banned
        existing_ban = IPBlacklist.query.filter_by(ip_address=ip_address, is_active=True).first()
        if existing_ban:
            return jsonify({'message': 'IP address is already banned'}), 400

        # Parse expiry date if provided
        expiry_date = None
        if expires_at:
            try:
                from datetime import datetime
                expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'message': 'Invalid expiry date format'}), 400

        # Create IP ban record using IPBlacklist.ban_ip method
        ip_ban = IPBlacklist.ban_ip(
            ip_address=ip_address,
            reason=reason,
            banned_by_admin_id=admin_id,
            expires_at=expiry_date,
            ban_type='manual'
        )

        # IPBlacklist.ban_ip already commits to database

        # Log admin action
        AdminAction.log_action(
            admin_id=admin_id,
            action_type='ban_ip',
            target_type='ip_address',
            target_id=None,
            description=f'Banned IP address {ip_address}: {reason}',
            action_metadata={
                'ip_address': ip_address,
                'reason': reason,
                'expires_at': expires_at
            },
            ip_address=request.remote_addr
        )

        current_app.logger.info(f"Admin {admin_id} banned IP {ip_address}: {reason}")

        return jsonify({
            'message': f'IP address {ip_address} banned successfully',
            'ban_id': ip_ban.id,
            'expires_at': expiry_date.isoformat() if expiry_date else None
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error banning IP: {str(e)}")
        return jsonify({'message': 'Failed to ban IP address', 'error': str(e)}), 500
