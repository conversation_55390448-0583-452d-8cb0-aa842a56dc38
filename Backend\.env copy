# Flask Configuration
FLASK_ENV=development
SECRET_KEY=TCacg_x783EU7n614K4PcE5_UWrBEFLH7BN-YZ7Rmqo
JWT_SECRET_KEY=XffWVzGlxv-n614K4PcE5_UWrBEFLH7BN-YZ7Rmqo
# Solana Configuration
SOLANA_NETWORK=mainnet
SOLANA_RPC_ENDPOINT=https://api.mainnet-beta.solana.com

# Treasury Wallet Address (REPLACE WITH YOUR ACTUAL WALLET ADDRESS)
SOLANA_TREASURY_WALLET=HBWRBVndpKZjAS6VFSSAdb93PB1pZfLZSn5QkDYGvn8k

# Token Mint Addresses
SOLANA_USDT_MINT=Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB
SOLANA_USDC_MINT=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v

# Tier 2 Payment Configuration
TIER_2_PAYMENT_AMOUNT=199
TIER_2_PAYMENT_TOKEN=USDT
TIER_2_PAYMENT_TOKEN_MINT=Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB
#USDC TOKEN ADDRESS: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
#USDT TOKEN ADDRESS: Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB

# MySQL Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=deeptrade
DB_USER=deeptrade_user
DB_PASSWORD=123456
MYSQL_ROOT_PASSWORD=123456

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
RATE_LIMIT_STORAGE_URL=redis://localhost:6379/1

# Google OAuth Configuration
GOOGLE_CLIENT_ID=65022818057-or6uhhh07v10uk2tfrmq7n7i8c0g1gou.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Binance API Configuration (for ML predictions)
BINANCE_API_KEY=73UnYTIA60rZV2c92ULMSUnEVHHjdaG5z1YAqlPz1oHT1ux1eQV5DZzLxFj21DNL
BINANCE_API_SECRET=API_SECRET = 'wKPsGu54IFCtsGnEC5RooQy34lOmGRVJjRmivFtBP3tmPzWueee9PXzaVwm6ffYB'

# Developer Wallet for Fee Collection
#DEVELOPER_WALLET_ADDRESS=HBWRBVndpKZjAS6VFSSAdb93PB1pZfLZSn5QkDYGvn8k

# JWT Token Expiration (in seconds)
JWT_ACCESS_TOKEN_EXPIRES=864000
JWT_REFRESH_TOKEN_EXPIRES=2592000

# Security Configuration
BCRYPT_LOG_ROUNDS=12

# Rate Limiting Configuration
RATE_LIMIT_MAX_ATTEMPTS=10
RATE_LIMIT_BLOCK_DURATION_MINUTES=10
RATE_LIMIT_RESET_HOURS=2
RATE_LIMIT_AUTO_BAN_THRESHOLD=20
RATE_LIMIT_AUTO_BAN_DURATION_HOURS=12

# Subscription Configuration
TIER_1_PROFIT_SHARE=0.40
TIER_2_PROFIT_SHARE=0.20
TIER_2_MONTHLY_FEE=199
TIER_3_PROFIT_SHARE=0.10

# Paper Trading Configuration
PAPER_TRADING_ENABLED=true
PAPER_TRADING_INITIAL_BALANCE=10000
PAPER_TRADING_MAX_RESET_PER_DAY=3

# Trading Symbol Configuration
DEFAULT_TRADING_SYMBOL=BTCUSDT
DEFAULT_BASE_ASSET=BTC
DEFAULT_QUOTE_ASSET=USDT
SUPPORTED_QUOTE_ASSETS=USDT,USDC,USD1,BUSD,USD

# Brevo SMTP Configuration for Email Verification
SMTP_SERVER=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=QjV46P1gtFkpmZhR
FROM_EMAIL=<EMAIL>
FROM_EMAIL2=<EMAIL>
FROM_EMAIL3=<EMAIL>

# Frontend URL for generating email verification links (change to your domain in production)
FRONTEND_URL=http://localhost:5173

# Application Settings
ITEMS_PER_PAGE=20

# Elite ML System Configuration (96% Accuracy)
ELITE_ML_ENABLED=true
ELITE_ML_MIN_DATA_POINTS=500
ELITE_ML_CONFIDENCE_THRESHOLD_BULL=0.90
ELITE_ML_CONFIDENCE_THRESHOLD_BEAR=0.88
ELITE_ML_CONFIDENCE_THRESHOLD_SIDEWAYS_LOW=0.92
ELITE_ML_CONFIDENCE_THRESHOLD_SIDEWAYS_HIGH=0.91

# SL/TP ML System Configuration (Independent ML Predictors)
SL_TP_ML_ENABLED=true
SL_TP_ML_MIN_CONFIDENCE=60.0
SL_TP_ML_MIN_RISK_REWARD=1.5

# Hybrid Deep Learning System Configuration
HYBRID_DEEP_ML_ENABLED=true
HYBRID_DEEP_ML_TRADITIONAL_WEIGHT=0.7
HYBRID_DEEP_ML_DEEP_WEIGHT=0.3
HYBRID_DEEP_ML_MIN_CONFIDENCE=70.0
ELITE_ML_TARGET_ACCURACY=0.96
ELITE_ML_RISK_REWARD_RATIO=2.0
ELITE_ML_AUTO_RETRAIN=true
ELITE_ML_RETRAIN_INTERVAL_HOURS=168