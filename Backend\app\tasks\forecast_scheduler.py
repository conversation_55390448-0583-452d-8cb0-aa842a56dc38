"""
Scheduled task for ML forecast generation and cache management.
This script should be run as a cron job every hour at minute :00.
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app import create_app
from app.services.market_data import ml_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('forecast_scheduler.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def refresh_ml_forecasts():
    """Refresh ML forecasts for all supported symbols."""
    logger.info("Starting ML forecast refresh...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # List of symbols to generate forecasts for
            symbols = ['BTCUSDT']  # Focus on BTC/USDT only
            timeframes = ['1h']  # Focus on 1h timeframe for now
            
            results = {
                'forecasts_generated': 0,
                'forecasts_failed': 0,
                'symbols_processed': [],
                'errors': []
            }
            
            for symbol in symbols:
                for timeframe in timeframes:
                    try:
                        logger.info(f"Generating forecast for {symbol} {timeframe}...")
                        
                        # Clear existing cache for this symbol/timeframe
                        cache_key = f"forecast_{symbol}_{timeframe}"
                        if hasattr(ml_service, 'cache') and cache_key in ml_service.cache:
                            del ml_service.cache[cache_key]
                            logger.info(f"Cleared cached forecast for {symbol} {timeframe}")
                        
                        # Generate new forecast
                        forecast = ml_service.generate_ensemble_forecast(
                            symbol=symbol,
                            timeframe=timeframe,
                            future_hours=72
                        )
                        
                        if forecast and 'error' not in forecast:
                            results['forecasts_generated'] += 1
                            results['symbols_processed'].append(f"{symbol}_{timeframe}")
                            logger.info(f"Successfully generated forecast for {symbol} {timeframe}")
                        else:
                            results['forecasts_failed'] += 1
                            error_msg = f"Failed to generate forecast for {symbol} {timeframe}"
                            results['errors'].append(error_msg)
                            logger.error(error_msg)
                            
                    except Exception as e:
                        results['forecasts_failed'] += 1
                        error_msg = f"Error generating forecast for {symbol} {timeframe}: {str(e)}"
                        results['errors'].append(error_msg)
                        logger.error(error_msg)
            
            logger.info(f"ML forecast refresh completed:")
            logger.info(f"  - Forecasts generated: {results['forecasts_generated']}")
            logger.info(f"  - Forecasts failed: {results['forecasts_failed']}")
            logger.info(f"  - Symbols processed: {results['symbols_processed']}")
            
            if results['errors']:
                logger.warning(f"  - Errors encountered: {len(results['errors'])}")
                for error in results['errors']:
                    logger.warning(f"    {error}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in ML forecast refresh: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None

def clear_old_forecasts():
    """Clear forecasts older than 2 hours."""
    logger.info("Clearing old forecasts...")
    
    try:
        if not hasattr(ml_service, 'cache'):
            logger.info("No forecast cache found")
            return 0
        
        cleared_count = 0
        current_time = datetime.utcnow()
        cache_keys_to_remove = []
        
        for cache_key, forecast_data in ml_service.cache.items():
            if isinstance(forecast_data, dict) and 'generated_at' in forecast_data:
                try:
                    # Parse the generated_at timestamp
                    generated_at = datetime.fromisoformat(forecast_data['generated_at'].replace('Z', '+00:00'))
                    generated_at = generated_at.replace(tzinfo=None)  # Remove timezone for comparison
                    
                    # Check if forecast is older than 2 hours
                    if current_time - generated_at > timedelta(hours=2):
                        cache_keys_to_remove.append(cache_key)
                        cleared_count += 1
                        
                except Exception as e:
                    logger.warning(f"Error parsing timestamp for {cache_key}: {e}")
                    # If we can't parse the timestamp, consider it old and remove it
                    cache_keys_to_remove.append(cache_key)
                    cleared_count += 1
        
        # Remove old forecasts
        for cache_key in cache_keys_to_remove:
            del ml_service.cache[cache_key]
            logger.info(f"Cleared old forecast: {cache_key}")
        
        logger.info(f"Cleared {cleared_count} old forecasts")
        return cleared_count
        
    except Exception as e:
        logger.error(f"Error clearing old forecasts: {str(e)}")
        return 0

def get_forecast_status():
    """Get status of current forecasts in cache."""
    logger.info("Getting forecast status...")
    
    try:
        if not hasattr(ml_service, 'cache'):
            logger.info("No forecast cache found")
            return {
                'total_forecasts': 0,
                'forecasts_by_symbol': {},
                'oldest_forecast': None,
                'newest_forecast': None
            }
        
        status = {
            'total_forecasts': len(ml_service.cache),
            'forecasts_by_symbol': {},
            'oldest_forecast': None,
            'newest_forecast': None
        }
        
        oldest_time = None
        newest_time = None
        
        for cache_key, forecast_data in ml_service.cache.items():
            if isinstance(forecast_data, dict):
                # Extract symbol from cache key
                symbol = cache_key.split('_')[1] if '_' in cache_key else 'unknown'
                
                if symbol not in status['forecasts_by_symbol']:
                    status['forecasts_by_symbol'][symbol] = 0
                status['forecasts_by_symbol'][symbol] += 1
                
                # Track oldest and newest forecasts
                if 'generated_at' in forecast_data:
                    try:
                        generated_at = datetime.fromisoformat(forecast_data['generated_at'].replace('Z', '+00:00'))
                        generated_at = generated_at.replace(tzinfo=None)
                        
                        if oldest_time is None or generated_at < oldest_time:
                            oldest_time = generated_at
                            status['oldest_forecast'] = {
                                'key': cache_key,
                                'generated_at': forecast_data['generated_at']
                            }
                        
                        if newest_time is None or generated_at > newest_time:
                            newest_time = generated_at
                            status['newest_forecast'] = {
                                'key': cache_key,
                                'generated_at': forecast_data['generated_at']
                            }
                            
                    except Exception as e:
                        logger.warning(f"Error parsing timestamp for {cache_key}: {e}")
        
        logger.info(f"Forecast status:")
        logger.info(f"  - Total forecasts: {status['total_forecasts']}")
        logger.info(f"  - Forecasts by symbol: {status['forecasts_by_symbol']}")
        if status['oldest_forecast']:
            logger.info(f"  - Oldest forecast: {status['oldest_forecast']['key']} ({status['oldest_forecast']['generated_at']})")
        if status['newest_forecast']:
            logger.info(f"  - Newest forecast: {status['newest_forecast']['key']} ({status['newest_forecast']['generated_at']})")
        
        return status
        
    except Exception as e:
        logger.error(f"Error getting forecast status: {str(e)}")
        return None

def run_forecast_maintenance():
    """Run all forecast maintenance tasks."""
    logger.info("Starting forecast maintenance tasks...")
    
    try:
        # Get current status
        status_before = get_forecast_status()
        
        # Clear old forecasts
        cleared_count = clear_old_forecasts()
        
        # Refresh forecasts
        refresh_results = refresh_ml_forecasts()
        
        # Get status after maintenance
        status_after = get_forecast_status()
        
        logger.info("Forecast maintenance completed successfully:")
        logger.info(f"  - Old forecasts cleared: {cleared_count}")
        if refresh_results:
            logger.info(f"  - New forecasts generated: {refresh_results['forecasts_generated']}")
            logger.info(f"  - Failed forecasts: {refresh_results['forecasts_failed']}")
        if status_before and status_after:
            logger.info(f"  - Forecasts before: {status_before['total_forecasts']}")
            logger.info(f"  - Forecasts after: {status_after['total_forecasts']}")
        
        return {
            'cleared_count': cleared_count,
            'refresh_results': refresh_results,
            'status_before': status_before,
            'status_after': status_after
        }
        
    except Exception as e:
        logger.error(f"Error in forecast maintenance: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='DeepTrade ML Forecast Scheduler')
    parser.add_argument('--task', choices=['refresh', 'clear', 'status', 'maintenance'],
                       default='maintenance', help='Task to run')
    
    args = parser.parse_args()
    
    if args.task == 'refresh':
        refresh_ml_forecasts()
    elif args.task == 'clear':
        clear_old_forecasts()
    elif args.task == 'status':
        get_forecast_status()
    elif args.task == 'maintenance':
        run_forecast_maintenance()
    
    logger.info("Forecast scheduler completed.")
