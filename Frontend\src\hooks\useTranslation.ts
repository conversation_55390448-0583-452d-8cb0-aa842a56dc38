/**
 * Enhanced Translation Hook for DeepTrade
 * 
 * Provides additional utilities on top of react-i18next for better DX.
 * Includes formatting helpers and type-safe translation keys.
 */

import { useTranslation as useI18nTranslation } from 'react-i18next';
import { getCurrentLanguage, isRTL } from '../i18n';

interface TranslationHelpers {
  t: any;
  i18n: any;
  ready: boolean;
  currentLanguage: string;
  isRTL: boolean;
  formatCurrency: (amount: number, currency?: string) => string;
  formatNumber: (value: number) => string;
  formatPercentage: (value: number) => string;
  formatDate: (date: Date | string | number) => string;
  formatDateTime: (date: Date | string | number) => string;
  formatRelativeTime: (date: Date | string | number) => string;
}

export const useTranslation = (namespace?: string): TranslationHelpers => {
  const { t, i18n, ready } = useI18nTranslation(namespace);
  const currentLanguage = getCurrentLanguage();
  const isRightToLeft = isRTL(currentLanguage);

  // Currency formatting
  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    try {
      return new Intl.NumberFormat(currentLanguage, {
        style: 'currency',
        currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 6,
      }).format(amount);
    } catch (error) {
      // Fallback to USD if currency is not supported
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 6,
      }).format(amount);
    }
  };

  // Number formatting
  const formatNumber = (value: number): string => {
    return new Intl.NumberFormat(currentLanguage, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Percentage formatting
  const formatPercentage = (value: number): string => {
    return new Intl.NumberFormat(currentLanguage, {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    }).format(value / 100);
  };

  // Date formatting
  const formatDate = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    return new Intl.DateTimeFormat(currentLanguage, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(dateObj);
  };

  // DateTime formatting
  const formatDateTime = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    return new Intl.DateTimeFormat(currentLanguage, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(dateObj);
  };

  // Relative time formatting
  const formatRelativeTime = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    // Use Intl.RelativeTimeFormat if available
    if (typeof Intl !== 'undefined' && Intl.RelativeTimeFormat) {
      const rtf = new Intl.RelativeTimeFormat(currentLanguage, { numeric: 'auto' });

      if (diffInSeconds < 60) {
        return rtf.format(-diffInSeconds, 'second');
      } else if (diffInSeconds < 3600) {
        return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
      } else if (diffInSeconds < 86400) {
        return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
      } else if (diffInSeconds < 2592000) {
        return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
      } else if (diffInSeconds < 31536000) {
        return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
      } else {
        return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
      }
    }

    // Fallback for browsers without Intl.RelativeTimeFormat
    if (diffInSeconds < 60) {
      return t('common.justNow', 'Just now');
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return t('common.minutesAgo', '{{count}} minutes ago', { count: minutes });
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return t('common.hoursAgo', '{{count}} hours ago', { count: hours });
    } else {
      return formatDate(dateObj);
    }
  };

  return {
    t,
    i18n,
    ready,
    currentLanguage,
    isRTL: isRightToLeft,
    formatCurrency,
    formatNumber,
    formatPercentage,
    formatDate,
    formatDateTime,
    formatRelativeTime,
  };
};

// Type-safe translation key helpers
export const createTranslationKey = (namespace: string, key: string): string => {
  return `${namespace}.${key}`;
};

// Common translation keys for type safety
export const TranslationKeys = {
  // Navigation
  NAV_DASHBOARD: 'navigation.dashboard',
  NAV_TRADING: 'navigation.trading',
  NAV_SETTINGS: 'navigation.settings',
  NAV_HELP: 'navigation.help',
  
  // Common actions
  COMMON_LOADING: 'common.loading',
  COMMON_ERROR: 'common.error',
  COMMON_SUCCESS: 'common.success',
  COMMON_SAVE: 'common.save',
  COMMON_CANCEL: 'common.cancel',
  COMMON_CONFIRM: 'common.confirm',
  
  // Auth
  AUTH_LOGIN: 'auth.login.title',
  AUTH_REGISTER: 'auth.register.title',
  AUTH_EMAIL: 'auth.login.email',
  AUTH_PASSWORD: 'auth.login.password',
  
  // Dashboard
  DASHBOARD_TITLE: 'dashboard.title',
  DASHBOARD_WELCOME: 'dashboard.welcome',
  DASHBOARD_BALANCE: 'dashboard.balance',
  DASHBOARD_PNL: 'dashboard.pnlToday',
  
  // Trading
  TRADING_SIGNALS: 'trading.signals',
  TRADING_POSITIONS: 'trading.positions',
  TRADING_HISTORY: 'trading.history',
  TRADING_AUTO_TRADING: 'trading.autoTrading',
  
  // Wallet
  WALLET_CONNECT: 'wallet.connect',
  WALLET_DISCONNECT: 'wallet.disconnect',
  WALLET_CONNECTED: 'wallet.connected',
  WALLET_BALANCE: 'wallet.balance',
} as const;

export default useTranslation;
