"""
Email service for sending payday notifications and other user communications.
"""
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending email notifications."""
    
    def __init__(self):
        # Use Brevo SMTP configuration
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp-relay.brevo.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
        self.smtp_username = os.getenv('SMTP_USERNAME', '')
        self.smtp_password = os.getenv('SMTP_PASSWORD', '')
        # Use FROM_EMAIL2 for alerts
        self.from_email = os.getenv('FROM_EMAIL2', os.getenv('FROM_EMAIL', '<EMAIL>'))
        self.from_name = os.getenv('FROM_NAME', 'DeepTrade Alerts')
    
    def send_email(self, to_email, subject, html_content, text_content=None):
        """Send an email."""
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = to_email
            
            # Add text version if provided
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)
            
            # Add HTML version
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {to_email}: {subject}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return False

    def send_password_reset_email(self, to_email, full_name, reset_token):
        """Send password reset email."""
        frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:5173')
        reset_url = f"{frontend_url}/reset-password?token={reset_token}"

        subject = "DeepTrade - Password Reset Request"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Reset - DeepTrade</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 28px;">DeepTrade</h1>
                <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Password Reset Request</p>
            </div>

            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Hello {full_name},</h2>

                <p>We received a request to reset your password for your DeepTrade account. If you made this request, click the button below to reset your password:</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="{reset_url}" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Reset My Password</a>
                </div>

                <p>Or copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: monospace; font-size: 14px;">{reset_url}</p>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-top: 0;">Security Information:</h3>
                    <ul style="color: #856404; margin: 0; padding-left: 20px;">
                        <li>This link will expire in 1 hour</li>
                        <li>If you didn't request this reset, please ignore this email</li>
                        <li>Your password will not be changed unless you click the link above</li>
                    </ul>
                </div>

                <p>If you didn't request a password reset, please ignore this email or contact our support team if you have concerns about your account security.</p>

                <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                <p style="font-size: 14px; color: #666;">
                    Best regards,<br>
                    The DeepTrade Security Team<br>
                    <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
                </p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        DeepTrade - Password Reset Request

        Hello {full_name},

        We received a request to reset your password for your DeepTrade account.

        To reset your password, please visit: {reset_url}

        This link will expire in 1 hour.

        If you didn't request this reset, please ignore this email.

        Best regards,
        The DeepTrade Security Team
        """

        return self.send_email(to_email, subject, html_content, text_content)

    def send_password_changed_notification(self, to_email, full_name):
        """Send notification when password is successfully changed."""
        subject = "DeepTrade - Password Changed Successfully"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Changed - DeepTrade</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 28px;">DeepTrade</h1>
                <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Password Changed Successfully</p>
            </div>

            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Hello {full_name},</h2>

                <p>Your DeepTrade account password has been successfully changed.</p>

                <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #155724; margin-top: 0;">✓ Password Updated</h3>
                    <p style="color: #155724; margin: 0;">Your password was changed on {datetime.now().strftime('%B %d, %Y at %I:%M %p UTC')}</p>
                </div>

                <p>If you did not make this change, please contact our support team immediately at <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a></p>

                <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                <p style="font-size: 14px; color: #666;">
                    Best regards,<br>
                    The DeepTrade Security Team<br>
                    <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
                </p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        DeepTrade - Password Changed Successfully

        Hello {full_name},

        Your DeepTrade account password has been successfully changed on {datetime.now().strftime('%B %d, %Y at %I:%M %p UTC')}.

        If you did not make this change, please contact our support team <NAME_EMAIL>

        Best regards,
        The DeepTrade Security Team
        """

        return self.send_email(to_email, subject, html_content, text_content)

    def send_2fa_reset_request_confirmation(self, to_email, full_name, request_id):
        """Send confirmation email when 2FA reset request is submitted."""
        subject = "DeepTrade - 2FA Reset Request Received"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>2FA Reset Request - DeepTrade</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 28px;">DeepTrade</h1>
                <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">2FA Reset Request Received</p>
            </div>

            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Hello {full_name},</h2>

                <p>We have received your request to reset Two-Factor Authentication (2FA) for your DeepTrade account.</p>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-top: 0;">Request Details:</h3>
                    <ul style="color: #856404; margin: 0; padding-left: 20px;">
                        <li><strong>Request ID:</strong> {request_id}</li>
                        <li><strong>Submitted:</strong> {datetime.now().strftime('%B %d, %Y at %I:%M %p UTC')}</li>
                        <li><strong>Status:</strong> Pending Review</li>
                    </ul>
                </div>

                <h3>What happens next?</h3>
                <ol>
                    <li>Our security team will review your request and the provided verification information</li>
                    <li>We may contact you for additional verification if needed</li>
                    <li>You will receive an email notification once your request has been processed</li>
                    <li>If approved, your 2FA will be disabled and you can set it up again</li>
                </ol>

                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #721c24; margin-top: 0;">⚠️ Important Security Notice</h3>
                    <p style="color: #721c24; margin: 0;">If you did not submit this request, please contact our support team immediately. This could indicate unauthorized access to your account.</p>
                </div>

                <p>For security reasons, please do not reply to this email. If you have questions, contact our support team at <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a></p>

                <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                <p style="font-size: 14px; color: #666;">
                    Best regards,<br>
                    The DeepTrade Security Team<br>
                    <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
                </p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        DeepTrade - 2FA Reset Request Received

        Hello {full_name},

        We have received your request to reset Two-Factor Authentication (2FA) for your DeepTrade account.

        Request Details:
        - Request ID: {request_id}
        - Submitted: {datetime.now().strftime('%B %d, %Y at %I:%M %p UTC')}
        - Status: Pending Review

        What happens next?
        1. Our security team will review your request and the provided verification information
        2. We may contact you for additional verification if needed
        3. You will receive an email notification once your request has been processed
        4. If approved, your 2FA will be disabled and you can set it up again

        If you did not submit this request, please contact our support team <NAME_EMAIL>

        Best regards,
        The DeepTrade Security Team
        """

        return self.send_email(to_email, subject, html_content, text_content)

    def send_2fa_reset_admin_notification(self, reset_request):
        """Send notification to admins about new 2FA reset request."""
        admin_emails = ['<EMAIL>']  # Configure admin emails
        subject = f"🔐 New 2FA Reset Request - {reset_request.risk_level.upper()} Risk"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New 2FA Reset Request - DeepTrade Admin</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 28px;">DeepTrade Admin</h1>
                <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">New 2FA Reset Request</p>
            </div>

            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">New 2FA Reset Request Submitted</h2>

                <div style="background: #fff; border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">Request Details:</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li><strong>Request ID:</strong> {reset_request.id}</li>
                        <li><strong>User Email:</strong> {reset_request.email_provided}</li>
                        <li><strong>User Name:</strong> {reset_request.full_name_provided}</li>
                        <li><strong>Risk Level:</strong> <span style="color: {'#dc3545' if reset_request.risk_level == 'high' else '#ffc107' if reset_request.risk_level == 'medium' else '#28a745'}; font-weight: bold;">{reset_request.risk_level.upper()}</span></li>
                        <li><strong>Submitted:</strong> {reset_request.created_at.strftime('%B %d, %Y at %I:%M %p UTC')}</li>
                        <li><strong>IP Address:</strong> {reset_request.ip_address or 'Unknown'}</li>
                    </ul>
                </div>

                <div style="background: #fff; border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">Reason for Reset:</h3>
                    <p style="margin: 0; font-style: italic;">"{reset_request.reason}"</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <p style="margin: 10px 0;">Please review this request in the admin dashboard:</p>
                    <a href="http://localhost:5000" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Review in Admin Dashboard</a>
                </div>

                <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                <p style="font-size: 14px; color: #666;">
                    DeepTrade Admin System<br>
                    Automated Security Notification
                </p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        DeepTrade Admin - New 2FA Reset Request

        New 2FA Reset Request Submitted

        Request Details:
        - Request ID: {reset_request.id}
        - User Email: {reset_request.email_provided}
        - User Name: {reset_request.full_name_provided}
        - Risk Level: {reset_request.risk_level.upper()}
        - Submitted: {reset_request.created_at.strftime('%B %d, %Y at %I:%M %p UTC')}
        - IP Address: {reset_request.ip_address or 'Unknown'}

        Reason for Reset:
        "{reset_request.reason}"

        Please review this request in the admin dashboard: http://localhost:5000

        DeepTrade Admin System
        Automated Security Notification
        """

        # Send to all admin emails
        for admin_email in admin_emails:
            self.send_email(admin_email, subject, html_content, text_content)

        return True

    def send_2fa_reset_approved_notification(self, to_email, full_name):
        """Send notification when 2FA reset request is approved."""
        subject = "DeepTrade - 2FA Reset Request Approved"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>2FA Reset Approved - DeepTrade</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 28px;">DeepTrade</h1>
                <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">2FA Reset Request Approved</p>
            </div>

            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Hello {full_name},</h2>

                <p>Your request to reset Two-Factor Authentication (2FA) has been approved by our security team.</p>

                <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #155724; margin-top: 0;">✓ 2FA Reset Completed</h3>
                    <p style="color: #155724; margin: 0;">Your Two-Factor Authentication has been disabled. You can now log in with just your email and password.</p>
                </div>

                <h3>Next Steps:</h3>
                <ol>
                    <li>Log in to your DeepTrade account using your email and password</li>
                    <li>Go to your Security Settings</li>
                    <li>Set up Two-Factor Authentication again for enhanced security</li>
                </ol>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-top: 0;">🔒 Security Recommendation</h3>
                    <p style="color: #856404; margin: 0;">We strongly recommend setting up 2FA again as soon as possible to keep your account secure. Your account is more vulnerable without 2FA protection.</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="http://localhost:5173/login" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Log In to Your Account</a>
                </div>

                <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                <p style="font-size: 14px; color: #666;">
                    Best regards,<br>
                    The DeepTrade Security Team<br>
                    <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
                </p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        DeepTrade - 2FA Reset Request Approved

        Hello {full_name},

        Your request to reset Two-Factor Authentication (2FA) has been approved by our security team.

        ✓ 2FA Reset Completed
        Your Two-Factor Authentication has been disabled. You can now log in with just your email and password.

        Next Steps:
        1. Log in to your DeepTrade account using your email and password
        2. Go to your Security Settings
        3. Set up Two-Factor Authentication again for enhanced security

        We strongly recommend setting up 2FA again as soon as possible to keep your account secure.

        Log in at: http://localhost:5173/login

        Best regards,
        The DeepTrade Security Team
        """

        return self.send_email(to_email, subject, html_content, text_content)

    def send_2fa_reset_rejected_notification(self, to_email, full_name, admin_notes):
        """Send notification when 2FA reset request is rejected."""
        subject = "DeepTrade - 2FA Reset Request Rejected"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>2FA Reset Rejected - DeepTrade</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 28px;">DeepTrade</h1>
                <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">2FA Reset Request Rejected</p>
            </div>

            <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Hello {full_name},</h2>

                <p>After careful review, we were unable to approve your request to reset Two-Factor Authentication (2FA) for your DeepTrade account.</p>

                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #721c24; margin-top: 0;">❌ Request Rejected</h3>
                    <p style="color: #721c24; margin: 0;"><strong>Reason:</strong> {admin_notes}</p>
                </div>

                <h3>What you can do:</h3>
                <ul>
                    <li>If you still have access to your 2FA device, try using backup codes</li>
                    <li>Contact our support team with additional verification information</li>
                    <li>Submit a new request with more detailed information</li>
                </ul>

                <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="color: #0c5460; margin-top: 0;">💡 Need Help?</h3>
                    <p style="color: #0c5460; margin: 0;">Our support team is here to help. Please contact us at <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a> with any questions or if you need assistance with account recovery.</p>
                </div>

                <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                <p style="font-size: 14px; color: #666;">
                    Best regards,<br>
                    The DeepTrade Security Team<br>
                    <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
                </p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        DeepTrade - 2FA Reset Request Rejected

        Hello {full_name},

        After careful review, we were unable to approve your request to reset Two-Factor Authentication (2FA) for your DeepTrade account.

        ❌ Request Rejected
        Reason: {admin_notes}

        What you can do:
        - If you still have access to your 2FA device, try using backup codes
        - Contact our support team with additional verification information
        - Submit a new request with more detailed information

        Our support team is here to help. Please contact <NAME_EMAIL> with any questions.

        Best regards,
        The DeepTrade Security Team
        """

        return self.send_email(to_email, subject, html_content, text_content)
    
    def send_payday_warning_email(self, user, tier_status, days_before):
        """Send payday warning email."""
        try:
            subject = f"⚠️ DeepTrade Payment Reminder - {days_before} Day{'s' if days_before > 1 else ''} Left"
            
            # Format deadline
            deadline_str = tier_status.next_payday_deadline.strftime('%A, %B %d, %Y at %H:%M UTC')
            
            # Create HTML content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Payment Reminder</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .amount {{ font-size: 24px; font-weight: bold; color: #e17055; }}
                    .deadline {{ font-size: 18px; font-weight: bold; color: #d63031; }}
                    .button {{ display: inline-block; background: #0984e3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🚨 Payment Reminder - DeepTrade</h1>
                        <p>Hello {user.username or user.email},</p>
                    </div>
                    
                    <div class="warning">
                        <h2>⚠️ Payment Due in {days_before} Day{'s' if days_before > 1 else ''}</h2>
                        <p>You have an outstanding profit share payment that needs to be settled:</p>
                        
                        <p><strong>Amount Due:</strong> <span class="amount">${tier_status.profit_share_owed:.6f} USDT</span></p>
                        <p><strong>Payment Deadline:</strong> <span class="deadline">{deadline_str}</span></p>
                    </div>
                    
                    <h3>🚫 Important: Account Suspension Warning</h3>
                    <p>If payment is not received by the deadline, your account will be <strong>automatically disabled</strong> and you will lose access to:</p>
                    <ul>
                        <li>Trading functionality</li>
                        <li>Profit tracking</li>
                        <li>All platform features</li>
                    </ul>
                    
                    <h3>💳 How to Pay</h3>
                    <p>To avoid account suspension, please make your payment immediately:</p>
                    <ol>
                        <li>Log in to your DeepTrade account</li>
                        <li>Go to the Tier Management page</li>
                        <li>Click "Pay with USDT" in the Profit Share section</li>
                        <li>Complete the Solana wallet payment</li>
                    </ol>
                    
                    <a href="https://deeptrade.com/tier" class="button">Pay Now</a>
                    
                    <h3>📅 Payment Schedule</h3>
                    <p>Remember: All profit share payments are due every <strong>Saturday at 0:00 GMT</strong>. Accounts with unpaid fees are automatically disabled after this deadline.</p>
                    
                    <div class="footer">
                        <p>This is an automated notification from DeepTrade. Please do not reply to this email.</p>
                        <p>If you have questions, please contact our support team.</p>
                        <p>© {datetime.now().year} DeepTrade. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Create text version
            text_content = f"""
            PAYMENT REMINDER - DeepTrade
            
            Hello {user.username or user.email},
            
            You have an outstanding profit share payment due in {days_before} day{'s' if days_before > 1 else ''}:
            
            Amount Due: ${tier_status.profit_share_owed:.6f} USDT
            Payment Deadline: {deadline_str}
            
            WARNING: If payment is not received by the deadline, your account will be automatically disabled.
            
            To pay:
            1. Log in to DeepTrade
            2. Go to Tier Management
            3. Click "Pay with USDT"
            4. Complete Solana payment
            
            Payment URL: https://deeptrade.com/tier
            
            This is an automated notification. Please do not reply.
            """
            
            return self.send_email(user.email, subject, html_content, text_content)
            
        except Exception as e:
            logger.error(f"Error sending payday warning email to {user.email}: {str(e)}")
            return False
    
    def send_account_disabled_email(self, user, tier_status):
        """Send account disabled notification email."""
        try:
            subject = "🚫 DeepTrade Account Disabled - Payment Required"
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Account Disabled</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: #ffe6e6; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 2px solid #ff6b6b; }}
                    .amount {{ font-size: 24px; font-weight: bold; color: #e17055; }}
                    .button {{ display: inline-block; background: #d63031; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🚫 Account Disabled - DeepTrade</h1>
                        <p>Hello {user.username or user.email},</p>
                    </div>
                    
                    <h2>Your account has been disabled due to unpaid fees</h2>
                    <p><strong>Outstanding Amount:</strong> <span class="amount">${tier_status.profit_share_owed:.6f} USDT</span></p>
                    
                    <p>Your account was disabled because payment was not received by the Saturday 0:00 GMT deadline.</p>
                    
                    <h3>🔄 How to Reactivate Your Account</h3>
                    <p>Your account will be automatically reactivated after successful payment:</p>
                    <ol>
                        <li>Log in to DeepTrade (limited access)</li>
                        <li>Go to Tier Management</li>
                        <li>Click "Pay to Reactivate"</li>
                        <li>Complete the payment</li>
                    </ol>
                    
                    <a href="https://deeptrade.com/tier" class="button">Pay to Reactivate</a>
                    
                    <div class="footer">
                        <p>This is an automated notification from DeepTrade.</p>
                        <p>© {datetime.now().year} DeepTrade. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            text_content = f"""
            ACCOUNT DISABLED - DeepTrade
            
            Hello {user.username or user.email},
            
            Your account has been disabled due to unpaid profit share fees.
            
            Outstanding Amount: ${tier_status.profit_share_owed:.6f} USDT
            
            To reactivate your account:
            1. Log in to DeepTrade
            2. Go to Tier Management  
            3. Click "Pay to Reactivate"
            4. Complete the payment
            
            Reactivation URL: https://deeptrade.com/tier
            
            This is an automated notification.
            """
            
            return self.send_email(user.email, subject, html_content, text_content)
            
        except Exception as e:
            logger.error(f"Error sending account disabled email to {user.email}: {str(e)}")
            return False

    def send_email_change_verification(self, current_email, new_email, token):
        """Send email change verification email."""
        try:
            subject = "🔐 DeepTrade - Verify Your New Email Address"

            # Create verification URL
            frontend_url = os.getenv('FRONTEND_URL', 'http://localhost:5173')
            verification_url = f"{frontend_url}/verify-email-change?token={token}"

            # Create HTML content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Email Change Verification</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .info {{ background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .button {{ display: inline-block; background: #0984e3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                    .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔐 Email Change Verification</h1>
                        <p>Hello,</p>
                    </div>

                    <div class="info">
                        <h2>📧 Email Change Request</h2>
                        <p>You have requested to change your DeepTrade account email address:</p>
                        <p><strong>From:</strong> {current_email}</p>
                        <p><strong>To:</strong> {new_email}</p>
                    </div>

                    <p>To complete this email change, please click the verification button below:</p>

                    <a href="{verification_url}" class="button">Verify New Email Address</a>

                    <div class="warning">
                        <h3>⚠️ Security Notice</h3>
                        <ul>
                            <li>This verification link will expire in 24 hours</li>
                            <li>If you didn't request this change, please ignore this email</li>
                            <li>Your current email address will remain active until verification is complete</li>
                        </ul>
                    </div>

                    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #666;">{verification_url}</p>

                    <div class="footer">
                        <p>This is an automated notification from DeepTrade.</p>
                        <p>© {datetime.now().year} DeepTrade. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            text_content = f"""
            EMAIL CHANGE VERIFICATION - DeepTrade

            Hello,

            You have requested to change your DeepTrade account email address:
            From: {current_email}
            To: {new_email}

            To complete this email change, please visit:
            {verification_url}

            Security Notice:
            - This verification link will expire in 24 hours
            - If you didn't request this change, please ignore this email
            - Your current email address will remain active until verification is complete

            This is an automated notification.
            """

            return self.send_email(new_email, subject, html_content, text_content)

        except Exception as e:
            logger.error(f"Error sending email change verification to {new_email}: {str(e)}")
            return False

    def send_2fa_code_email(self, email, code):
        """Send 2FA verification code via email."""
        try:
            subject = "🔐 DeepTrade - Your 2FA Verification Code"

            # Create HTML content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>2FA Verification Code</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .code-box {{ background: #e3f2fd; border: 2px solid #2196f3; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }}
                    .code {{ font-size: 32px; font-weight: bold; color: #1976d2; letter-spacing: 8px; font-family: monospace; }}
                    .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔐 Two-Factor Authentication</h1>
                        <p>Hello,</p>
                    </div>

                    <p>You have requested to enable two-factor authentication for your DeepTrade account.</p>

                    <div class="code-box">
                        <h2>Your Verification Code</h2>
                        <div class="code">{code}</div>
                    </div>

                    <div class="warning">
                        <h3>⚠️ Security Notice</h3>
                        <ul>
                            <li>This code will expire in 10 minutes</li>
                            <li>Do not share this code with anyone</li>
                            <li>If you didn't request this code, please ignore this email</li>
                        </ul>
                    </div>

                    <p>Enter this code in your DeepTrade account to complete the 2FA setup process.</p>

                    <div class="footer">
                        <p>This is an automated notification from DeepTrade.</p>
                        <p>© {datetime.now().year} DeepTrade. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            text_content = f"""
            TWO-FACTOR AUTHENTICATION - DeepTrade

            Hello,

            You have requested to enable two-factor authentication for your DeepTrade account.

            Your Verification Code: {code}

            Security Notice:
            - This code will expire in 10 minutes
            - Do not share this code with anyone
            - If you didn't request this code, please ignore this email

            Enter this code in your DeepTrade account to complete the 2FA setup process.

            This is an automated notification.
            """

            return self.send_email(email, subject, html_content, text_content)

        except Exception as e:
            logger.error(f"Error sending 2FA code email to {email}: {str(e)}")
            return False

    @staticmethod
    def send_admin_verification_email(email, code, action_type):
        """Send admin verification code via email."""
        try:
            service = EmailService()
            subject = f"🔐 DeepTrade Admin - Verification Code for {action_type.title()}"

            # Create HTML content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Admin Verification Code</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: #1f2937; padding: 20px; border-radius: 8px; margin-bottom: 20px; color: white; }}
                    .code-box {{ background: #dbeafe; border: 2px solid #3b82f6; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }}
                    .code {{ font-size: 32px; font-weight: bold; color: #1d4ed8; letter-spacing: 8px; font-family: monospace; }}
                    .warning {{ background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                    .admin-badge {{ background: #dc2626; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔐 Admin Verification Required</h1>
                        <span class="admin-badge">ADMIN PANEL</span>
                        <p>Hello Admin,</p>
                    </div>

                    <p>You have requested to perform the following admin action: <strong>{action_type}</strong></p>

                    <div class="code-box">
                        <h2>Your Verification Code</h2>
                        <div class="code">{code}</div>
                    </div>

                    <div class="warning">
                        <h3>⚠️ Security Notice</h3>
                        <ul>
                            <li>This code will expire in 10 minutes</li>
                            <li>Do not share this code with anyone</li>
                            <li>If you didn't request this action, please secure your admin account immediately</li>
                            <li>This email was sent to verify admin-level changes</li>
                        </ul>
                    </div>

                    <p>Enter this code in the DeepTrade Admin Panel to complete the {action_type} process.</p>

                    <div class="footer">
                        <p>This is an automated security notification from DeepTrade Admin System.</p>
                        <p>© {datetime.now().year} DeepTrade. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            text_content = f"""
            ADMIN VERIFICATION CODE - DeepTrade

            Hello Admin,

            You have requested to perform the following admin action: {action_type}

            Your Verification Code: {code}

            Security Notice:
            - This code will expire in 10 minutes
            - Do not share this code with anyone
            - If you didn't request this action, please secure your admin account immediately
            - This email was sent to verify admin-level changes

            Enter this code in the DeepTrade Admin Panel to complete the {action_type} process.

            This is an automated security notification.
            """

            return service.send_email(email, subject, html_content, text_content)

        except Exception as e:
            logger.error(f"Error sending admin verification email to {email}: {str(e)}")
            return False

    def send_account_deletion_confirmation(self, email, full_name):
        """Send account deletion confirmation email."""
        try:
            subject = "🗑️ DeepTrade - Account Deletion Confirmation"

            # Create HTML content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Account Deletion Confirmation</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .warning {{ background: #ffebee; border: 2px solid #f44336; padding: 20px; border-radius: 8px; margin: 20px 0; }}
                    .info {{ background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🗑️ Account Deletion Confirmation</h1>
                        <p>Hello {full_name},</p>
                    </div>

                    <div class="warning">
                        <h2>⚠️ Your Account Has Been Deleted</h2>
                        <p>This email confirms that your DeepTrade account has been successfully deleted.</p>
                        <p><strong>Email:</strong> {email}</p>
                        <p><strong>Deletion Date:</strong> {datetime.now().strftime('%B %d, %Y at %H:%M UTC')}</p>
                    </div>

                    <div class="info">
                        <h3>📋 What This Means</h3>
                        <ul>
                            <li>Your account has been deactivated and you can no longer log in</li>
                            <li>All trading activities have been suspended</li>
                            <li>Your data is retained for legal and compliance purposes</li>
                            <li>This action cannot be undone</li>
                        </ul>
                    </div>

                    <h3>🔒 Data Retention</h3>
                    <p>While your account is deactivated, we retain your data for legal, regulatory, and compliance purposes. This includes:</p>
                    <ul>
                        <li>Trading history and transaction records</li>
                        <li>Account information and verification documents</li>
                        <li>Communication logs and support tickets</li>
                    </ul>

                    <p>If you have any questions about data retention or need assistance, please contact our support team.</p>

                    <div class="footer">
                        <p>This is an automated notification from DeepTrade.</p>
                        <p>© {datetime.now().year} DeepTrade. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            text_content = f"""
            ACCOUNT DELETION CONFIRMATION - DeepTrade

            Hello {full_name},

            This email confirms that your DeepTrade account has been successfully deleted.

            Account Details:
            Email: {email}
            Deletion Date: {datetime.now().strftime('%B %d, %Y at %H:%M UTC')}

            What This Means:
            - Your account has been deactivated and you can no longer log in
            - All trading activities have been suspended
            - Your data is retained for legal and compliance purposes
            - This action cannot be undone

            Data Retention:
            While your account is deactivated, we retain your data for legal, regulatory, and compliance purposes, including trading history, account information, and communication logs.

            If you have any questions, please contact our support team.

            This is an automated notification.
            """

            return self.send_email(email, subject, html_content, text_content)

        except Exception as e:
            logger.error(f"Error sending account deletion confirmation to {email}: {str(e)}")
            return False
