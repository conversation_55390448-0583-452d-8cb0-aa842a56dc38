# DeepTrade Environment Configuration Example

# Database Configuration
DATABASE_URL=sqlite:///deeptrade.db

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-here

# Solana Configuration
SOLANA_NETWORK=mainnet
SOLANA_TREASURY_WALLET=9tveNp6FvLn857ZSMNhAJhzG3vDmkDXABgFDdX7iQiPD

# Tier Payment Configuration
TIER_2_PAYMENT_AMOUNT=199
TIER_2_PAYMENT_TOKEN=USDT
TIER_2_PAYMENT_TOKEN_MINT=Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB

# Trading Configuration
DEFAULT_TRADING_SYMBOL=BTCUSDT
DEFAULT_BASE_ASSET=BTC
DEFAULT_QUOTE_ASSET=USDT
SUPPORTED_QUOTE_ASSETS=USDT,USDC,USD1,BUSD,USD

# Paper Trading Configuration
PAPER_TRADING_ENABLED=true
PAPER_TRADING_INITIAL_BALANCE=10000
PAPER_TRADING_MAX_RESET_PER_DAY=3

# Email Configuration for Payday Notifications (Brevo SMTP)
SMTP_SERVER=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-brevo-smtp-key
FROM_EMAIL=<EMAIL>
FROM_EMAIL2=<EMAIL>
FROM_NAME=DeepTrade Alerts

# Application Configuration
FLASK_ENV=production
DEBUG=False

# Security Configuration
SECRET_KEY=your-flask-secret-key-here

# Rate Limiting Configuration
RATE_LIMIT_MAX_ATTEMPTS=10
RATE_LIMIT_BLOCK_DURATION_MINUTES=10
RATE_LIMIT_RESET_HOURS=2
RATE_LIMIT_AUTO_BAN_THRESHOLD=20
RATE_LIMIT_AUTO_BAN_DURATION_HOURS=12

# Logging Configuration
LOG_LEVEL=INFO
