from flask import Blueprint, jsonify, request, current_app
from flask_cors import cross_origin
from app.services.market_data import ml_service
from app.services.forecast_service import get_forecast_service_status, force_forecast_update, restart_forecast_service
from functools import wraps
import logging
import os

logger = logging.getLogger(__name__)

forecast_bp = Blueprint('forecast', __name__)

def log_request(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        logger.info(f"Request: {request.method} {request.url}")
        return f(*args, **kwargs)
    return decorated_function

@forecast_bp.route('/<symbol>', methods=['GET', 'OPTIONS'])
@cross_origin(origins=[
    'https://deeptrade.capitolchilax.com',
    'https://deep-trade-frontend.vercel.app',
    'http://localhost:5173',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173'
],
supports_credentials=True,
methods=['GET', 'OPTIONS'],
allow_headers=['Content-Type', 'Authorization', 'Accept'],
expose_headers=['Content-Type'])
@log_request
def get_forecast(symbol):
    if request.method == 'OPTIONS':
        return jsonify({'status': 'ok'}), 200
    try:
        # Get query parameters
        timeframe = request.args.get('timeframe', '1h')
        theme = request.args.get('theme', 'light')  # Default to light theme
        try:
            future_hours = int(request.args.get('future_hours', '72'))
        except ValueError:
            return jsonify({"error": "Invalid future_hours parameter. Must be an integer."}), 400
        
        # Validate timeframe
        if timeframe not in ['1h', '4h', '1d']:
            return jsonify({"error": "Invalid timeframe. Must be one of: 1h, 4h, 1d"}), 400
        
        # Validate theme
        if theme not in ['light', 'dark']:
            theme = 'light'  # Default to light if invalid theme provided
            
        # Validate hours
        if not 1 <= future_hours <= 168:  # Limit to 1 week of forecast
            return jsonify({"error": "future_hours must be between 1 and 168 (1 week)"}), 400
        
        logger.info(f"Generating forecast for {symbol} - {timeframe} - {future_hours}h - Theme: {theme}")
        
        try:
            # Get forecast from ML service
            forecast = ml_service.generate_ensemble_forecast(
                symbol=symbol.upper(),  # Ensure symbol is uppercase for Binance
                timeframe=timeframe,
                future_hours=future_hours
            )
            
            if not forecast:
                logger.error(f"Empty forecast returned for {symbol}")
                return jsonify({"error": "Failed to generate forecast"}), 500
                
            if 'error' in forecast:
                logger.error(f"Forecast error for {symbol}: {forecast['error']}")
                return jsonify({"error": forecast['error']}), 500
                
            # Return the complete forecast including the chart HTML
            return jsonify({
                'status': 'success',
                'symbol': forecast['symbol'],
                'timeframe': forecast['timeframe'],
                'current_price': forecast['current_price'],
                'forecast': forecast['forecast'],
                'forecast_dates': forecast['forecast_dates'],
                'chart_html': forecast['chart_html'],
                'support_level': forecast['support_level'],
                'resistance_level': forecast['resistance_level'],
                'generated_at': forecast['generated_at']
            })
            
        except Exception as e:
            logger.exception(f"Error in generate_ensemble_forecast for {symbol}:")
            return jsonify({"error": f"Error generating forecast: {str(e)}"}), 500
        
    except Exception as e:
        logger.exception(f"Unexpected error in get_forecast: {str(e)}")
        return jsonify({"error": "An unexpected error occurred"}), 500

# Health check endpoint
@forecast_bp.route('/health', methods=['GET', 'OPTIONS'])
@cross_origin(origins=[
    'https://deeptrade.capitolchilax.com',
    'https://deep-trade-frontend.vercel.app',
    'http://localhost:5173',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173'
],
supports_credentials=True,
methods=['GET', 'OPTIONS'],
allow_headers=['Content-Type', 'Authorization', 'Accept'],
expose_headers=['Content-Type'])
@log_request
def health_check():
    if request.method == 'OPTIONS':
        return jsonify({'status': 'ok'}), 200
    return jsonify({"status": "ok", "message": "Forecast service is running", "service": "forecast"})

@forecast_bp.route('/service/status', methods=['GET', 'OPTIONS'])
@cross_origin(origins=[
    'https://deeptrade.capitolchilax.com',
    'https://deep-trade-frontend.vercel.app',
    'http://localhost:5173',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173'
],
supports_credentials=True,
methods=['GET', 'OPTIONS'],
allow_headers=['Content-Type', 'Authorization', 'Accept'],
expose_headers=['Content-Type'])
@log_request
def get_service_status():
    """Get the status of the ML forecast background service."""
    if request.method == 'OPTIONS':
        return jsonify({'status': 'ok'}), 200

    try:
        status = get_forecast_service_status()
        return jsonify({
            "status": "success",
            "service_status": status
        }), 200
    except Exception as e:
        logger.error(f"Error getting forecast service status: {str(e)}")
        return jsonify({"error": "Failed to get service status"}), 500

@forecast_bp.route('/service/force-update', methods=['POST', 'OPTIONS'])
@cross_origin(origins=[
    'https://deeptrade.capitolchilax.com',
    'https://deep-trade-frontend.vercel.app',
    'http://localhost:5173',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173'
],
supports_credentials=True,
methods=['POST', 'OPTIONS'],
allow_headers=['Content-Type', 'Authorization', 'Accept'],
expose_headers=['Content-Type'])
@log_request
def force_service_update():
    """Force an immediate forecast update."""
    if request.method == 'OPTIONS':
        return jsonify({'status': 'ok'}), 200

    try:
        logger.info("Forcing forecast update via API request")
        results = force_forecast_update()

        if results:
            return jsonify({
                "status": "success",
                "message": "Forecast update completed",
                "results": results
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "Forecast update failed"
            }), 500

    except Exception as e:
        logger.error(f"Error forcing forecast update: {str(e)}")
        return jsonify({"error": "Failed to force forecast update"}), 500


@forecast_bp.route('/service/detailed-status', methods=['GET', 'OPTIONS'])
@cross_origin(origins=[
    'https://deeptrade.capitolchilax.com',
    'https://deep-trade-frontend.vercel.app',
    'http://localhost:5173',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173'
],
supports_credentials=True,
methods=['GET', 'OPTIONS'],
allow_headers=['Content-Type', 'Authorization', 'Accept'],
expose_headers=['Content-Type'])
@log_request
def get_detailed_service_status():
    """Get the detailed current status of the forecast service."""
    if request.method == 'OPTIONS':
        return jsonify({'status': 'ok'}), 200

    try:
        status = get_forecast_service_status()
        return jsonify({
            "status": "success",
            "service_status": status
        }), 200

    except Exception as e:
        logger.error(f"Error getting forecast service status: {str(e)}")
        return jsonify({"error": "Failed to get service status"}), 500


@forecast_bp.route('/service/restart', methods=['POST', 'OPTIONS'])
@cross_origin(origins=[
    'https://deeptrade.capitolchilax.com',
    'https://deep-trade-frontend.vercel.app',
    'http://localhost:5173',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173'
],
supports_credentials=True,
methods=['POST', 'OPTIONS'],
allow_headers=['Content-Type', 'Authorization', 'Accept'],
expose_headers=['Content-Type'])
@log_request
def restart_service():
    """Restart the forecast service."""
    if request.method == 'OPTIONS':
        return jsonify({'status': 'ok'}), 200

    try:
        logger.info("Restarting forecast service via API request")
        status = restart_forecast_service()
        return jsonify({
            "status": "success",
            "message": "Forecast service restarted successfully",
            "service_status": status
        }), 200

    except Exception as e:
        logger.error(f"Error restarting forecast service: {str(e)}")
        return jsonify({"error": "Failed to restart forecast service"}), 500
