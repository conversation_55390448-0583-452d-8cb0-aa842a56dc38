from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, create_access_token
from app.models.user import User
from app.models.admin import CouponCode, CouponUsage
from app import db
from datetime import datetime
import time

trading_bp = Blueprint('trading', __name__, url_prefix='/api/trading')

@trading_bp.route('/tier/update', methods=['POST'])
@jwt_required()
def update_user_tier():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        # Accept boolean tier fields and validate
        t1 = bool(data.get('tier_1', False))
        t2 = bool(data.get('tier_2', False))
        t3 = bool(data.get('tier_3', False))
        print("DEBUG: Received tier payload:", data)
        if not (t1 or t2 or t3):
            return jsonify({'error': 'At least one tier must be selected', 'payload': data}), 400

        user_tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        if not user_tier_status:
            user_tier_status = UserTierStatus(user_id=user_id)
            db.session.add(user_tier_status)
        # Set only one tier to True
        user_tier_status.tier_1 = t1
        user_tier_status.tier_2 = t2
        user_tier_status.tier_3 = t3
        db.session.commit()
        selected_tier = user_tier_status.get_current_tier()
        new_token = create_access_token(identity=user_id, additional_claims={'tier': f'tier_{selected_tier}'})
        return jsonify({'tier': selected_tier, 'access_token': new_token}), 200
    except Exception as e:
        import traceback
        print("Error in update_user_tier:", e)
        traceback.print_exc()
        return jsonify({'error': str(e), 'traceback': traceback.format_exc()}), 500


@trading_bp.route('/coupon/redeem', methods=['POST'])
@jwt_required()
def redeem_coupon():
    """Redeem a coupon code and upgrade user tier"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        if not data or 'coupon_code' not in data:
            return jsonify({'error': 'Coupon code is required'}), 400

        coupon_code = data['coupon_code'].strip().upper()

        if not coupon_code:
            return jsonify({'error': 'Coupon code cannot be empty'}), 400

        # Find the coupon
        coupon = CouponCode.query.filter_by(code=coupon_code).first()
        if not coupon:
            return jsonify({'error': 'Invalid coupon code'}), 404

        # Check if coupon is valid
        if not coupon.is_valid():
            if not coupon.is_active:
                return jsonify({'error': 'This coupon has been deactivated'}), 400
            elif coupon.expiration_date <= datetime.utcnow():
                return jsonify({'error': 'This coupon has expired'}), 400
            elif coupon.usage_count >= coupon.max_uses:
                return jsonify({'error': 'This coupon has reached its usage limit'}), 400
            else:
                return jsonify({'error': 'This coupon is not valid'}), 400

        # Check if user can use this coupon
        if not coupon.can_be_used_by(user_id):
            return jsonify({'error': 'You have already used this coupon'}), 400

        # Get current user tier status
        user_tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        if not user_tier_status:
            user_tier_status = UserTierStatus(user_id=user_id)
            db.session.add(user_tier_status)

        current_tier = user_tier_status.get_current_tier()
        coupon_tier = coupon.tier_level

        # Check if user is trying to downgrade
        if current_tier >= coupon_tier:
            return jsonify({
                'error': f'You already have access to Tier {current_tier} or higher. This coupon is for Tier {coupon_tier}.'
            }), 400

        # Use the coupon
        try:
            usage = coupon.use_coupon(user_id)

            # Update user tier based on coupon
            if coupon_tier == 2:
                user_tier_status.tier_1 = False
                user_tier_status.tier_2 = True
                user_tier_status.tier_3 = False

                # Set tier 2 membership dates for 30 days access
                user_tier_status.last_payment_date = datetime.utcnow()
                user_tier_status.next_payment_date = datetime.utcnow() + timedelta(days=30)

            elif coupon_tier == 3:
                user_tier_status.tier_1 = False
                user_tier_status.tier_2 = False
                user_tier_status.tier_3 = True

            db.session.commit()

            # Create new JWT token with updated tier
            new_tier = user_tier_status.get_current_tier()
            new_token = create_access_token(
                identity=user_id,
                additional_claims={'tier': f'tier_{new_tier}'}
            )

            # Calculate expiration info
            expires_in_days = None
            if coupon_tier == 2 and user_tier_status.next_payment_date:
                expires_in_days = (user_tier_status.next_payment_date - datetime.utcnow()).days

            return jsonify({
                'message': f'Coupon redeemed successfully! Welcome to Tier {new_tier}!',
                'success': True,
                'coupon': {
                    'code': coupon.code,
                    'tier_level': coupon.tier_level,
                    'description': coupon.description
                },
                'user_tier': {
                    'previous_tier': current_tier,
                    'new_tier': new_tier,
                    'expires_in_days': expires_in_days
                },
                'access_token': new_token
            }), 200

        except ValueError as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error redeeming coupon: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
"""
Trading Routes for DeepTrade Platform - PRODUCTION VERSION
Handles trading operations, API credentials, and trading sessions with full security.
"""

from flask import Blueprint, request, jsonify, current_app
from datetime import datetime, timedelta
from app.models.subscription import Subscription, SubscriptionTier, SubscriptionStatus
from flask_jwt_extended import get_jwt_identity, jwt_required as flask_jwt_required
from app.auth.decorators import jwt_required, subscription_required
from app.auth.security import SecurityManager
from app.models.user import User
from app.models.trade import Trade, TradingSession
from app.models.security_log import APICredential, ExchangeType
from app.models.user_tier_status import UserTierStatus
from app.models.subscription import Subscription
from app import db, limiter



@trading_bp.route('/sessions', methods=['GET'])
@jwt_required()
def get_trading_sessions():
    """Get user's trading sessions."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        status = request.args.get('status')
        
        # Build query
        query = TradingSession.query.filter_by(user_id=user_id)
        if status:
            query = query.filter_by(status=status)
        
        sessions = query.order_by(TradingSession.started_at.desc())\
                       .limit(limit).offset(offset).all()
        
        # Log API access
        SecurityManager.log_api_access(
            user_id=user_id,
            endpoint='GET /api/trading/sessions',
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'sessions': [session.to_dict() for session in sessions],
            'total': TradingSession.query.filter_by(user_id=user_id).count(),
            'limit': limit,
            'offset': offset
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting trading sessions: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/sessions', methods=['POST'])
@jwt_required()
@subscription_required()
def create_trading_session():
    """Create a new trading session."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['trading_pair', 'initial_balance']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Create trading session (using actual model constructor)
        session = TradingSession(
            user_id=user_id,
            symbol=data['trading_pair'],  # trading_pair maps to symbol in the model
            initial_balance=float(data['initial_balance']),
            leverage=data.get('leverage', 10),
            investment_percentage=data.get('investment_percentage', 50)
        )
        
        db.session.add(session)
        db.session.commit()
        
        # Log session creation
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='trading_session_created',
            ip_address=request.remote_addr,
            details={
                'session_id': session.id,
                'symbol': data['trading_pair'],
                'initial_balance': data['initial_balance']
            },
            risk_level='medium'
        )
        
        return jsonify({
            'message': 'Trading session created successfully',
            'session': session.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating trading session: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/trades', methods=['GET'])
@jwt_required()
def get_trades():
    """Get user's trade history."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        limit = min(int(request.args.get('limit', 50)), 200)
        offset = int(request.args.get('offset', 0))
        session_id = request.args.get('session_id')
        source = request.args.get('source')  # Filter by 'app' or 'external'
        
        # Build query
        query = Trade.query.filter_by(user_id=user_id)
        if session_id:
            query = query.filter_by(trading_session_id=session_id)
        if source:
            query = query.filter_by(source=source)
        
        trades = query.order_by(Trade.entry_time.desc())\
                     .limit(limit).offset(offset).all()
        
        # Get totals for both app and external trades
        total_all = Trade.query.filter_by(user_id=user_id).count()
        total_app = Trade.query.filter_by(user_id=user_id, source='app').count()
        total_external = Trade.query.filter_by(user_id=user_id, source='external').count()
        
        # Log API access
        SecurityManager.log_api_access(
            user_id=user_id,
            endpoint='GET /api/trading/trades',
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'trades': [trade.to_dict() for trade in trades],
            'total': total_all,
            'total_app': total_app,
            'total_external': total_external,
            'limit': limit,
            'offset': offset
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting trades: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/credentials', methods=['GET'])
@jwt_required()
def get_api_credentials():
    """Get user's API credentials (without sensitive data)."""
    try:
        # Log Authorization header and JWT identity for debugging
        from flask import request
        auth_header = request.headers.get('Authorization')
        current_app.logger.info(f"[DEBUG] /credentials Authorization: {auth_header}")
        user_id = get_jwt_identity()
        current_app.logger.info(f"[DEBUG] /credentials JWT identity: {user_id}")

        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        credentials = APICredential.query.filter_by(user_id=user_id, is_active=True).all()
        
        return jsonify({
            'credentials': [cred.to_dict(include_credentials=False) for cred in credentials]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting API credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/credentials', methods=['POST'])
@jwt_required()
@limiter.limit("10 per hour")
def add_api_credentials():
    """Add new API credentials for an exchange."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['exchange', 'api_key', 'secret_key']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate exchange type
        try:
            exchange_type = ExchangeType(data['exchange'])
        except ValueError:
            return jsonify({'error': 'Invalid exchange type'}), 400
        
        # Check if credentials already exist for this exchange
        existing = APICredential.query.filter_by(
            user_id=user_id,
            exchange=exchange_type,
            is_active=True,
            is_valid=True
        ).first()
        
        if existing:
            return jsonify({'error': 'Active credentials already exist for this exchange'}), 400
        
        # Create new credentials
        credentials = APICredential(
            user_id=user_id,
            exchange=exchange_type,
            api_key=data['api_key'],
            secret_key=data['secret_key'],
            passphrase=data.get('passphrase')
        )
        
        db.session.add(credentials)
        db.session.commit()
        
        # Validate credentials
        is_valid = credentials.validate_credentials()
        db.session.commit()
        
        # Log credential addition
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='api_credentials_added',
            ip_address=request.remote_addr,
            details={
                'exchange': data['exchange'],
                'is_valid': is_valid
            },
            risk_level='high'
        )
        
        return jsonify({
            'message': 'API credentials added successfully',
            'credentials': credentials.to_dict(include_credentials=False),
            'validation_status': 'valid' if is_valid else 'invalid'
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error adding API credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/credentials/<credential_id>', methods=['DELETE'])
@jwt_required()
def remove_api_credentials(credential_id):
    """Remove API credentials."""
    try:
        user_id = get_jwt_identity()
        
        credentials = APICredential.query.filter_by(
            id=credential_id,
            user_id=user_id
        ).first()
        
        if not credentials:
            return jsonify({'error': 'Credentials not found'}), 404
        
        # Deactivate credentials
        credentials.deactivate()
        db.session.commit()
        
        # Log credential removal
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='api_credentials_removed',
            ip_address=request.remote_addr,
            details={
                'credential_id': credential_id,
                'exchange': credentials.exchange.value
            },
            risk_level='medium'
        )
        
        return jsonify({'message': 'API credentials removed successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error removing API credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
@trading_bp.route('/credentials/<credential_id>', methods=['PATCH'])
@jwt_required()
def update_api_credentials(credential_id):
    """Edit API credentials for an exchange."""
    try:
        user_id = get_jwt_identity()
        credentials = APICredential.query.filter_by(
            id=credential_id,
            user_id=user_id
        ).first()
        if not credentials:
            return jsonify({'error': 'Credentials not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Update fields if provided
        if 'api_key' in data:
            credentials.set_credentials(
                data['api_key'],
                data.get('secret_key', credentials.get_credentials()['secret_key']),
                data.get('passphrase', credentials.get_credentials().get('passphrase'))
            )
        elif 'secret_key' in data or 'passphrase' in data:
            creds = credentials.get_credentials()
            credentials.set_credentials(
                data.get('api_key', creds['api_key']),
                data.get('secret_key', creds['secret_key']),
                data.get('passphrase', creds.get('passphrase'))
            )

        db.session.commit()
        # Re-validate credentials after update
        is_valid = credentials.validate_credentials()
        db.session.commit()

        return jsonify({
            'message': 'API credentials updated successfully',
            'credentials': credentials.to_dict(include_credentials=False),
            'validation_status': 'valid' if is_valid else 'invalid'
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error updating API credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/credentials/<credential_id>/activate', methods=['POST'])
@jwt_required()
def activate_api_credentials(credential_id):
    """Activate API credentials for an exchange (deactivates others for same user/exchange)."""
    try:
        user_id = get_jwt_identity()
        credentials = APICredential.query.filter_by(
            id=credential_id,
            user_id=user_id
        ).first()
        if not credentials:
            return jsonify({'error': 'Credentials not found'}), 404

        # Deactivate other credentials for this user/exchange
        APICredential.query.filter_by(
            user_id=user_id,
            exchange=credentials.exchange
        ).filter(
            APICredential.id != credential_id,
            APICredential.is_active == True
        ).update({'is_active': False})
        credentials.is_active = True
        db.session.commit()

        return jsonify({'message': 'API credentials activated successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Error activating API credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/credentials/<credential_id>/deactivate', methods=['POST'])
@jwt_required()
def deactivate_api_credentials(credential_id):
    """Deactivate API credentials for an exchange."""
    try:
        user_id = get_jwt_identity()
        credentials = APICredential.query.filter_by(
            id=credential_id,
            user_id=user_id
        ).first()
        if not credentials:
            return jsonify({'error': 'Credentials not found'}), 404

        credentials.is_active = False
        db.session.commit()

        return jsonify({'message': 'API credentials deactivated successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Error deactivating API credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/credentials/<credential_id>/validate', methods=['POST'])
@jwt_required()
@limiter.limit("20 per hour")
def validate_api_credentials(credential_id):
    """Validate API credentials."""
    try:
        user_id = get_jwt_identity()
        
        credentials = APICredential.query.filter_by(
            id=credential_id,
            user_id=user_id
        ).first()
        
        if not credentials:
            return jsonify({'error': 'Credentials not found'}), 404
        
        # Validate credentials
        is_valid = credentials.validate_credentials()
        db.session.commit()
        
        # Log validation attempt
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='api_credentials_validated',
            ip_address=request.remote_addr,
            details={
                'credential_id': credential_id,
                'exchange': credentials.exchange.value,
                'is_valid': is_valid
            },
            risk_level='low'
        )
        
        return jsonify({
            'is_valid': is_valid,
            'last_validated_at': credentials.last_validated_at.isoformat() if credentials.last_validated_at else None,
            'validation_error': credentials.validation_error
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error validating API credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/credentials/test', methods=['POST'])
@jwt_required()
@limiter.limit("5 per minute")
def test_credentials():
    """Test API credentials without saving them - PRODUCTION SECURE VERSION"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['exchange', 'api_key', 'secret_key']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Get exchange service
        from app.services.exchange_service import get_exchange_service
        
        try:
            exchange_service = get_exchange_service(
                data['exchange'],
                data['api_key'],
                data['secret_key'],
                data.get('passphrase')
            )
        except ValueError as e:
            return jsonify({'error': str(e)}), 400
        
        # Test credentials
        is_valid, result = exchange_service.validate_credentials()
        
        # Log credential testing attempt - PRODUCTION SECURITY
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='api_credentials_tested',
            ip_address=request.remote_addr,
            details={
                'exchange': data['exchange'],
                'is_valid': is_valid,
                'test_only': True  # Mark as test-only operation
            },
            risk_level='medium'
        )
        
        if is_valid:
            return jsonify({
                'success': True,
                'balance': result.get('balance', 0),
                'can_trade': result.get('can_trade', False),
                'can_withdraw': result.get('can_withdraw', False),
                'permissions': result
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Validation failed')
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"Error testing credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/signals/<symbol>', methods=['GET'])
@jwt_required()
@limiter.limit("60 per hour")
def get_trading_signals(symbol):
    """Get trading signals for a symbol"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get user's active credentials
        credentials = APICredential.query.filter_by(
            user_id=user_id, 
            is_active=True, 
            is_valid=True
        ).first()
        
        if not credentials:
            return jsonify({'error': 'No valid API credentials found. Please add and validate your exchange credentials first.'}), 400
        
        # Get exchange service
        from app.services.exchange_service import get_exchange_service
        creds = credentials.get_credentials()
        
        if not creds:
            return jsonify({'error': 'Failed to decrypt credentials'}), 500
        
        exchange_service = get_exchange_service(
            credentials.exchange.value,
            creds['api_key'],
            creds['secret_key'],
            creds.get('passphrase')
        )
        
        # Generate signals
        from app.services.trading_signals import TradingSignalGenerator
        signal_generator = TradingSignalGenerator(user_id, exchange_service)
        
        timeframe = request.args.get('timeframe', '1h')
        signals = signal_generator.generate_signals(symbol, timeframe)
        
        # Log signal generation
        SecurityManager.log_api_access(
            user_id=user_id,
            endpoint=f'GET /api/trading/signals/{symbol}',
            ip_address=request.remote_addr
        )
        
        return jsonify(signals), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting trading signals: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/signals/monitor', methods=['GET'])
@jwt_required()
@limiter.limit("30 per hour")
def monitor_signals():
    """Monitor BTC/USDT trading signals only"""
    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"[SIGNAL_DEBUG] monitor_signals called for user: {user_id}")
        
        # Fixed to BTC/USDT only - remove all other symbols
        symbol = 'BTCUSDT'
        timeframe = request.args.get('timeframe', '1h')
        current_app.logger.info(f"[SIGNAL_DEBUG] Processing signal for symbol: {symbol}, timeframe: {timeframe}")
        
        # Enforce tier payment status before using credentials
        from app.models.user_tier_status import UserTierStatus
        user_tier = UserTierStatus.query.filter_by(user_id=user_id).first()
        if user_tier and user_tier.payment_status == 'unpaid' and user_tier.profit_share_owed > 0:
            current_app.logger.warning(f"[SIGNAL_BLOCKED] User {user_id} blocked due to unpaid profit share.")
            return jsonify({
                'error': 'Trading and API credential usage blocked: Unpaid profit share for your tier. Please settle your balance to continue.',
                'blocked': True,
                'owed': str(user_tier.profit_share_owed),
                'tier': user_tier.tier
            }), 403

        # Get user's credentials
        current_app.logger.info(f"[SIGNAL_DEBUG] Querying API credentials for user: {user_id}")
        credentials = APICredential.query.filter_by(
            user_id=user_id,
            is_active=True,
            is_valid=True
        ).first()
        
        if not credentials:
            current_app.logger.warning(f"[SIGNAL_DEBUG] No valid API credentials found for user: {user_id}")
            return jsonify({
                'error': 'No valid API credentials found',
                'code': 'missing_credentials'
            }), 401
        
        current_app.logger.info(f"[SIGNAL_DEBUG] Found credentials for exchange: {credentials.exchange.value}")
        
        # Get exchange service
        try:
            from app.services.exchange_service import get_exchange_service
            current_app.logger.info(f"[SIGNAL_DEBUG] Decrypting credentials...")
            creds = credentials.get_credentials()
            
            if not creds or not creds.get('api_key') or not creds.get('secret_key'):
                current_app.logger.error(f"[SIGNAL_DEBUG] Failed to decrypt credentials or missing keys")
                return jsonify({
                    'error': 'Failed to decrypt API credentials',
                    'code': 'credential_decrypt_error'
                }), 500
            
            current_app.logger.info(f"[SIGNAL_DEBUG] Creating exchange service for {credentials.exchange.value}")
            exchange_service = get_exchange_service(
                credentials.exchange.value,
                creds['api_key'],
                creds['secret_key'],
                creds.get('passphrase')
            )
            current_app.logger.info(f"[SIGNAL_DEBUG] Exchange service created successfully")
            
        except Exception as e:
            current_app.logger.error(f"[SIGNAL_DEBUG] Error creating exchange service: {str(e)}")
            return jsonify({
                'error': f'Exchange service error: {str(e)}',
                'code': 'exchange_service_error'
            }), 500
        
        # Generate signals for BTC/USDT only
        try:
            current_app.logger.info(f"[SIGNAL_DEBUG] Importing TradingSignalGenerator...")
            from app.services.trading_signals import TradingSignalGenerator
            
            current_app.logger.info(f"[SIGNAL_DEBUG] Creating signal generator...")
            signal_generator = TradingSignalGenerator(user_id, exchange_service)
            
            # Generate signal for BTC/USDT only
            current_app.logger.info(f"[SIGNAL_DEBUG] Generating signals for {symbol}")
            btc_signals = signal_generator.generate_signals(symbol, timeframe)
            current_app.logger.info(f"[SIGNAL_DEBUG] Signal generation completed. Result type: {type(btc_signals)}")
            current_app.logger.info(f"[SIGNAL_DEBUG] Generated signals: {btc_signals}")
            
            # Check if signal generation returned an error
            if isinstance(btc_signals, dict) and 'error' in btc_signals:
                current_app.logger.error(f"[SIGNAL_DEBUG] Signal generation error: {btc_signals['error']}")
                return jsonify({
                    'error': f"Signal generation failed: {btc_signals['error']}",
                    'code': 'signal_generation_error'
                }), 500
            
        except Exception as e:
            current_app.logger.error(f"[SIGNAL_DEBUG] Error in signal generation: {str(e)}", exc_info=True)
            return jsonify({
                'error': f'Signal generation error: {str(e)}',
                'code': 'signal_generation_exception'
            }), 500
        
        all_signals = {symbol: btc_signals}
        
        # Log signal monitoring
        try:
            SecurityManager.log_api_access(
                user_id=user_id,
                endpoint='GET /api/trading/signals/monitor',
                ip_address=request.remote_addr
            )
        except Exception:
            pass

        response_data = {
            'symbols': all_signals,
            'generated_at': datetime.utcnow().isoformat()
        }

        return jsonify(response_data), 200
        
    except Exception as e:
        current_app.logger.error(f"Error monitoring signals: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_trading_statistics():
    """Get user's trading statistics."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Calculate statistics
        stats = user.calculate_trading_stats()
        
        return jsonify({'statistics': stats}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting trading statistics: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/live-price/<symbol>', methods=['GET'])
def get_live_price(symbol):
    """Get live price for a symbol using centralized price service."""
    try:
        from app.services.price_service import price_service

        # Ensure the symbol is being tracked
        price_service.add_symbol(symbol.upper())

        # Get current price data from centralized service
        price_data = price_service.get_current_price(symbol.upper())

        if price_data is None:
            return jsonify({'error': f'Price data not available for {symbol}'}), 404

        return jsonify(price_data), 200

    except Exception as e:
        current_app.logger.error(f"Error getting live price for {symbol}: {str(e)}")
        return jsonify({'error': 'Failed to get live price'}), 500


@trading_bp.route('/chart-data', methods=['GET'])
def get_chart_data():
    """Get historical price data for charting."""
    try:
        # Get query parameters
        symbol = request.args.get('symbol', 'BTCUSDT')
        timeframe = request.args.get('timeframe', '1h')
        limit = min(int(request.args.get('limit', 100)), 200)

        # Fetch real market data
        from app.services.market_data import ml_service

        klines = ml_service.market_data.get_klines(symbol, timeframe, limit)

        if not klines:
            return jsonify({'error': 'Unable to fetch chart data'}), 500
        
        # Format data for frontend
        chart_data = []
        for kline in klines:
            chart_data.append({
                'timestamp': kline['timestamp'],
                'datetime': datetime.fromtimestamp(kline['timestamp'] / 1000).isoformat(),
                'open': kline['open'],
                'high': kline['high'],
                'low': kline['low'],
                'close': kline['close'],
                'volume': kline['volume']
            })
        
        return jsonify({
            'symbol': symbol,
            'timeframe': timeframe,
            'data': chart_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting chart data: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/predictions', methods=['GET'])
def get_market_predictions():
    """Get ML-powered market predictions."""
    try:
        # Get query parameters
        symbol = request.args.get('symbol', 'BTCUSDT')
        timeframe = request.args.get('timeframe', '1h')
        
        # Use real ML prediction service
        from app.services.market_data import ml_service
        
        predictions = ml_service.generate_prediction(symbol, timeframe)
        
        if not predictions:
            return jsonify({'error': 'Unable to generate predictions at this time'}), 500
        
        return jsonify({'predictions': predictions}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting market predictions: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/forecast', methods=['GET'])
def get_forecast_chart():
    """Get advanced ML forecast with interactive chart."""
    try:
        symbol = request.args.get('symbol', 'BTCUSDT')
        timeframe = request.args.get('timeframe', '1h')
        future_hours = int(request.args.get('future_hours', 72))

        # Use enhanced ensemble forecast from ML service
        from app.services.market_data import ml_service

        forecast_data = ml_service.generate_ensemble_forecast(symbol, timeframe, future_hours)

        if not forecast_data:
            return jsonify({'error': 'Unable to generate forecast at this time'}), 500

        return jsonify(forecast_data), 200

    except Exception as e:
        current_app.logger.error(f"Error getting forecast: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/forecast-chart', methods=['GET'])
def get_forecast_chart_html():
    """Get forecast chart as HTML for direct embedding."""
    try:
        symbol = request.args.get('symbol', 'BTCUSDT')
        timeframe = request.args.get('timeframe', '1h')
        future_hours = int(request.args.get('future_hours', 72))

        # Use enhanced ensemble forecast from ML service
        from app.services.market_data import ml_service

        forecast_data = ml_service.generate_ensemble_forecast(symbol, timeframe, future_hours)

        if not forecast_data or not forecast_data.get('chart_html'):
            return '<div class="text-red-500 text-center">Error loading chart data</div>', 500

        return forecast_data['chart_html'], 200, {'Content-Type': 'text/html'}
    except Exception as e:
        current_app.logger.error(f"Error getting forecast chart HTML: {str(e)}")
        return '<div class="text-red-500 text-center">Error loading chart data</div>', 500

@trading_bp.route('/real-balance', methods=['GET'])
@jwt_required()
def get_real_balance():
    """Get real account balance from exchange API."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            current_app.logger.warning(f"User not found: {user_id}")
            return jsonify({'error': 'User not found'}), 404
        
        # Get user's active credentials
        credentials = APICredential.query.filter_by(
            user_id=user_id,
            is_active=True,
            is_valid=True
        ).first()
        
        if not credentials:
            current_app.logger.warning(f"No valid API credentials found for user: {user_id}")
            return jsonify({
                'error': 'No valid API credentials found. Please add and validate your exchange credentials first.',
                'code': 'missing_credentials'
            }), 400
        
        # Get exchange service
        from app.services.exchange_service import get_exchange_service
        
        try:
            creds = credentials.get_credentials()
            if not creds:
                current_app.logger.error(f"Failed to decrypt credentials for user: {user_id}")
                return jsonify({
                    'error': 'Failed to decrypt credentials. Please update your API credentials.',
                    'code': 'decryption_error'
                }), 500
                
            exchange_service = get_exchange_service(
                credentials.exchange.value,
                creds['api_key'],
                creds['secret_key'],
                creds.get('passphrase')
            )
            
            # Get real account balance
            balance = exchange_service.get_balance()
            
            if balance is None:
                raise ValueError("Failed to retrieve balance from exchange")
                
            balance_data = {
                'balance': float(balance),
                'currency': 'USDT',
                'exchange': credentials.exchange.value,
                'last_updated': datetime.utcnow().isoformat(),
                'is_mock': False
            }
            
            # Log successful balance retrieval
            current_app.logger.info(f"Successfully retrieved balance for user {user_id}")
            
            # Log API access
            SecurityManager.log_api_access(
                user_id=user_id,
                endpoint='GET /api/trading/real-balance',
                ip_address=request.remote_addr
            )
            
            return jsonify(balance_data), 200
            
        except ValueError as ve:
            current_app.logger.error(f"Value error getting balance: {str(ve)}")
            return jsonify({
                'error': str(ve) or 'Invalid data received from exchange',
                'code': 'invalid_exchange_data'
            }), 400
            
        except Exception as e:
            current_app.logger.error(f"Error getting balance from exchange: {str(e)}", exc_info=True)
            return jsonify({
                'error': 'Failed to get balance from exchange. Please check your API credentials and try again.',
                'code': 'exchange_error'
            }), 400
        
    except Exception as e:
        current_app.logger.error(f"Unexpected error in get_real_balance: {str(e)}", exc_info=True)
        return jsonify({
            'error': 'An unexpected error occurred while processing your request',
            'code': 'internal_error'
        }), 500


@trading_bp.route('/real-history', methods=['GET'])
@jwt_required()
def get_real_history():
    """Get real trading history from database."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        symbol = request.args.get('symbol', 'BTCUSDT')
        limit = min(int(request.args.get('limit', 50)), 100)
        
        # Get real trades from database
        trades_query = Trade.query.filter_by(user_id=user_id)
        
        if symbol:
            trades_query = trades_query.filter_by(symbol=symbol)
        
        trades = trades_query.order_by(Trade.entry_time.desc()).limit(limit).all()
        
        # Convert trades to JSON format
        trades_data = []
        for trade in trades:
            trade_data = {
                'id': trade.id,
                'symbol': trade.symbol,
                'side': trade.side,
                'amount': float(trade.amount),
                'entry_price': float(trade.entry_price) if trade.entry_price else None,
                'exit_price': float(trade.exit_price) if trade.exit_price else None,
                'entry_time': trade.entry_time.isoformat() if trade.entry_time else None,
                'exit_time': trade.exit_time.isoformat() if trade.exit_time else None,
                'pnl': float(trade.pnl) if trade.pnl else 0.0,
                'status': trade.status,
            }
            trades_data.append(trade_data)
        return jsonify({'trades': trades_data}), 200
    except Exception as e:
        current_app.logger.error(f"Error getting real trading history: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

# --- MISSING ENDPOINTS FOR FRONTEND COMPATIBILITY ---

@trading_bp.route('/blocked_status', methods=['GET'])
@jwt_required()
def get_blocked_status():
    """Return blocked status for the user based on tier and profit share debt."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        
        if not tier_status:
            # Create default tier status if it doesn't exist
            tier_status = UserTierStatus(user_id=user_id, tier_1=True, tier_2=False, tier_3=False)
            db.session.add(tier_status)
            db.session.commit()
        
        # User is blocked if they have unpaid profit share debt or account is disabled
        is_blocked = (tier_status.payment_status == 'unpaid' and
                     float(tier_status.profit_share_owed) > 0) or tier_status.account_disabled
        
        response = {
            'blocked': is_blocked,
            'owed': str(tier_status.profit_share_owed) if tier_status.profit_share_owed is not None else '0',
            'tier': f"Tier {tier_status.get_current_tier()}",
            'payment_status': tier_status.payment_status or 'unpaid',
            'effective_rate': float(tier_status.get_effective_profit_share_rate()) if hasattr(tier_status, 'get_effective_profit_share_rate') else 0.0
        }
        return jsonify(response), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting blocked status: {str(e)}", exc_info=True)
        return jsonify({'error': 'Internal server error', 'details': str(e)}), 500

@trading_bp.route('/active_position', methods=['GET'])
@jwt_required()
def get_active_position():
    """Return the user's active trading positions from both app and external sources."""
    current_app.logger.error("DEBUGGING: ActivePosition endpoint called!")
    import traceback
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        if not user:
            current_app.logger.error("ActivePosition: User not found")
            return jsonify({'error': 'User not found'}), 404

        # Get both app and external active positions
        source_filter = request.args.get('source', 'all')  # 'app', 'external', or 'all'
        current_app.logger.info(f"ActivePosition: Fetching positions for user {user_id}, source: {source_filter}")
        
        # Get active app trades
        app_trades = []
        external_positions = []
        credential_status = None
        external_error = None
        
        if source_filter in ['app', 'all']:
            app_trades = Trade.query.filter_by(
                user_id=user_id,
                status='open',
                source='app'
            ).all()
            current_app.logger.error(f"DEBUGGING ActivePosition: Found {len(app_trades)} app trades")

        if source_filter in ['external', 'all']:
            # Find active API credentials for external positions
            from app.models.security_log import APICredential
            credential = APICredential.query.filter_by(user_id=user_id, is_active=True).first()
            
            if credential:
                credential_status = f"Found active {credential.exchange.value} credentials"
                current_app.logger.error(f"DEBUGGING ActivePosition: {credential_status}")  # Using ERROR level to ensure visibility
                
                # Get exchange service
                from app.services.exchange_service import get_exchange_service
                creds = credential.get_credentials() if hasattr(credential, "get_credentials") else None
                current_app.logger.error(f"DEBUGGING: Got credentials - API Key exists: {bool(creds and creds.get('api_key'))}")
                
                if creds and creds.get("api_key") and creds.get("secret_key"):
                    try:
                        current_app.logger.error(f"DEBUGGING: About to call exchange service for {credential.exchange}")
                        service = get_exchange_service(credential.exchange, creds['api_key'], creds['secret_key'], creds.get('passphrase'))
                        raw_external_positions = service.get_open_positions() or []
                        current_app.logger.error(f"DEBUGGING ActivePosition: Found {len(raw_external_positions)} external positions")
                        current_app.logger.error(f"DEBUGGING: Raw positions: {raw_external_positions}")
                        
                        # Enhance external positions with market data calculations
                        external_positions = []
                        for pos in raw_external_positions:
                            enhanced_pos = dict(pos)
                            enhanced_pos['source'] = 'external'
                            
                            # Debug: Log the raw position data structure
                            current_app.logger.error(f"DEBUGGING Raw external position data: {pos}")
                            
                            # Try to enhance with market data calculations
                            try:
                                from app.services.market_data import BinanceMarketData
                                market_data = BinanceMarketData()
                                
                                # Handle different possible field names for symbol
                                symbol = enhanced_pos.get('symbol') or enhanced_pos.get('instId') or enhanced_pos.get('instrument')
                                if symbol:
                                    current_price = market_data.get_current_price(symbol)
                                    enhanced_pos['current_price'] = current_price
                                    
                                    # Handle different possible field names for entry price and size
                                    entry_price = None
                                    quantity = None
                                    
                                    # Try different field names for entry price
                                    entry_price_fields = ['entry_price', 'entry', 'entryPrice', 'avgPx', 'avg_price', 'markPx']
                                    for field in entry_price_fields:
                                        field_value = enhanced_pos.get(field)
                                        if field_value is not None:
                                            try:
                                                entry_price = float(field_value)
                                                enhanced_pos['entry_price'] = entry_price  # Normalize field name
                                                break
                                            except (ValueError, TypeError):
                                                continue
                                    
                                    # Try different field names for quantity/size
                                    quantity_fields = ['size', 'quantity', 'pos', 'amount', 'sz']
                                    for field in quantity_fields:
                                        field_value = enhanced_pos.get(field)
                                        if field_value is not None:
                                            try:
                                                quantity = abs(float(field_value))  # Take absolute value
                                                enhanced_pos['quantity'] = quantity  # Normalize field name
                                                break
                                            except (ValueError, TypeError):
                                                continue
                                    
                                    # Handle different field names for side
                                    side = enhanced_pos.get('side') or enhanced_pos.get('posSide')
                                    if not side and quantity:
                                        # Infer side from positive/negative quantity
                                        for field in quantity_fields:
                                            original_qty_value = enhanced_pos.get(field)
                                            if original_qty_value is not None:
                                                try:
                                                    original_qty = float(original_qty_value)
                                                    side = 'long' if original_qty > 0 else 'short'
                                                    break
                                                except (ValueError, TypeError):
                                                    continue
                                    
                                    enhanced_pos['side'] = side  # Normalize field name
                                    
                                    current_app.logger.error(f"DEBUGGING Processed position - Symbol: {symbol}, Entry: {entry_price}, Qty: {quantity}, Side: {side}, Current: {current_price}")
                                    
                                    if current_price and entry_price and quantity and side:
                                        # Calculate real-time P&L
                                        if side.lower() == 'long':
                                            unrealized_pnl = (current_price - entry_price) * quantity
                                        else:  # short position
                                            unrealized_pnl = (entry_price - current_price) * quantity
                                        
                                        enhanced_pos['unrealized_pnl'] = unrealized_pnl
                                        enhanced_pos['unrealized_roi'] = (unrealized_pnl / (entry_price * quantity)) * 100 if entry_price * quantity > 0 else 0
                                        
                                        # Calculate margin and liquidation price (simplified calculation)
                                        leverage = float(enhanced_pos.get('leverage') or enhanced_pos.get('lever') or 10)
                                        margin_used = (entry_price * quantity) / leverage if leverage > 0 else 0
                                        enhanced_pos['margin_used'] = margin_used
                                        enhanced_pos['leverage'] = leverage
                                        
                                        # Use the liquidation price provided by the CEX, do not recalculate
                                        enhanced_pos['liquidation_price'] = pos.get('liquidation_price')
                                        enhanced_pos['margin_rate'] = (1/leverage) * 100 if leverage > 0 else None
                                        
                                        # Add TP/SL if available
                                        enhanced_pos['take_profit'] = enhanced_pos.get('take_profit') or enhanced_pos.get('tpTriggerPx')
                                        enhanced_pos['stop_loss'] = enhanced_pos.get('stop_loss') or enhanced_pos.get('slTriggerPx')
                                        
                                        current_app.logger.error(f"DEBUGGING Enhanced position with P&L: {unrealized_pnl}, ROI: {enhanced_pos['unrealized_roi']}")
                                    else:
                                        current_app.logger.error(f"DEBUGGING Missing data for calculations - Price: {current_price}, Entry: {entry_price}, Qty: {quantity}, Side: {side}")
                                    
                            except Exception as e:
                                current_app.logger.error(f"Error enhancing external position {pos.get('symbol', 'unknown')}: {str(e)}")
                                import traceback
                                current_app.logger.error(traceback.format_exc())
                                
                                # Set default values for missing calculations
                                enhanced_pos.update({
                                    'current_price': None,
                                    'unrealized_pnl': None,
                                    'unrealized_roi': None,
                                    'margin_used': None,
                                    'liquidation_price': None,
                                    'leverage': None,
                                    'margin_rate': None,
                                    'take_profit': None,
                                    'stop_loss': None
                                })
                            
                            external_positions.append(enhanced_pos)
                    except Exception as ex:
                        external_error = f"Error fetching external positions: {str(ex)}"
                        current_app.logger.error(f"ActivePosition: {external_error}")
                else:
                    external_error = "Failed to decrypt API credentials or missing keys"
                    current_app.logger.error(f"ActivePosition: {external_error}")
            else:
                credential_status = "No active API credentials found"
                current_app.logger.info(f"ActivePosition: {credential_status}")

        # Combine results with enhanced trading data
        app_positions = []
        for trade in app_trades:
            position_data = trade.to_dict()
            position_data['source'] = 'app'
            
            # Get current market price and calculate enhanced data
            try:
                from app.services.market_data import BinanceMarketData
                market_data = BinanceMarketData()
                current_price = market_data.get_current_price(trade.symbol)
                position_data['current_price'] = current_price
                
                # Get entry price and quantity for calculations
                entry_price = float(trade.entry_price)
                quantity = float(trade.quantity)
                
                # Calculate real-time P&L if we have current price
                if current_price and trade.status == 'open':
                    if trade.is_long():
                        unrealized_pnl = (current_price - entry_price) * quantity
                    else:  # Short position
                        unrealized_pnl = (entry_price - current_price) * quantity
                    
                    position_data['unrealized_pnl'] = unrealized_pnl
                    position_data['unrealized_roi'] = (unrealized_pnl / (entry_price * quantity)) * 100 if entry_price * quantity > 0 else 0
                
                # Calculate margin and liquidation price (simplified calculation)
                # For futures trading with leverage
                leverage = 1  # Default to 1x leverage - users must configure their risk settings
                margin_used = (entry_price * quantity) / leverage
                position_data['margin_used'] = margin_used
                position_data['leverage'] = leverage
                
                # Simplified liquidation price calculation
                if trade.is_long():
                    # For long positions: liq_price = entry_price * (1 - 1/leverage)
                    liq_price = entry_price * (1 - 1/leverage)
                else:
                    # For short positions: liq_price = entry_price * (1 + 1/leverage)
                    liq_price = entry_price * (1 + 1/leverage)
                
                position_data['liquidation_price'] = liq_price
                position_data['margin_rate'] = (1/leverage) * 100  # Margin rate as percentage
                
                # Ensure TP/SL are included (should come from trade.to_dict() but ensure they exist)
                if 'take_profit' not in position_data:
                    position_data['take_profit'] = getattr(trade, 'take_profit', None)
                if 'stop_loss' not in position_data:
                    position_data['stop_loss'] = getattr(trade, 'stop_loss', None)
                
            except Exception as e:
                current_app.logger.error(f"Error fetching market data for {trade.symbol}: {e}")
                position_data['current_price'] = None
                position_data['unrealized_pnl'] = None
                position_data['unrealized_roi'] = None
                position_data['margin_used'] = None
                position_data['liquidation_price'] = None
                position_data['leverage'] = None
                position_data['margin_rate'] = None
                position_data['take_profit'] = getattr(trade, 'take_profit', None)
                position_data['stop_loss'] = getattr(trade, 'stop_loss', None)
            
            app_positions.append(position_data)
        
        # Prepare diagnostic information
        diagnostics = {
            'app_trades_count': len(app_trades),
            'external_positions_count': len(external_positions),
            'credential_status': credential_status,
            'external_error': external_error,
            'user_id': user_id
        }
        
        current_app.logger.info(f"ActivePosition: Returning data - {diagnostics}")
        
        if source_filter == 'app':
            return jsonify({
                'positions': app_positions,
                'source': 'app',
                'diagnostics': diagnostics
            }), 200
        elif source_filter == 'external':
            return jsonify({
                'positions': external_positions,
                'source': 'external',
                'diagnostics': diagnostics
            }), 200
        else:
            return jsonify({
                'app_positions': app_positions,
                'external_positions': external_positions,
                'source': 'all',
                'diagnostics': diagnostics
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching active position: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        try:
            user_id = get_jwt_identity()
        except:
            user_id = None
        return jsonify({
            'error': f'Internal server error: {str(e)}',
            'diagnostics': {
                'user_id': user_id,
                'endpoint': '/active_position'
            }
        }), 500

@trading_bp.route('/history', methods=['GET'])
@jwt_required()
def get_trading_history():
    """Return the user's trading history - only app-initiated trades."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)

        # Get only trades initiated by our app (source='app')
        trades_query = Trade.query.filter(
            Trade.user_id == user_id,
            Trade.source == 'app'  # Only app-initiated trades
        ).order_by(Trade.entry_time.desc())

        # Paginate results
        trades = trades_query.paginate(page=page, per_page=per_page, error_out=False)

        # Format trade data
        trades_data = []
        for trade in trades.items:
            trade_data = {
                'id': trade.id,
                'date': trade.entry_time.strftime('%Y-%m-%d'),
                'symbol': trade.symbol,
                'side': trade.side,
                'size': float(trade.quantity),
                'entry': float(trade.entry_price),
                'exit': float(trade.exit_price) if trade.exit_price else None,
                'pnl': float(trade.pnl) if trade.pnl else None,
                'status': trade.status,
                'source': trade.source,
                'exit_time': trade.exit_time.isoformat() if trade.exit_time else None,
                'profit_share_applicable': trade.pnl and float(trade.pnl) > 0  # Only positive PnL
            }
            trades_data.append(trade_data)

        return jsonify({
            'trades': trades_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': trades.total,
                'pages': trades.pages,
                'has_next': trades.has_next,
                'has_prev': trades.has_prev
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting trading history: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

# --- TIER MANAGEMENT ENDPOINTS ---



@trading_bp.route('/tier/status', methods=['GET'])
@jwt_required()
def get_tier_status():
    """Get user's tier status and profit share information with membership validation."""
    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"[TIER_STATUS] Checking status for user_id: {user_id}")

        user = User.query.get(user_id)
        if not user:
            current_app.logger.warning(f"[TIER_STATUS] User not found: {user_id}")
            return jsonify({'error': 'User not found'}), 404

        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        if not tier_status:
            current_app.logger.info(f"[TIER_STATUS] Creating default tier status for new user: {user_id}")
            # Create default tier status for new user (Tier 1)
            tier_status = UserTierStatus(
                user_id=user_id,
                tier_1=True,
                tier_2=False,
                tier_3=False
            )
            db.session.add(tier_status)
            db.session.commit()
            current_app.logger.info(f"[TIER_STATUS] Created default tier status for user: {user_id}")

        # Validate membership status for Tier 2 users
        from app.services.membership_service import MembershipService
        membership_validation = MembershipService.validate_user_membership(user_id)

        # If Tier 2 membership expired, auto-downgrade
        if membership_validation.get('should_downgrade'):
            current_app.logger.info(f"[TIER_STATUS] Auto-downgrading user {user_id} due to expired Tier 2 membership")
            tier_status.tier_1 = True
            tier_status.tier_2 = False
            tier_status.tier_3 = False
            tier_status.updated_at = datetime.utcnow()
            db.session.commit()

        subscription = Subscription.query.filter_by(user_id=user_id).first()
        if not subscription:
            current_app.logger.info(f"[TIER_STATUS] Creating default subscription for new user: {user_id}")
            # Create default Tier 1 subscription for new user
            subscription = Subscription(user_id=user_id)
            db.session.add(subscription)
            db.session.commit()
            current_app.logger.info(f"[TIER_STATUS] Created default subscription for user: {user_id}")

        # Get tier value, handling both SubscriptionTier enum and raw int
        subscription_tier = subscription.tier.value if subscription.tier is not None and hasattr(subscription.tier, 'value') else subscription.tier

        response = {
            'tier': tier_status.get_current_tier() if hasattr(tier_status, 'get_current_tier') else None,
            'tier_1': tier_status.tier_1,
            'tier_2': tier_status.tier_2,
            'tier_3': tier_status.tier_3,
            'profit_share_owed': str(tier_status.profit_share_owed) if hasattr(tier_status, 'profit_share_owed') else "0",
            'status': 'active',
            'subscription': {
                'id': str(subscription.id),
                'tier': subscription_tier,
                'status': subscription.status.value if hasattr(subscription.status, 'value') else subscription.status,
                'created_at': subscription.created_at.isoformat() if hasattr(subscription, 'created_at') and subscription.created_at else None
            },
            'profit_share_rate': float(tier_status.get_profit_share_rate() * 100) if hasattr(tier_status, 'get_profit_share_rate') else None,
            'membership_validation': membership_validation
        }

        # Add detailed tier status information
        tier_status_dict = tier_status.to_dict()
        response.update({
            'tier_status': tier_status_dict
        })

        current_app.logger.info(f"[TIER_STATUS] Response for user {user_id}:")
        current_app.logger.info(f"  - profit_share_owed: {response['profit_share_owed']} (type: {type(response['profit_share_owed'])})")
        current_app.logger.info(f"  - tier_status.profit_share_owed: {tier_status.profit_share_owed} (type: {type(tier_status.profit_share_owed)})")
        current_app.logger.info(f"  - tier_status.payment_status: {tier_status.payment_status}")
        current_app.logger.info(f"  - tier_status.account_disabled: {tier_status.account_disabled}")
        return jsonify(response), 200

    except Exception as e:
        current_app.logger.error(f"[TIER_STATUS] Error getting tier status: {str(e)}", exc_info=True)
        return jsonify({'error': 'Internal server error', 'details': str(e)}), 500

# @trading_bp.route('/tier/update', methods=['POST'])
# @jwt_required()
# def update_tier():
#     """Update user's tier with proper subscription handling."""
#     try:
#         user_id = get_jwt_identity()
#         user = User.query.get(user_id)
#
#         if not user:
#             return jsonify({'error': 'User not found'}), 404
#
#         data = request.get_json()
#         if not data or 'tier' not in data:
#             return jsonify({'error': 'Missing tier field'}), 400
#
#         new_tier = int(data['tier'])
#         if new_tier not in [1, 2, 3]:
#             return jsonify({'error': 'Invalid tier. Must be 1, 2, or 3'}), 400
#
#         # Start a database transaction
#         db.session.begin_nested()
#
#         try:
#             from app.models.user_tier_status import UserTierStatus
#             from app.models.subscription import Subscription, SubscriptionStatus
#
#             # Get or create user tier status
#             tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
#             current_tier = tier_status.tier if tier_status else 1
#
#             # Create or update tier status
#             if not tier_status:
#                 tier_status = UserTierStatus(
#                     user_id=user_id,
#                     tier_1=(new_tier == 1),
#                     tier_2=(new_tier == 2),
#                     tier_3=(new_tier == 3)
#                 )
#                 db.session.add(tier_status)
#             else:
#                 # Only update if tier is changing
#                 if not getattr(tier_status, f"tier_{new_tier}"):
#                     tier_status.tier_1 = (new_tier == 1)
#                     tier_status.tier_2 = (new_tier == 2)
#                     tier_status.tier_3 = (new_tier == 3)
#                     tier_status.updated_at = datetime.utcnow()
#
#             # Handle subscription for paid tiers (2 and 3)
#             if new_tier in [2, 3]:
#                 # Check for existing active subscription
#                 subscription = Subscription.query.filter_by(
#                     user_id=user_id,
#                     status=SubscriptionStatus.ACTIVE
#                 ).first()
#
#                 # If no active subscription or changing tiers, create a new one
#                 if not subscription or subscription.tier != new_tier:
#                     # End any existing subscription
#                     if subscription:
#                         subscription.status = SubscriptionStatus.CANCELED
#                         subscription.ended_at = datetime.utcnow()
#
#                     # Create new subscription
#                     new_subscription = Subscription(user_id=user_id)
#                     new_subscription._tier = new_tier
#                     new_subscription.status = SubscriptionStatus.ACTIVE
#                     new_subscription.current_period_start = datetime.utcnow()
#                     new_subscription.current_period_end = datetime.utcnow() + timedelta(days=30)
#                     db.session.add(new_subscription)
#
#             # For free tier, cancel any active subscriptions
#             elif new_tier == 1 and tier_status and tier_status.tier > 1:
#                 subscription = Subscription.query.filter_by(
#                     user_id=user_id,
#                     status=SubscriptionStatus.ACTIVE
#                 ).first()
#
#                 if subscription:
#                     subscription.status = SubscriptionStatus.CANCELED
#                     subscription.ended_at = datetime.utcnow()
#
#             db.session.commit()
#
#             # Get updated tier status with relationships
#             tier_status = UserTierStatus.query.get(tier_status.id)
#
#             return jsonify({
#                 'message': 'Tier updated successfully',
#                 'tier_status': tier_status.to_dict()
#             }), 200
#
#         except Exception as e:
#             db.session.rollback()
#             current_app.logger.error(f"Error in tier update transaction: {str(e)}", exc_info=True)
#             return jsonify({
#                 'error': 'Failed to update tier',
#                 'details': str(e)
#             }), 500
#
#     except Exception as e:
#         current_app.logger.error(f"Error in update_tier endpoint: {str(e)}", exc_info=True)
#         return jsonify({
#             'error': 'Internal server error',
#             'details': str(e)
#         }), 500

@trading_bp.route('/tier/payment', methods=['POST'])
@jwt_required()
def make_tier_payment():
    """Make a payment towards profit share debt."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data or 'amount' not in data:
            return jsonify({'error': 'Missing amount field'}), 400
        
        payment_amount = float(data['amount'])
        if payment_amount <= 0:
            return jsonify({'error': 'Payment amount must be positive'}), 400
        
        from app.models.user_tier_status import UserTierStatus
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        
        if not tier_status:
            return jsonify({'error': 'Tier status not found'}), 404
        
        # Process payment (this would integrate with actual payment processor)
        tier_status.clear_debt(payment_amount)
        
        # Check if this is a daily payment for discount eligibility
        is_daily_payment = data.get('daily_payment', False)
        if is_daily_payment:
            tier_status.make_daily_payment()
        
        db.session.commit()
        
        # Log payment
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='tier_payment_made',
            ip_address=request.remote_addr,
            details={
                'amount': payment_amount,
                'remaining_debt': float(tier_status.profit_share_owed),
                'daily_payment': is_daily_payment
            },
            risk_level='medium'
        )
        
        return jsonify({
            'message': 'Payment processed successfully',
            'tier_status': tier_status.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error processing tier payment: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/tier/daily-payment', methods=['POST'])
@jwt_required()
def make_daily_payment():
    """Mark daily payment as made for discount eligibility."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        from app.models.user_tier_status import UserTierStatus
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        
        if not tier_status:
            return jsonify({'error': 'Tier status not found'}), 404
        
        tier_status.make_daily_payment()
        db.session.commit()
        
        return jsonify({
            'message': 'Daily payment recorded successfully',
            'tier_status': tier_status.to_dict()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error recording daily payment: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@trading_bp.route('/payday/initialize', methods=['POST'])
@jwt_required()
def initialize_payday_system():
    """Initialize payday deadlines for all users."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # For now, allow any authenticated user to initialize
        # In production, you might want to restrict this to admins
        from app.services.payday_service import PaydayService

        initialized_count = PaydayService.initialize_payday_deadlines()

        return jsonify({
            'message': f'Payday system initialized for {initialized_count} users',
            'initialized_count': initialized_count
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error initializing payday system: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@trading_bp.route('/payday/status', methods=['GET'])
@jwt_required()
def get_payday_status():
    """Get payday status for current user."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        from app.models.user_tier_status import UserTierStatus
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()

        if not tier_status:
            return jsonify({'error': 'Tier status not found'}), 404

        # Initialize payday deadline if not set
        if not tier_status.next_payday_deadline:
            tier_status.update_payday_deadline()
            db.session.commit()



        return jsonify({
            'next_payday_deadline': tier_status.next_payday_deadline.isoformat() if tier_status.next_payday_deadline else None,
            'account_disabled': tier_status.account_disabled,
            'disabled_at': tier_status.disabled_at.isoformat() if tier_status.disabled_at else None,
            'profit_share_owed': float(tier_status.profit_share_owed),
            'payment_status': tier_status.payment_status,
            'is_past_payday_deadline': tier_status.is_past_payday_deadline(),
            'should_disable_account': tier_status.should_disable_account(),
            'needs_payday_warning': tier_status.needs_payday_warning(),
            'payday_warning_sent': tier_status.payday_warning_sent
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting payday status: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


# --- PROFIT SHARE ENDPOINTS ---

@trading_bp.route('/profit-share/summary', methods=['GET'])
@jwt_required()
def get_profit_share_summary():
    """Get comprehensive profit share summary for user."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Try to get data from service first
        try:
            from app.services.profit_share_service import ProfitShareService
            summary = ProfitShareService.get_profit_share_summary(user_id)

            if summary:
                return jsonify(summary), 200
        except Exception as service_error:
            current_app.logger.warning(f"Profit share service failed: {str(service_error)}")

        # Fallback: Create default summary if service data doesn't exist
        tier_status = getattr(user, 'tier_status', None)
        current_tier = 1
        if tier_status and hasattr(tier_status, 'get_current_tier'):
            current_tier = tier_status.get_current_tier()

        profit_share_rate = user.get_profit_share_rate() if hasattr(user, 'get_profit_share_rate') else 0.3

        # Try to create balance tracker with real exchange balance
        try:
            from app.models.security_log import APICredential
            from app.services.exchange_service import get_exchange_service

            credentials = APICredential.query.filter_by(
                user_id=user_id,
                is_active=True,
                is_valid=True
            ).first()

            if credentials:
                creds = credentials.get_credentials()
                if creds:
                    exchange_service = get_exchange_service(
                        credentials.exchange.value,
                        creds['api_key'],
                        creds['secret_key'],
                        creds.get('passphrase')
                    )
                    current_balance = exchange_service.get_balance()

                    # Create balance tracker with real exchange balance
                    from app.services.profit_share_service import ProfitShareService
                    tracker = ProfitShareService.get_or_create_balance_tracker(user_id, float(current_balance))

                    if tracker:
                        # Try to get summary again now that tracker exists
                        summary = ProfitShareService.get_profit_share_summary(user_id)
                        if summary:
                            current_app.logger.info(f"[PROFIT_TRACKING] Created tracker and returned real data for user {user_id}")
                            return jsonify(summary), 200

                    current_app.logger.info(f"[PROFIT_TRACKING] Created balance tracker for user {user_id} with real balance {current_balance} USDT")

                    # Return summary with real balance data
                    real_summary = {
                        'tier_info': {
                            'current_tier': current_tier,
                            'profit_share_rate': profit_share_rate,
                            'profit_share_owed': 0.0,
                            'payment_status': 'paid'
                        },
                        'profit_analysis': {
                            'initial_balance': float(current_balance),
                            'current_balance': float(current_balance),
                            'net_deposits': 0.0,
                            'trading_profit': 0.0,
                            'true_profit': 0.0,
                            'profit_percentage': 0.0,
                            'is_profitable': False
                        }
                    }

                    return jsonify(real_summary), 200

        except Exception as balance_error:
            current_app.logger.error(f"Error getting real balance for profit share: {str(balance_error)}")

        # Final fallback with warning
        current_app.logger.warning(f"[PROFIT_TRACKING] Using fallback mock data for user {user_id} - no API credentials or balance fetch failed")

        # Default summary for users without tracking data (fallback only)
        default_summary = {
            'tier_info': {
                'current_tier': current_tier,
                'profit_share_rate': profit_share_rate,
                'profit_share_owed': 0.0,
                'payment_status': 'paid'
            },
            'profit_analysis': {
                'initial_balance': 0.0,  # Changed from 1000.0 to 0.0 to indicate no data
                'current_balance': 0.0,
                'net_deposits': 0.0,
                'trading_profit': 0.0,
                'true_profit': 0.0,
                'profit_percentage': 0.0,
                'is_profitable': False
            }
        }

        return jsonify(default_summary), 200

    except Exception as e:
        current_app.logger.error(f"Error getting profit share summary: {str(e)}")
        import traceback
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500


@trading_bp.route('/profit-share/initialize', methods=['POST'])
@jwt_required()
def initialize_profit_tracking():
    """Initialize profit tracking for user with starting balance."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        initial_balance = data.get('initial_balance')
        if not initial_balance or initial_balance <= 0:
            return jsonify({'error': 'Valid initial balance required'}), 400

        from app.services.profit_share_service import ProfitShareService
        tracker = ProfitShareService.get_or_create_balance_tracker(user_id, initial_balance)

        if not tracker:
            return jsonify({'error': 'Failed to create balance tracker'}), 500

        return jsonify({
            'message': 'Profit tracking initialized successfully',
            'tracker': tracker.to_dict()
        }), 201

    except Exception as e:
        current_app.logger.error(f"Error initializing profit tracking: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@trading_bp.route('/profit-share/update-balance', methods=['POST'])
@jwt_required()
def update_balance_from_trade():
    """Update balance from trade result and calculate profit share."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        trade_pnl = data.get('trade_pnl')
        trade_id = data.get('trade_id')

        if trade_pnl is None:
            return jsonify({'error': 'Trade PnL required'}), 400

        from app.services.profit_share_service import ProfitShareService
        result = ProfitShareService.update_balance_from_trade(user_id, trade_pnl, trade_id)

        if not result:
            return jsonify({'error': 'Failed to update balance'}), 500

        return jsonify({
            'message': 'Balance updated successfully',
            'result': result
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error updating balance from trade: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@trading_bp.route('/wallet/disconnect', methods=['POST'])
@jwt_required()
def handle_wallet_disconnect():
    """Handle wallet disconnection and tier updates."""
    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"[WALLET_DISCONNECT] Processing disconnect for user_id: {user_id}")

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        if not tier_status:
            return jsonify({'error': 'Tier status not found'}), 404

        # Handle Tier 3 users - always downgrade to Tier 1 when wallet disconnects
        if tier_status.tier_3:
            current_app.logger.info(f"[WALLET_DISCONNECT] Downgrading Tier 3 user {user_id} to Tier 1")
            tier_status.tier_1 = True
            tier_status.tier_2 = False
            tier_status.tier_3 = False
            tier_status.updated_at = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'success': True,
                'action': 'downgraded',
                'new_tier': 1,
                'message': 'Downgraded to Starter tier. Reconnect wallet and verify NFT ownership to regain Tier 3 access.'
            }), 200

        # Handle Tier 2 users - check membership validity
        elif tier_status.tier_2:
            from app.services.membership_service import MembershipService
            membership_validation = MembershipService.validate_user_membership(user_id)

            if membership_validation.get('should_downgrade'):
                current_app.logger.info(f"[WALLET_DISCONNECT] Downgrading Tier 2 user {user_id} due to expired membership")
                tier_status.tier_1 = True
                tier_status.tier_2 = False
                tier_status.tier_3 = False
                tier_status.updated_at = datetime.utcnow()
                db.session.commit()

                return jsonify({
                    'success': True,
                    'action': 'downgraded',
                    'new_tier': 1,
                    'message': 'Tier 2 membership expired. Downgraded to Starter tier.'
                }), 200
            else:
                return jsonify({
                    'success': True,
                    'action': 'preserved',
                    'new_tier': 2,
                    'message': f"Tier 2 access preserved. {membership_validation.get('days_remaining', 0)} days remaining.",
                    'days_remaining': membership_validation.get('days_remaining', 0)
                }), 200

        # Tier 1 users - no action needed
        else:
            return jsonify({
                'success': True,
                'action': 'no_change',
                'new_tier': 1,
                'message': 'Wallet disconnected successfully.'
            }), 200

    except Exception as e:
        current_app.logger.error(f"[WALLET_DISCONNECT] Error: {str(e)}")
        return jsonify({'error': 'Failed to process wallet disconnect'}), 500

# --- AUTO-TRADING CONTROL ENDPOINTS ---

@trading_bp.route('/auto-trade/toggle', methods=['POST'])
@jwt_required()
def toggle_auto_trading():
    """Toggle auto-trading for user"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data or 'enabled' not in data:
            return jsonify({'message': 'enabled field is required'}), 400

        enabled = bool(data.get('enabled', False))

        # Get user
        user = User.query.get(user_id)
        if not user:
            return jsonify({'message': 'User not found'}), 404

        # If trying to enable auto-trading, validate minimum balance requirement
        if enabled:
            # Check if user has valid API credentials
            credentials = APICredential.query.filter_by(
                user_id=user_id,
                is_active=True,
                is_valid=True
            ).first()

            if not credentials:
                return jsonify({
                    'message': 'Cannot enable auto-trading: No valid API credentials found. Please add and validate your exchange credentials first.',
                    'error_code': 'missing_credentials',
                    'auto_trading_enabled': False
                }), 400

            # Get real account balance to validate minimum requirement
            try:
                from app.services.exchange_service import get_exchange_service

                creds = credentials.get_credentials()
                if not creds:
                    return jsonify({
                        'message': 'Cannot enable auto-trading: Failed to decrypt credentials. Please update your API credentials.',
                        'error_code': 'decryption_error',
                        'auto_trading_enabled': False
                    }), 400

                exchange_service = get_exchange_service(
                    credentials.exchange.value,
                    creds['api_key'],
                    creds['secret_key'],
                    creds.get('passphrase')
                )

                # Get real account balance
                balance = exchange_service.get_balance()

                if balance is None:
                    return jsonify({
                        'message': 'Cannot enable auto-trading: Failed to retrieve balance from exchange. Please check your API credentials.',
                        'error_code': 'balance_check_failed',
                        'auto_trading_enabled': False
                    }), 400

                # Check minimum balance requirement (100 USDT)
                MIN_BALANCE_REQUIRED = 100.0
                if float(balance) < MIN_BALANCE_REQUIRED:
                    current_app.logger.info(f"[AUTO_TRADING] Balance validation failed for user {user_id}: {balance} USDT < {MIN_BALANCE_REQUIRED} USDT (minimum required)")
                    return jsonify({
                        'message': f'Cannot enable auto-trading: Minimum balance of {MIN_BALANCE_REQUIRED} USDT required. Current balance: {float(balance):.2f} USDT',
                        'error_code': 'insufficient_balance',
                        'current_balance': float(balance),
                        'minimum_required': MIN_BALANCE_REQUIRED,
                        'auto_trading_enabled': False
                    }), 400

                current_app.logger.info(f"[AUTO_TRADING] Balance validation passed for user {user_id}: {balance} USDT >= {MIN_BALANCE_REQUIRED} USDT")

            except Exception as balance_error:
                current_app.logger.error(f"Error checking balance for auto-trading toggle: {str(balance_error)}")
                return jsonify({
                    'message': 'Cannot enable auto-trading: Failed to verify account balance. Please check your API credentials and try again.',
                    'error_code': 'balance_verification_failed',
                    'auto_trading_enabled': False
                }), 400

        # Initialize profit tracking when enabling auto-trading
        if enabled:
            from app.services.profit_share_service import ProfitShareService
            from app.models.user_balance_tracker import UserBalanceTracker

            tracker = UserBalanceTracker.query.filter_by(user_id=user_id).first()
            if not tracker:
                # Get current exchange balance to use as initial balance
                try:
                    from app.services.exchange_service import get_exchange_service
                    credentials = APICredential.query.filter_by(
                        user_id=user_id,
                        is_active=True,
                        is_valid=True
                    ).first()

                    if credentials:
                        creds = credentials.get_credentials()
                        if creds:
                            exchange_service = get_exchange_service(
                                credentials.exchange.value,
                                creds['api_key'],
                                creds['secret_key'],
                                creds.get('passphrase')
                            )
                            current_balance = exchange_service.get_balance()

                            # Create balance tracker with current exchange balance
                            tracker = ProfitShareService.get_or_create_balance_tracker(user_id, float(current_balance))
                            current_app.logger.info(f"[PROFIT_TRACKING] Created balance tracker for user {user_id} with initial balance {current_balance} USDT")
                    else:
                        # Fallback to default initial balance if no credentials
                        tracker = ProfitShareService.get_or_create_balance_tracker(user_id, 1000.0)
                        current_app.logger.warning(f"[PROFIT_TRACKING] Created balance tracker for user {user_id} with default initial balance (no credentials found)")

                except Exception as tracker_error:
                    current_app.logger.error(f"Error creating balance tracker for user {user_id}: {str(tracker_error)}")
                    # Continue without tracker - user can initialize manually later
            else:
                current_app.logger.info(f"[PROFIT_TRACKING] Balance tracker already exists for user {user_id}")

        # Update auto-trading preference
        user.auto_trading_enabled = enabled
        db.session.commit()

        # Log the change
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='auto_trading_toggled',
            ip_address=request.remote_addr,
            details={'enabled': enabled},
            risk_level='medium'
        )

        current_app.logger.info(f"[AUTO_TRADING] User {user_id} {'enabled' if enabled else 'disabled'} auto-trading")

        # Prepare response message
        if enabled:
            message = 'Auto-trading enabled successfully. Balance requirement verified.'
        else:
            message = 'Auto-trading disabled successfully.'

        return jsonify({
            'message': message,
            'auto_trading_enabled': enabled
        }), 200

    except Exception as e:
        current_app.logger.error(f"Toggle auto-trading failed: {str(e)}")
        return jsonify({'message': 'Failed to toggle auto-trading', 'error': str(e)}), 500


@trading_bp.route('/auto-trade/status', methods=['GET'])
@jwt_required()
def get_auto_trading_status():
    """Get current auto-trading status"""
    try:
        user_id = get_jwt_identity()

        # Get user
        user = User.query.get(user_id)
        if not user:
            return jsonify({'message': 'User not found'}), 404

        return jsonify({
            'auto_trading_enabled': user.auto_trading_enabled,
            'user_id': user_id
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get auto-trading status failed: {str(e)}")
        return jsonify({'message': 'Failed to get auto-trading status', 'error': str(e)}), 500

# --- PER-EXCHANGE AUTO-TRADING ENDPOINTS ---

@trading_bp.route('/credentials/<credential_id>/auto-trading', methods=['GET'])
@jwt_required()
def get_credential_auto_trading_status(credential_id):
    """Get auto-trading status for specific API credential"""
    try:
        user_id = get_jwt_identity()

        # Get credential and verify ownership
        credential = APICredential.query.filter_by(
            id=credential_id,
            user_id=user_id
        ).first()

        if not credential:
            return jsonify({'error': 'Credential not found'}), 404

        # For now, return the user's global auto-trading status
        # Later we can add per-credential auto-trading if needed
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({
            'enabled': user.auto_trading_enabled and credential.is_active and credential.is_valid,
            'credential_id': credential_id,
            'exchange': credential.exchange.value
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get credential auto-trading status failed: {str(e)}")
        return jsonify({'error': 'Failed to get auto-trading status'}), 500


@trading_bp.route('/credentials/<credential_id>/auto-trading', methods=['POST'])
@jwt_required()
def toggle_credential_auto_trading(credential_id):
    """Toggle auto-trading for specific API credential"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data or 'enabled' not in data:
            return jsonify({'error': 'enabled field is required'}), 400

        enabled = bool(data.get('enabled', False))

        # Get credential and verify ownership
        credential = APICredential.query.filter_by(
            id=credential_id,
            user_id=user_id
        ).first()

        if not credential:
            return jsonify({'error': 'Credential not found'}), 404

        # Get user
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # For now, update the user's global auto-trading status
        # This ensures consistency between Dashboard and ApiCredentials
        user.auto_trading_enabled = enabled
        db.session.commit()

        # Log the change
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='credential_auto_trading_toggled',
            ip_address=request.remote_addr,
            details={
                'enabled': enabled,
                'credential_id': credential_id,
                'exchange': credential.exchange.value
            },
            risk_level='medium'
        )

        current_app.logger.info(f"[AUTO_TRADING] User {user_id} {'enabled' if enabled else 'disabled'} auto-trading for {credential.exchange.value}")

        return jsonify({
            'message': f'Auto-trading {"enabled" if enabled else "disabled"} for {credential.exchange.value}',
            'enabled': enabled,
            'credential_id': credential_id,
            'exchange': credential.exchange.value
        }), 200

    except Exception as e:
        current_app.logger.error(f"Toggle credential auto-trading failed: {str(e)}")
        return jsonify({'error': 'Failed to toggle auto-trading'}), 500


@trading_bp.route('/auto-trade/notifications', methods=['GET'])
@jwt_required()
def get_auto_trading_notifications():
    """Get recent auto-trading notifications for user"""
    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"Getting auto-trade notifications for user: {user_id}")

        user = User.query.get(user_id)
        current_app.logger.info(f"User found: {user is not None}")

        if not user:
            current_app.logger.warning(f"User not found: {user_id}")
            return jsonify({'error': 'User not found'}), 404

        # Generate notifications based on current user state
        notifications = []
        current_time = datetime.utcnow().isoformat() + 'Z'

        current_app.logger.info(f"User investment_percentage: {user.investment_percentage}")

        # Check for API credentials
        api_credentials = APICredential.query.filter_by(user_id=user_id, is_active=True, is_valid=True).first()
        has_api_credentials = api_credentials is not None
        current_app.logger.info(f"User has valid API credentials: {has_api_credentials}")

        # Check if user has any conditions that would disable auto-trading
        if user.investment_percentage is None or user.investment_percentage == 0:
            current_app.logger.info("Adding investment percentage notification")
            notifications.append({
                'id': f'balance_{user_id}',
                'reason': 'Investment percentage is set to 0%. Please adjust your risk settings.',
                'timestamp': current_time,
                'risk_level': 'warning'
            })

        if not has_api_credentials:
            current_app.logger.info("Adding API key notification")
            notifications.append({
                'id': f'api_{user_id}',
                'reason': 'No exchange API credentials configured. Please add your API keys.',
                'timestamp': current_time,
                'risk_level': 'error'
            })

        current_app.logger.info(f"Returning {len(notifications)} notifications")
        return jsonify({
            'notifications': notifications,
            'count': len(notifications)
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get auto-trading notifications failed: {str(e)}")
        import traceback
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Failed to get notifications'}), 500

# --- RISK SETTINGS ENDPOINTS ---

@trading_bp.route('/risk-settings', methods=['GET'])
@jwt_required()
def get_risk_settings():
    """Get user's risk management settings with tier and exchange limits."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get user's current tier
        tier_status = getattr(user, 'tier_status', None)
        current_tier = 1
        if tier_status and hasattr(tier_status, 'get_current_tier'):
            current_tier = tier_status.get_current_tier()

        # Get the correct default account type for the exchange
        default_account_types = {
            'binance': 'FUTURES',
            'binance_us': 'SPOT_MARGIN',
            'kraken': 'FUTURES',
            'bitso': 'SPOT'
        }

        # Use user's account type or default for their exchange
        account_type = user.account_type
        if account_type == 'SPOT' and user.selected_exchange in ['binance', 'kraken']:
            # Auto-correct to proper account type for futures exchanges
            account_type = default_account_types.get(user.selected_exchange, 'SPOT')

        # Calculate max leverage for current exchange and account type
        max_leverage_for_exchange = user.get_max_leverage_for_exchange(
            user.selected_exchange,
            account_type
        )

        # Get tier-based max leverage (without exchange limits)
        tier_limits = {1: 3.0, 2: 5.0, 3: 10.0}
        max_leverage_for_tier = tier_limits.get(current_tier, 1.0)

        return jsonify({
            'investment_percentage': float(user.investment_percentage),
            'leverage': float(user.leverage_multiplier),
            'max_leverage_for_tier': max_leverage_for_tier,
            'max_leverage_for_exchange': max_leverage_for_exchange,
            'current_tier': current_tier,
            'exchange': user.selected_exchange,
            'account_type': account_type,  # Use corrected account type
            'is_configured': user.risk_settings_configured
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get risk settings failed: {str(e)}")
        return jsonify({'error': 'Failed to get risk settings'}), 500


@trading_bp.route('/risk-settings', methods=['POST'])
@jwt_required()
def save_risk_settings():
    """Save user's risk management settings with validation."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['investment_percentage', 'leverage', 'exchange', 'account_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        investment_percentage = float(data['investment_percentage'])
        leverage = float(data['leverage'])
        exchange = data['exchange'].lower()
        account_type = data['account_type'].upper()

        # Validate investment percentage (0-10%)
        if not (0 <= investment_percentage <= 10):
            return jsonify({'error': 'Investment percentage must be between 0% and 10%'}), 400

        # Validate leverage with tier and exchange limits
        if not user.validate_leverage_setting(leverage, exchange, account_type):
            max_allowed = user.get_max_leverage_for_exchange(exchange, account_type)
            return jsonify({
                'error': f'Leverage {leverage}x exceeds maximum allowed {max_allowed}x for your tier and exchange'
            }), 400

        # Validate exchange
        supported_exchanges = ['binance', 'binance_us', 'kraken', 'bitso']
        if exchange not in supported_exchanges:
            return jsonify({'error': f'Unsupported exchange: {exchange}'}), 400

        # Validate account type for specific exchanges
        exchange_valid_account_types = {
            'binance': ['FUTURES'],  # Binance only supports Futures
            'binance_us': ['SPOT', 'SPOT_MARGIN'],  # Binance US supports Spot and Spot Margin
            'kraken': ['FUTURES'],  # Kraken only supports Futures
            'bitso': ['SPOT']  # Bitso only supports Spot
        }

        valid_types_for_exchange = exchange_valid_account_types.get(exchange, ['SPOT'])
        if account_type not in valid_types_for_exchange:
            return jsonify({
                'error': f'Invalid account type {account_type} for {exchange}. Valid types: {valid_types_for_exchange}'
            }), 400

        # Update user settings
        user.investment_percentage = investment_percentage
        user.leverage_multiplier = leverage
        user.selected_exchange = exchange
        user.account_type = account_type
        user.risk_settings_configured = True
        user.updated_at = datetime.utcnow()

        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='risk_settings_updated',
            ip_address=request.remote_addr,
            details={
                'investment_percentage': investment_percentage,
                'leverage': leverage,
                'exchange': exchange,
                'account_type': account_type
            },
            risk_level='low'
        )

        return jsonify({
            'message': 'Risk settings saved successfully',
            'settings': {
                'investment_percentage': float(user.investment_percentage),
                'leverage': float(user.leverage_multiplier),
                'exchange': user.selected_exchange,
                'account_type': user.account_type,
                'is_configured': user.risk_settings_configured
            }
        }), 200

    except ValueError as e:
        return jsonify({'error': f'Invalid numeric value: {str(e)}'}), 400
    except Exception as e:
        current_app.logger.error(f"Save risk settings failed: {str(e)}")
        return jsonify({'error': 'Failed to save risk settings'}), 500

# --- END OF PLACEHOLDER ENDPOINTS ---