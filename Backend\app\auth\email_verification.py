import uuid
from datetime import datetime, timedelta
from flask import current_app, url_for, render_template
from app.models.user import User
from app import db
from app.utils.email_service import send_email

class EmailVerification:
    @staticmethod
    def generate_verification_token():
        """Generate a unique verification token."""
        return str(uuid.uuid4())
    
    @staticmethod
    def send_verification_email(user):
        """Send verification email to user."""
        # Generate token using User model method
        token = user.generate_verification_token()
        db.session.commit()

        # Create verification URL
        frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:5174')
        verification_url = f"{frontend_url}/verify-email?token={token}"

        # Brevo-optimized email template with professional design
        subject = "Verify Your Email Address - DeepTrade"
        body = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <title>Email Verification - DeepTrade</title>
            <!--[if mso]>
            <noscript>
                <xml>
                    <o:OfficeDocumentSettings>
                        <o:PixelsPerInch>96</o:PixelsPerInch>
                    </o:OfficeDocumentSettings>
                </xml>
            </noscript>
            <![endif]-->
            <style>
                /* Reset styles */
                body, table, td, p, a, li, blockquote {{ -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }}
                table, td {{ mso-table-lspace: 0pt; mso-table-rspace: 0pt; }}
                img {{ -ms-interpolation-mode: bicubic; }}

                /* Base styles */
                body {{ margin: 0; padding: 0; width: 100% !important; min-width: 100%; background-color: #f4f4f4; }}
                table {{ border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; }}
                img {{ border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; }}

                /* Container styles */
                .email-container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }}
                .header h1 {{ color: #ffffff; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 28px; font-weight: 300; margin: 0; }}
                .content {{ padding: 40px 30px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; }}
                .content h2 {{ color: #333333; font-size: 24px; font-weight: 400; margin: 0 0 20px 0; }}
                .content p {{ color: #666666; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0; }}

                /* Button styles */
                .button-container {{ text-align: center; margin: 30px 0; }}
                .button {{ display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff !important; text-decoration: none; border-radius: 6px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 16px; font-weight: 600; text-align: center; }}
                .button:hover {{ background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%); color: #ffffff !important; }}

                /* Footer styles */
                .footer {{ background-color: #f8f9fa; padding: 30px 20px; text-align: center; border-top: 1px solid #e9ecef; }}
                .footer p {{ color: #6c757d; font-size: 14px; margin: 0 0 10px 0; }}

                /* Security notice */
                .security-notice {{ background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0; }}
                .security-notice p {{ color: #856404; font-size: 14px; margin: 0; }}

                /* Responsive styles */
                @media only screen and (max-width: 600px) {{
                    .email-container {{ width: 100% !important; }}
                    .content {{ padding: 20px !important; }}
                    .header {{ padding: 30px 20px !important; }}
                    .header h1 {{ font-size: 24px !important; }}
                    .content h2 {{ font-size: 20px !important; }}
                    .button {{ padding: 14px 28px !important; font-size: 14px !important; }}
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <!-- Header -->
                <div class="header">
                    <h1>🚀 Welcome to DeepTrade!</h1>
                </div>

                <!-- Main Content -->
                <div class="content">
                    <h2>Verify Your Email Address</h2>
                    <p>Hello <strong>{user.full_name}</strong>,</p>
                    <p>Thank you for joining DeepTrade! You're just one step away from accessing our advanced trading platform with AI-powered insights.</p>

                    <p>To complete your registration and start your trading journey, please verify your email address:</p>

                    <div class="button-container">
                        <a href="{verification_url}" class="button">Verify Email Address</a>
                    </div>

                    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #667eea; font-size: 14px; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">{verification_url}</p>

                    <div class="security-notice">
                        <p><strong>⏰ Important:</strong> This verification link will expire in 24 hours for security reasons.</p>
                    </div>

                    <p>Once verified, you'll have access to:</p>
                    <ul style="color: #666666; padding-left: 20px;">
                        <li>AI-powered trading signals</li>
                        <li>Real-time market analysis</li>
                        <li>Automated Execution & Profit Tracking</li>
                        <li>Secure trading environment</li>
                    </ul>

                    <p>If you didn't create an account with DeepTrade, please ignore this email and no further action is required.</p>
                </div>

                <!-- Footer -->
                <div class="footer">
                    <p><strong>DeepTrade</strong> - Advanced AI Trading Platform</p>
                    <p>© 2025 DeepTrade. All rights reserved.</p>
                    <p>This is an automated email. Please do not reply to this message.</p>
                    <p style="font-size: 12px; color: #adb5bd;">Sent via Brevo • Trusted Email Delivery</p>
                </div>
            </div>
        </body>
        </html>
        """

        send_email(user.email, subject, body)
        current_app.logger.info(f"Verification email sent to {user.email} with token {token[:8]}...")

        return token
    
    @staticmethod
    def verify_email(token):
        """Verify user email with token."""
        user = User.query.filter_by(verification_token=token).first()

        if not user:
            return False, "Invalid verification token"

        # Use User model method for verification
        success, message = user.verify_email(token)
        if success:
            db.session.commit()
            current_app.logger.info(f"Email verified for user: {user.email}")

        return success, message