/**
 * Mobile Modal Components for DeepTrade
 * 
 * Provides mobile-optimized modal layouts with proper touch interactions,
 * swipe gestures, and responsive design patterns.
 */

import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { useMobile } from '@/hooks/useResponsiveDesign';
import { Button } from '@/components/ui/Button';

interface MobileModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'full';
  position?: 'center' | 'bottom' | 'top';
  closeOnOverlayClick?: boolean;
  showCloseButton?: boolean;
  className?: string;
}

interface MobileModalHeaderProps {
  title: string;
  onClose?: () => void;
  leftAction?: React.ReactNode;
  rightAction?: React.ReactNode;
  className?: string;
}

interface MobileModalContentProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

interface MobileModalFooterProps {
  children: React.ReactNode;
  className?: string;
  layout?: 'horizontal' | 'vertical' | 'stacked';
}

interface MobileBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  snapPoints?: number[];
  initialSnap?: number;
  className?: string;
}

const MobileModal: React.FC<MobileModalProps> = ({
  isOpen,
  onClose,
  children,
  title,
  size = 'md',
  position = 'center',
  closeOnOverlayClick = true,
  showCloseButton = true,
  className,
}) => {
  const { isMobile } = useMobile();
  const modalRef = useRef<HTMLDivElement>(null);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: isMobile ? "max-w-sm" : "max-w-md",
    md: isMobile ? "max-w-md" : "max-w-lg",
    lg: isMobile ? "max-w-lg" : "max-w-2xl",
    full: "w-full h-full max-w-none",
  };

  const positionClasses = {
    center: "items-center justify-center",
    bottom: "items-end justify-center",
    top: "items-start justify-center pt-20",
  };

  const modalClasses = {
    center: "rounded-xl",
    bottom: isMobile ? "rounded-t-2xl rounded-b-none" : "rounded-xl",
    top: "rounded-xl",
  };

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={closeOnOverlayClick ? onClose : undefined}
      />
      
      {/* Modal Container */}
      <div className={cn(
        "relative flex w-full",
        positionClasses[position],
        isMobile ? "p-4" : "p-6"
      )}>
        <div
          ref={modalRef}
          className={cn(
            "relative bg-white dark:bg-gray-800 shadow-2xl",
            "max-h-[90vh] overflow-hidden flex flex-col",
            sizeClasses[size],
            modalClasses[position],
            size === 'full' && isMobile ? "h-full" : "",
            className
          )}
        >
          {/* Header */}
          {(title || showCloseButton) && (
            <MobileModalHeader
              title={title || ''}
              onClose={showCloseButton ? onClose : undefined}
            />
          )}
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

const MobileModalHeader: React.FC<MobileModalHeaderProps> = ({
  title,
  onClose,
  leftAction,
  rightAction,
  className,
}) => {
  const { isMobile } = useMobile();

  return (
    <div className={cn(
      "flex items-center justify-between border-b border-gray-200 dark:border-gray-700",
      isMobile ? "px-4 py-4" : "px-6 py-4",
      className
    )}>
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {leftAction && (
          <div className="flex-shrink-0">
            {leftAction}
          </div>
        )}
        {title && (
          <h2 className={cn(
            "font-semibold text-gray-900 dark:text-white truncate",
            isMobile ? "text-lg" : "text-xl"
          )}>
            {title}
          </h2>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        {rightAction && (
          <div className="flex-shrink-0">
            {rightAction}
          </div>
        )}
        {onClose && (
          <Button
            variant="ghost"
            size={isMobile ? "mobile-icon" : "icon"}
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </Button>
        )}
      </div>
    </div>
  );
};

const MobileModalContent: React.FC<MobileModalContentProps> = ({
  children,
  className,
  padding = 'md',
}) => {
  const { isMobile } = useMobile();

  const paddingClasses = {
    none: "",
    sm: isMobile ? "p-3" : "p-4",
    md: isMobile ? "p-4" : "p-6",
    lg: isMobile ? "p-6" : "p-8",
  };

  return (
    <div className={cn(
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  );
};

const MobileModalFooter: React.FC<MobileModalFooterProps> = ({
  children,
  className,
  layout = 'horizontal',
}) => {
  const { isMobile } = useMobile();

  const layoutClasses = {
    horizontal: "flex space-x-3",
    vertical: "flex flex-col space-y-3",
    stacked: isMobile ? "flex flex-col space-y-3" : "flex space-x-3",
  };

  return (
    <div className={cn(
      "border-t border-gray-200 dark:border-gray-700",
      isMobile ? "p-4" : "p-6",
      layoutClasses[layout],
      className
    )}>
      {children}
    </div>
  );
};

const MobileBottomSheet: React.FC<MobileBottomSheetProps> = ({
  isOpen,
  onClose,
  children,
  title,
  snapPoints = [0.5, 0.9],
  initialSnap = 0,
  className,
}) => {
  const { isMobile } = useMobile();
  const sheetRef = useRef<HTMLDivElement>(null);
  const [currentSnap] = React.useState(snapPoints[initialSnap]);

  // Only render on mobile
  if (!isMobile) {
    return (
      <MobileModal
        isOpen={isOpen}
        onClose={onClose}
        title={title}
        position="center"
      >
        {children}
      </MobileModal>
    );
  }

  // Prevent body scroll when sheet is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Bottom Sheet */}
      <div
        ref={sheetRef}
        className={cn(
          "absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800",
          "rounded-t-2xl shadow-2xl transform transition-transform duration-300",
          "max-h-[90vh] overflow-hidden flex flex-col",
          className
        )}
        style={{ height: `${currentSnap * 100}vh` }}
      >
        {/* Handle */}
        <div className="flex justify-center py-3">
          <div className="w-10 h-1 bg-gray-300 dark:bg-gray-600 rounded-full" />
        </div>
        
        {/* Header */}
        {title && (
          <div className="px-4 pb-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white text-center">
              {title}
            </h2>
          </div>
        )}
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

// Mobile-specific toast/notification component
interface MobileToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
  position?: 'top' | 'bottom';
}

const MobileToast: React.FC<MobileToastProps> = ({
  message,
  type = 'info',
  isVisible,
  onClose,
  duration = 4000,
  position = 'top',
}) => {
  const { isMobile } = useMobile();

  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  if (!isVisible) return null;

  const typeClasses = {
    success: "bg-green-600 text-white",
    error: "bg-red-600 text-white",
    warning: "bg-yellow-600 text-white",
    info: "bg-blue-600 text-white",
  };

  const positionClasses = {
    top: "top-4",
    bottom: "bottom-4",
  };

  return (
    <div className={cn(
      "fixed left-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg",
      "transform transition-all duration-300 ease-in-out",
      typeClasses[type],
      positionClasses[position],
      isVisible ? "translate-y-0 opacity-100" : "translate-y-2 opacity-0"
    )}>
      <div className="flex items-center justify-between">
        <p className={cn(
          "font-medium flex-1",
          isMobile ? "text-sm" : "text-base"
        )}>
          {message}
        </p>
        <button
          onClick={onClose}
          className="ml-3 text-white/80 hover:text-white"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export {
  MobileModal,
  MobileModalHeader,
  MobileModalContent,
  MobileModalFooter,
  MobileBottomSheet,
  MobileToast,
};
