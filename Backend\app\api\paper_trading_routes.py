"""
Paper Trading API Routes

This module provides REST API endpoints for paper trading functionality
including mode switching, account management, trade history, and analytics.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.paper_trading_service import PaperTradingService
from app.auth.security import SecurityManager
from app.models.user import User
from app import db

paper_trading_bp = Blueprint('paper_trading', __name__, url_prefix='/api/paper-trading')


@paper_trading_bp.route('/mode', methods=['GET'])
@jwt_required()
def get_trading_mode():
    """Get current trading mode (paper or live)."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'success': True,
            'mode': 'paper' if user.paper_trading_mode else 'live',
            'paper_trading_enabled': user.paper_trading_mode
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting trading mode for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/mode', methods=['POST'])
@jwt_required()
def switch_trading_mode():
    """Switch between paper and live trading modes."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'paper_mode' not in data:
            return jsonify({'error': 'paper_mode field is required'}), 400
        
        paper_mode = bool(data['paper_mode'])
        
        # Switch trading mode
        result = PaperTradingService.switch_trading_mode(user_id, paper_mode)
        
        if result['success']:
            # Log security event
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='trading_mode_switch',
                ip_address=request.remote_addr,
                details={
                    'new_mode': 'paper' if paper_mode else 'live',
                    'previous_mode': 'live' if paper_mode else 'paper'
                },
                risk_level='medium'
            )
            
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        current_app.logger.error(f"Error switching trading mode for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/account', methods=['GET'])
@jwt_required()
def get_paper_account():
    """Get paper trading account summary."""
    try:
        user_id = get_jwt_identity()
        result = PaperTradingService.get_paper_account_summary(user_id)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        current_app.logger.error(f"Error getting paper account for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/account/reset', methods=['POST'])
@jwt_required()
def reset_paper_account():
    """Reset paper trading account to initial state."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}
        
        new_balance = data.get('new_balance')
        if new_balance is not None:
            try:
                new_balance = float(new_balance)
                if new_balance <= 0:
                    return jsonify({'error': 'New balance must be positive'}), 400
            except (ValueError, TypeError):
                return jsonify({'error': 'Invalid new_balance value'}), 400
        
        result = PaperTradingService.reset_paper_account(user_id, new_balance)
        
        if result['success']:
            # Log security event
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='paper_account_reset',
                ip_address=request.remote_addr,
                details={
                    'new_balance': result.get('new_balance'),
                    'reset_count': result.get('reset_count')
                },
                risk_level='low'
            )
            
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        current_app.logger.error(f"Error resetting paper account for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/history', methods=['GET'])
@jwt_required()
def get_paper_trading_history():
    """Get paper trading history with pagination."""
    try:
        user_id = get_jwt_identity()
        
        # Get pagination parameters
        limit = min(int(request.args.get('limit', 50)), 100)  # Max 100 records
        offset = max(int(request.args.get('offset', 0)), 0)
        
        result = PaperTradingService.get_paper_trading_history(user_id, limit, offset)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except ValueError as e:
        return jsonify({'error': 'Invalid pagination parameters'}), 400
    except Exception as e:
        current_app.logger.error(f"Error getting paper trading history for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/analytics', methods=['GET'])
@jwt_required()
def get_paper_trading_analytics():
    """Get paper trading analytics for specified period."""
    try:
        user_id = get_jwt_identity()
        
        # Get period parameter (default 30 days)
        days = int(request.args.get('days', 30))
        if days <= 0 or days > 365:
            return jsonify({'error': 'Days must be between 1 and 365'}), 400
        
        result = PaperTradingService.get_paper_trading_analytics(user_id, days)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except ValueError as e:
        return jsonify({'error': 'Invalid days parameter'}), 400
    except Exception as e:
        current_app.logger.error(f"Error getting paper trading analytics for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/session', methods=['POST'])
@jwt_required()
def create_paper_trading_session():
    """Create a new paper trading session."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'Request body is required'}), 400
        
        # Validate required fields
        symbol = data.get('symbol', 'BTCUSDT')
        leverage = int(data.get('leverage', 1))
        investment_percentage = int(data.get('investment_percentage', 0))
        
        # Validate parameters
        if leverage < 1 or leverage > 100:
            return jsonify({'error': 'Leverage must be between 1 and 100'}), 400
        
        if investment_percentage < 0 or investment_percentage > 100:
            return jsonify({'error': 'Investment percentage must be between 0 and 100'}), 400
        
        result = PaperTradingService.create_paper_trading_session(
            user_id, symbol, leverage, investment_percentage
        )
        
        if result['success']:
            # Log security event
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='paper_session_created',
                ip_address=request.remote_addr,
                details={
                    'symbol': symbol,
                    'leverage': leverage,
                    'investment_percentage': investment_percentage
                },
                risk_level='low'
            )
            
            return jsonify(result), 201
        else:
            return jsonify(result), 400
            
    except ValueError as e:
        return jsonify({'error': 'Invalid parameter values'}), 400
    except Exception as e:
        current_app.logger.error(f"Error creating paper trading session for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/positions', methods=['GET'])
@jwt_required()
def get_paper_positions():
    """Get current paper trading positions."""
    try:
        user_id = get_jwt_identity()
        
        # Get paper exchange service
        paper_service = PaperTradingService.get_paper_exchange_service(user_id)
        positions = paper_service.get_open_positions()
        
        return jsonify({
            'success': True,
            'positions': positions
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting paper positions for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/positions/<position_id>/close', methods=['POST'])
@jwt_required()
def close_paper_position(position_id):
    """Close a specific paper trading position."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}
        
        exit_reason = data.get('reason', 'manual')
        
        # Get paper exchange service
        paper_service = PaperTradingService.get_paper_exchange_service(user_id)
        result = paper_service.close_position(position_id, exit_reason)
        
        if result['success']:
            # Log security event
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='paper_position_closed',
                ip_address=request.remote_addr,
                details={
                    'position_id': position_id,
                    'exit_reason': exit_reason,
                    'pnl': result.get('pnl')
                },
                risk_level='low'
            )
            
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        current_app.logger.error(f"Error closing paper position {position_id} for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@paper_trading_bp.route('/balance', methods=['GET'])
@jwt_required()
def get_paper_balance():
    """Get current paper trading balance."""
    try:
        user_id = get_jwt_identity()

        # Ensure paper account exists
        account = PaperTradingService.get_or_create_paper_account(user_id)

        # Get paper exchange service
        paper_service = PaperTradingService.get_paper_exchange_service(user_id)
        balance = paper_service.get_balance()

        return jsonify({
            'success': True,
            'balance': float(balance),
            'currency': 'USDT'
        })

    except Exception as e:
        current_app.logger.error(f"Error getting paper balance for user {user_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500
