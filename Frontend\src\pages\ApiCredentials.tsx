// API Credentials Management Page for Top 10 CEXs

import React, { useEffect, useState } from "react";
import { Button } from "../components/ui/Button";
import { Input } from "../components/ui/input";
import { Card } from "../components/ui/card";
import { toastSuccess, toastError } from "../components/ui/use-toast";
import axios from "../api/trading.service";
import { useAutoTradingNotifications } from '../hooks/useAutoTradingNotifications';
import { useTranslation } from '../hooks/useTranslation';
import { useMobile } from '../hooks/useResponsiveDesign';

const CEX_LIST = [
  { key: "BINANCE", name: "Binance", icon: "/icons/binance.png" },
  { key: "BINANCE_US", name: "Binance US", icon: "/icons/binance-us.png" },
  { key: "KRAKEN", name: "Kraken", icon: "/icons/kraken.png" },
  { key: "BYBIT", name: "Bybit", icon: "/icons/bybit.png" },
  { key: "HYPERLIQUID", name: "Hyperliquid", icon: "/icons/hyperliquid.png" },
];

type Credential = {
  id: string;
  exchange: string;
  is_active: boolean;
  is_valid: boolean;
  created_at: string;
  updated_at: string;
};

type CredentialForm = {
  api_key: string;
  secret_key: string;
  passphrase?: string;
};

const ApiCredentials: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const [credentials, setCredentials] = useState<Record<string, Credential | null>>({});
  const [forms, setForms] = useState<Record<string, CredentialForm>>(
    Object.fromEntries(
      CEX_LIST.map((cex) => [
        cex.key,
        { api_key: "", secret_key: "", passphrase: "" }
      ])
    )
  );

  // Market type information for each exchange
  const getMarketTypeInfo = (exchange: string) => {
    const marketTypes = {
      'BINANCE': { type: t('apiCredentials.marketTypes.futures'), note: t('apiCredentials.marketTypes.binanceNote') },
      'BINANCE_US': { type: t('apiCredentials.marketTypes.spot'), note: t('apiCredentials.marketTypes.binanceUsNote') },
      'KRAKEN': { type: t('apiCredentials.marketTypes.futures'), note: t('apiCredentials.marketTypes.krakenNote') },
      'BYBIT': { type: t('apiCredentials.marketTypes.futures'), note: t('apiCredentials.marketTypes.bybitNote') },
      'HYPERLIQUID': { type: t('apiCredentials.marketTypes.futures'), note: t('apiCredentials.marketTypes.hyperliquidNote') }
    };
    return marketTypes[exchange as keyof typeof marketTypes] || { type: t('apiCredentials.marketTypes.spot'), note: t('apiCredentials.marketTypes.standardNote') };
  };
  const [loading, setLoading] = useState(false);
  const [selectedCex, setSelectedCex] = useState<string>(CEX_LIST[0].key);
  const [testStatus, setTestStatus] = useState<"idle" | "success" | "error" | "testing">("idle");
  const [testError, setTestError] = useState<string | null>(null);

  // Tier status state
  const [tierStatus, setTierStatus] = useState<{ tier: number } | null>(null);

  // Auto-trading notifications
  useAutoTradingNotifications();

  // Auto trading state
  const [autoTradingEnabled, setAutoTradingEnabled] = useState<Record<string, boolean>>({});

  useEffect(() => {
    fetchCredentials();
    fetchTierStatus();
  }, []);

  // Fetch user tier status from API
  const fetchTierStatus = async () => {
    try {
      const response = await axios.get("/api/trading/tier/status");
      if (response.data && typeof response.data.tier === "number") {
        setTierStatus({ tier: response.data.tier });
      } else if (response.data && (response.data.tier_1 || response.data.tier_2 || response.data.tier_3)) {
        // Fallback for boolean tier fields
        if (response.data.tier_3) setTierStatus({ tier: 3 });
        else if (response.data.tier_2) setTierStatus({ tier: 2 });
        else setTierStatus({ tier: 1 });
      } else {
        setTierStatus(null);
      }
    } catch (e) {
      setTierStatus(null);
    }
  };

  const fetchCredentials = async () => {
    setLoading(true);
    try {
      console.log('Fetching credentials from API...');
      const res = await axios.get("/api/trading/credentials");
      console.log('API Response:', res.data);
      
      // Handle both array and object response formats
      let creds: Credential[] = [];
      if (Array.isArray(res.data)) {
        creds = res.data;
      } else if (res.data.credentials && Array.isArray(res.data.credentials)) {
        creds = res.data.credentials;
      } else if (res.data && typeof res.data === 'object' && !Array.isArray(res.data)) {
        // Handle case where credentials are in an object keyed by exchange
        creds = Object.values(res.data).filter(Boolean) as Credential[];
      }
      
      console.log('Processed credentials:', creds);
      
      const map: Record<string, Credential | null> = {};
      // Initialize all exchanges to null first
      CEX_LIST.forEach((cex) => {
        map[cex.key] = null;
      });
      
      // Then populate with actual credentials
      creds.forEach((cred) => {
        const exchangeKey = Object.keys(CEX_LIST).find(
          (key) => CEX_LIST[key as any].key === cred.exchange.toUpperCase()
        );
        if (exchangeKey) {
          map[cred.exchange.toUpperCase()] = cred;
        }
      });
      
      console.log('Mapped credentials:', map);
      setCredentials(map);

      // Fetch auto trading status for active credentials
      await fetchAutoTradingStatus(map);
    } catch (e: any) {
      console.error('Error fetching credentials:', e);
      toastError({
        title: t('apiCredentials.errors.loadFailed'),
        description: e.response?.data?.error || e.message
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchAutoTradingStatus = async (credentialsMap: Record<string, Credential | null>) => {
    try {
      const autoTradingStatus: Record<string, boolean> = {};

      for (const [exchangeKey, cred] of Object.entries(credentialsMap)) {
        if (cred && cred.is_active) {
          try {
            const res = await axios.get(`/api/trading/credentials/${cred.id}/auto-trading`);
            autoTradingStatus[exchangeKey] = res.data.enabled || false;
          } catch (e) {
            // If endpoint doesn't exist or fails, default to false
            autoTradingStatus[exchangeKey] = false;
          }
        }
      }

      setAutoTradingEnabled(autoTradingStatus);
    } catch (e) {
      console.error('Error fetching auto trading status:', e);
    }
  };

  const handleInputChange = (exchange: string, field: string, value: string) => {
    setForms((prev) => ({
      ...prev,
      [exchange]: { ...prev[exchange], [field]: value },
    }));
  };

  const handleSave = async (exchange: string) => {
    const form = forms[exchange];
    if (!form.api_key || !form.secret_key) {
      toastError({ title: t('apiCredentials.errors.keysRequired') });
      return;
    }

    // Tier-based validation
    if (!tierStatus) {
      toastError({ title: t('apiCredentials.errors.tierUndetermined') });
      return;
    }
    const tier = tierStatus.tier;
    const existingCreds = Object.values(credentials).filter(Boolean) as Credential[];

    // Tier 1: Only one unique credential (any CEX)
    if (tier === 1) {
      if (existingCreds.length >= 1) {
        toastError({ title: t('apiCredentials.errors.tier1Limit') });
        return;
      }
    }

    // Tier 2: Up to two unique credentials, one per supported CEX
    if (tier === 2) {
      const credsForExchange = existingCreds.find(c => c.exchange.toUpperCase() === exchange.toUpperCase());
      if (credsForExchange) {
        toastError({ title: t('apiCredentials.errors.tier2PerCex') });
        return;
      }
      if (existingCreds.length >= 2) {
        toastError({ title: t('apiCredentials.errors.tier2Limit') });
        return;
      }
    }

    // Tier 3: Only one credential for all allowed CEXs
    if (tier === 3) {
      if (existingCreds.length >= 1) {
        toastError({ title: t('apiCredentials.errors.tier3Limit') });
        return;
      }
    }

    setLoading(true);
    try {
      await axios.post("/api/trading/credentials", {
        exchange: exchange.toLowerCase(),
        ...form,
      });
      toastSuccess({ title: t('apiCredentials.success.credentialsSaved') });
      fetchCredentials();
      setTestStatus("idle");
    } catch (e: any) {
      toastError({ title: e.response?.data?.error || t('apiCredentials.errors.saveFailed') });
    }
    setLoading(false);
  };

  const handleTestConnection = async (exchange: string) => {
    setTestStatus("testing");
    setTestError(null);
    try {
      const form = forms[exchange];
      const res = await axios.post("/api/trading/credentials/test", {
        exchange: exchange.toUpperCase(),
        ...form,
      });
      if (res.data && res.data.success) {
        setTestStatus("success");
        toastSuccess({ title: t('apiCredentials.success.connectionSuccessful') });
        // Refresh credentials to show updated connected status
        fetchCredentials();
      } else {
        setTestStatus("error");
        setTestError(res.data?.error || t('apiCredentials.errors.validationFailed'));
        toastError({ title: res.data?.error || t('apiCredentials.errors.validationFailed') });
      }
    } catch (e: any) {
      setTestStatus("error");
      setTestError(e.response?.data?.error || t('apiCredentials.errors.validationFailed'));
      toastError({ title: e.response?.data?.error || t('apiCredentials.errors.validationFailed') });
    }
  };

  const handleActivate = async (cred: Credential) => {
    setLoading(true);
    try {
      await axios.post(`/api/trading/credentials/${cred.id}/activate`);
      toastSuccess({ title: t('apiCredentials.success.activated') });
      fetchCredentials();
    } catch (e: any) {
      toastError({ title: e.response?.data?.error || t('apiCredentials.errors.activationFailed') });
    }
    setLoading(false);
  };

  const handleDeactivate = async (cred: Credential) => {
    setLoading(true);
    try {
      await axios.post(`/api/trading/credentials/${cred.id}/deactivate`);
      toastSuccess({ title: t('apiCredentials.success.deactivated') });
      fetchCredentials();
    } catch (e: any) {
      toastError({ title: e.response?.data?.error || t('apiCredentials.errors.deactivationFailed') });
    }
    setLoading(false);
  };

  const handleAutoTradingToggle = async (exchangeKey: string, enabled: boolean) => {
    try {
      setLoading(true);
      const cred = credentials[exchangeKey];
      if (!cred) {
        toastError({ title: t('apiCredentials.errors.noCredentials') });
        return;
      }

      // Use the global auto-trading toggle endpoint with proper validation
      // instead of the per-credential endpoint to ensure consistent validation
      const response = await fetch('https://52.196.239.9:5000/api/trading/auto-trade/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        credentials: 'include',
        body: JSON.stringify({ enabled }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Handle specific error cases with same logic as Dashboard.tsx
        switch (errorData.error_code) {
          case 'insufficient_balance':
            console.info('Auto-trade toggle blocked: Insufficient balance', {
              current: errorData.current_balance,
              required: errorData.minimum_required
            });
            toastError({
              title: t('notifications.autoTrading.insufficientBalanceTitle'),
              description: t('notifications.autoTrading.insufficientBalanceDescription', {
                required: errorData.minimum_required,
                current: errorData.current_balance
              })
            });
            break;
          case 'missing_credentials':
            console.info('Auto-trade toggle blocked: Missing API credentials');
            toastError({
              title: t('notifications.autoTrading.missingCredentialsTitle'),
              description: t('notifications.autoTrading.missingCredentialsDescription')
            });
            break;
          case 'balance_check_failed':
          case 'balance_verification_failed':
            console.warn('Auto-trade toggle failed: Balance verification error', errorData.message);
            toastError({
              title: t('notifications.autoTrading.balanceVerificationFailedTitle'),
              description: t('notifications.autoTrading.balanceVerificationFailedDescription')
            });
            break;
          default:
            console.error('Unexpected error toggling auto-trade:', errorData);
            toastError({
              title: t('notifications.autoTrading.errorTitle'),
              description: errorData.message || t('notifications.autoTrading.errorDescription')
            });
        }
        return;
      }

      const data = await response.json();

      // Update all exchange toggles to reflect the global state
      setAutoTradingEnabled(prev => {
        const newState: Record<string, boolean> = {};
        Object.keys(prev).forEach(key => {
          newState[key] = data.auto_trading_enabled;
        });
        return newState;
      });

      // Show success message
      if (data.auto_trading_enabled && enabled) {
        toastSuccess({
          title: t('notifications.autoTrading.enabledTitle'),
          description: t('notifications.autoTrading.enabledDescription')
        });
      } else if (!data.auto_trading_enabled && !enabled) {
        toastSuccess({
          title: t('notifications.autoTrading.disabledTitle'),
          description: t('notifications.autoTrading.disabledDescription')
        });
      }

      console.log(`Auto-trade ${data.auto_trading_enabled ? 'enabled' : 'disabled'} from ApiCredentials page`);

    } catch (e: any) {
      console.error('Unexpected error toggling auto-trade:', e);
      toastError({
        title: t('notifications.autoTrading.errorTitle'),
        description: e.message || t('notifications.autoTrading.errorDescription')
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading && Object.keys(credentials).length === 0) {
    return (
      <div className="max-w-3xl mx-auto py-4 px-4 sm:py-8 sm:px-6">
        <h1 className="text-xl sm:text-2xl font-bold mb-6">{t('apiCredentials.title')}</h1>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  // Note: Removed mobile redirect to ensure feature parity between desktop and mobile

  return (
    <div className={`${isMobile ? 'max-w-full px-3 py-3' : 'max-w-3xl mx-auto py-4 px-4 sm:py-8 sm:px-6'} overflow-x-hidden`}>
      <div className={`flex flex-col ${isMobile ? 'gap-3 mb-4' : 'sm:flex-row sm:justify-between sm:items-center mb-6 gap-4'}`}>
        <h1 className={`${isMobile ? 'text-lg' : 'text-xl sm:text-2xl'} font-bold`}>{t('apiCredentials.title')}</h1>
        <div className={`flex items-center ${isMobile ? 'flex-col gap-2' : 'flex-wrap'}`}>
          <span className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 ${isMobile ? '' : 'mr-2'} whitespace-nowrap`}>{t('apiCredentials.exchanger')}:</span>
          {/* Tier 1: Hide dropdown if a credential exists */}
          {tierStatus?.tier === 1 && Object.values(credentials).filter(Boolean).length >= 1 ? (
            <span className="text-sm text-gray-400">{t('apiCredentials.credentialAddedLimitReached')}</span>
          ) : (
            <select
              className={`border border-gray-300 dark:border-gray-600 rounded px-3 py-1 ${isMobile ? 'text-xs w-full' : 'text-sm'} bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400`}
              value={selectedCex}
              onChange={(e) => {
                setSelectedCex(e.target.value);
                setTestStatus("idle");
                setTestError(null);
              }}
            >
              {CEX_LIST.map((cex) => (
                <option key={cex.key} value={cex.key}>
                  {cex.name}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>
      <div>
        {(() => {
          // Tier 1: If a credential exists, show only that credential card, hide forms for others
          if (tierStatus?.tier === 1 && Object.values(credentials).filter(Boolean).length >= 1) {
            const credKey = Object.keys(credentials).find(k => credentials[k]);
            const cex = CEX_LIST.find((c) => c.key === credKey)!;
            const cred = credentials[cex.key];
            return (
              <Card key={cex.key} className="p-3 sm:p-4 flex flex-col gap-2 min-w-0">
                <div className="flex items-center gap-2 mb-2 min-w-0">
                  <img src={cex.icon} alt={cex.name} className="w-6 h-6 flex-shrink-0" />
                  <span className="font-semibold truncate">{cex.name}</span>
                  {cred && (
                    <span
                      className={`ml-2 text-xs px-2 py-1 rounded flex-shrink-0 ${
                        cred.is_valid
                          ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
                          : "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300"
                      }`}
                    >
                      {cred.is_valid ? t('apiCredentials.connected') : t('apiCredentials.invalid')}
                    </span>
                  )}
                  {cred && cred.is_active && (
                    <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded flex-shrink-0">
                      {t('apiCredentials.active')}
                    </span>
                  )}
                </div>

                {/* Market Type Requirements */}
                <div className="market-requirements bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg mt-3">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 text-sm">{t('apiCredentials.requiredMarketType')}</h4>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1">
                    <span className="px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded text-sm flex-shrink-0">
                      {getMarketTypeInfo(cex.key).type}
                    </span>
                    <span className="text-sm text-blue-700 dark:text-blue-300 break-words">
                      {getMarketTypeInfo(cex.key).note}
                    </span>
                  </div>
                </div>

                {/* Show disconnect button if credentials exist */}
                {cred && (
                  <div className="flex flex-col gap-4 mt-4">
                    <div className={`p-3 rounded-md border ${cred.is_valid ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'}`}>
                      <div className="flex items-start">
                        {cred.is_valid ? (
                          <>
                            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <p className="text-green-700 dark:text-green-300 break-words">{t('apiCredentials.connectedTo')} {cex.name}</p>
                          </>
                        ) : (
                          <>
                            <svg className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <p className="text-yellow-700 break-words">{t('apiCredentials.connectionNeedsAttention')} {cex.name}</p>
                          </>
                        )}
                      </div>
                      {cred.updated_at && (
                        <p className="text-xs mt-1 text-gray-500 break-words">{t('apiCredentials.lastUpdated')}: {new Date(cred.updated_at).toLocaleString()}</p>
                      )}
                      {!cred.is_valid && (
                        <p className="text-xs mt-1 text-yellow-600 break-words">
                          {t('apiCredentials.updateCredentialsMessage')}
                        </p>
                      )}
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 items-stretch sm:items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeactivate(cred)}
                        disabled={loading}
                        className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-800 dark:hover:text-red-200 border-red-200 dark:border-red-800 w-full sm:w-auto"
                      >
                        {loading ? t('apiCredentials.disconnecting') : t('apiCredentials.disconnect')}
                      </Button>
                      {!cred.is_active && (
                        <Button
                          size="sm"
                          onClick={() => handleActivate(cred)}
                          disabled={loading}
                          className="w-full sm:w-auto"
                        >
                          {loading ? t('apiCredentials.activating') : t('apiCredentials.setAsActive')}
                        </Button>
                      )}

                    </div>

                    {/* Auto Trading Toggle */}
                    {cred.is_active && cred.is_valid && (
                      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                          <div className="min-w-0">
                            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">{t('apiCredentials.autoTrading')}</h4>
                            <p className="text-xs text-blue-700 dark:text-blue-300 mt-1 break-words">
                              {t('apiCredentials.enableAutoTradingDesc')}
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer flex-shrink-0">
                            <input
                              type="checkbox"
                              className="sr-only peer"
                              checked={autoTradingEnabled[cex.key] || false}
                              onChange={(e) => handleAutoTradingToggle(cex.key, e.target.checked)}
                              disabled={loading}
                            />
                            <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                            <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300 whitespace-nowrap">
                              {autoTradingEnabled[cex.key] ? t('common.on') : t('common.off')}
                            </span>
                          </label>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </Card>
            );
          }
          // Tier 2: If two credentials exist, disable form for new credentials
          if (tierStatus?.tier === 2 && Object.values(credentials).filter(Boolean).length >= 2) {
            const credKeys = Object.keys(credentials).filter(k => credentials[k]);
            return (
              <>
                {credKeys.map((credKey) => {
                  const cex = CEX_LIST.find((c) => c.key === credKey)!;
                  const cred = credentials[cex.key];
                  return (
                    <Card key={cex.key} className="p-3 sm:p-4 flex flex-col gap-2 min-w-0">
                      <div className="flex items-center gap-2 mb-2 min-w-0">
                        <img src={cex.icon} alt={cex.name} className="w-6 h-6 flex-shrink-0" />
                        <span className="font-semibold truncate">{cex.name}</span>
                        {cred && (
                          <span
                            className={`ml-2 text-xs px-2 py-1 rounded flex-shrink-0 ${
                              cred.is_valid
                                ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
                                : "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300"
                            }`}
                          >
                            {cred.is_valid ? t('apiCredentials.connected') : t('apiCredentials.disconnected')}
                          </span>
                        )}
                        {cred && cred.is_active && (
                          <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded flex-shrink-0">
                            {t('apiCredentials.active')}
                          </span>
                        )}
                      </div>

                      {/* Market Type Requirements */}
                      <div className="market-requirements bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg mt-3">
                        <h4 className="font-medium text-blue-900 dark:text-blue-100 text-sm">{t('apiCredentials.requiredMarketType')}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded text-sm">
                            {getMarketTypeInfo(cex.key).type}
                          </span>
                          <span className="text-sm text-blue-700 dark:text-blue-300">
                            {getMarketTypeInfo(cex.key).note}
                          </span>
                        </div>
                      </div>

                      {/* Show disconnect button if credentials exist */}
                      {cred && (
                        <div className="flex flex-col gap-4 mt-4">
                          <div className={`p-3 rounded-md border ${cred.is_valid ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'}`}>
                            <div className="flex items-center">
                              {cred.is_valid ? (
                                <>
                                  <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                  <p className="text-green-700 dark:text-green-300">{t('apiCredentials.connectedTo')} {cex.name}</p>
                                </>
                              ) : (
                                <>
                                  <svg className="h-5 w-5 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                  </svg>
                                  <p className="text-yellow-700">{t('apiCredentials.connectionNeedsAttention')} {cex.name} {t('apiCredentials.needsAttention')}</p>
                                </>
                              )}
                            </div>
                            {cred.updated_at && (
                              <p className="text-xs mt-1 text-gray-500">{t('apiCredentials.lastUpdated')}: {new Date(cred.updated_at).toLocaleString()}</p>
                            )}
                            {!cred.is_valid && (
                              <p className="text-xs mt-1 text-yellow-600">
                                {t('apiCredentials.updateCredentialsMessage')}
                              </p>
                            )}
                          </div>
                          <div className="flex gap-2 items-center">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeactivate(cred)}
                              disabled={loading}
                              className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-800 dark:hover:text-red-200 border-red-200 dark:border-red-800"
                            >
                              {loading ? t('apiCredentials.disconnecting') : t('apiCredentials.disconnect')}
                            </Button>
                            {!cred.is_active && (
                              <Button
                                size="sm"
                                onClick={() => handleActivate(cred)}
                                disabled={loading}
                              >
                                {loading ? t('apiCredentials.activating') : t('apiCredentials.setAsActive')}
                              </Button>
                            )}

                          </div>

                          {/* Auto Trading Toggle */}
                          {cred.is_active && cred.is_valid && (
                            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                              <div className="flex items-center justify-between">
                                <div>
                                  <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">{t('apiCredentials.autoTrading')}</h4>
                                  <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                                    {t('apiCredentials.enableAutoTradingDesc')}
                                  </p>
                                </div>
                                <label className="relative inline-flex items-center cursor-pointer">
                                  <input
                                    type="checkbox"
                                    className="sr-only peer"
                                    checked={autoTradingEnabled[cex.key] || false}
                                    onChange={(e) => handleAutoTradingToggle(cex.key, e.target.checked)}
                                    disabled={loading}
                                  />
                                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                  <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                                    {autoTradingEnabled[cex.key] ? t('common.on') : t('common.off')}
                                  </span>
                                </label>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </Card>
                  );
                })}
                <div className="text-sm text-gray-400 mt-4">{t('apiCredentials.tier2LimitReached')}</div>
              </>
            );
          }
          // Default: show selected CEX card/form
          const cex = CEX_LIST.find((c) => c.key === selectedCex)!;
          const cred = credentials[cex.key];
          const form = forms[cex.key] || { api_key: "", secret_key: "", passphrase: "" };
          return (
            <Card key={cex.key} className="p-3 sm:p-4 flex flex-col gap-2 min-w-0">
              <div className="flex items-center gap-2 mb-2 min-w-0">
                <img src={cex.icon} alt={cex.name} className="w-6 h-6 flex-shrink-0" />
                <span className="font-semibold truncate">{cex.name}</span>
                {cred && (
                  <span
                    className={`ml-2 text-xs px-2 py-1 rounded flex-shrink-0 ${
                      cred.is_valid
                        ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
                        : "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300"
                    }`}
                  >
                    {cred.is_valid ? t('apiCredentials.connected') : t('apiCredentials.invalid')}
                  </span>
                )}
                {cred && cred.is_active && (
                  <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded flex-shrink-0">
                    {t('apiCredentials.active')}
                  </span>
                )}
              </div>

              {/* Market Type Requirements */}
              <div className="market-requirements bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg mt-3">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 text-sm">{t('apiCredentials.requiredMarketType')}</h4>
                <div className="flex items-center gap-2 mt-1">
                  <span className="px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded text-sm">
                    {getMarketTypeInfo(cex.key).type}
                  </span>
                  <span className="text-sm text-blue-700 dark:text-blue-300">
                    {getMarketTypeInfo(cex.key).note}
                  </span>
                </div>
              </div>

              {/* Show disconnect button if credentials exist */}
              {cred ? (
                <div className="flex flex-col gap-4 mt-4">
                  <div className={`p-3 rounded-md border ${cred.is_valid ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'}`}>
                    <div className="flex items-center">
                      {cred.is_valid ? (
                        <>
                          <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <p className="text-green-700 dark:text-green-300">{t('apiCredentials.connectedTo')} {cex.name}</p>
                        </>
                      ) : (
                        <>
                          <svg className="h-5 w-5 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                          <p className="text-yellow-700">{t('apiCredentials.connectionNeedsAttention')} {cex.name} {t('apiCredentials.needsAttention')}</p>
                        </>
                      )}
                    </div>
                    {cred.updated_at && (
                      <p className="text-xs mt-1 text-gray-500">{t('apiCredentials.lastUpdated')}: {new Date(cred.updated_at).toLocaleString()}</p>
                    )}
                    {!cred.is_valid && (
                      <p className="text-xs mt-1 text-yellow-600">
                        {t('apiCredentials.updateCredentialsMessage')}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2 items-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeactivate(cred)}
                      disabled={loading}
                      className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-800 dark:hover:text-red-200 border-red-200 dark:border-red-800"
                    >
                      {loading ? t('apiCredentials.disconnecting') : t('apiCredentials.disconnect')}
                    </Button>
                    {!cred.is_active && (
                      <Button
                        size="sm"
                        onClick={() => handleActivate(cred)}
                        disabled={loading}
                      >
                        {loading ? t('apiCredentials.activating') : t('apiCredentials.setAsActive')}
                      </Button>
                    )}

                  </div>

                  {/* Auto Trading Toggle */}
                  {cred.is_active && cred.is_valid && (
                    <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">{t('apiCredentials.autoTrading')}</h4>
                          <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                            {t('apiCredentials.enableAutoTradingDesc')}
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={autoTradingEnabled[cex.key] || false}
                            onChange={(e) => handleAutoTradingToggle(cex.key, e.target.checked)}
                            disabled={loading}
                          />
                          <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                          <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                            {autoTradingEnabled[cex.key] ? t('common.on') : t('common.off')}
                          </span>
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // Show form if no valid credentials exist
                <>
                  <Input
                    placeholder={t('apiCredentials.apiKey')}
                    value={form.api_key || ""}
                    onChange={(e) => handleInputChange(cex.key, "api_key", e.target.value)}
                  />
                  <Input
                    placeholder={t('apiCredentials.secretKey')}
                    value={form.secret_key || ""}
                    onChange={(e) => handleInputChange(cex.key, "secret_key", e.target.value)}
                  />
                  {(cex.key === "coinbase" || cex.key === "okx" || cex.key === "kucoin" || cex.key === "htx" || cex.key === "huobi") && (
                    <Input
                      placeholder={t('apiCredentials.passphrase')}
                      value={form.passphrase || ""}
                      onChange={(e) => handleInputChange(cex.key, "passphrase", e.target.value)}
                    />
                  )}
                  <div className="flex flex-col sm:flex-row gap-2 mt-2">
                    <Button
                      size="sm"
                      onClick={() => handleTestConnection(cex.key)}
                      disabled={loading || testStatus === "testing"}
                      variant="default"
                      className="w-full sm:w-auto"
                    >
                      {testStatus === "testing" ? t('apiCredentials.testing') : t('apiCredentials.testConnection')}
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleSave(cex.key)}
                      disabled={loading || testStatus !== "success"}
                      variant="default"
                      className="w-full sm:w-auto"
                    >
                      {cred ? t('apiCredentials.update') : t('apiCredentials.add')}
                    </Button>
                  </div>
                  {testStatus === "error" && (
                    <div className="text-red-600 text-sm mt-2">{testError}</div>
                  )}
                  {testStatus === "success" && (
                    <div className="text-green-600 text-sm mt-2">{t('apiCredentials.connectionValidated')}</div>
                  )}
                </>
              )}
            </Card>
          );
        })()}
      </div>
    </div>
  );
};

export default ApiCredentials;