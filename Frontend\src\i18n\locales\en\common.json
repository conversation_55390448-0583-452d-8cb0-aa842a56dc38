{"app": {"name": "DeepTrade", "tagline": "AI-Powered Cryptocurrency Trading", "description": "Advanced trading signals and automated portfolio management"}, "navigation": {"dashboard": "Dashboard", "trading": "Trading", "signals": "Signals", "apiCredentials": "API Credentials", "autoTrading": "Auto Trading", "tierManagement": "Tier Management", "referrals": "Referrals", "accessSecurity": "Access & Security", "settings": "Settings", "help": "Help & Support", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "home": "Home"}, "auth": {"login": {"title": "Welcome Back", "subtitle": "Sign in to your DeepTrade account", "email": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signIn": "Sign In", "noAccount": "Don't have an account?", "signUp": "Sign up", "googleSignIn": "Continue with Google"}, "register": {"title": "Create Account", "subtitle": "Join DeepTrade and start trading smarter", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "agreeTerms": "I agree to the Terms & Conditions", "createAccount": "Create Account", "hasAccount": "Already have an account?", "signIn": "Sign in"}, "passwordRequirements": {"title": "Password Requirements", "length": "At least 8 characters", "uppercase": "One uppercase letter", "lowercase": "One lowercase letter", "number": "One number", "special": "One special character"}, "errors": {"invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordsNotMatch": "Passwords do not match", "emailInvalid": "Please enter a valid email address", "termsRequired": "You must agree to the terms and conditions"}}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back!", "overview": "Overview", "trading": "Trading", "tier": "Tier", "balance": "Balance", "pnlToday": "P&L Today", "totalTrades": "Total Trades", "winRate": "Win Rate", "activeSignals": "Active Signals", "tradingOverview": "Trading Overview", "quickActions": "Quick Actions", "viewSignals": "View Signals", "autoTrading": "Auto Trading", "autoTradingDescription": "Automatically execute trading signals", "currentTier": "Current Tier", "progressTo": "Progress to Tier {{tier}}", "yourBenefits": "Your Benefits", "advancedSignals": "Advanced Trading Signals", "autoTradingFeatures": "Auto-Trading Features", "prioritySupport": "Priority Support"}, "trading": {"title": "Trading", "signals": "Signals", "positions": "Positions", "history": "History", "autoTradingActive": "Auto Trading Active", "autoTradingDisabled": "Auto Trading Disabled", "autoTradingActiveDesc": "Signals will be executed automatically", "autoTradingDisabledDesc": "Enable to automatically execute trading signals", "tradingMode": "Trading Mode", "paperMode": "Paper", "liveMode": "Live", "paperTradingActive": "Paper Trading Active", "liveTradingActive": "Live Trading Active", "paperTradingDesc": "Practice trading with virtual funds", "liveTradingDesc": "Trade with real money", "virtualBalance": "Virtual Balance", "paperTradingHelp": "Paper Trading Help", "resetAccount": "Reset Account", "paperTradingGuide": "Paper Trading Guide", "noActiveSignals": "No Active Signals", "waitingForSignals": "Waiting for new trading opportunities...", "enableAutoTrading": "Enable auto trading to receive signals", "refreshSignals": "Refresh Signals", "noOpenPositions": "No Open Positions", "positionsDesc": "Your active trading positions will appear here", "noTradingHistory": "No Trading History", "historyDesc": "Your completed trades will appear here", "executeTradeButton": "Execute Trade", "viewDetailsButton": "View Details", "entry": "Entry", "stopLoss": "Stop Loss", "takeProfit": "Take Profit", "confidence": "{{value}}% confidence", "strategy": "Strategy", "firstTp": "First TP", "secondTp": "Second TP", "autoMoveStopLoss": "Auto-move SL to breakeven after first TP"}, "tiers": {"title": "Tier Management", "currentTier": "Current Tier", "tier1": "Tier 1", "tier2": "Tier 2", "tier3": "Tier 3", "free": "Free", "monthly": "30 days access", "nftRequired": "NFT Required", "features": {"basicSignals": "Basic Trading Signals", "advancedSignals": "Advanced Trading Signals", "autoTrading": "Auto-Trading Features", "prioritySupport": "Priority Support", "premiumFeatures": "Premium Features", "exclusiveAccess": "Exclusive Access"}, "upgrade": "Upgrade", "current": "Current", "paymentRequired": "Payment Required", "nftVerification": "NFT Verification Required"}, "settings": {"title": "Settings", "profile": "Profile", "security": "Security", "notifications": "Notifications", "preferences": "Preferences", "account": "Account", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "twoFactorAuth": "Two-Factor Authentication", "enable2FA": "Enable 2FA", "disable2FA": "Disable 2FA", "emailNotifications": "Email Notifications", "tradingAlerts": "Trading Alerts", "accountUpdates": "Account Updates", "marketNews": "Market News", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "saveChanges": "Save Changes", "deleteAccount": "Delete Account", "deleteAccountWarning": "This action cannot be undone"}, "accessSecurity": {"title": "Access & Security", "subtitle": "Monitor your account security and login activity", "totalLogins": "Total Logins", "successRate": "Success Rate", "uniqueIPs": "Unique IPs", "twoFactorStatus": "2FA Status", "enabled": "Enabled", "disabled": "Disabled", "currentSession": "Current Session", "currentSessionDescription": "Information about your current login session", "recentLoginActivity": "Recent Login Activity", "recentLoginDescription": "Your recent login attempts and sessions", "refresh": "Refresh", "ipAddress": "IP Address", "timestamp": "Timestamp", "status": "Status", "userAgent": "User Agent", "successful": "Successful", "failed": "Failed", "noLoginActivity": "No login activity found", "currentDevice": "Current Device", "location": "Location", "sessionStart": "Session Start"}, "help": {"title": "Help & Support", "subtitle": "Find answers to common questions, review our legal policies, and get in touch with our support team", "faq": "FAQ", "legal": "Legal", "contact": "Contact", "general": "General", "setup": "Setup", "billing": "Billing", "security": "Security", "legalDocuments": "Legal Documents", "privacyPolicy": "Privacy Policy", "termsConditions": "Terms & Conditions", "riskDisclosure": "Risk Disclosure", "getInTouch": "Get in Touch", "emailSupport": "Email Support", "responseTime": "Response time: 24-48 hours", "liveChat": "Live Chat", "liveChatDesc": "Available for Tier 2+ users", "businessHours": "Monday - Friday, 9 AM - 6 PM EST", "prioritySupport": "Priority Support", "prioritySupportDesc": "Tier 3 NFT holders only", "priorityResponseTime": "Response time: 2-4 hours", "supportGuidelines": "Support Guidelines", "beforeContacting": "Before Contacting Support", "whenContacting": "When Contacting Support", "importantNotice": "Important Notice", "securityWarning": "We will never ask for your private keys, passwords, or API secret keys. Keep this information secure and never share it with anyone."}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "enable": "Enable", "disable": "Disable", "active": "Active", "inactive": "Inactive", "yes": "Yes", "no": "No", "ok": "OK", "apply": "Apply", "clear": "Clear", "select": "Select", "upload": "Upload", "download": "Download", "copy": "Copy", "copied": "Copied!", "share": "Share", "print": "Print", "export": "Export", "import": "Import", "and": "and", "toggleOff": "Off", "toggleOn": "On"}, "wallet": {"connect": "Connect Wallet", "disconnect": "Disconnect", "connected": "Connected", "notConnected": "Not Connected", "balance": "Balance", "address": "Address", "solanaBlockchain": "Solana Blockchain Powered", "wrongNetwork": "Wrong Network", "switchNetwork": "Switch Network", "transactionPending": "Transaction Pending", "transactionConfirmed": "Transaction Confirmed", "transactionFailed": "Transaction Failed"}, "notifications": {"autoTradingEnabled": "Auto-trading has been enabled", "autoTradingDisabled": "Auto-trading has been disabled", "settingsSaved": "Settings have been saved successfully", "passwordChanged": "Password changed successfully", "profileUpdated": "Profile updated successfully", "walletConnected": "<PERSON><PERSON> connected successfully", "walletDisconnected": "Wallet disconnected", "signalExecuted": "Trading signal executed", "tradeCompleted": "Trade completed successfully", "paymentSuccessful": "Payment processed successfully", "tierUpgraded": "Tier upgraded successfully"}, "errors": {"generic": "Something went wrong. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "serverError": "Server error. Please try again later.", "validationError": "Please check your input and try again.", "walletNotConnected": "Please connect your wallet first.", "insufficientBalance": "Insufficient balance.", "transactionFailed": "Transaction failed. Please try again.", "apiError": "API error. Please contact support if this persists."}}