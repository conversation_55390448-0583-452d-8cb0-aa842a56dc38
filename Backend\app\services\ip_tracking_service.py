"""
IP Tracking Service for DeepTrade
Handles IP geolocation, proxy detection, and comprehensive logging.
"""

import requests
import re
from datetime import datetime, timedelta
from flask import request, current_app
from app.models.ip_tracking import IPAccessLog, IPBlacklist, IPRateLimit
from app import db


class IPTrackingService:
    """Service for comprehensive IP tracking and management."""
    
    # Private IP ranges for detection
    PRIVATE_IP_RANGES = [
        re.compile(r'^127\.'),  # Loopback
        re.compile(r'^10\.'),   # Private Class A
        re.compile(r'^172\.(1[6-9]|2[0-9]|3[0-1])\.'),  # Private Class B
        re.compile(r'^192\.168\.'),  # Private Class C
        re.compile(r'^169\.254\.'),  # Link-local
        re.compile(r'^::1$'),   # IPv6 loopback
        re.compile(r'^fc00:'),  # IPv6 private
        re.compile(r'^fe80:'),  # IPv6 link-local
    ]
    
    @classmethod
    def get_client_ip(cls, request_obj=None):
        """Extract the real client IP address from request headers."""
        if not request_obj:
            request_obj = request
            
        # Check various headers for real IP (in order of preference)
        ip_headers = [
            'X-Forwarded-For',
            'X-Real-IP',
            'X-Client-IP',
            'CF-Connecting-IP',  # Cloudflare
            'True-Client-IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ]
        
        for header in ip_headers:
            ip = request_obj.headers.get(header) or request_obj.environ.get(header)
            if ip:
                # Handle comma-separated IPs (X-Forwarded-For can have multiple IPs)
                ip = ip.split(',')[0].strip()
                if cls._is_valid_ip(ip) and not cls._is_private_ip(ip):
                    return ip
        
        # Fallback to remote_addr
        return request_obj.remote_addr or '127.0.0.1'
    
    @classmethod
    def _is_valid_ip(cls, ip):
        """Check if IP address is valid."""
        try:
            # Basic IPv4 validation
            parts = ip.split('.')
            if len(parts) == 4:
                return all(0 <= int(part) <= 255 for part in parts)
            # Basic IPv6 validation (simplified)
            return ':' in ip and len(ip) <= 39
        except (ValueError, AttributeError):
            return False
    
    @classmethod
    def _is_private_ip(cls, ip):
        """Check if IP address is private/internal."""
        for pattern in cls.PRIVATE_IP_RANGES:
            if pattern.match(ip):
                return True
        return False
    
    @classmethod
    def get_ip_geolocation(cls, ip_address):
        """Get geolocation information for an IP address."""
        if cls._is_private_ip(ip_address):
            return {
                'country_code': 'XX',
                'country': 'Private Network',
                'city': 'Local',
                'isp': 'Private Network',
                'is_proxy': False,
                'is_vpn': False
            }
        
        try:
            # Using ip-api.com (free tier: 1000 requests/month)
            response = requests.get(
                f'http://ip-api.com/json/{ip_address}?fields=status,country,countryCode,city,isp,proxy,query',
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return {
                        'country_code': data.get('countryCode', ''),
                        'country': data.get('country', ''),
                        'city': data.get('city', ''),
                        'isp': data.get('isp', ''),
                        'is_proxy': data.get('proxy', False),
                        'is_vpn': False  # Basic service doesn't detect VPN
                    }
        except Exception as e:
            current_app.logger.warning(f"Failed to get geolocation for IP {ip_address}: {e}")
        
        return {
            'country_code': '',
            'country': 'Unknown',
            'city': 'Unknown',
            'isp': 'Unknown',
            'is_proxy': False,
            'is_vpn': False
        }
    
    @classmethod
    def log_login_attempt(cls, user_id=None, admin_id=None, login_successful=True, 
                         failure_reason=None, request_obj=None):
        """Log a login attempt with comprehensive IP tracking."""
        if not request_obj:
            request_obj = request
        
        ip_address = cls.get_client_ip(request_obj)
        user_agent = request_obj.headers.get('User-Agent', '')
        
        # Get geolocation data
        geo_data = cls.get_ip_geolocation(ip_address)
        
        # Create log entry
        log_entry = IPAccessLog.log_access(
            user_id=user_id,
            admin_id=admin_id,
            ip_address=ip_address,
            user_agent=user_agent,
            login_successful=login_successful,
            failure_reason=failure_reason,
            geographic_location=f"{geo_data['city']}, {geo_data['country']}",
            country_code=geo_data['country_code'],
            city=geo_data['city'],
            isp=geo_data['isp'],
            is_proxy=geo_data['is_proxy'],
            is_vpn=geo_data['is_vpn']
        )
        
        # Update user/admin last IP
        if admin_id:
            try:
                from app.models.admin import AdminUser
                admin = AdminUser.query.get(admin_id)
                if admin:
                    admin.last_ip_address = ip_address
                    admin.last_login_ip = ip_address
                    admin.ip_login_count = (admin.ip_login_count or 0) + 1
                    db.session.commit()
            except Exception as e:
                current_app.logger.warning(f"Failed to update admin IP: {e}")
        
        elif user_id:
            try:
                from app.models.user import User
                user = User.query.filter_by(id=user_id).first()
                if user:
                    user.last_ip_address = ip_address
                    user.last_login_ip = ip_address
                    user.ip_login_count = (user.ip_login_count or 0) + 1
                    db.session.commit()
            except Exception as e:
                current_app.logger.warning(f"Failed to update user IP: {e}")
        
        return log_entry
    
    @classmethod
    def check_ip_restrictions(cls, request_obj=None):
        """Check if IP is banned or rate limited."""
        if not request_obj:
            request_obj = request
        
        ip_address = cls.get_client_ip(request_obj)
        
        # Check if IP is banned
        if IPBlacklist.is_ip_banned(ip_address):
            ban = IPBlacklist.query.filter_by(ip_address=ip_address, is_active=True).first()
            return {
                'allowed': False,
                'reason': 'ip_banned',
                'message': f'IP address is banned: {ban.ban_reason if ban else "Unknown reason"}',
                'ban_info': ban.to_dict() if ban else None
            }
        
        # Check rate limiting
        if IPRateLimit.is_ip_rate_limited(ip_address):
            rate_limit = IPRateLimit.query.filter_by(ip_address=ip_address).first()
            return {
                'allowed': False,
                'reason': 'rate_limited',
                'message': f'Too many login attempts. Try again later.',
                'rate_limit_info': rate_limit.to_dict() if rate_limit else None
            }
        
        return {'allowed': True}
    
    @classmethod
    def record_failed_attempt(cls, request_obj=None):
        """Record a failed login attempt and apply rate limiting."""
        if not request_obj:
            request_obj = request
        
        ip_address = cls.get_client_ip(request_obj)
        
        # Record rate limiting attempt
        rate_limit = IPRateLimit.record_attempt(ip_address)
        
        # Auto-ban if too many attempts (configurable threshold)
        from flask import current_app
        auto_ban_threshold = current_app.config.get('RATE_LIMIT_AUTO_BAN_THRESHOLD', 20)
        auto_ban_duration_hours = current_app.config.get('RATE_LIMIT_AUTO_BAN_DURATION_HOURS', 12)

        if rate_limit.attempt_count >= auto_ban_threshold:
            IPBlacklist.ban_ip(
                ip_address=ip_address,
                reason=f'Automatic ban due to {rate_limit.attempt_count} failed login attempts',
                banned_by_admin_id=1,  # System admin
                expires_at=datetime.utcnow() + timedelta(hours=auto_ban_duration_hours),
                ban_type='automatic'
            )
        
        return rate_limit
    
    @classmethod
    def get_ip_summary(cls, ip_address):
        """Get comprehensive summary for an IP address."""
        # Get access logs
        access_logs = IPAccessLog.get_ip_activity(ip_address, limit=20)

        # Get ban status
        ban = IPBlacklist.query.filter_by(ip_address=ip_address).first()

        # Get rate limit status
        rate_limit = IPRateLimit.query.filter_by(ip_address=ip_address).first()

        # Get geolocation
        geo_data = cls.get_ip_geolocation(ip_address)

        # Enhance access logs with user information
        enhanced_logs = []
        for log in access_logs:
            log_dict = log.to_dict()

            # Add user email and full name if user_id exists
            if log.user_id:
                try:
                    from app.models.user import User
                    user = User.query.filter_by(id=log.user_id).first()
                    if user:
                        log_dict['user_email'] = user.email
                        log_dict['user_full_name'] = user.full_name
                        log_dict['complete_user_id'] = user.id  # Full user ID
                    else:
                        log_dict['user_email'] = 'User not found'
                        log_dict['user_full_name'] = 'Unknown'
                        log_dict['complete_user_id'] = log.user_id
                except Exception as e:
                    current_app.logger.warning(f"Failed to get user info for {log.user_id}: {e}")
                    log_dict['user_email'] = 'Error loading user'
                    log_dict['user_full_name'] = 'Unknown'
                    log_dict['complete_user_id'] = log.user_id
            else:
                log_dict['user_email'] = None
                log_dict['user_full_name'] = None
                log_dict['complete_user_id'] = None

            enhanced_logs.append(log_dict)

        return {
            'ip_address': ip_address,
            'geolocation': geo_data,
            'access_logs': enhanced_logs,
            'ban_status': ban.to_dict() if ban else None,
            'rate_limit_status': rate_limit.to_dict() if rate_limit else None,
            'total_logins': len([log for log in access_logs if log.login_successful]),
            'failed_attempts': len([log for log in access_logs if not log.login_successful]),
            'last_activity': access_logs[0].login_timestamp.isoformat() if access_logs else None
        }
    
    @classmethod
    def cleanup_old_logs(cls, days_to_keep=90):
        """Clean up old IP access logs (retention policy)."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        deleted_count = IPAccessLog.query.filter(
            IPAccessLog.login_timestamp < cutoff_date
        ).delete()
        
        db.session.commit()
        
        current_app.logger.info(f"Cleaned up {deleted_count} old IP access logs")
        return deleted_count
    
    @classmethod
    def get_suspicious_ips(cls, days=7, min_failed_attempts=5):
        """Get list of suspicious IP addresses based on failed attempts."""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Query for IPs with multiple failed attempts
        suspicious_ips = db.session.query(
            IPAccessLog.ip_address,
            db.func.count(IPAccessLog.id).label('failed_count'),
            db.func.max(IPAccessLog.login_timestamp).label('last_attempt')
        ).filter(
            IPAccessLog.login_timestamp >= cutoff_date,
            IPAccessLog.login_successful == False
        ).group_by(
            IPAccessLog.ip_address
        ).having(
            db.func.count(IPAccessLog.id) >= min_failed_attempts
        ).order_by(
            db.func.count(IPAccessLog.id).desc()
        ).all()
        
        return [
            {
                'ip_address': ip.ip_address,
                'failed_attempts': ip.failed_count,
                'last_attempt': ip.last_attempt.isoformat() if ip.last_attempt else None,
                'is_banned': IPBlacklist.is_ip_banned(ip.ip_address)
            }
            for ip in suspicious_ips
        ]
