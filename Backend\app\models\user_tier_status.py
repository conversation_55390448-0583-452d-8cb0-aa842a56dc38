from app import db
from datetime import datetime, timedelta
import pytz

class UserTierStatus(db.Model):
    __tablename__ = 'user_tier_status'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(36), db.<PERSON>('users.id'), nullable=False, unique=True)
    tier_1 = db.Column(db.Bo<PERSON>, nullable=False, default=True)
    tier_2 = db.Column(db.Bo<PERSON>, nullable=False, default=False)
    tier_3 = db.Column(db.Boolean, nullable=False, default=False)
    # Only one tier should be True at a time
    profit_share_owed = db.Column(db.Numeric(precision=12, scale=2), default=0)
    payment_status = db.Column(db.String(32), default='unpaid')  # 'unpaid', 'paid', 'pending', 'failed'
    
    last_payment_date = db.Column(db.DateTime, nullable=True)
    next_payment_date = db.Column(db.DateTime, nullable=True)

    # Saturday payday limit fields
    next_payday_deadline = db.Column(db.DateTime, nullable=True)  # Next Saturday 0:00 GMT
    account_disabled = db.Column(db.Boolean, default=False)  # Account disabled for unpaid fees
    disabled_at = db.Column(db.DateTime, nullable=True)  # When account was disabled
    payday_warning_sent = db.Column(db.Boolean, default=False)  # Warning notification sent

    # Email notification tracking (3-day advance warning system)
    email_warning_3_days_sent = db.Column(db.Boolean, default=False)  # 3 days before deadline
    email_warning_2_days_sent = db.Column(db.Boolean, default=False)  # 2 days before deadline
    email_warning_1_day_sent = db.Column(db.Boolean, default=False)   # 1 day before deadline
    email_warning_3_days_sent_at = db.Column(db.DateTime, nullable=True)
    email_warning_2_days_sent_at = db.Column(db.DateTime, nullable=True)
    email_warning_1_day_sent_at = db.Column(db.DateTime, nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    user = db.relationship('User', back_populates='tier_status')

    def get_current_tier(self):
        """Return the current tier as int (1, 2, or 3) based on boolean fields."""
        if self.tier_1:
            return 1
        if self.tier_2:
            return 2
        if self.tier_3:
            return 3
        return 1  # Default to tier 1 if none set

    def get_profit_share_rate(self):
        """Get profit share rate based on tier booleans."""
        rates = {1: 0.40, 2: 0.20, 3: 0.10}
        return rates.get(self.get_current_tier(), 0.0)
    
    def get_effective_profit_share_rate(self):
        """Get effective profit share rate."""
        return self.get_profit_share_rate()
    
    def add_profit_share(self, profit_amount):
        """Add profit share owed based on current tier."""
        if profit_amount <= 0:
            return
        
        rate = self.get_effective_profit_share_rate()
        share_amount = profit_amount * rate
        self.profit_share_owed += share_amount
        self.updated_at = datetime.utcnow()
    
    def update_payment_status(self, status):
        """Update payment status and timestamps."""
        self.payment_status = status
        self.updated_at = datetime.utcnow()

        if status == 'paid':
            self.last_payment_date = datetime.utcnow()
            # Set next payment date to next month for tier 2
            if self.tier_2 and self.next_payment_date is None:
                self.next_payment_date = datetime.utcnow() + timedelta(days=30)

    def is_tier_2_membership_active(self):
        """Check if Tier 2 membership is still active within 30-day window."""
        if not self.tier_2:
            return False

        # Check if user has any confirmed membership payments
        from app.models.solana_payment import SolanaPayment, SolanaPaymentType, SolanaPaymentStatus

        confirmed_payment = SolanaPayment.query.filter_by(
            user_id=self.user_id,
            payment_type=SolanaPaymentType.MEMBERSHIP_FEE,
            status=SolanaPaymentStatus.CONFIRMED
        ).order_by(SolanaPayment.processed_at.desc()).first()

        if not confirmed_payment or not confirmed_payment.processed_at:
            return False

        # Check if payment is within 30-day window
        payment_date = confirmed_payment.processed_at
        current_date = datetime.utcnow()
        days_since_payment = (current_date - payment_date).days

        return days_since_payment <= 30

    def get_membership_expiry_date(self):
        """Get the expiry date of current Tier 2 membership."""
        if not self.tier_2:
            return None

        from app.models.solana_payment import SolanaPayment, SolanaPaymentType, SolanaPaymentStatus

        confirmed_payment = SolanaPayment.query.filter_by(
            user_id=self.user_id,
            payment_type=SolanaPaymentType.MEMBERSHIP_FEE,
            status=SolanaPaymentStatus.CONFIRMED
        ).order_by(SolanaPayment.processed_at.desc()).first()

        if not confirmed_payment or not confirmed_payment.processed_at:
            return None

        return confirmed_payment.processed_at + timedelta(days=30)

    def get_days_remaining_in_membership(self):
        """Get number of days remaining in current Tier 2 membership."""
        expiry_date = self.get_membership_expiry_date()
        if not expiry_date:
            return 0

        current_date = datetime.utcnow()
        days_remaining = (expiry_date - current_date).days
        return max(0, days_remaining)

    def get_next_saturday_gmt(self):
        """Get the next Saturday at 0:00 GMT."""
        gmt = pytz.timezone('GMT')
        now_gmt = datetime.utcnow().replace(tzinfo=pytz.utc).astimezone(gmt)

        # Find next Saturday
        days_until_saturday = (5 - now_gmt.weekday()) % 7  # Saturday is weekday 5
        if days_until_saturday == 0:  # Today is Saturday
            # Check if it's already past 0:00 GMT
            if now_gmt.hour > 0 or now_gmt.minute > 0 or now_gmt.second > 0:
                days_until_saturday = 7  # Next Saturday

        next_saturday = now_gmt + timedelta(days=days_until_saturday)
        next_saturday = next_saturday.replace(hour=0, minute=0, second=0, microsecond=0)

        # Convert back to UTC for storage
        return next_saturday.astimezone(pytz.utc).replace(tzinfo=None)

    def update_payday_deadline(self):
        """Update the next payday deadline to next Saturday 0:00 GMT."""
        self.next_payday_deadline = self.get_next_saturday_gmt()
        self.payday_warning_sent = False  # Reset warning flag
        # Reset email warning flags for new deadline
        self.email_warning_3_days_sent = False
        self.email_warning_2_days_sent = False
        self.email_warning_1_day_sent = False
        self.email_warning_3_days_sent_at = None
        self.email_warning_2_days_sent_at = None
        self.email_warning_1_day_sent_at = None
        self.updated_at = datetime.utcnow()

    def is_past_payday_deadline(self):
        """Check if current time is past the payday deadline."""
        if not self.next_payday_deadline:
            return False
        return datetime.utcnow() > self.next_payday_deadline

    def should_disable_account(self):
        """Check if account should be disabled for unpaid fees."""
        return (self.profit_share_owed > 0 and
                self.payment_status == 'unpaid' and
                self.is_past_payday_deadline() and
                not self.account_disabled)

    def disable_account(self):
        """Disable account for unpaid fees."""
        self.account_disabled = True
        self.disabled_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def enable_account(self):
        """Enable account after successful payment."""
        self.account_disabled = False
        self.disabled_at = None
        self.updated_at = datetime.utcnow()

    def clear_debt(self, payment_amount):
        """Clear debt and handle account re-enabling."""
        # Existing debt clearing logic
        if payment_amount >= float(self.profit_share_owed):
            self.profit_share_owed = 0
            self.payment_status = 'paid'
            # Re-enable account if it was disabled
            if self.account_disabled:
                self.enable_account()
        else:
            self.profit_share_owed -= payment_amount
            self.payment_status = 'partial'

        self.last_payment_date = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def needs_payday_warning(self):
        """Check if user needs payday warning notification."""
        if not self.next_payday_deadline or self.payday_warning_sent:
            return False

        # Send warning 24 hours before deadline
        warning_time = self.next_payday_deadline - timedelta(hours=24)
        return (datetime.utcnow() >= warning_time and
                self.profit_share_owed > 0 and
                self.payment_status == 'unpaid')

    def mark_payday_warning_sent(self):
        """Mark that payday warning has been sent."""
        self.payday_warning_sent = True
        self.updated_at = datetime.utcnow()

    def needs_email_warning_3_days(self):
        """Check if user needs 3-day email warning."""
        if not self.next_payday_deadline or self.email_warning_3_days_sent:
            return False

        # Send warning 3 days before deadline
        warning_time = self.next_payday_deadline - timedelta(days=3)
        return (datetime.utcnow() >= warning_time and
                self.profit_share_owed > 0 and
                self.payment_status == 'unpaid')

    def needs_email_warning_2_days(self):
        """Check if user needs 2-day email warning."""
        if not self.next_payday_deadline or self.email_warning_2_days_sent:
            return False

        # Send warning 2 days before deadline
        warning_time = self.next_payday_deadline - timedelta(days=2)
        return (datetime.utcnow() >= warning_time and
                self.profit_share_owed > 0 and
                self.payment_status == 'unpaid')

    def needs_email_warning_1_day(self):
        """Check if user needs 1-day email warning."""
        if not self.next_payday_deadline or self.email_warning_1_day_sent:
            return False

        # Send warning 1 day before deadline
        warning_time = self.next_payday_deadline - timedelta(days=1)
        return (datetime.utcnow() >= warning_time and
                self.profit_share_owed > 0 and
                self.payment_status == 'unpaid')

    def mark_email_warning_sent(self, days_before):
        """Mark that email warning has been sent."""
        now = datetime.utcnow()
        if days_before == 3:
            self.email_warning_3_days_sent = True
            self.email_warning_3_days_sent_at = now
        elif days_before == 2:
            self.email_warning_2_days_sent = True
            self.email_warning_2_days_sent_at = now
        elif days_before == 1:
            self.email_warning_1_day_sent = True
            self.email_warning_1_day_sent_at = now
        self.updated_at = now

    def should_downgrade_from_tier_2(self):
        """Check if user should be downgraded from Tier 2 due to expired membership."""
        if not self.tier_2:
            return False

        return not self.is_tier_2_membership_active()
    

    
    def to_dict(self):
        """Convert to dictionary."""
        base_dict = {
            'id': self.id,
            'user_id': self.user_id,
            'tier': self.get_current_tier(),
            'profit_share_owed': float(self.profit_share_owed),
            'payment_status': self.payment_status,
            'last_payment_date': self.last_payment_date.isoformat() if self.last_payment_date else None,
            'next_payment_date': self.next_payment_date.isoformat() if self.next_payment_date else None,
            'profit_share_rate': self.get_profit_share_rate(),
            'updated_at': self.updated_at.isoformat(),
            # Saturday payday limit fields
            'next_payday_deadline': self.next_payday_deadline.isoformat() if self.next_payday_deadline else None,
            'account_disabled': self.account_disabled,
            'disabled_at': self.disabled_at.isoformat() if self.disabled_at else None,
            'payday_warning_sent': self.payday_warning_sent,
            'is_past_payday_deadline': self.is_past_payday_deadline(),
            'should_disable_account': self.should_disable_account(),
            'needs_payday_warning': self.needs_payday_warning(),
            # Email notification status
            'email_warning_3_days_sent': self.email_warning_3_days_sent,
            'email_warning_2_days_sent': self.email_warning_2_days_sent,
            'email_warning_1_day_sent': self.email_warning_1_day_sent,
            'needs_email_warning_3_days': self.needs_email_warning_3_days(),
            'needs_email_warning_2_days': self.needs_email_warning_2_days(),
            'needs_email_warning_1_day': self.needs_email_warning_1_day()
        }

        # Add Tier 2 membership information
        if self.tier_2:
            base_dict.update({
                'membership_active': self.is_tier_2_membership_active(),
                'membership_expiry_date': self.get_membership_expiry_date().isoformat() if self.get_membership_expiry_date() else None,
                'days_remaining': self.get_days_remaining_in_membership(),
                'should_downgrade': self.should_downgrade_from_tier_2()
            })

        return base_dict

    def __repr__(self):
        return f'<UserTierStatus user_id={self.user_id} tier={self.get_current_tier()} owed={self.profit_share_owed} status={self.payment_status}>'