import os
from environs import Env

env = Env()
# Explicitly read from backend/.env file to avoid confusion with frontend .env
backend_env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
env.read_env(backend_env_path)

class Config:
    """Base configuration."""
    SECRET_KEY = env.str('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Database
    DB_HOST = env.str('DB_HOST', 'localhost')
    DB_PORT = env.str('DB_PORT', '3306')
    DB_NAME = env.str('DB_NAME', 'deeptrade')
    DB_USER = env.str('DB_USER', 'deeptrade_user')
    DB_PASSWORD = env.str('DB_PASSWORD', 'your_secure_password')
    SQLALCHEMY_DATABASE_URI = f'mysql+mysqldb://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4'
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_recycle': 3600,
        'pool_pre_ping': True,
    }
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Redis
    REDIS_URL = env.str('REDIS_URL', 'redis://localhost:6379/0')
    
    # JWT
    JWT_SECRET_KEY = env.str('JWT_SECRET_KEY', 'jwt-secret-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = env.int('JWT_ACCESS_TOKEN_EXPIRES', 864000)  # 10 days (as requested)
    JWT_REFRESH_TOKEN_EXPIRES = env.int('JWT_REFRESH_TOKEN_EXPIRES', 2592000)  # 30 days
    
    # Google OAuth
    GOOGLE_CLIENT_ID = env.str('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = env.str('GOOGLE_CLIENT_SECRET')
    GOOGLE_DISCOVERY_URL = "https://accounts.google.com/.well-known/openid-configuration"
    
    # Stripe (legacy, optional)
    STRIPE_PUBLISHABLE_KEY = env.str('STRIPE_PUBLISHABLE_KEY', '')
    STRIPE_SECRET_KEY = env.str('STRIPE_SECRET_KEY', '')
    STRIPE_WEBHOOK_SECRET = env.str('STRIPE_WEBHOOK_SECRET', '')
    
    # Binance (for ML predictions)
    BINANCE_API_KEY = env.str('BINANCE_API_KEY')
    BINANCE_API_SECRET = env.str('BINANCE_API_SECRET')
    
    # Developer wallet for fee collection
    #DEVELOPER_WALLET_ADDRESS = env.str('DEVELOPER_WALLET_ADDRESS')
    
    # Security
    BCRYPT_LOG_ROUNDS = env.int('BCRYPT_LOG_ROUNDS', 12)
    RATE_LIMIT_STORAGE_URL = env.str('RATE_LIMIT_STORAGE_URL', 'redis://localhost:6379/1')

    # Rate Limiting Configuration
    RATE_LIMIT_MAX_ATTEMPTS = env.int('RATE_LIMIT_MAX_ATTEMPTS', 10)
    RATE_LIMIT_BLOCK_DURATION_MINUTES = env.int('RATE_LIMIT_BLOCK_DURATION_MINUTES', 10)
    RATE_LIMIT_RESET_HOURS = env.int('RATE_LIMIT_RESET_HOURS', 2)
    RATE_LIMIT_AUTO_BAN_THRESHOLD = env.int('RATE_LIMIT_AUTO_BAN_THRESHOLD', 20)
    RATE_LIMIT_AUTO_BAN_DURATION_HOURS = env.int('RATE_LIMIT_AUTO_BAN_DURATION_HOURS', 12)
    
    # App settings
    ITEMS_PER_PAGE = env.int('ITEMS_PER_PAGE', 20)

    # Email configuration for verification (Brevo SMTP)
    SMTP_SERVER = env.str('SMTP_SERVER', 'smtp.gmail.com')
    SMTP_PORT = env.int('SMTP_PORT', 587)
    SMTP_USERNAME = env.str('SMTP_USERNAME', '<EMAIL>')
    SMTP_PASSWORD = env.str('SMTP_PASSWORD', 'your-app-password')
    FROM_EMAIL = env.str('FROM_EMAIL', '<EMAIL>')
    FRONTEND_URL = env.str('FRONTEND_URL', 'http://localhost:5173')
    
    # Subscription tiers
    TIER_1_PROFIT_SHARE = env.float('TIER_1_PROFIT_SHARE', 0.40)  # 40%
    TIER_2_PROFIT_SHARE = env.float('TIER_2_PROFIT_SHARE', 0.10)  # 10%
    TIER_2_MONTHLY_FEE = env.float('TIER_2_MONTHLY_FEE', 29.90)  # USDC
    


    # Trading symbol configuration
    DEFAULT_TRADING_SYMBOL = env.str('DEFAULT_TRADING_SYMBOL', 'BTCUSDT')
    DEFAULT_BASE_ASSET = env.str('DEFAULT_BASE_ASSET', 'BTC')
    DEFAULT_QUOTE_ASSET = env.str('DEFAULT_QUOTE_ASSET', 'USDT')

    # Supported quote assets for balance checking
    SUPPORTED_QUOTE_ASSETS = env.list('SUPPORTED_QUOTE_ASSETS', default=['USDT', 'USDC', 'USD1', 'BUSD', 'USD'])

    # Solana Configuration
    SOLANA_NETWORK = env.str('SOLANA_NETWORK', 'mainnet')
    SOLANA_RPC_ENDPOINT = env.str('SOLANA_RPC_ENDPOINT', 'https://api.mainnet-beta.solana.com')
    SOLANA_TREASURY_WALLET = env.str('SOLANA_TREASURY_WALLET', None)
    SOLANA_USDT_MINT = env.str('SOLANA_USDT_MINT', 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB')
    SOLANA_USDC_MINT = env.str('SOLANA_USDC_MINT', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')

    # Tier 2 Payment Configuration
    TIER_2_PAYMENT_AMOUNT = env.float('TIER_2_PAYMENT_AMOUNT', 199.0)
    TIER_2_PAYMENT_TOKEN = env.str('TIER_2_PAYMENT_TOKEN', 'USDT')
    TIER_2_PAYMENT_TOKEN_MINT = env.str('TIER_2_PAYMENT_TOKEN_MINT', 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB')


class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    DEVELOPMENT = True
    WTF_CSRF_ENABLED = False


class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DEBUG = True
    DB_NAME = 'deeptrade_test'
    SQLALCHEMY_DATABASE_URI = f'mysql+mysqldb://{Config.DB_USER}:{Config.DB_PASSWORD}@{Config.DB_HOST}:{Config.DB_PORT}/{DB_NAME}?charset=utf8mb4'
    WTF_CSRF_ENABLED = False
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1 hour for testing


class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    DEVELOPMENT = False
    
    # Production security settings
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # Force HTTPS
    PREFERRED_URL_SCHEME = 'https'


config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}