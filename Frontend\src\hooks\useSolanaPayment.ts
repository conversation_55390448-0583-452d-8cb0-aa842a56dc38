import { useCallback } from 'react';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { 
  PublicKey, 
  Transaction, 
  SystemProgram,
  LAMPORTS_PER_SOL 
} from '@solana/web3.js';
import {
  TOKEN_PROGRAM_ID,
  getAssociatedTokenAddress,
  createTransferInstruction,
  createAssociatedTokenAccountInstruction,
  getAccount
} from '@solana/spl-token';

interface PaymentParams {
  amount: number;
  recipientAddress: string;
  tokenMint?: string; // Optional token mint address
  tokenSymbol?: string; // Optional token symbol for display
  onSuccess?: (signature: string) => void;
  onError?: (error: Error) => void;
}

export const useSolanaPayment = () => {
  const { connection } = useConnection();
  const { publicKey, sendTransaction } = useWallet();

  // USDT mint address on Solana mainnet
  const USDT_MINT = new PublicKey('Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB');

  const sendTokenPayment = useCallback(async ({
    amount,
    recipientAddress,
    tokenMint,
    tokenSymbol = 'TOKEN',
    onSuccess,
    onError
  }: PaymentParams) => {
    console.log('🔍 sendTokenPayment called with:', {
      amount,
      recipientAddress,
      tokenMint,
      tokenSymbol
    });

    if (!publicKey || !sendTransaction) {
      const error = new Error('Wallet not connected');
      onError?.(error);
      throw error;
    }

    try {
      // Use provided token mint or default to USDT
      const TOKEN_MINT = tokenMint ? new PublicKey(tokenMint) : USDT_MINT;

      console.log(`Starting ${tokenSymbol} payment...`, { amount, recipientAddress, tokenMint });

      const recipientPubkey = new PublicKey(recipientAddress);

      // Get the sender's token account
      const senderTokenAccount = await getAssociatedTokenAddress(
        TOKEN_MINT,
        publicKey
      );

      // Get the recipient's token account
      const recipientTokenAccount = await getAssociatedTokenAddress(
        TOKEN_MINT,
        recipientPubkey
      );

      console.log('Token accounts:', {
        sender: senderTokenAccount.toString(),
        recipient: recipientTokenAccount.toString()
      });

      // Check if sender has tokens
      try {
        const senderAccount = await getAccount(connection, senderTokenAccount);
        const balance = Number(senderAccount.amount) / 1000000; // Most tokens have 6 decimals
        const requiredAmount = amount;

        console.log(`Sender ${tokenSymbol} balance:`, balance);
        console.log(`Required ${tokenSymbol} amount:`, requiredAmount);

        if (balance < requiredAmount) {
          throw new Error(`Insufficient ${tokenSymbol} balance. You have ${balance} ${tokenSymbol}, but need ${requiredAmount} ${tokenSymbol}.`);
        }

        // Check for minimum transaction amount
        if (requiredAmount < 0.01) {
          console.warn(`Very small transaction amount: ${requiredAmount} ${tokenSymbol}. This might cause simulation issues.`);
        }

      } catch (error) {
        console.error(`Token account error for ${tokenSymbol}:`, error);
        if (error instanceof Error && error.message.includes('could not find account')) {
          throw new Error(`No ${tokenSymbol} token account found. Please create a ${tokenSymbol} account in your wallet first.`);
        }
        throw new Error(`No ${tokenSymbol} tokens found in wallet or insufficient balance`);
      }

      // Create transaction
      const transaction = new Transaction();

      // Check if recipient token account exists, if not create it
      try {
        await getAccount(connection, recipientTokenAccount);
        console.log('Recipient token account exists');
      } catch (error) {
        console.log('Recipient token account does not exist, creating it...');
        // Add instruction to create the recipient token account
        const createAccountInstruction = createAssociatedTokenAccountInstruction(
          publicKey, // payer
          recipientTokenAccount, // associated token account
          new PublicKey(recipientAddress), // owner
          new PublicKey(tokenMint || USDT_MINT.toString()) // mint
        );
        transaction.add(createAccountInstruction);
      }

      // Add token transfer instruction
      const transferInstruction = createTransferInstruction(
        senderTokenAccount,
        recipientTokenAccount,
        publicKey,
        amount * 1000000, // Most tokens have 6 decimals
        [],
        TOKEN_PROGRAM_ID
      );

      transaction.add(transferInstruction);

      // Get recent blockhash
      const { blockhash } = await connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = publicKey;

      console.log('Sending transaction...');

      // Send transaction
      const signature = await sendTransaction(transaction, connection);

      console.log('Transaction sent:', signature);

      // Wait for confirmation
      const confirmation = await connection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}`);
      }

      console.log('Transaction confirmed:', signature);
      onSuccess?.(signature);
      return signature;

    } catch (error) {
      console.error(`${tokenSymbol} payment error:`, error);
      const paymentError = error instanceof Error ? error : new Error(`${tokenSymbol} payment failed`);
      onError?.(paymentError);
      throw paymentError;
    }
  }, [connection, publicKey, sendTransaction]);

  const sendSOLPayment = useCallback(async ({
    amount,
    recipientAddress,
    onSuccess,
    onError
  }: PaymentParams) => {
    if (!publicKey || !sendTransaction) {
      const error = new Error('Wallet not connected');
      onError?.(error);
      throw error;
    }

    try {
      console.log('Starting SOL payment...', { amount, recipientAddress });

      const recipientPubkey = new PublicKey(recipientAddress);
      
      // Create transaction
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: publicKey,
          toPubkey: recipientPubkey,
          lamports: amount * LAMPORTS_PER_SOL,
        })
      );

      // Get recent blockhash
      const { blockhash } = await connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = publicKey;

      console.log('Sending SOL transaction...');

      // Send transaction
      const signature = await sendTransaction(transaction, connection);

      console.log('SOL transaction sent:', signature);

      // Wait for confirmation
      const confirmation = await connection.confirmTransaction(signature, 'confirmed');
      
      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${confirmation.value.err}`);
      }

      console.log('SOL transaction confirmed:', signature);
      onSuccess?.(signature);
      return signature;

    } catch (error) {
      console.error('SOL payment error:', error);
      const paymentError = error instanceof Error ? error : new Error('SOL payment failed');
      onError?.(paymentError);
      throw paymentError;
    }
  }, [connection, publicKey, sendTransaction]);

  // Legacy function name for backward compatibility
  const sendUSDTPayment = useCallback((params: PaymentParams) => {
    return sendTokenPayment({
      ...params,
      tokenMint: params.tokenMint || USDT_MINT.toString(),
      tokenSymbol: params.tokenSymbol || 'USDT'
    });
  }, [sendTokenPayment]);

  return {
    sendUSDTPayment,
    sendTokenPayment,
    sendSOLPayment,
    isWalletConnected: !!publicKey,
    walletAddress: publicKey?.toString()
  };
};
