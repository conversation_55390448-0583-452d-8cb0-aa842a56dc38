<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download Flag Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .flag-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .flag-item { border: 1px solid #ddd; padding: 15px; border-radius: 8px; text-align: center; }
        .flag-item img { width: 64px; height: 48px; object-fit: cover; border: 1px solid #ccc; }
        .download-btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px; }
        .download-btn:hover { background: #0056b3; }
        .instructions { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>DeepTrade Flag Icons Download Helper</h1>
    
    <div class="instructions">
        <h3>Instructions:</h3>
        <ol>
            <li>Right-click on each flag image below</li>
            <li>Select "Save image as..."</li>
            <li>Save with the exact filename shown (e.g., "us.png")</li>
            <li>Save all files to: <code>frontend/public/icons/flags/</code></li>
        </ol>
    </div>

    <div class="flag-grid">
        <div class="flag-item">
            <h4>United States (English)</h4>
            <img src="https://flagsapi.com/US/flat/64.png" alt="US Flag">
            <br>
            <strong>Filename: us.png</strong>
            <br>
            <a href="https://flagsapi.com/US/flat/64.png" download="us.png" class="download-btn">Download us.png</a>
        </div>

        <div class="flag-item">
            <h4>Spain (Spanish)</h4>
            <img src="https://flagsapi.com/ES/flat/64.png" alt="ES Flag">
            <br>
            <strong>Filename: es.png</strong>
            <br>
            <a href="https://flagsapi.com/ES/flat/64.png" download="es.png" class="download-btn">Download es.png</a>
        </div>

        <div class="flag-item">
            <h4>Brazil (Portuguese)</h4>
            <img src="https://flagsapi.com/BR/flat/64.png" alt="BR Flag">
            <br>
            <strong>Filename: br.png</strong>
            <br>
            <a href="https://flagsapi.com/BR/flat/64.png" download="br.png" class="download-btn">Download br.png</a>
        </div>

        <div class="flag-item">
            <h4>South Korea (Korean)</h4>
            <img src="https://flagsapi.com/KR/flat/64.png" alt="KR Flag">
            <br>
            <strong>Filename: kr.png</strong>
            <br>
            <a href="https://flagsapi.com/KR/flat/64.png" download="kr.png" class="download-btn">Download kr.png</a>
        </div>

        <div class="flag-item">
            <h4>Japan (Japanese)</h4>
            <img src="https://flagsapi.com/JP/flat/64.png" alt="JP Flag">
            <br>
            <strong>Filename: jp.png</strong>
            <br>
            <a href="https://flagsapi.com/JP/flat/64.png" download="jp.png" class="download-btn">Download jp.png</a>
        </div>

        <div class="flag-item">
            <h4>Germany (German)</h4>
            <img src="https://flagsapi.com/DE/flat/64.png" alt="DE Flag">
            <br>
            <strong>Filename: de.png</strong>
            <br>
            <a href="https://flagsapi.com/DE/flat/64.png" download="de.png" class="download-btn">Download de.png</a>
        </div>

        <div class="flag-item">
            <h4>France (French)</h4>
            <img src="https://flagsapi.com/FR/flat/64.png" alt="FR Flag">
            <br>
            <strong>Filename: fr.png</strong>
            <br>
            <a href="https://flagsapi.com/FR/flat/64.png" download="fr.png" class="download-btn">Download fr.png</a>
        </div>

        <div class="flag-item">
            <h4>China (Chinese)</h4>
            <img src="https://flagsapi.com/CN/flat/64.png" alt="CN Flag">
            <br>
            <strong>Filename: cn.png</strong>
            <br>
            <a href="https://flagsapi.com/CN/flat/64.png" download="cn.png" class="download-btn">Download cn.png</a>
        </div>
    </div>

    <div class="instructions">
        <h3>Alternative Download Methods:</h3>
        <p><strong>PowerShell Commands:</strong> (Run in the flags folder)</p>
        <pre>
Invoke-WebRequest -Uri "https://flagsapi.com/US/flat/64.png" -OutFile "us.png"
Invoke-WebRequest -Uri "https://flagsapi.com/ES/flat/64.png" -OutFile "es.png"
Invoke-WebRequest -Uri "https://flagsapi.com/BR/flat/64.png" -OutFile "br.png"
Invoke-WebRequest -Uri "https://flagsapi.com/KR/flat/64.png" -OutFile "kr.png"
Invoke-WebRequest -Uri "https://flagsapi.com/JP/flat/64.png" -OutFile "jp.png"
Invoke-WebRequest -Uri "https://flagsapi.com/DE/flat/64.png" -OutFile "de.png"
Invoke-WebRequest -Uri "https://flagsapi.com/FR/flat/64.png" -OutFile "fr.png"
Invoke-WebRequest -Uri "https://flagsapi.com/CN/flat/64.png" -OutFile "cn.png"
        </pre>
    </div>
</body>
</html>
