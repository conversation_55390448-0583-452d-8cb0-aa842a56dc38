
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Deep Forecast: BTCUSDT</title>
            <script src="https://cdn.tailwindcss.com"></script>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
            <!-- Updated to explicit Plotly.js version to avoid deprecated plotly-latest.min.js -->
            <script src="https://cdn.plot.ly/plotly-2.32.0.min.js"></script>
            <script>
                // Add error handling for Plotly
                window.addEventListener('load', function() {
                    if (typeof Plotly === 'undefined') {
                        // Handle Plotly loading failure
                    }
                });
                
                // Handle Plotly errors
                window.addEventListener('error', function(e) {
                    if (e.message && e.message.includes('Plotly')) {
                        // Handle Plotly errors silently
                    }
                });
                tailwind.config = {
                    darkMode: 'class',
                    theme: {
                        extend: {
                            colors: {
                                primary: { DEFAULT: '#0EA5E9', dark: '#38BDF8' },
                                secondary: { DEFAULT: '#64748B', dark: '#94A3B8' },
                                success: { DEFAULT: '#22C55E', dark: '#4ADE80' },
                                danger: { DEFAULT: '#EF4444', dark: '#F87171' },
                                warning: { DEFAULT: '#F59E0B', dark: '#FBBF24' },
                                lightBg: '#F8FAFC',
                                darkBg: '#0F172A',
                                lightCard: '#FFFFFF',
                                darkCard: '#1E293B',
                                lightText: '#1E293B',
                                darkText: '#E2E8F0',
                                lightMuted: '#64748B',
                                darkMuted: '#94A3B8'
                            },
                            fontFamily: {
                                sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', '"Noto Sans"', 'sans-serif', '"Apple Color Emoji"', '"Segoe UI Emoji"', '"Segoe UI Symbol"', '"Noto Color Emoji"']
                            },
                            boxShadow: {
                                'glow-primary': '0 0 15px rgba(14, 165, 233, 0.5)',
                                'glow-primary-hover': '0 0 25px rgba(14, 165, 233, 0.7)',
                                'glow-green': '0 0 15px rgba(34, 197, 94, 0.4)',
                                'glow-green-hover': '0 0 25px rgba(34, 197, 94, 0.6)',
                                'glow-red': '0 0 15px rgba(239, 68, 68, 0.4)',
                                'glow-red-hover': '0 0 25px rgba(239, 68, 68, 0.6)'
                            }
                        }
                    }
                }
            </script>
            <link rel="preconnect" href="https://fonts.googleapis.com">
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
            <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
            <script>
                // Immediate theme application to prevent flash
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                document.documentElement.classList.toggle('dark', theme === 'dark');
            </script>
            <style>
                /* Theme initialization */
                :root { color-scheme: light dark; }
                .hidden { display: none !important; }
                body { font-family: 'Inter', sans-serif; }
                /* Basic transition for dark mode */
                body, .bg-lightBg, .dark .bg-darkBg, .bg-lightCard, .dark .bg-darkCard, .text-lightText, .dark .text-darkText {
                    transition: background-color 0.3s ease, color 0.3s ease;
                }
                /* Custom scrollbar */
                ::-webkit-scrollbar { width: 8px; height: 8px; }
                ::-webkit-scrollbar-track { background: transparent; }
                ::-webkit-scrollbar-thumb { background-color: rgba(100, 116, 139, 0.5); border-radius: 10px; border: 2px solid transparent; background-clip: content-box; }
                ::-webkit-scrollbar-thumb:hover { background-color: rgba(100, 116, 139, 0.8); }

                /* Plotly Dark Mode Adjustments */
                .dark .js-plotly-plot .plotly .gridlayer path { stroke: rgba(226, 232, 240, 0.15) !important; }
                .dark .js-plotly-plot .plotly .xaxislayer-above path,
                .dark .js-plotly-plot .plotly .yaxislayer-above path { stroke: rgba(226, 232, 240, 0.6) !important; }
                .dark .js-plotly-plot .plotly text { fill: #E2E8F0 !important; }
                .dark .js-plotly-plot .plotly .legend { bgcolor: rgba(30, 41, 59, 0.9) !important; bordercolor: rgba(148, 163, 184, 0.4) !important; }
                .dark .js-plotly-plot .plotly .legend text { fill: #F1F5F9 !important; }
                .dark .modebar-btn path { fill: #E2E8F0 !important; }
                /* Dark theme hover and label styles */
                .dark .js-plotly-plot .plotly .hoverlayer .hover text,
                .dark .js-plotly-plot .plotly .hoverlayer .axistext text { fill: #F1F5F9 !important; }
                .dark .js-plotly-plot .plotly .hoverlayer .hover rect,
                .dark .js-plotly-plot .plotly .hoverlayer .axistext rect { fill: rgba(30, 41, 59, 0.8) !important; stroke: rgba(148, 163, 184, 0.2) !important; }
                .dark .js-plotly-plot .plotly .hover text,
                .dark .js-plotly-plot .plotly g.hoverlayer .axistext text { fill: #F1F5F9 !important; }
                .dark .js-plotly-plot .plotly .hoverlayer:not(.crisp) rect { fill: rgba(30, 41, 59, 0.8) !important; }

                /* Light mode hover styles */
                .js-plotly-plot .plotly .hoverlayer .axistext rect { fill: rgba(255, 255, 255, 0.8) !important; }
                .js-plotly-plot .plotly .hoverlayer:not(.crisp) rect { fill: rgba(255, 255, 255, 0.8) !important; }
                /* Dark theme spike line styles */
                .dark .js-plotly-plot .plotly .spikeline { stroke: rgba(226, 232, 240, 0.4) !important; }

                /* Chart container */
                .chart-container {
                    background-color: transparent !important;
                    position: relative;
                    width: 100%;
                }
                .chart-container .js-plotly-plot {
                    width: 100% !important;
                }
                @media (max-width: 768px) {
                    .chart-container {
                        height: 240px !important;
                        overflow-x: auto;
                        position: relative;
                    }
                    .chart-container .js-plotly-plot {
                        min-width: 360px !important;
                        width: 360px !important;
                    }
                    .chart-container .plot-container,
                    .chart-container .main-svg {
                        width: 100% !important;
                    }
                }
                @media (min-width: 769px) {
                    .chart-container {
                        height: 600px !important;
                    }
                }

                /* Responsive table text */
                @media (max-width: 640px) {
                    .responsive-table th, .responsive-table td { font-size: 0.75rem; padding-left: 0.5rem; padding-right: 0.5rem; }
                    .price-card-text { font-size: 1.5rem; }
                    .js-plotly-plot { max-width: 100% !important; }
                    .js-plotly-plot .main-svg { width: 100% !important; }
                    .js-plotly-plot .plot-container { height: auto !important; }
                }
                .price-card-text { font-size: 1.625rem; }
                @media (max-width: 1024px) {
                    .price-card-text { font-size: 1.5rem; }
                }
            </style>
        </head>
        <body class="bg-lightBg dark:bg-darkBg text-lightText dark:text-darkText min-h-screen">
            <div class="container mx-auto px-4 py-8 max-w-8xl transition-all duration-300">
                <!-- Header -->
                <div class="bg-gradient-to-r from-primary dark:from-primary-dark to-sky-700 dark:to-sky-800 rounded-lg p-6 mb-8 text-white shadow-lg relative">
                    <div class="flex flex-col sm:flex-row justify-between items-center">
                        <div class="mb-4 sm:mb-0 text-center sm:text-left">
                            <h1 class="text-2xl md:text-3xl font-bold mb-1">Deep Forecast AI</h1>
                            <p class="text-sm opacity-90">Powered by www.capitolchilax.com</p>
                            </p>   
                        </div>
                        <div class="text-center sm:text-right">
                            <div class="flex items-center justify-center sm:justify-end space-x-4">
                                <div>
                                    <p class="text-xs opacity-80">Last Updated (UTC)</p>
                                    <p class="text-base font-semibold">2025-06-15 15:09:08</p>
                                    <p class="text-xs opacity-80">Updates Hourly</p>
                                </div>
                                <button id="theme-toggle" class="bg-white/20 hover:bg-white/30 text-white rounded-full p-2.5 transition duration-300" aria-label="Toggle theme">
                                    <i class="fas fa-moon block"></i>
                                    <i class="fas fa-sun hidden"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Price Cards -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 md:gap-4 mb-6">
                    
        <div class="group bg-lightCard dark:bg-darkCard rounded-xl p-4 transform transition-all duration-300 hover:scale-105 border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-glow-primary">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-secondary dark:text-secondary-dark tracking-wide uppercase">Current Price</h3>
                <i class="fas fa-dollar-sign text-primary dark:text-primary-dark text-lg opacity-70 group-hover:opacity-100 transition-opacity"></i>
            </div>
            <p class="price-card-text font-bold text-primary dark:text-primary-dark">$105,689.9000</p>
        </div>
    
        <div class="group bg-lightCard dark:bg-darkCard rounded-xl p-4 transform transition-all duration-300 hover:scale-105 border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-glow-green">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-secondary dark:text-secondary-dark tracking-wide uppercase">Highest Upper</h3>
                <i class="fas fa-arrow-trend-up text-success dark:text-success-dark text-lg opacity-70 group-hover:opacity-100 transition-opacity"></i>
            </div>
            <p class="price-card-text font-bold text-success dark:text-success-dark">$107,174.6851</p>
        </div>
    
        <div class="group bg-lightCard dark:bg-darkCard rounded-xl p-4 transform transition-all duration-300 hover:scale-105 border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-glow-green">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-secondary dark:text-secondary-dark tracking-wide uppercase">Predicted High</h3>
                <i class="fas fa-chart-line text-success dark:text-success-dark text-lg opacity-70 group-hover:opacity-100 transition-opacity"></i>
            </div>
            <p class="price-card-text font-bold text-success dark:text-success-dark">$106,113.5496</p>
        </div>
    
        <div class="group bg-lightCard dark:bg-darkCard rounded-xl p-4 transform transition-all duration-300 hover:scale-105 border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-glow-red">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-secondary dark:text-secondary-dark tracking-wide uppercase">Predicted Low</h3>
                <i class="fas fa-chart-line text-danger dark:text-danger-dark text-lg opacity-70 group-hover:opacity-100 transition-opacity"></i>
            </div>
            <p class="price-card-text font-bold text-danger dark:text-danger-dark">$105,161.9405</p>
        </div>
    
        <div class="group bg-lightCard dark:bg-darkCard rounded-xl p-4 transform transition-all duration-300 hover:scale-105 border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-glow-red">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-secondary dark:text-secondary-dark tracking-wide uppercase">Lowest Lower</h3>
                <i class="fas fa-arrow-trend-down text-danger dark:text-danger-dark text-lg opacity-70 group-hover:opacity-100 transition-opacity"></i>
            </div>
            <p class="price-card-text font-bold text-danger dark:text-danger-dark">$104,110.3211</p>
        </div>
    
                </div>

                <!-- Chart Section -->
                <div class="bg-lightCard dark:bg-darkCard rounded-xl p-4 md:p-6 mb-8 border border-gray-200 dark:border-gray-700 shadow-md">
                    <div class="chart-container overflow-x-auto max-h-[500px] md:max-h-[600px] relative rounded-lg w-full">
                        <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-2.26.0.min.js"></script>                <div id="34c1f16f-76fb-4628-8e70-8b1c5b6de5ec" class="plotly-graph-div" style="height:600px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("34c1f16f-76fb-4628-8e70-8b1c5b6de5ec")) {                    Plotly.newPlot(                        "34c1f16f-76fb-4628-8e70-8b1c5b6de5ec",                        [{"close":[108720.61,108711.37,109282.38,109526.3,109960.0,109863.07,109727.87,110274.39,109825.54,109605.53,109677.03,109722.95,109562.75,109468.03,109595.01,109446.74,109576.02,109269.83,109373.12,109252.09,109714.87,109684.0,109866.69,109735.7,109439.38,108984.72,108807.25,108742.37,108906.44,108508.18,108443.38,108645.12,108705.99,108515.99,108400.52,107860.05,107765.12,107746.91,107909.02,107633.53,107731.67,107507.35,107323.16,106900.95,106988.54,107070.8,107612.69,107020.95,107744.71,108287.4,107561.35,106788.8,105973.62,105894.31,105890.7,105671.73,103797.81,103773.65,103641.21,104277.22,104404.52,104002.25,104704.26,104895.0,104739.45,104830.36,104874.01,105039.48,104852.29,104732.44,104970.74,105436.87,105783.66,105390.94,105193.04,105110.0,105424.0,105612.59,105751.28,106066.59,105826.32,105470.35,105374.17,105228.82,105371.07,105288.3,105105.13,105076.59,105095.03,104862.26,105011.92,105082.01,105053.63,105017.41,104913.01,104841.3,104457.85,104608.17,104892.81,104645.67,104894.29,105315.54,105428.74,105414.64,105643.99,105421.03,105481.02,105473.55,105559.68,106073.94,105440.69,105428.0,105197.19,104915.15,105119.99,104970.51,105289.81,105515.97,105657.64,105689.9],"decreasing":{"fillcolor":"rgba(239, 68, 68, 0.6)","line":{"color":"#EF4444"}},"high":["109251.41000000","109064.06000000","109480.67000000","110400.00000000","110018.07000000","110000.00000000","110181.10000000","110299.73000000","110274.40000000","109832.94000000","109858.22000000","109858.23000000","109735.24000000","109650.59000000","109679.24000000","109745.29000000","109675.69000000","109603.57000000","109468.67000000","109391.75000000","109927.04000000","109900.00000000","110392.01000000","110042.90000000","109815.84000000","109768.85000000","109072.91000000","108960.00000000","109001.50000000","109219.51000000","108633.38000000","108691.87000000","108813.55000000","108706.00000000","108759.00000000","108448.03000000","107917.41000000","107922.43000000","107993.58000000","107961.01000000","107835.83000000","107771.55000000","107518.85000000","107349.31000000","107431.82000000","107198.75000000","107721.17000000","107822.50000000","107858.32000000","108450.16000000","108419.21000000","107879.92000000","106960.77000000","106430.39000000","106225.40000000","106140.00000000","105694.00000000","103938.59000000","103976.64000000","104590.00000000","104506.36000000","104450.00000000","104722.50000000","104970.12000000","105434.34000000","104916.00000000","104925.10000000","105045.79000000","105290.14000000","105300.00000000","105011.76000000","105747.47000000","105981.00000000","105783.65000000","105480.00000000","105436.90000000","105508.34000000","105788.79000000","105840.00000000","106179.53000000","106252.00000000","105826.33000000","105611.67000000","105440.00000000","105397.06000000","105397.06000000","105321.82000000","105144.57000000","105132.36000000","105110.15000000","105117.07000000","105096.00000000","105100.00000000","105131.25000000","105060.42000000","104983.05000000","104900.00000000","104721.53000000","104995.72000000","105045.78000000","104991.00000000","105458.71000000","105480.00000000","105480.00000000","105663.98000000","105680.00000000","105624.00000000","105566.22000000","105563.04000000","106100.00000000","106128.57000000","105453.46000000","105482.40000000","105197.19000000","105188.96000000","105162.51000000","105296.00000000","105660.31000000","105838.24000000","105705.89000000"],"increasing":{"fillcolor":"rgba(34, 197, 94, 0.6)","line":{"color":"#22C55E"}},"low":["108690.59000000","108616.68000000","108518.86000000","109282.37000000","109239.68000000","109420.00000000","109610.40000000","109600.00000000","109780.00000000","109565.10000000","109533.97000000","109622.03000000","109552.68000000","109430.38000000","109373.67000000","109446.73000000","109446.73000000","109233.62000000","109200.00000000","109193.89000000","109008.82000000","109301.42000000","109650.94000000","109660.72000000","109341.79000000","108964.16000000","108412.01000000","108576.00000000","108480.00000000","108367.94000000","108064.00000000","108324.00000000","108326.55000000","108258.49000000","108390.34000000","107730.00000000","107320.00000000","107593.67000000","107640.33000000","107438.65000000","107440.13000000","107488.95000000","107200.00000000","106874.47000000","106870.00000000","106600.00000000","107070.80000000","106890.88000000","106985.38000000","107702.42000000","107229.47000000","106680.00000000","105772.73000000","105860.90000000","105726.65000000","105671.72000000","103213.87000000","102664.31000000","103065.58000000","103605.12000000","103782.75000000","104000.00000000","103825.11000000","104376.43000000","104690.85000000","104500.00000000","104425.79000000","104640.00000000","104822.36000000","104674.14000000","104118.00000000","104860.00000000","105308.83000000","104867.65000000","104668.73000000","104900.00000000","105020.87000000","105326.41000000","105542.00000000","105751.28000000","105703.61000000","105440.00000000","105312.88000000","105197.96000000","105212.09000000","105233.95000000","105105.13000000","104792.98000000","104871.06000000","104850.54000000","104836.06000000","104948.38000000","104745.10000000","104902.28000000","104841.96000000","104615.38000000","104359.50000000","104404.90000000","104359.08000000","104630.19000000","104300.00000000","104867.14000000","105289.45000000","105348.81000000","105264.75000000","105410.50000000","105421.03000000","105463.54000000","105473.54000000","105507.65000000","105410.43000000","105200.00000000","105134.69000000","104867.64000000","104915.14000000","104915.14000000","104955.48000000","105220.65000000","105308.82000000","105564.73000000"],"name":"Current Price","open":["109015.41000000","108720.61000000","108711.37000000","109282.38000000","109526.30000000","109960.01000000","109863.06000000","109727.88000000","110274.39000000","109825.55000000","109605.53000000","109677.03000000","109722.95000000","109562.75000000","109468.04000000","109595.02000000","109446.74000000","109576.03000000","109269.82000000","109373.13000000","109252.10000000","109714.87000000","109684.00000000","109866.69000000","109735.70000000","109439.38000000","108984.72000000","108807.25000000","108742.37000000","108906.45000000","108508.17000000","108443.38000000","108645.13000000","108706.00000000","108515.99000000","108400.52000000","107860.04000000","107765.13000000","107746.91000000","107909.02000000","107633.53000000","107731.67000000","107507.35000000","107323.16000000","106900.95000000","106988.55000000","107070.80000000","107612.70000000","107020.96000000","107744.70000000","108287.40000000","107561.36000000","106789.80000000","105973.61000000","105894.32000000","105890.69000000","105671.74000000","103797.81000000","103773.65000000","103641.20000000","104277.22000000","104404.52000000","104002.26000000","104704.27000000","104895.01000000","104739.44000000","104830.36000000","104874.00000000","105039.48000000","104852.28000000","104732.82000000","104970.74000000","105436.87000000","105783.65000000","105390.94000000","105193.04000000","105110.01000000","105424.00000000","105612.58000000","105751.29000000","106066.59000000","105826.32000000","105470.35000000","105374.18000000","105228.83000000","105371.08000000","105288.30000000","105105.14000000","105076.59000000","105095.04000000","104862.26000000","105011.92000000","105082.01000000","105053.64000000","105017.41000000","104913.01000000","104841.29000000","104457.84000000","104608.18000000","104892.82000000","104645.66000000","104894.30000000","105315.55000000","105428.75000000","105414.63000000","105643.99000000","105421.03000000","105481.03000000","105473.54000000","105559.69000000","106073.95000000","105440.70000000","105428.00000000","105197.18000000","104915.15000000","105119.99000000","104970.51000000","105289.81000000","105515.97000000","105657.63000000"],"x":["2025-06-10T16:00:00","2025-06-10T17:00:00","2025-06-10T18:00:00","2025-06-10T19:00:00","2025-06-10T20:00:00","2025-06-10T21:00:00","2025-06-10T22:00:00","2025-06-10T23:00:00","2025-06-11T00:00:00","2025-06-11T01:00:00","2025-06-11T02:00:00","2025-06-11T03:00:00","2025-06-11T04:00:00","2025-06-11T05:00:00","2025-06-11T06:00:00","2025-06-11T07:00:00","2025-06-11T08:00:00","2025-06-11T09:00:00","2025-06-11T10:00:00","2025-06-11T11:00:00","2025-06-11T12:00:00","2025-06-11T13:00:00","2025-06-11T14:00:00","2025-06-11T15:00:00","2025-06-11T16:00:00","2025-06-11T17:00:00","2025-06-11T18:00:00","2025-06-11T19:00:00","2025-06-11T20:00:00","2025-06-11T21:00:00","2025-06-11T22:00:00","2025-06-11T23:00:00","2025-06-12T00:00:00","2025-06-12T01:00:00","2025-06-12T02:00:00","2025-06-12T03:00:00","2025-06-12T04:00:00","2025-06-12T05:00:00","2025-06-12T06:00:00","2025-06-12T07:00:00","2025-06-12T08:00:00","2025-06-12T09:00:00","2025-06-12T10:00:00","2025-06-12T11:00:00","2025-06-12T12:00:00","2025-06-12T13:00:00","2025-06-12T14:00:00","2025-06-12T15:00:00","2025-06-12T16:00:00","2025-06-12T17:00:00","2025-06-12T18:00:00","2025-06-12T19:00:00","2025-06-12T20:00:00","2025-06-12T21:00:00","2025-06-12T22:00:00","2025-06-12T23:00:00","2025-06-13T00:00:00","2025-06-13T01:00:00","2025-06-13T02:00:00","2025-06-13T03:00:00","2025-06-13T04:00:00","2025-06-13T05:00:00","2025-06-13T06:00:00","2025-06-13T07:00:00","2025-06-13T08:00:00","2025-06-13T09:00:00","2025-06-13T10:00:00","2025-06-13T11:00:00","2025-06-13T12:00:00","2025-06-13T13:00:00","2025-06-13T14:00:00","2025-06-13T15:00:00","2025-06-13T16:00:00","2025-06-13T17:00:00","2025-06-13T18:00:00","2025-06-13T19:00:00","2025-06-13T20:00:00","2025-06-13T21:00:00","2025-06-13T22:00:00","2025-06-13T23:00:00","2025-06-14T00:00:00","2025-06-14T01:00:00","2025-06-14T02:00:00","2025-06-14T03:00:00","2025-06-14T04:00:00","2025-06-14T05:00:00","2025-06-14T06:00:00","2025-06-14T07:00:00","2025-06-14T08:00:00","2025-06-14T09:00:00","2025-06-14T10:00:00","2025-06-14T11:00:00","2025-06-14T12:00:00","2025-06-14T13:00:00","2025-06-14T14:00:00","2025-06-14T15:00:00","2025-06-14T16:00:00","2025-06-14T17:00:00","2025-06-14T18:00:00","2025-06-14T19:00:00","2025-06-14T20:00:00","2025-06-14T21:00:00","2025-06-14T22:00:00","2025-06-14T23:00:00","2025-06-15T00:00:00","2025-06-15T01:00:00","2025-06-15T02:00:00","2025-06-15T03:00:00","2025-06-15T04:00:00","2025-06-15T05:00:00","2025-06-15T06:00:00","2025-06-15T07:00:00","2025-06-15T08:00:00","2025-06-15T09:00:00","2025-06-15T10:00:00","2025-06-15T11:00:00","2025-06-15T12:00:00","2025-06-15T13:00:00","2025-06-15T14:00:00","2025-06-15T15:00:00"],"type":"candlestick"},{"hovertemplate":"Price: $%{y:.4f}\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"rgba(14, 165, 233, 0.9)","dash":"dot","width":2.5},"mode":"lines","name":"Predicted Price","x":["2025-06-15T16:00:00","2025-06-15T17:00:00","2025-06-15T18:00:00","2025-06-15T19:00:00","2025-06-15T20:00:00","2025-06-15T21:00:00","2025-06-15T22:00:00","2025-06-15T23:00:00","2025-06-16T00:00:00","2025-06-16T01:00:00","2025-06-16T02:00:00","2025-06-16T03:00:00","2025-06-16T04:00:00","2025-06-16T05:00:00","2025-06-16T06:00:00","2025-06-16T07:00:00","2025-06-16T08:00:00","2025-06-16T09:00:00","2025-06-16T10:00:00","2025-06-16T11:00:00","2025-06-16T12:00:00","2025-06-16T13:00:00","2025-06-16T14:00:00","2025-06-16T15:00:00","2025-06-16T16:00:00","2025-06-16T17:00:00","2025-06-16T18:00:00","2025-06-16T19:00:00","2025-06-16T20:00:00","2025-06-16T21:00:00","2025-06-16T22:00:00","2025-06-16T23:00:00","2025-06-17T00:00:00","2025-06-17T01:00:00","2025-06-17T02:00:00","2025-06-17T03:00:00","2025-06-17T04:00:00","2025-06-17T05:00:00","2025-06-17T06:00:00","2025-06-17T07:00:00","2025-06-17T08:00:00","2025-06-17T09:00:00","2025-06-17T10:00:00","2025-06-17T11:00:00","2025-06-17T12:00:00","2025-06-17T13:00:00","2025-06-17T14:00:00","2025-06-17T15:00:00","2025-06-17T16:00:00","2025-06-17T17:00:00","2025-06-17T18:00:00","2025-06-17T19:00:00","2025-06-17T20:00:00","2025-06-17T21:00:00","2025-06-17T22:00:00","2025-06-17T23:00:00","2025-06-18T00:00:00","2025-06-18T01:00:00","2025-06-18T02:00:00","2025-06-18T03:00:00","2025-06-18T04:00:00","2025-06-18T05:00:00","2025-06-18T06:00:00","2025-06-18T07:00:00","2025-06-18T08:00:00","2025-06-18T09:00:00","2025-06-18T10:00:00","2025-06-18T11:00:00","2025-06-18T12:00:00","2025-06-18T13:00:00","2025-06-18T14:00:00","2025-06-18T15:00:00"],"y":[105674.42503992119,105578.59305477251,105641.00400597065,105742.31143535313,105751.401952307,105838.2566190993,105937.61738443632,105975.34049637929,105958.47329245383,105937.71695469115,105969.58264214503,105980.76791891122,105983.53104533521,106031.46332260422,105979.37896191631,106000.16575090865,106113.54956094033,106052.34883887052,105948.51010460353,105963.35076653275,106065.10429555962,106031.79673215201,106036.28816746241,106036.54476420845,106097.00674065393,106034.98539640836,105926.06325096644,105945.98207911443,105826.53111247378,105848.85945526793,105914.86398849414,105776.79363106607,105754.18475548636,105860.59459490994,105917.1769766111,105940.50985555147,105917.03591594278,105775.46460643064,105819.57045312396,105836.51389120182,105676.46919156583,105649.75840202578,105575.4609450748,105492.54210747156,105463.38638312834,105501.41091192841,105494.85231549665,105407.44596486381,105428.997746583,105413.74488042858,105291.13814818126,105297.75098099222,105361.39878673782,105350.11709444346,105444.59493709248,105562.35020558187,105463.81978517557,105520.70569920714,105656.79970783452,105583.21251546573,105550.66445354106,105493.46949156909,105487.75878693014,105415.88439087494,105387.07883310685,105349.82084544067,105491.73689461114,105398.57798206994,105308.02366438019,105229.56566200405,105161.94046527616,105180.7209778622],"type":"scatter"},{"hovertemplate":"Lower: $%{y:.4f}\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"rgba(34, 197, 94, 0.4)","width":0},"mode":"lines","showlegend":false,"x":["2025-06-15T16:00:00","2025-06-15T17:00:00","2025-06-15T18:00:00","2025-06-15T19:00:00","2025-06-15T20:00:00","2025-06-15T21:00:00","2025-06-15T22:00:00","2025-06-15T23:00:00","2025-06-16T00:00:00","2025-06-16T01:00:00","2025-06-16T02:00:00","2025-06-16T03:00:00","2025-06-16T04:00:00","2025-06-16T05:00:00","2025-06-16T06:00:00","2025-06-16T07:00:00","2025-06-16T08:00:00","2025-06-16T09:00:00","2025-06-16T10:00:00","2025-06-16T11:00:00","2025-06-16T12:00:00","2025-06-16T13:00:00","2025-06-16T14:00:00","2025-06-16T15:00:00","2025-06-16T16:00:00","2025-06-16T17:00:00","2025-06-16T18:00:00","2025-06-16T19:00:00","2025-06-16T20:00:00","2025-06-16T21:00:00","2025-06-16T22:00:00","2025-06-16T23:00:00","2025-06-17T00:00:00","2025-06-17T01:00:00","2025-06-17T02:00:00","2025-06-17T03:00:00","2025-06-17T04:00:00","2025-06-17T05:00:00","2025-06-17T06:00:00","2025-06-17T07:00:00","2025-06-17T08:00:00","2025-06-17T09:00:00","2025-06-17T10:00:00","2025-06-17T11:00:00","2025-06-17T12:00:00","2025-06-17T13:00:00","2025-06-17T14:00:00","2025-06-17T15:00:00","2025-06-17T16:00:00","2025-06-17T17:00:00","2025-06-17T18:00:00","2025-06-17T19:00:00","2025-06-17T20:00:00","2025-06-17T21:00:00","2025-06-17T22:00:00","2025-06-17T23:00:00","2025-06-18T00:00:00","2025-06-18T01:00:00","2025-06-18T02:00:00","2025-06-18T03:00:00","2025-06-18T04:00:00","2025-06-18T05:00:00","2025-06-18T06:00:00","2025-06-18T07:00:00","2025-06-18T08:00:00","2025-06-18T09:00:00","2025-06-18T10:00:00","2025-06-18T11:00:00","2025-06-18T12:00:00","2025-06-18T13:00:00","2025-06-18T14:00:00","2025-06-18T15:00:00"],"y":[104617.68078952198,104522.80712422478,104584.59396591094,104684.8883209996,104693.88793278392,104779.8740529083,104878.24121059196,104915.5870914155,104898.88855952928,104878.33978514423,104909.88681572358,104920.96023972212,104923.69573488185,104971.14868937818,104919.58517229716,104940.16409339957,105052.41406533093,104991.82535048181,104889.02500355749,104903.71725886741,105004.45325260403,104971.47876483049,104975.92528578779,104976.17931656635,105036.03667324739,104974.63554244427,104866.80261845677,104886.52225832328,104768.26580134904,104790.37086071525,104855.7153486092,104719.02569475542,104696.6429079315,104801.98864896085,104858.00520684499,104881.10475699595,104857.86555678335,104717.70996036634,104761.37474859272,104778.1487522898,104619.70449965018,104593.26081800552,104519.70633562404,104437.61668639684,104408.75251929705,104446.39680280913,104439.90379234169,104353.37150521518,104374.70776911717,104359.60743162429,104238.22676669944,104244.7734711823,104307.78479887043,104296.61592349902,104390.14898772155,104506.72670352604,104409.18158732381,104465.49864221507,104600.23171075617,104527.38039031107,104495.15780900564,104438.5347966534,104432.88119906084,104361.72554696619,104333.20804477578,104296.32263698627,104436.81952566502,104344.59220224925,104254.94342773639,104177.27000538401,104110.32106062339,104128.91376808358],"type":"scatter"},{"fill":"tonexty","fillcolor":"rgba(34, 197, 94, 0.15)","hovertemplate":"Upper: $%{y:.4f}\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"rgba(34, 197, 94, 0.4)","width":0},"mode":"lines","name":"Price Range","x":["2025-06-15T16:00:00","2025-06-15T17:00:00","2025-06-15T18:00:00","2025-06-15T19:00:00","2025-06-15T20:00:00","2025-06-15T21:00:00","2025-06-15T22:00:00","2025-06-15T23:00:00","2025-06-16T00:00:00","2025-06-16T01:00:00","2025-06-16T02:00:00","2025-06-16T03:00:00","2025-06-16T04:00:00","2025-06-16T05:00:00","2025-06-16T06:00:00","2025-06-16T07:00:00","2025-06-16T08:00:00","2025-06-16T09:00:00","2025-06-16T10:00:00","2025-06-16T11:00:00","2025-06-16T12:00:00","2025-06-16T13:00:00","2025-06-16T14:00:00","2025-06-16T15:00:00","2025-06-16T16:00:00","2025-06-16T17:00:00","2025-06-16T18:00:00","2025-06-16T19:00:00","2025-06-16T20:00:00","2025-06-16T21:00:00","2025-06-16T22:00:00","2025-06-16T23:00:00","2025-06-17T00:00:00","2025-06-17T01:00:00","2025-06-17T02:00:00","2025-06-17T03:00:00","2025-06-17T04:00:00","2025-06-17T05:00:00","2025-06-17T06:00:00","2025-06-17T07:00:00","2025-06-17T08:00:00","2025-06-17T09:00:00","2025-06-17T10:00:00","2025-06-17T11:00:00","2025-06-17T12:00:00","2025-06-17T13:00:00","2025-06-17T14:00:00","2025-06-17T15:00:00","2025-06-17T16:00:00","2025-06-17T17:00:00","2025-06-17T18:00:00","2025-06-17T19:00:00","2025-06-17T20:00:00","2025-06-17T21:00:00","2025-06-17T22:00:00","2025-06-17T23:00:00","2025-06-18T00:00:00","2025-06-18T01:00:00","2025-06-18T02:00:00","2025-06-18T03:00:00","2025-06-18T04:00:00","2025-06-18T05:00:00","2025-06-18T06:00:00","2025-06-18T07:00:00","2025-06-18T08:00:00","2025-06-18T09:00:00","2025-06-18T10:00:00","2025-06-18T11:00:00","2025-06-18T12:00:00","2025-06-18T13:00:00","2025-06-18T14:00:00","2025-06-18T15:00:00"],"y":[106731.1692903204,106634.37898532025,106697.41404603036,106799.73454970666,106808.91597183008,106896.63918529029,106996.99355828068,107035.09390134308,107018.05802537837,106997.09412423806,107029.27846856648,107040.57559810033,107043.36635578857,107091.77795583026,107039.17275153547,107060.16740841774,107174.68505654973,107112.87232725922,107007.99520564957,107022.98427419808,107125.75533851521,107092.11469947353,107096.65104913703,107096.91021185054,107157.97680806047,107095.33525037245,106985.32388347611,107005.44189990558,106884.79642359851,106907.3480498206,106974.01262837907,106834.56156737673,106811.72660304123,106919.20054085903,106976.3487463772,106999.914954107,106976.2062751022,106833.21925249495,106877.7661576552,106894.87903011384,106733.23388348149,106706.25598604603,106631.21555452555,106547.46752854627,106518.02024695963,106556.42502104769,106549.80083865162,106461.52042451245,106483.28772404883,106467.88232923287,106344.04952966307,106350.72849080214,106415.0127746052,106403.6182653879,106499.04088646341,106617.97370763769,106518.45798302733,106575.91275619922,106713.36770491287,106639.04464062038,106606.17109807648,106548.40418648478,106542.63637479943,106470.04323478369,106440.94962143792,106403.31905389507,106546.65426355726,106452.56376189063,106361.10390102399,106281.8613186241,106213.55986992893,106232.52818764083],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmapgl":[{"type":"heatmapgl","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"font":{"size":24},"text":"BTCUSDT Price Forecast","y":0.95,"x":0.5,"xanchor":"center","yanchor":"top"},"legend":{"font":{"size":12,"color":"#1E293B"},"yanchor":"top","y":0.99,"xanchor":"left","x":0.01,"bgcolor":"rgba(255, 255, 255, 0.85)","bordercolor":"rgba(0, 0, 0, 0.15)","borderwidth":1},"margin":{"l":50,"r":50,"t":80,"b":50},"xaxis":{"rangeslider":{"visible":false},"title":{"text":"Time (UTC)"},"showspikes":true,"spikemode":"across","spikesnap":"cursor","spikecolor":"#94A3B8","spikethickness":1,"spikedash":"solid","tickfont":{"size":10,"color":"#374151"},"showline":true,"linewidth":1,"linecolor":"#2E4053","mirror":true,"showgrid":true,"gridwidth":1,"gridcolor":"rgba(189, 195, 199, 0.4)"},"font":{"family":"Arial, sans-serif","size":12,"color":"#2E4053"},"hoverlabel":{"font":{"size":12,"family":"Arial, sans-serif"}},"yaxis":{"title":{"text":"Price (USD)"},"tickfont":{"size":10,"color":"#374151"},"showline":true,"linewidth":1,"linecolor":"#2E4053","mirror":true,"showgrid":true,"gridwidth":1,"gridcolor":"rgba(189, 195, 199, 0.4)","tickprefix":"$","tickformat":",.2f"},"plot_bgcolor":"rgba(0,0,0,0)","paper_bgcolor":"rgba(0,0,0,0)","autosize":true,"height":600,"hovermode":"x unified","modebar":{"orientation":"h"}},                        {"responsive": true}                    )                };                            </script>        </div>
                    </div>
                </div>

                <!-- Predictions Table -->
                <div class="bg-lightCard dark:bg-darkCard rounded-xl p-4 md:p-6 mb-8 border border-gray-200 dark:border-gray-700 shadow-md">
                    <h2 class="text-xl md:text-2xl font-semibold text-primary dark:text-primary-dark text-center mb-4 tracking-tight">Hourly Price Predictions</h2>
                    <div class="overflow-x-auto max-h-[480px] relative">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 responsive-table">
                            <thead class="bg-gray-50 dark:bg-gray-700/50 sticky top-0 z-10">
                                <tr>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-lightMuted dark:text-darkMuted uppercase tracking-wider">Date & Time (UTC)</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-primary dark:text-primary-dark uppercase tracking-wider">Predicted Price</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-success dark:text-success-dark uppercase tracking-wider">Upper Level</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-danger dark:text-danger-dark uppercase tracking-wider">Lower Level</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 text-center">
        
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 16:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,674.4250</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,731.1693</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,617.6808</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 17:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,578.5931</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,634.3790</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,522.8071</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 18:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,641.0040</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,697.4140</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,584.5940</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 19:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,742.3114</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,799.7345</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,684.8883</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 20:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,751.4020</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,808.9160</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,693.8879</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 21:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,838.2566</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,896.6392</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,779.8741</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 22:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,937.6174</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,996.9936</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,878.2412</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-15 23:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,975.3405</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,035.0939</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,915.5871</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 00:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,958.4733</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,018.0580</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,898.8886</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 01:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,937.7170</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,997.0941</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,878.3398</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 02:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,969.5826</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,029.2785</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,909.8868</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 03:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,980.7679</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,040.5756</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,920.9602</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 04:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,983.5310</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,043.3664</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,923.6957</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 05:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,031.4633</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,091.7780</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,971.1487</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 06:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,979.3790</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,039.1728</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,919.5852</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 07:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,000.1658</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,060.1674</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,940.1641</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 08:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,113.5496</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,174.6851</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$105,052.4141</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 09:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,052.3488</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,112.8723</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,991.8254</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 10:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,948.5101</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,007.9952</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,889.0250</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 11:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,963.3508</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,022.9843</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,903.7173</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 12:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,065.1043</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,125.7553</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$105,004.4533</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 13:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,031.7967</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,092.1147</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,971.4788</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 14:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,036.2882</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,096.6510</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,975.9253</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 15:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,036.5448</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,096.9102</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,976.1793</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 16:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,097.0067</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,157.9768</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$105,036.0367</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 17:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$106,034.9854</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,095.3353</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,974.6355</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 18:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,926.0633</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,985.3239</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,866.8026</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 19:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,945.9821</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$107,005.4419</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,886.5223</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 20:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,826.5311</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,884.7964</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,768.2658</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 21:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,848.8595</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,907.3480</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,790.3709</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 22:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,914.8640</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,974.0126</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,855.7153</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-16 23:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,776.7936</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,834.5616</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,719.0257</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 00:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,754.1848</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,811.7266</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,696.6429</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 01:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,860.5946</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,919.2005</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,801.9886</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 02:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,917.1770</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,976.3487</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,858.0052</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 03:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,940.5099</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,999.9150</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,881.1048</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 04:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,917.0359</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,976.2063</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,857.8656</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 05:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,775.4646</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,833.2193</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,717.7100</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 06:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,819.5705</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,877.7662</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,761.3747</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 07:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,836.5139</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,894.8790</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,778.1488</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 08:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,676.4692</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,733.2339</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,619.7045</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 09:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,649.7584</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,706.2560</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,593.2608</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 10:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,575.4609</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,631.2156</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,519.7063</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 11:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,492.5421</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,547.4675</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,437.6167</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 12:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,463.3864</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,518.0202</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,408.7525</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 13:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,501.4109</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,556.4250</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,446.3968</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 14:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,494.8523</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,549.8008</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,439.9038</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 15:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,407.4460</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,461.5204</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,353.3715</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 16:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,428.9977</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,483.2877</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,374.7078</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 17:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,413.7449</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,467.8823</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,359.6074</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 18:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,291.1381</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,344.0495</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,238.2268</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 19:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,297.7510</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,350.7285</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,244.7735</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 20:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,361.3988</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,415.0128</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,307.7848</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 21:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,350.1171</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,403.6183</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,296.6159</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 22:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,444.5949</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,499.0409</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,390.1490</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-17 23:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,562.3502</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,617.9737</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,506.7267</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 00:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,463.8198</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,518.4580</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,409.1816</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 01:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,520.7057</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,575.9128</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,465.4986</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 02:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,656.7997</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,713.3677</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,600.2317</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 03:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,583.2125</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,639.0446</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,527.3804</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 04:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,550.6645</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,606.1711</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,495.1578</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 05:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,493.4695</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,548.4042</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,438.5348</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 06:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,487.7588</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,542.6364</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,432.8812</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 07:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,415.8844</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,470.0432</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,361.7255</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 08:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,387.0788</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,440.9496</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,333.2080</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 09:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,349.8208</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,403.3191</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,296.3226</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 10:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,491.7369</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,546.6543</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,436.8195</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 11:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,398.5780</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,452.5638</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,344.5922</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 12:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,308.0237</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,361.1039</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,254.9434</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 13:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,229.5657</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,281.8613</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,177.2700</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 14:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,161.9405</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,213.5599</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,110.3211</td>
                                </tr>
            
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-150">
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center text-lightText dark:text-darkText">2025-06-18 15:00</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-primary dark:text-primary-dark">$105,180.7210</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-success dark:text-success-dark">$106,232.5282</td>
                                    <td class="px-3 py-3 whitespace-nowrap text-sm text-center font-medium text-danger dark:text-danger-dark">$104,128.9138</td>
                                </tr>
            
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Disclaimer -->
                
    <div class="mt-8 text-center bg-lightCard dark:bg-darkCard rounded-xl p-4 md:p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
        <p class="font-semibold text-base mb-2 text-warning dark:text-warning-dark">Disclaimer</p>
        <p class="text-xs text-lightMuted dark:text-darkMuted leading-relaxed">
            This tool provides experimental forecasts based on historical data and machine learning models.
            It is for informational purposes only and does not constitute financial advice.
            Market conditions can change rapidly and unpredictably. Past performance is not indicative of future results.
            Users are solely responsible for their own trading and investment decisions. No guarantee of accuracy is provided.
        </p>
    </div>
    
            </div>
        </body>
        <script>
            
        const themeToggleBtn = document.getElementById('theme-toggle');
        const htmlElement = document.documentElement;
        
        // Function to apply theme
        const applyTheme = (theme) => {
            if (theme === 'dark') {
                htmlElement.classList.add('dark');
                const moonIcon = themeToggleBtn.querySelector('.fa-moon');
                const sunIcon = themeToggleBtn.querySelector('.fa-sun');
                moonIcon.classList.add('hidden');
                sunIcon.classList.remove('hidden');
                // Update Plotly chart layout for dark theme
                if (typeof Plotly !== 'undefined') {
                    const chartDivs = document.querySelectorAll('.js-plotly-plot');
                    chartDivs.forEach(div => {
                        if (div.layout) {
                            Plotly.update(div, {}, {
                                'plot_bgcolor': 'rgba(0,0,0,0)',
                                'paper_bgcolor': 'rgba(0,0,0,0)',
                                'font.color': '#F1F5F9',
                                'xaxis.gridcolor': 'rgba(226, 232, 240, 0.15)',
                                'yaxis.gridcolor': 'rgba(226, 232, 240, 0.15)',
                                'xaxis.linecolor': 'rgba(226, 232, 240, 0.6)',
                                'yaxis.linecolor': 'rgba(226, 232, 240, 0.6)',
                                'xaxis.tickfont.color': '#F1F5F9',
                                'yaxis.tickfont.color': '#F1F5F9',
                                'legend.bgcolor': 'rgba(30, 41, 59, 0.9)',
                                'legend.font.color': '#F1F5F9',
                                'xaxis.spikecolor': 'rgba(226, 232, 240, 0.4)'
                            });
                        }
                    });
                }
            } else {
                htmlElement.classList.remove('dark');
                const moonIcon = themeToggleBtn.querySelector('.fa-moon');
                const sunIcon = themeToggleBtn.querySelector('.fa-sun');
                moonIcon.classList.remove('hidden');
                sunIcon.classList.add('hidden');
                // Update Plotly chart layout for light theme
                if (typeof Plotly !== 'undefined') {
                    const chartDivs = document.querySelectorAll('.js-plotly-plot');
                    chartDivs.forEach(div => {
                        if (div.layout) {
                            Plotly.update(div, {}, {
                                'plot_bgcolor': 'rgba(0,0,0,0)',
                                'paper_bgcolor': 'rgba(0,0,0,0)',
                                'font.color': '#1E293B',
                                'xaxis.gridcolor': 'rgba(203, 213, 225, 0.3)',
                                'yaxis.gridcolor': 'rgba(203, 213, 225, 0.3)',
                                'xaxis.linecolor': 'rgba(148, 163, 184, 0.7)',
                                'yaxis.linecolor': 'rgba(148, 163, 184, 0.7)',
                                'xaxis.spikecolor': 'rgba(100, 116, 139, 0.5)',
                                'xaxis.tickfont.color': '#1E293B',
                                'yaxis.tickfont.color': '#1E293B',
                                'legend.bgcolor': 'rgba(255, 255, 255, 0.85)',
                                'legend.font.color': '#1E293B',
                                'hoverlabel.bgcolor': 'white',
                                'hoverlabel.font.color': '#1E293B',
                                'hoverlabel.bordercolor': 'rgba(0, 0, 0, 0.1)'
                            });
                        }
                    });
                }
            }
        };

        // Check localStorage on initial load
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // Apply initial theme
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            applyTheme('dark');
            const moonIcon = themeToggleBtn.querySelector('.fa-moon');
            const sunIcon = themeToggleBtn.querySelector('.fa-sun');
            moonIcon.classList.add('hidden');
            sunIcon.classList.remove('hidden');
        } else {
            applyTheme('light');
            const moonIcon = themeToggleBtn.querySelector('.fa-moon');
            const sunIcon = themeToggleBtn.querySelector('.fa-sun');
            moonIcon.classList.remove('hidden');
            sunIcon.classList.add('hidden');
        }

        // Add event listener for theme toggle button
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = htmlElement.classList.contains('dark') ? 'dark' : 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
            
            const moonIcon = themeToggleBtn.querySelector('.fa-moon');
            const sunIcon = themeToggleBtn.querySelector('.fa-sun');
            if (newTheme === 'dark') {
                moonIcon.classList.add('hidden');
                sunIcon.classList.remove('hidden');
            } else {
                moonIcon.classList.remove('hidden');
                sunIcon.classList.add('hidden');
            }
        });

        // Ensure theme is applied after Plotly chart is rendered
        window.addEventListener('load', () => {
            const currentTheme = localStorage.getItem('theme') || (prefersDark ? 'dark' : 'light');
            applyTheme(currentTheme);
        });
    
        </script>
        </html>
        