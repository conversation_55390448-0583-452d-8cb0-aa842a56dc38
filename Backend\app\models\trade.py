# UPDATED: Changed Enum usage to String for better MySQL compatibility
import uuid
from datetime import datetime
from enum import Enum
from sqlalchemy import Enum as SQLEnum
from app import db

class TradingSessionStatus(str, Enum):
    ACTIVE = 'ACTIVE'
    PAUSED = 'PAUSED'
    STOPPED = 'STOPPED'
    COMPLETED = 'COMPLETED'

class TradeStatus(str, Enum):
    OPEN = 'OPEN'
    CLOSED = 'CLOSED'
    CANCELLED = 'CANCELLED'

class TradeSide(str, Enum):
    BUY = 'BUY'
    SELL = 'SELL'
    LONG = 'LONG'
    SHORT = 'SHORT'

class TradingSession(db.Model):
    """Model to track user trading sessions."""
    __tablename__ = 'trading_sessions'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON><PERSON>('users.id'), nullable=False)
    
    # Session details
    symbol = db.Column(db.String(20), nullable=False)  # e.g., 'BTCUSDT'
    status = db.Column(db.Enum(TradingSessionStatus), nullable=False, default=TradingSessionStatus.ACTIVE)
    
    # Balance tracking
    initial_balance = db.Column(db.Numeric(20, 8), nullable=False)
    current_balance = db.Column(db.Numeric(20, 8), nullable=False)
    
    # Settings
    leverage = db.Column(db.Integer, default=10)
    investment_percentage = db.Column(db.Integer, default=50)  # Percentage of balance to use
    
    # Performance metrics
    total_trades = db.Column(db.Integer, default=0)
    winning_trades = db.Column(db.Integer, default=0)
    losing_trades = db.Column(db.Integer, default=0)
    total_profit = db.Column(db.Numeric(20, 8), default=0)
    total_loss = db.Column(db.Numeric(20, 8), default=0)
    
    # Timestamps
    started_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    ended_at = db.Column(db.DateTime)
    
    # Relationships
    trades = db.relationship('Trade', backref='session', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, user_id, symbol, initial_balance, leverage=10, investment_percentage=50):
        self.user_id = user_id
        self.symbol = symbol
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.leverage = leverage
        self.investment_percentage = investment_percentage
    
    def get_roi(self):
        """Calculate return on investment percentage."""
        if self.initial_balance == 0:
            return 0
        return ((self.current_balance - self.initial_balance) / self.initial_balance) * 100
    
    def get_win_rate(self):
        """Calculate win rate percentage."""
        if self.total_trades == 0:
            return 0
        return (self.winning_trades / self.total_trades) * 100
    
    def get_profit_factor(self):
        """Calculate profit factor (total profit / total loss)."""
        if self.total_loss == 0:
            return float('inf') if self.total_profit > 0 else 0
        return abs(self.total_profit / self.total_loss)
    
    def update_balance(self, new_balance):
        """Update current balance."""
        self.current_balance = new_balance
    
    def add_trade_result(self, pnl):
        """Add trade result to session statistics."""
        self.total_trades += 1
        
        if pnl > 0:
            self.winning_trades += 1
            self.total_profit += pnl
        else:
            self.losing_trades += 1
            self.total_loss += abs(pnl)
        
        # Update current balance
        self.current_balance += pnl
    
    def pause_session(self):
        """Pause the trading session."""
        self.status = TradingSessionStatus.PAUSED
    
    def resume_session(self):
        """Resume the trading session."""
        self.status = TradingSessionStatus.ACTIVE
    
    def stop_session(self):
        """Stop the trading session."""
        self.status = TradingSessionStatus.STOPPED
        self.ended_at = datetime.utcnow()
    
    def complete_session(self):
        """Complete the trading session."""
        self.status = TradingSessionStatus.COMPLETED
        self.ended_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'symbol': self.symbol,
            'status': self.status.value,
            'initial_balance': float(self.initial_balance),
            'current_balance': float(self.current_balance),
            'leverage': self.leverage,
            'investment_percentage': self.investment_percentage,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'total_profit': float(self.total_profit),
            'total_loss': float(self.total_loss),
            'roi': self.get_roi(),
            'win_rate': self.get_win_rate(),
            'profit_factor': self.get_profit_factor(),
            'started_at': self.started_at.isoformat(),
            'ended_at': self.ended_at.isoformat() if self.ended_at else None
        }
    
    def __repr__(self):
        return f'<TradingSession {self.id} - User: {self.user_id} - Symbol: {self.symbol} - Status: {self.status.value}>'


class Trade(db.Model):
    """Model to track individual trades."""
    __tablename__ = 'trades'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    session_id = db.Column(db.String(36), db.ForeignKey('trading_sessions.id'), nullable=True)
    
    # Trade details
    symbol = db.Column(db.String(20), nullable=False)
    side = db.Column(db.String(10), nullable=False)
    quantity = db.Column(db.Numeric(20, 8), nullable=False)
    source = db.Column(db.String(20), nullable=False, default='app')  # 'app' or 'external'
    
    # Price information
    entry_price = db.Column(db.Numeric(20, 8), nullable=False)
    exit_price = db.Column(db.Numeric(20, 8))
    stop_loss = db.Column(db.Numeric(20, 8))
    take_profit = db.Column(db.Numeric(20, 8))
    
    # P&L and fees
    pnl = db.Column(db.Numeric(20, 8))  # Profit and Loss
    fee_charged = db.Column(db.Numeric(20, 8), default=0)  # Developer fee charged
    exchange_fee = db.Column(db.Numeric(20, 8), default=0)  # Exchange trading fee
    
    # Status and metadata
    status = db.Column(db.String(20), nullable=False, default=TradeStatus.OPEN)
    exchange_order_id = db.Column(db.String(255))  # Exchange-specific order ID
    
    # Exit information
    exit_reason = db.Column(db.String(100))  # 'take_profit', 'stop_loss', 'manual', etc.
    
    # Timestamps
    entry_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    exit_time = db.Column(db.DateTime)
    
    def __init__(self, user_id, symbol, side, quantity, entry_price,
                 stop_loss=None, take_profit=None, session_id=None, source='app'):
        self.user_id = user_id
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        self.entry_price = entry_price
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.session_id = session_id
        self.source = source
    
    def is_long(self):
        """Check if this is a long position."""
        return self.side in [TradeSide.BUY, TradeSide.LONG]
    
    def is_short(self):
        """Check if this is a short position."""
        return self.side in [TradeSide.SELL, TradeSide.SHORT]
    
    def calculate_pnl(self, current_price=None):
        """Calculate P&L for the trade."""
        if self.status == TradeStatus.CLOSED and self.exit_price:
            # Use actual exit price for closed trades
            price_diff = self.exit_price - self.entry_price
        elif current_price:
            # Use current price for open trades
            price_diff = current_price - self.entry_price
        else:
            return 0
        
        # Calculate P&L based on position direction
        if self.is_long():
            pnl = price_diff * self.quantity
        else:  # Short position
            pnl = -price_diff * self.quantity
        
        return float(pnl)
    
    def calculate_roi(self, current_price=None):
        """Calculate return on investment percentage."""
        pnl = self.calculate_pnl(current_price)
        investment = float(self.entry_price * self.quantity)
        
        if investment == 0:
            return 0
        
        return (pnl / investment) * 100
    
    def close_trade(self, exit_price, exit_reason='manual', fee_charged=0):
        """Close the trade."""
        self.exit_price = exit_price
        self.exit_time = datetime.utcnow()
        self.exit_reason = exit_reason
        self.status = TradeStatus.CLOSED
        self.fee_charged = fee_charged
        
        # Calculate final P&L
        self.pnl = self.calculate_pnl()
        
        # Update session statistics if associated with a session
        if self.session_id:
            session = TradingSession.query.get(self.session_id)
            if session:
                net_pnl = float(self.pnl) - float(self.fee_charged)
                session.add_trade_result(net_pnl)
    
    def cancel_trade(self):
        """Cancel the trade."""
        self.status = TradeStatus.CANCELLED
        self.exit_time = datetime.utcnow()
        self.exit_reason = 'cancelled'
    
    def update_stop_loss(self, new_stop_loss):
        """Update stop loss price."""
        self.stop_loss = new_stop_loss
    
    def update_take_profit(self, new_take_profit):
        """Update take profit price."""
        self.take_profit = new_take_profit
    
    def should_stop_loss(self, current_price):
        """Check if stop loss should be triggered."""
        if not self.stop_loss or self.status != TradeStatus.OPEN:
            return False
        
        if self.is_long():
            return current_price <= self.stop_loss
        else:  # Short position
            return current_price >= self.stop_loss
    
    def should_take_profit(self, current_price):
        """Check if take profit should be triggered."""
        if not self.take_profit or self.status != TradeStatus.OPEN:
            return False
        
        if self.is_long():
            return current_price >= self.take_profit
        else:  # Short position
            return current_price <= self.take_profit
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': float(self.quantity),
            'entry_price': float(self.entry_price),
            'exit_price': float(self.exit_price) if self.exit_price else None,
            'stop_loss': float(self.stop_loss) if self.stop_loss else None,
            'take_profit': float(self.take_profit) if self.take_profit else None,
            'pnl': float(self.pnl) if self.pnl else None,
            'fee_charged': float(self.fee_charged),
            'exchange_fee': float(self.exchange_fee),
            'status': self.status.value,
            'exchange_order_id': self.exchange_order_id,
            'exit_reason': self.exit_reason,
            'entry_time': self.entry_time.isoformat(),
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'is_long': self.is_long(),
            'is_short': self.is_short(),
            'roi': self.calculate_roi(),
            'source': self.source
        }
    
    def __repr__(self):
        return f'<Trade {self.id} - User: {self.user_id} - {self.symbol} {self.side.value} - Status: {self.status.value}>'