import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Card, CardContent } from "@/components/ui/card";
import { Star, Shield, Crown, Gem, Loader2, Check } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { useWallet, useConnection } from "@solana/wallet-adapter-react";
import { toast, toastSuccess, toastError } from "@/components/ui/use-toast";
import { PublicKey } from "@solana/web3.js";
import { useSolanaPayment } from "../hooks/useSolanaPayment";
import { useTranslation } from "@/hooks/useTranslation";
import { useMobile } from "@/hooks/useResponsiveDesign";

// Helper function to convert string to Uint8Array
const stringToUint8Array = (str: string): Uint8Array => {
  const encoder = new TextEncoder();
  return encoder.encode(str);
};

interface TierStatus {
  id: number;
  user_id: string;
  tier: number;
  created_at: string;
  updated_at: string;
  monthly_payment_status: 'active' | 'inactive' | 'canceled';
  effective_profit_share_rate: number;
  profit_share_owed: string;
  membership_validation?: {
    valid: boolean;
    reason: string;
    tier: number;
    membership_active: boolean;
    expiry_date?: string;
    days_remaining?: number;
    should_downgrade?: boolean;
  };
  tier_status?: {
    membership_active?: boolean;
    membership_expiry_date?: string;
    days_remaining?: number;
    should_downgrade?: boolean;
  };
}

// Membership status component
const MembershipStatus = ({ tierStatus }: { tierStatus: TierStatus | null }) => {
  if (!tierStatus || tierStatus.tier !== 2) return null;

  const membershipValidation = tierStatus.membership_validation;
  const tierStatusData = tierStatus.tier_status;

  if (!membershipValidation && !tierStatusData) return null;

  const isActive = membershipValidation?.membership_active || tierStatusData?.membership_active;
  const daysRemaining = membershipValidation?.days_remaining || tierStatusData?.days_remaining || 0;
  const expiryDate = membershipValidation?.expiry_date || tierStatusData?.membership_expiry_date;

  return (
    <div className={`mt-4 p-4 rounded-lg border ${isActive ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
      <div className="flex items-center gap-2 mb-2">
        {isActive ? (
          <Check className="h-5 w-5 text-green-600" />
        ) : (
          <Shield className="h-5 w-5 text-red-600" />
        )}
        <span className={`font-semibold ${isActive ? 'text-green-800' : 'text-red-800'}`}>
          Membership Status: {isActive ? 'Active' : 'Expired'}
        </span>
      </div>

      {isActive && (
        <div className="text-sm space-y-1">
          <p className="text-gray-600">
            <span className="font-medium">Days remaining:</span> {daysRemaining} days
          </p>
          {expiryDate && (
            <p className="text-gray-600">
              <span className="font-medium">Expires on:</span> {new Date(expiryDate).toLocaleDateString()}
            </p>
          )}
        </div>
      )}

      {!isActive && (
        <p className="text-sm text-red-600 mt-2">
          Your Tier 2 membership has expired. Please make a new payment to reactivate your membership.
        </p>
      )}
    </div>
  );
};

// Confirmation dialog component for tier activation
const ActivateConfirmDialog = ({ open, onConfirm, onCancel, tierName }: { open: boolean, onConfirm: () => void, onCancel: () => void, tierName: string }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-4 sm:p-6 w-full max-w-sm mx-4">
        <h2 className="text-lg font-bold mb-4">Activate {tierName} Tier</h2>
        <p className="mb-6">Are you sure you want to activate the <span className="font-semibold">{tierName}</span> tier?</p>
        <div className="flex justify-end gap-3">
          <button className="px-4 py-2 rounded bg-gray-200 dark:bg-gray-700" onClick={onCancel}>Cancel</button>
          <button className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white" onClick={onConfirm}>Activate</button>
        </div>
      </div>
    </div>
  );
}

// Legacy confirmation dialog component (kept for tier 1)
const ConfirmDialog = ({ open, onConfirm, onCancel, tierName }: { open: boolean, onConfirm: () => void, onCancel: () => void, tierName: string }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-4 sm:p-6 w-full max-w-sm mx-4">
        <h2 className="text-lg font-bold mb-4">Confirm Tier Change</h2>
        <p className="mb-6">Are you sure you want to switch to the <span className="font-semibold">{tierName}</span> tier?</p>
        <div className="flex justify-end gap-3">
          <button className="px-4 py-2 rounded bg-gray-200 dark:bg-gray-700" onClick={onCancel}>Cancel</button>
          <button className="px-4 py-2 rounded bg-primary text-white" onClick={onConfirm}>Confirm</button>
        </div>
      </div>
    </div>
  );
}

export default function TierManagement() {
  const { publicKey, connected } = useWallet();
  const { connection } = useConnection();
  const { sendTokenPayment } = useSolanaPayment();
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const navigate = useNavigate();

  // Create a more reliable wallet connection state
  const isWalletConnected = connected && publicKey;

  // Note: Removed mobile redirect to ensure feature parity between desktop and mobile

  // Confirmation dialog state
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingTier, setPendingTier] = useState<number | null>(null);
  const [tierStatus, setTierStatus] = useState<TierStatus | null>(null);
  const [selectedTier, setSelectedTier] = useState<number | null>(null);
  const [isCheckingNFT, setIsCheckingNFT] = useState(false);
  const [nftStatus, setNftStatus] = useState({
    owns_nft: false,
    is_expired: true
  });
  // Payment related states
  const [profitShareOwed, setProfitShareOwed] = useState(0);

  // Helper function to safely format profit share amount
  const formatProfitShare = (amount: any, decimals: number = 6): string => {
    const numAmount = Number(amount) || 0;
    return numAmount.toFixed(decimals);
  };
  const [paymentHistory, setPaymentHistory] = useState<any[]>([]);
  const [membershipBillings, setMembershipBillings] = useState<any[]>([]);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentType, setPaymentType] = useState<'profit_share' | 'membership'>('profit_share');
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentConfig, setPaymentConfig] = useState<{
    amount: number;
    token: string;
    tokenMint: string;
  } | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentStep, setPaymentStep] = useState<'confirm' | 'processing' | 'success' | 'error'>('confirm');

  // Payday limit states
  const [paydayStatus, setPaydayStatus] = useState<{
    next_payday_deadline: string | null;
    account_disabled: boolean;
    disabled_at: string | null;
    is_past_payday_deadline: boolean;
    should_disable_account: boolean;
    needs_payday_warning: boolean;
    payday_warning_sent: boolean;
  } | null>(null);
  const [, setCurrentTierStatus] = useState<any>(null);
  const [currentTier, setCurrentTier] = useState<number>(1);

  const [showActivateDialog, setShowActivateDialog] = useState(false);
  const [activateTierPending, setActivateTierPending] = useState<number | null>(null);

  // Hardcoded collection address for Capitol Chilax
  // Use base58 format for Solana PublicKey (required by @solana/web3.js)
  // Capitol Chilax collection address (base58): 7qXTaNobP9Zobr4SCYKRdGfHJk44hctGCoTfwN5L5NkL
  // Use base58 format for Solana PublicKey (Capitol Chilax creator address)
  const nftCollectionAddress = "7qXTaNobP9Zobr4SCYKRdGfHJk44hctGCoTfwN5L5NkL";

  // Function to save tier selection to database
  const saveTierSelection = async (tier: number) => {
    try {
      // Special handling for Tier 2 - don't save to database, just open payment modal
      if (tier === 2) {
        toastSuccess({
          title: 'Payment Required',
          description: 'Complete your payment for 30 days access to activate Pro features.'
        });
        openPaymentModal('membership', paymentConfig?.amount || 199);
        return;
      }

      const token = localStorage.getItem('access_token');
      let tierPayload = { tier_1: false, tier_2: false, tier_3: false };

      if (tier === 1) tierPayload.tier_1 = true;
      if (tier === 3) tierPayload.tier_3 = true;

      const response = await fetch('/api/trading/tier/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(tierPayload)
      });

      if (response.ok) {
        const data = await response.json();
        setTierStatus(data);
        setCurrentTier(tier);
        setSelectedTier(tier);

        toastSuccess({
          title: 'Success!',
          description: `Your tier has been updated to ${tiers.find(t => t.id === tier)?.name}.`
        });
      } else {
        throw new Error('Failed to update tier');
      }
    } catch (error) {
      console.error('Error saving tier:', error);
      toastError({
        title: 'Error',
        description: 'Failed to activate tier. Please try again.'
      });
    }
  };

  // Function to check if user should be downgraded when wallet is disconnected
  const checkTierOnWalletDisconnect = async () => {
    try {
      const token = localStorage.getItem('access_token');

      // Check current tier status and payment validity
      const response = await fetch('/api/trading/tier/status', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const tierData = await response.json();
        console.log('Tier status on wallet disconnect:', tierData);

        // If user is Tier 2, check if payment is still valid
        if (tierData.current_tier === 2) {
          // Check membership validation
          const membershipResponse = await fetch('/api/payments/solana/membership/validate', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (membershipResponse.ok) {
            const membershipData = await membershipResponse.json();
            console.log('Membership validation on disconnect:', membershipData);

            // Only downgrade if membership is expired
            if (membershipData.should_downgrade) {
              console.log('Membership expired, downgrading to Tier 1');
              await revertToTier1();
              toastError({
                title: 'Membership Expired',
                description: 'Your Tier 2 membership has expired. You have been downgraded to Starter tier.'
              });
            } else {
              console.log('Membership still valid, keeping Tier 2 despite wallet disconnect');
              toast({
                title: 'Wallet Disconnected',
                description: `Your Tier 2 access remains active for ${membershipData.days_remaining} more days.`
              });
            }
            // Refresh tier status after wallet disconnect
            await fetchTierAndPaymentData();
          } else {
            // If we can't validate membership, keep current tier for safety
            console.log('Could not validate membership, keeping current tier');
            toast({
              title: 'Wallet Disconnected',
              description: 'Your tier status has been preserved. Reconnect your wallet to make payments.'
            });
          }
        } else {
          // User is Tier 1 or 3, no special handling needed
          console.log('User is not Tier 2, no tier change needed on disconnect');
        }
        // Refresh tier status after wallet disconnect check
        await fetchTierAndPaymentData();
      }
    } catch (error) {
      console.error('Error checking tier on wallet disconnect:', error);
      // On error, don't change tier to be safe
      toast({
        title: 'Wallet Disconnected',
        description: 'Your tier status has been preserved.'
      });
      // Still refresh tier status even on error
      await fetchTierAndPaymentData();
    }
  };

  // Function to revert to tier 1 (only called when membership is actually expired)
  const revertToTier1 = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/trading/tier/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ tier_1: true, tier_2: false, tier_3: false })
      });

      if (response.ok) {
        setCurrentTier(1);
        setSelectedTier(1);
        console.log('Tier reverted to Starter');
        // Refresh tier data
        fetchTierAndPaymentData();
      }
    } catch (error) {
      console.error('Error reverting tier:', error);
    }
  };

  // Fetch payment and tier data
  useEffect(() => {
    console.log('🚀 useEffect triggered, calling fetchTierAndPaymentData');
    fetchTierAndPaymentData().catch(error => {
      console.error('❌ Error in useEffect fetchTierAndPaymentData:', error);
    });
  }, []);

  const fetchTierAndPaymentData = async () => {
    console.log('🔄 fetchTierAndPaymentData called');



    try {
      const token = localStorage.getItem('access_token');
      let newProfitShareOwed = 0; // Initialize at function level

      // Fetch tier status
      console.log('🌐 Making API call to /api/trading/tier/status');
      const tierResponse = await fetch('/api/trading/tier/status', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Tier response status:', tierResponse.status);
      if (tierResponse.ok) {
        const tierData = await tierResponse.json();
        console.log('📊 Tier data received:', tierData);
        setCurrentTierStatus(tierData);
        newProfitShareOwed = Number(tierData.profit_share_owed) || 0;
        console.log('📊 Setting profit share owed:', {
          raw: tierData.profit_share_owed,
          converted: newProfitShareOwed,
          type: typeof tierData.profit_share_owed
        });
        setProfitShareOwed(newProfitShareOwed);
        setTierStatus(tierData); // Also set tierStatus for MembershipStatus component

        // Set current tier
        if (tierData.tier_3) {
          setSelectedTier(3);
          setCurrentTier(3);
        } else if (tierData.tier_2) {
          setSelectedTier(2);
          setCurrentTier(2);
        } else {
          setSelectedTier(1);
          setCurrentTier(1);
        }
      }

      // Fetch payday status
      const paydayResponse = await fetch('/api/trading/payday/status', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (paydayResponse.ok) {
        const paydayData = await paydayResponse.json();
        setPaydayStatus(paydayData);

        // Show payday-related notifications
        console.log('🔔 Toast notification check:', {
          account_disabled: paydayData.account_disabled,
          is_past_payday_deadline: paydayData.is_past_payday_deadline,
          needs_payday_warning: paydayData.needs_payday_warning,
          payday_warning_sent: paydayData.payday_warning_sent,
          newProfitShareOwed: newProfitShareOwed,
          profitShareOwedNumber: Number(newProfitShareOwed)
        });



        if (paydayData.account_disabled) {
          console.log('🚫 Showing account disabled toast');
          toastError({
            title: '🚫 Account Disabled',
            description: `Your account has been disabled due to unpaid profit share fees of $${formatProfitShare(newProfitShareOwed)}. Please make a payment to reactivate your account.`
          });
        } else if (paydayData.is_past_payday_deadline && Number(newProfitShareOwed) > 0) {
          console.log('⚠️ Showing payment overdue toast');
          toastError({
            title: '⚠️ Payment Overdue',
            description: `Your profit share payment of $${formatProfitShare(newProfitShareOwed)} is overdue. Your account may be disabled soon.`
          });
        } else if (paydayData.needs_payday_warning && !paydayData.payday_warning_sent) {
          console.log('⏰ Showing payment reminder toast');
          toast({
            title: '⏰ Payment Reminder',
            description: `Your profit share payment of $${formatProfitShare(newProfitShareOwed)} is due by Saturday 0:00 GMT. Please pay to avoid account suspension.`
          });
        } else {
          console.log('ℹ️ No toast notification conditions met');
        }
      }

      // Fetch payment configuration
      const configResponse = await fetch('/api/payments/solana/config', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (configResponse.ok) {
        const configData = await configResponse.json();
        setPaymentConfig({
          amount: configData.payment_amount || 199,
          token: configData.payment_token || 'USDT',
          tokenMint: configData.payment_token_mint || 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
        });
      }

      // Fetch payment history
      const paymentsResponse = await fetch('/api/payments/solana/payments', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json();
        setPaymentHistory(paymentsData.payments || []);
      }

      // Fetch membership billings for Tier 2 users
      const billingsResponse = await fetch('/api/payments/solana/membership/billings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (billingsResponse.ok) {
        const billingsData = await billingsResponse.json();
        setMembershipBillings(billingsData.billings || []);
      }

    } catch (error: unknown) {
      console.error('❌ Error fetching tier and payment data:', error);
      const errorObj = error as any;
      console.error('❌ Error stack:', errorObj.stack);
      toastError({
        title: 'Data Fetch Error',
        description: `Failed to fetch tier data: ${errorObj.message || 'Unknown error'}`
      });
    }
  };

  const tiers = [
    {
      id: 1,
      name: "Starter",
      price: "Free",
      profitShare: 40,
      description: "Perfect for getting started",
      features: [
        "Basic Trading Signals",
        "Trading Leverage up to 2.5x",
        "Limited to One API Access",
        "40% Profit Share Fee"
      ]
    },
    {
      id: 2,
      name: "Pro",
      price: 199,
      profitShare: 20,
      description: "For serious traders",
      features: [
        "Advanced Trading Signals",
        "Trading Leverage up to 5x",
        "Limited to Two API Access",
        "DeepForecast Access to Multiple Cryptocurrencies",
        "20% Profit Share Fee",
        "$199 USDT for 30 days access"
      ]
    },
    {
      id: 3,
      name: "NFT Elite",
      price: "Lifetime",
      profitShare: 10,
      description: (
        <>
          For verified Capitolchilax NFT collectors.{' '}
          <a
            href="https://capitolchilax.com"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary/80 underline font-medium"
          >
            Visit capitolchilax.com
          </a>
        </>
      ),
      features: [
        "Advanced Trading Signals",
        "Trading Leverage up to 10x",
        "Unlimited API Connections",
        "DeepForecast Access to Multiple Cryptocurrencies & Meme Coins",
        "10% Profit Share Fee",
        "No Monthly Membership Fee"
      ]
    }
  ];

  const handleTierSelect = async (tier: number) => {
    // Check if user has outstanding profit share fees
    if (Number(profitShareOwed) > 0) {
      toastError({
        title: 'Payment Required',
        description: `You must pay your outstanding profit share fees ($${formatProfitShare(profitShareOwed)}) before changing tiers.`
      });
      return;
    }

    // Prevent downgrading from higher tiers
    if (tier < currentTier) {
      toastError({
        title: 'Downgrade Not Allowed',
        description: `You cannot downgrade from Tier ${currentTier} to Tier ${tier}. You can only upgrade to higher tiers.`
      });
      return;
    }

    setSelectedTier(tier);

    if (tier === 1) {
      // Free tier - no NFT required
      try {
        let tierPayload = { tier_1: true, tier_2: false, tier_3: false };
        const response = await fetch('/api/trading/tier/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
          },
          body: JSON.stringify(tierPayload)
        });
        
        if (!response.ok) throw new Error('Failed to update tier');
        
        const data = await response.json();
        setTierStatus(data);
        toastSuccess({
          title: 'Success!',
          description: 'Your tier has been updated successfully.'
        });
      } catch (error) {
        console.error('Error updating tier:', error);
        toastError({
          title: 'Error',
          description: 'Failed to update tier. Please try again.'
        });
      }
    } else if (tier === 2) {
      // Tier 2 requires Solana wallet connection and membership payment
      if (!isWalletConnected) {
        toastError({
          title: 'Wallet Connection Required',
          description: 'Please connect your Solana wallet to access Tier 2.'
        });
        return;
      }

      // For tier 2, call saveTierSelection which will handle payment modal
      saveTierSelection(2);
    } else if (tier === 3) {
      // Tier 3 requires NFT ownership check
      if (!nftStatus.owns_nft) {
        toastError({
          title: 'NFT Required',
          description: 'You need to own a specific NFT to access this tier.'
        });
        return;
      }

      // For tier 3, directly call saveTierSelection (this is called from the activation dialog)
      saveTierSelection(3);
    }
  };

  useEffect(() => {
    // Function to check if the user holds an NFT from the specified creator address
    const verifyNFTOwnership = async () => {
      if (!publicKey) {
        // Reset NFT status when wallet is disconnected or publicKey is null
        setNftStatus({ owns_nft: false, is_expired: true });
        return;
      }

      try {
        setIsCheckingNFT(true);
        // Reset NFT status before checking to ensure clean state
        setNftStatus({ owns_nft: false, is_expired: true });

        const userPublicKey = new PublicKey(publicKey.toString());
        const collectionAddress = nftCollectionAddress; // base58 collection address

        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(userPublicKey, {
          programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'),
        });

        for (const account of tokenAccounts.value) {
          const tokenData = account.account.data as any;
          const mintAddress = new PublicKey(tokenData.parsed.info.mint);

          // Fetch the NFT's metadata account
          const [metadataPDA] = await PublicKey.findProgramAddress(
            [
              stringToUint8Array('metadata'),
              new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s').toBytes(),
              mintAddress.toBytes(),
            ],
            new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')
          );

          const metadataAccount = await connection.getAccountInfo(metadataPDA);
          if (!metadataAccount) continue;

          // Parse the collection field from the metadata buffer
          try {
            // Metaplex metadata: collection field is 1 byte (option), then 32 bytes (address), then 1 byte (verified)
            // We'll search for the collection address as a 32-byte buffer
            const data = metadataAccount.data;
            const collectionKeyBuffer = new PublicKey(collectionAddress).toBuffer();
            let found = false;
            for (let offset = 0; offset <= data.length - 32; offset++) {
              let match = true;
              for (let j = 0; j < 32; j++) {
                if (data[offset + j] !== collectionKeyBuffer[j]) {
                  match = false;
                  break;
                }
              }
              if (match) {
                found = true;
                break;
              }
            }
            if (found) {
              setNftStatus({ owns_nft: true, is_expired: false });
              // NFT found - user can now activate tier 3 through the tier card button
              return;
            }
          } catch (e) {
            // fallback: do nothing, continue
          }
        }

        setNftStatus({ owns_nft: false, is_expired: true });
      } catch (error) {
        console.error('Error checking NFT ownership:', error);
        setNftStatus({ owns_nft: false, is_expired: true });
      } finally {
        setIsCheckingNFT(false);
      }
    };

    if (isWalletConnected) {
      verifyNFTOwnership();
    } else {
      // Reset NFT status when wallet is disconnected
      setNftStatus({ owns_nft: false, is_expired: true });
      setIsCheckingNFT(false);

      // Check if user should be downgraded when wallet is disconnected
      if (currentTier > 1) {
        checkTierOnWalletDisconnect();
      }
    }
  }, [isWalletConnected, publicKey]);

  useEffect(() => {
    const fetchTierStatus = async () => {
      try {
        const response = await fetch('/api/trading/tier/status', {
          credentials: 'include',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        });
        
        if (response.ok) {
          const data = await response.json();
          setTierStatus(data);
          setSelectedTier(data.tier);
        } else {
          toastError({
            title: 'Failed to fetch tier status',
            description: 'Please try again later'
          });
        }
      } catch (err) {
        toastError({
          title: 'Error loading tier information',
          description: 'Please try again later'
        });
      }
    };

    if (isWalletConnected) {
      fetchTierStatus();
    }
  }, [isWalletConnected]);



  // Payment functions
  const openPaymentModal = (type: 'profit_share' | 'membership', amount?: number) => {
    console.log('=== OPENING PAYMENT MODAL ===');
    console.log('Connected:', connected);
    console.log('PublicKey:', publicKey?.toString());
    console.log('Payment Type:', type);
    console.log('Payment Amount:', amount);

    setPaymentType(type);

    // Auto-detect amount for profit share payments
    if (type === 'profit_share') {
      setPaymentAmount(Number(profitShareOwed) || 0);
    } else {
      setPaymentAmount(amount || 0);
    }

    setShowPaymentModal(true);
  };



  const createSolanaPayment = async () => {
    console.log('=== CREATE SOLANA PAYMENT WITH WALLET POPUP ===');
    console.log('Connected:', connected);
    console.log('PublicKey:', publicKey?.toString());
    console.log('Payment Type:', paymentType);
    console.log('Payment Amount:', paymentAmount);
    console.log('Profit Share Owed (raw):', profitShareOwed);
    console.log('Profit Share Owed (Number):', Number(profitShareOwed));

    if (!isWalletConnected) {
      toastError({
        title: 'Wallet Not Connected',
        description: 'Please connect your Solana wallet to proceed with payment.'
      });
      return;
    }

    setIsProcessingPayment(true);
    setPaymentStep('processing');

    try {
      // Get treasury wallet address from backend
      const token = localStorage.getItem('access_token');

      const configResponse = await fetch('/api/payments/solana/config', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!configResponse.ok) {
        throw new Error('Failed to get payment configuration');
      }

      const config = await configResponse.json();
      const treasuryWallet = config.treasury_wallet;

      console.log('Treasury wallet:', treasuryWallet);

      // Configure payment based on type
      let configAmount, configToken, configTokenMint;

      if (paymentType === 'profit_share') {
        // For profit share payments, use the exact amount owed
        configAmount = paymentAmount;
        configToken = 'USDC';  // Profit share payments use USDC
        configTokenMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC mint
      } else {
        // For membership payments, use config from backend
        configAmount = paymentConfig?.amount || paymentAmount;
        configToken = paymentConfig?.token || 'USDT';
        configTokenMint = paymentConfig?.tokenMint || 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB';
      }

      console.log(`Sending ${configToken} payment via wallet popup...`);
      console.log('Payment Config Details:', {
        amount: configAmount,
        token: configToken,
        tokenMint: configTokenMint,
        treasuryWallet: treasuryWallet
      });

      // Show wallet popup and send token payment
      const signature = await sendTokenPayment({
        amount: configAmount,
        tokenMint: configTokenMint,
        tokenSymbol: configToken,
        recipientAddress: treasuryWallet,
        onSuccess: (txSignature) => {
          console.log('Payment successful! Transaction:', txSignature);
        },
        onError: (error) => {
          console.error('Payment failed:', error);
        }
      });

      console.log('Transaction signature:', signature);

      // Create payment record in backend with transaction signature
      const response = await fetch('/api/payments/solana/create-payment', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          payment_type: paymentType === 'membership' ? 'membership_fee' : paymentType,
          amount: configAmount,
          wallet_address: publicKey.toString(),
          transaction_signature: signature
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Payment record created:', data);

        setPaymentStep('success');

        // If this was a membership payment, update tier to Tier 2
        if (paymentType === 'membership') {
          // Update tier to Tier 2 after successful payment
          try {
            const tierResponse = await fetch('/api/trading/tier/update', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({ tier_1: false, tier_2: true, tier_3: false })
            });

            if (tierResponse.ok) {
              const tierData = await tierResponse.json();
              setTierStatus(tierData);
              setCurrentTier(2);
              setSelectedTier(2);
            }
          } catch (tierError) {
            console.error('Error updating tier after payment:', tierError);
          }
        }

        setTimeout(() => {
          setShowPaymentModal(false);
          setPaymentStep('confirm');
          setIsProcessingPayment(false);
          // Refresh data after payment creation
          fetchTierAndPaymentData();

          toastSuccess({
            title: 'Payment Successful',
            description: `Your ${paymentType === 'profit_share' ? 'profit share' : '30 days access'} payment has been processed successfully!`
          });
        }, 2000);
      } else {
        let errorMessage = 'Failed to create payment record';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error creating payment:', error);
      setPaymentStep('error');
      setIsProcessingPayment(false);

      const errorMessage = error instanceof Error ? error.message : 'There was an error processing your payment. Please try again.';

      toastError({
        title: 'Payment Failed',
        description: errorMessage
      });
    }
  };

  // Always show the page, even if not connected

  return (
    <div className={`container mx-auto ${isMobile ? 'px-3 py-4' : 'px-4 sm:px-6 py-6 sm:py-8'} max-w-full overflow-x-hidden`}>






      {/* Account Disabled Warning - Only show when account is actually disabled */}
      {paydayStatus?.account_disabled && (
        <Card className="border-red-200 bg-red-50 dark:bg-red-900/20 mb-8">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-red-100 dark:bg-red-800 rounded-full flex items-center justify-center">
                <span className="text-red-600 dark:text-red-400 text-sm font-bold">!</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
                  Account Trading Disabled
                </h3>
                <p className="text-red-600 dark:text-red-300">
                  Your trading account is disabled due to outstanding profit share fees. Pay ${formatProfitShare(profitShareOwed)} to reactivate trading.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payday Warning Notification */}
      {paydayStatus?.needs_payday_warning && !paydayStatus?.payday_warning_sent && (
        <div className="mb-6">
          <Card className="border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="text-yellow-600 dark:text-yellow-400 text-xl">⚠️</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                    Payment Deadline Approaching
                  </h3>
                  <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                    Your profit share payment of <strong>${formatProfitShare(profitShareOwed)}</strong> is due by{' '}
                    <strong>Saturday at 0:00 GMT</strong>. Your account will be automatically disabled if payment is not received by the deadline.
                  </p>
                  {paydayStatus?.next_payday_deadline && (
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">
                      Deadline: {new Date(paydayStatus.next_payday_deadline).toLocaleString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        timeZoneName: 'short'
                      })}
                    </p>
                  )}
                </div>
                <Button
                  onClick={() => openPaymentModal('profit_share')}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                  size="sm"
                >
                  Pay Now
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Profit Share Status Section - Always Show */}
      <div className="mb-8">
        <Card className={`border-2 ${
          Number(profitShareOwed) > 0 ? (
            paydayStatus?.account_disabled
              ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
              : paydayStatus?.is_past_payday_deadline
                ? 'border-red-300 bg-red-50 dark:bg-red-900/20'
                : 'border-orange-200 bg-orange-50 dark:bg-orange-900/20'
          ) : 'border-green-200 bg-green-50 dark:bg-green-900/20'
        }`}>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className={`text-lg font-semibold ${
                    Number(profitShareOwed) > 0 ? (
                      paydayStatus?.account_disabled
                        ? 'text-red-800 dark:text-red-200'
                        : paydayStatus?.is_past_payday_deadline
                          ? 'text-red-700 dark:text-red-300'
                          : 'text-orange-800 dark:text-orange-200'
                    ) : 'text-green-800 dark:text-green-200'
                  }`}>
                    {Number(profitShareOwed) > 0 ? (
                      paydayStatus?.account_disabled ? '🚫 Account Disabled' : 'Profit Share Payment Due'
                    ) : '✅ Profit Share Status'}
                  </h3>
                  {Number(profitShareOwed) > 0 && paydayStatus?.is_past_payday_deadline && !paydayStatus?.account_disabled && (
                    <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                      OVERDUE
                    </span>
                  )}
                </div>

                <p className={`mb-2 ${
                  Number(profitShareOwed) > 0 ? (
                    paydayStatus?.account_disabled
                      ? 'text-red-600 dark:text-red-300'
                      : paydayStatus?.is_past_payday_deadline
                        ? 'text-red-600 dark:text-red-300'
                        : 'text-orange-600 dark:text-orange-300'
                  ) : 'text-green-600 dark:text-green-300'
                }`}>
                  {Number(profitShareOwed) > 0 ? (
                    <>You owe ${formatProfitShare(profitShareOwed)} in profit share fees</>
                  ) : (
                    <>No outstanding profit share fees - You're all caught up! 🎉</>
                  )}
                </p>

                {Number(profitShareOwed) > 0 && paydayStatus?.account_disabled && (
                  <div className="mb-3 p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                    <p className="text-sm text-red-800 dark:text-red-200 font-medium">
                      ⚠️ Your account has been disabled due to unpaid fees.
                      Trading is suspended until payment is made.
                    </p>
                    {paydayStatus.disabled_at && (
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        Disabled on: {new Date(paydayStatus.disabled_at).toLocaleString()}
                      </p>
                    )}
                  </div>
                )}

                {paydayStatus?.next_payday_deadline && (
                  <div className="text-sm space-y-1">
                    <p className={`${
                      Number(profitShareOwed) > 0 ? (
                        paydayStatus?.is_past_payday_deadline
                          ? 'text-red-600 dark:text-red-400'
                          : 'text-gray-600 dark:text-gray-400'
                      ) : 'text-green-600 dark:text-green-400'
                    }`}>
                      {Number(profitShareOwed) > 0 ? (
                        paydayStatus?.is_past_payday_deadline ? 'Payment was due:' : 'Payment due:'
                      ) : 'Next payment deadline:'} {' '}
                      <span className="font-medium">
                        {new Date(paydayStatus.next_payday_deadline).toLocaleString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          timeZoneName: 'short'
                        })}
                      </span>
                    </p>
                    {Number(profitShareOwed) === 0 && (
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        Profit share payments are due every Saturday at 0:00 GMT
                      </p>
                    )}
                    {Number(profitShareOwed) > 0 && !paydayStatus?.is_past_payday_deadline && (
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        Accounts are disabled every Saturday at 0:00 GMT for unpaid fees
                      </p>
                    )}
                  </div>
                )}
              </div>

              {Number(profitShareOwed) > 0 && (
                <div className="ml-4 flex flex-col gap-2">
                  <Button
                    onClick={() => openPaymentModal('profit_share')}
                    className={`${
                      paydayStatus?.account_disabled
                        ? 'bg-red-600 hover:bg-red-700'
                        : paydayStatus?.is_past_payday_deadline
                          ? 'bg-red-500 hover:bg-red-600'
                          : 'bg-orange-600 hover:bg-orange-700'
                    }`}
                    disabled={isProcessingPayment}
                  >
                    {paydayStatus?.account_disabled ? 'Pay to Reactivate' : 'Pay with USDT'}
                  </Button>


                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Membership Payment Status Cards */}
      {membershipBillings.some(b => !b.is_paid) && (
        <div className="mb-8 space-y-4">
          {membershipBillings.filter(b => !b.is_paid).map((billing) => (
            <Card key={billing.id} className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
              <CardContent className="p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
                      30 Days Access Due
                    </h3>
                    <p className="text-blue-600 dark:text-blue-300">
                      ${billing.amount} due for {new Date(billing.billing_period_start).toLocaleDateString()} - {new Date(billing.billing_period_end).toLocaleDateString()}
                    </p>
                  </div>
                  <Button
                    onClick={() => openPaymentModal('membership', billing.amount)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Pay with {paymentConfig?.token || 'USDT'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t('tiers.choosePlan')}</h1>
        <p className="text-xl text-muted-foreground">
          {t('tiers.choosePlan')}
        </p>

        {/* Redeem Coupon Button */}
        <div className="mt-6">
          <button
            onClick={() => navigate('/redeem-coupon')}
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
            </svg>
            Have a Coupon? Redeem Here
          </button>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
            Upgrade your tier instantly with a coupon code
          </p>
        </div>
      </div>

      {/* Summary section for current tier */}
      {selectedTier && (
        <div
          className="mb-6 flex items-center justify-center"
          role="status"
          aria-live="polite"
        >
          <span className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-green-100 text-green-800 font-semibold text-lg border border-green-400">
            <Check className="w-5 h-5 text-green-500" aria-hidden="true" />
            {`You are currently on the ${tiers.find(t => t.id === selectedTier)?.name} tier`}
            <span className="sr-only">This is your current tier</span>
          </span>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mt-6 sm:mt-8">
        {tiers.map((tier) => (
          <Card
            key={tier.id}
            className={`border-2 transition-all ${
              selectedTier === tier.id ? 'border-primary' : 'border-transparent hover:border-gray-200 dark:hover:border-gray-700'
            }`}
          >
            <CardContent className="p-6" {...(selectedTier === tier.id ? { 'aria-current': 'true' } : {})}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold flex items-center gap-2">
                  {tier.id === 1 && <Star className="w-5 h-5 text-yellow-500" />}
                  {tier.id === 2 && <Shield className="w-5 h-5 text-blue-500" />}
                  {tier.id === 3 && <Crown className="w-5 h-5 text-purple-500" />}
                  {tier.name}
                </h3>
                <div className="px-3 py-1 text-sm font-medium bg-primary/10 text-primary rounded-full">
                  {tier.id === 1 ? "Free" : tier.id === 3 ? "Lifetime" : `$${tier.price} for 30-day access`}
                </div>
              </div>
                {/* Current Tier Badge */}
                {selectedTier === tier.id && (
                  <div
                    className="absolute top-4 right-4 z-10"
                    aria-label="Your current tier"
                  >
                    <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-green-600 text-white text-sm font-bold shadow-lg">
                      <Check className="w-4 h-4 mr-1" aria-hidden="true" />
                      Your Current Tier
                      <span className="sr-only">This is your current tier</span>
                    </span>
                  </div>
                )}


              <div className="text-muted-foreground mb-6">{tier.description}</div>
                
              <ul className="space-y-3 mb-6">
                {tier.features.map((feature, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>
                      {/* Dynamic pricing for Tier 2 */}
                      {tier.id === 2 && feature.includes('$199 USDT for 30 days access')
                        ? `$${paymentConfig?.amount || 199} ${paymentConfig?.token || 'USDT'} for 30 days access`
                        : feature
                      }
                    </span>
                  </li>
                ))}
              </ul>

              {/* Show membership status for Tier 2 */}
              {tier.id === 2 && selectedTier === 2 && <MembershipStatus tierStatus={tierStatus} />}

              <div className="mt-6 flex flex-col items-center justify-center min-h-[70px]">
                <div className="w-full flex items-center justify-center">
                  {/* First priority: Show current tier status regardless of wallet connection */}
                  {selectedTier === tier.id ? (
                    <span className="inline-flex items-center text-green-600 font-semibold" aria-label="Current tier">
                      <Check className="w-4 h-4 mr-1" aria-hidden="true" />
                      Current Tier
                    </span>
                  ) : selectedTier !== null && selectedTier > tier.id ? (
                    <span className="inline-flex items-center text-gray-500 font-medium text-sm" aria-label="Lower tier">
                      <Check className="w-4 h-4 mr-1" aria-hidden="true" />
                      Already Unlocked
                    </span>
                  ) : !isWalletConnected && tier.id > 1 ? (
                    <div className="flex flex-col items-center gap-3 w-full">
                      {tier.id === 2 && (
                        <p className="text-xs text-blue-700 dark:text-blue-300 text-center mb-2">
                          To access this tier, use the wallet button in the top navigation bar to connect your Solana wallet and pay for 30 days access.
                        </p>
                      )}
                      {tier.id === 3 && (
                        <p className="text-xs text-yellow-700 dark:text-yellow-300 text-center mb-2">
                          To access this tier, use the wallet button in the top navigation bar to connect and verify your NFT eligibility.
                        </p>
                      )}
                    </div>
                  ) : (
                    <Button
                      className="w-full flex items-center justify-center"
                      onClick={() => {
                        if (tier.id === 1) {
                          // For tier 1, use the old confirmation dialog
                          setPendingTier(tier.id);
                          setShowConfirmDialog(true);
                        } else {
                          // For tier 2 and 3, use the new activation dialog
                          setActivateTierPending(tier.id);
                          setShowActivateDialog(true);
                        }
                      }}
                      disabled={
                        isCheckingNFT ||
                        (tier.id === 3 && !nftStatus.owns_nft) ||
                        (tier.id === 2 && !isWalletConnected) ||
                        (Number(profitShareOwed) > 0)
                      }
                    >
                      {isCheckingNFT ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Gem className="w-4 h-4 mr-2" />
                      )}
                      {Number(profitShareOwed) > 0
                        ? 'Payment Required'
                        : tier.id === 2 && !isWalletConnected
                        ? 'Connect Wallet'
                        : tier.id === 3 && !nftStatus.owns_nft
                        ? 'NFT Required'
                        : tier.id === 1
                        ? `Select ${tier.name}`
                        : `Activate ${tier.name}`}
                    </Button>
                  )}
                  {/* Confirmation Dialog for Tier 1 */}
                  <ConfirmDialog
                    open={showConfirmDialog}
                    tierName={tiers.find(t => t.id === pendingTier)?.name || ""}
                    onCancel={() => setShowConfirmDialog(false)}
                    onConfirm={() => {
                      if (pendingTier !== null) handleTierSelect(pendingTier);
                      setShowConfirmDialog(false);
                    }}
                  />

                  {/* Activation Dialog for Tier 2 and 3 */}
                  <ActivateConfirmDialog
                    open={showActivateDialog}
                    tierName={tiers.find(t => t.id === activateTierPending)?.name || ""}
                    onCancel={() => {
                      setShowActivateDialog(false);
                      setActivateTierPending(null);
                    }}
                    onConfirm={() => {
                      if (activateTierPending !== null) {
                        saveTierSelection(activateTierPending);
                      }
                      setShowActivateDialog(false);
                      setActivateTierPending(null);
                    }}
                  />
                </div>
                
                {tier.id === 3 && isWalletConnected && !nftStatus.owns_nft && (
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    {isCheckingNFT
                      ? 'Verifying NFT ownership...'
                      : 'You need to own a specific NFT to access this tier'}
                  </p>
                )}
                {tier.id === 3 && isWalletConnected && nftStatus.owns_nft && (
                  <p className="text-xs text-green-600 mt-2 text-center">
                    You own an NFT from the required collection.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-4 sm:p-6 w-full max-w-lg mx-4 relative">
            {/* Close button */}
            <button
              onClick={() => {
                setShowPaymentModal(false);
                setPaymentStep('confirm');
                setIsProcessingPayment(false);
              }}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label="Close payment modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <h2 className="text-xl font-bold mb-6 pr-8">
              {paymentType === 'profit_share' ? (
                paydayStatus?.account_disabled ? 'Pay to Reactivate Account' : 'Pay Profit Share'
              ) : 'Pay for 30 Days Access'}
            </h2>

            {/* Payday Status Alert in Modal */}
            {paymentType === 'profit_share' && paydayStatus && (
              <div className={`mb-4 p-3 rounded-lg border ${
                paydayStatus.account_disabled
                  ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                  : paydayStatus.is_past_payday_deadline
                    ? 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800'
                    : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
              }`}>
                <div className="flex items-start gap-2">
                  <span className="text-lg">
                    {paydayStatus.account_disabled ? '🚫' : paydayStatus.is_past_payday_deadline ? '⚠️' : '⏰'}
                  </span>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${
                      paydayStatus.account_disabled
                        ? 'text-red-800 dark:text-red-200'
                        : paydayStatus.is_past_payday_deadline
                          ? 'text-orange-800 dark:text-orange-200'
                          : 'text-yellow-800 dark:text-yellow-200'
                    }`}>
                      {paydayStatus.account_disabled
                        ? 'Account Disabled - Payment Required'
                        : paydayStatus.is_past_payday_deadline
                          ? 'Payment Overdue'
                          : 'Payment Due Soon'
                      }
                    </p>
                    {paydayStatus.next_payday_deadline && (
                      <p className={`text-xs mt-1 ${
                        paydayStatus.account_disabled
                          ? 'text-red-600 dark:text-red-400'
                          : paydayStatus.is_past_payday_deadline
                            ? 'text-orange-600 dark:text-orange-400'
                            : 'text-yellow-600 dark:text-yellow-400'
                      }`}>
                        {paydayStatus.is_past_payday_deadline ? 'Was due:' : 'Due:'} {' '}
                        {new Date(paydayStatus.next_payday_deadline).toLocaleString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          timeZoneName: 'short'
                        })}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {paymentStep === 'confirm' && (
              <>
                <div className="mb-6 space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Payment Details</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Amount:</span>
                        <span className="font-bold">${paymentAmount.toFixed(6)} {paymentConfig?.token || 'USDT'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Network:</span>
                        <span className="font-medium">Solana</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Wallet:</span>
                        <span className="font-mono text-xs">
                          {publicKey ? `${publicKey.toString().slice(0, 8)}...${publicKey.toString().slice(-8)}` : 'Not connected'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Connection Status:</span>
                        <span className={`font-medium ${isWalletConnected ? 'text-green-600' : 'text-red-600'}`}>
                          {isWalletConnected ? 'Connected' : 'Disconnected'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">⚠️ Important Notes</h4>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                      <li>• Ensure you have sufficient USDT in your wallet</li>
                      <li>• Keep some SOL for transaction fees (~0.001 SOL)</li>
                      <li>• Payment will be processed on Solana mainnet</li>
                      <li>• Transaction is irreversible once confirmed</li>
                    </ul>
                  </div>
                </div>

                <div className="flex justify-end gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowPaymentModal(false)}
                    disabled={isProcessingPayment}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      console.log('=== PAYMENT BUTTON CLICKED ===');
                      console.log('Connected:', connected);
                      console.log('PublicKey:', publicKey?.toString());
                      console.log('isWalletConnected:', isWalletConnected);
                      console.log('isProcessingPayment:', isProcessingPayment);
                      createSolanaPayment();
                    }}
                    className="bg-blue-600 hover:bg-blue-700"
                    disabled={isProcessingPayment || !isWalletConnected}
                  >
                    {!isWalletConnected ? 'Connect Wallet First' : 'Proceed with Payment'}
                  </Button>
                </div>
              </>
            )}

            {paymentStep === 'processing' && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Processing Payment</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Please confirm the transaction in your wallet and wait for confirmation...
                </p>
              </div>
            )}

            {paymentStep === 'success' && (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-green-600 dark:text-green-400 text-xl">✓</span>
                </div>
                <h3 className="text-lg font-semibold mb-2 text-green-600 dark:text-green-400">Payment Successful!</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Your payment has been processed successfully. Your tier will be updated shortly.
                </p>
              </div>
            )}

            {paymentStep === 'error' && (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-red-600 dark:text-red-400 text-xl">✗</span>
                </div>
                <h3 className="text-lg font-semibold mb-2 text-red-600 dark:text-red-400">Payment Failed</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  There was an error processing your payment. Please try again.
                </p>
                <Button
                  onClick={() => {
                    setPaymentStep('confirm');
                    setIsProcessingPayment(false);
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Try Again
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Payment History Section */}
      {paymentHistory.length > 0 && (
        <div className="mt-8">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Payment History</h3>
              <div className="space-y-3">
                {paymentHistory.slice(0, 5).map((payment) => (
                  <div key={payment.id} className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <p className="font-medium">
                        {payment.payment_type === 'profit_share' ? 'Profit Share' : '30 Days Access'}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(payment.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${payment.amount}</p>
                      <span className={`px-2 py-1 rounded text-xs ${
                        payment.status === 'confirmed'
                          ? 'bg-green-100 text-green-800'
                          : payment.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {payment.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}