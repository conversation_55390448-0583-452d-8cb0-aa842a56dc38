import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTranslation } from '../hooks/useTranslation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/badge';
import { Shield, MapPin, Clock, Monitor, RefreshCw, AlertTriangle } from 'lucide-react';
import { useToast } from '../components/ui/use-toast';

interface AccessLog {
  id: number;
  ip_address: string;
  login_timestamp: string;
  login_successful: boolean;
  user_agent: string;
  geolocation_data: any;
  failure_reason?: string;
}

interface SecurityStats {
  user_id: string;
  email: string;
  account_created: string;
  two_fa_enabled: boolean;
  is_active: boolean;
  statistics: {
    period_days: number;
    total_logins: number;
    successful_logins: number;
    failed_logins: number;
    unique_ips: number;
    success_rate: number;
  };
  last_login: {
    timestamp: string | null;
    ip_address: string | null;
    user_agent: string | null;
    geolocation: any;
  };
}

interface CurrentSession {
  current_session: {
    ip_address: string;
    user_agent: string;
    geolocation: any;
    session_start: string | null;
    is_current_device: boolean;
  };
}

export default function AccessSecurity() {
  const { user } = useAuth();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [accessLogs, setAccessLogs] = useState<AccessLog[]>([]);
  const [securityStats, setSecurityStats] = useState<SecurityStats | null>(null);
  const [currentSession, setCurrentSession] = useState<CurrentSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchSecurityData = async () => {
    try {
      const token = localStorage.getItem('access_token');
      console.log('Token found:', !!token);
      console.log('User from context:', user);

      if (!token) {
        console.error('No authentication token found');
        toast({
          title: "Authentication Error",
          description: "Please log in again to view security data.",
          variant: "destructive",
        });
        return;
      }

      // Fetch access logs (last 5 connections)
      console.log('Fetching access logs...');
      const logsResponse = await fetch('/api/users/security/access-logs?limit=5', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Access logs response status:', logsResponse.status);
      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        console.log('Access logs data:', logsData);
        setAccessLogs(logsData.access_logs || []);
      } else {
        const errorData = await logsResponse.text();
        console.error('Failed to fetch access logs:', logsResponse.status, logsResponse.statusText, errorData);
      }

      // Fetch security statistics
      console.log('Fetching security stats...');
      const statsResponse = await fetch('/api/users/security/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Security stats response status:', statsResponse.status);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        console.log('Security stats data:', statsData);
        setSecurityStats(statsData);
      } else {
        const errorData = await statsResponse.text();
        console.error('Failed to fetch security stats:', statsResponse.status, statsResponse.statusText, errorData);
      }

      // Fetch current session
      console.log('Fetching current session...');
      const sessionResponse = await fetch('/api/users/security/current-session', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Current session response status:', sessionResponse.status);
      if (sessionResponse.ok) {
        const sessionData = await sessionResponse.json();
        console.log('Current session data:', sessionData);
        setCurrentSession(sessionData);
      } else {
        const errorData = await sessionResponse.text();
        console.error('Failed to fetch current session:', sessionResponse.status, sessionResponse.statusText, errorData);
      }

    } catch (error) {
      console.error('Error fetching security data:', error);
      toast({
        title: "Network Error",
        description: "Failed to fetch security data. Please check your connection.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (!user) {
      console.log('No user found, redirecting to login...');
      // Don't fetch data if user is not authenticated
      setLoading(false);
      return;
    }
    fetchSecurityData();
  }, [user]);

  const handleRefresh = async () => {
    setRefreshing(true);
    console.log('Refreshing security data...');

    try {
      await fetchSecurityData();
      toast({
        title: "Security data refreshed",
        description: "Your security information has been updated successfully.",
        variant: "success",
      });
      console.log('Security data refresh completed');
    } catch (error) {
      toast({
        title: "Refresh failed",
        description: "Failed to refresh security data. Please try again.",
        variant: "destructive",
      });
      console.error('Security data refresh failed:', error);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const getLocationString = (geolocation: any) => {
    if (!geolocation) return 'Unknown';
    const { city, country, region } = geolocation;
    return [city, region, country].filter(Boolean).join(', ') || 'Unknown';
  };

  const getBrowserInfo = (userAgent: string) => {
    if (!userAgent) return 'Unknown';
    // Simple browser detection
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
            <p className="text-muted-foreground">Please log in to view your security information.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">{t('accessSecurity.title')}</h1>
          <p className="text-muted-foreground mt-1">{t('accessSecurity.subtitle')}</p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={refreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {t('accessSecurity.refresh')}
        </Button>
      </div>

      {/* Security Overview */}
      {securityStats && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('accessSecurity.totalLogins')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityStats.statistics.total_logins}</div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('accessSecurity.successRate')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityStats.statistics.success_rate}%</div>
                <p className="text-xs text-muted-foreground">Login success rate</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('accessSecurity.uniqueIPs')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityStats.statistics.unique_ips}</div>
                <p className="text-xs text-muted-foreground">Different locations</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('accessSecurity.twoFactorStatus')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Shield className={`h-5 w-5 ${securityStats.two_fa_enabled ? 'text-green-500' : 'text-red-500'}`} />
                  <span className="text-sm font-medium">
                    {securityStats.two_fa_enabled ? t('accessSecurity.enabled') : t('accessSecurity.disabled')}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Last Login Activity */}
          {securityStats.last_login && securityStats.last_login.timestamp && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Last Login Activity
                </CardTitle>
                <CardDescription>Your most recent successful login</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Login Time</label>
                    <p className="text-sm">{formatDate(securityStats.last_login.timestamp)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">IP Address</label>
                    <p className="text-sm">{securityStats.last_login.ip_address || 'Unknown'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Location</label>
                    <p className="text-sm">{getLocationString(securityStats.last_login.geolocation)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Browser</label>
                    <p className="text-sm">{getBrowserInfo(securityStats.last_login.user_agent || '')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Current Session */}
      {currentSession && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Monitor className="h-5 w-5 mr-2" />
              {t('accessSecurity.currentSession')}
            </CardTitle>
            <CardDescription>{t('accessSecurity.currentSessionDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('accessSecurity.ipAddress')}</label>
                <p className="text-sm">{currentSession.current_session.ip_address}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('accessSecurity.location')}</label>
                <p className="text-sm">{getLocationString(currentSession.current_session.geolocation)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('accessSecurity.userAgent')}</label>
                <p className="text-sm">{getBrowserInfo(currentSession.current_session.user_agent)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('accessSecurity.sessionStart')}</label>
                <p className="text-sm">{formatDate(currentSession.current_session.session_start)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Login History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            {t('accessSecurity.recentLoginActivity')}
          </CardTitle>
          <CardDescription>{t('accessSecurity.recentLoginDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {accessLogs.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">{t('accessSecurity.noLoginActivity')}</p>
            ) : (
              accessLogs.map((log) => (
                <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-full ${log.login_successful ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
                      {log.login_successful ? (
                        <Shield className="h-4 w-4" />
                      ) : (
                        <AlertTriangle className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={log.login_successful ? "default" : "destructive"}>
                          {log.login_successful ? t('accessSecurity.successful') : t('accessSecurity.failed')}
                        </Badge>
                        <span className="text-sm font-medium">{log.ip_address}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                        <span className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {getLocationString(log.geolocation_data)}
                        </span>
                        <span className="flex items-center">
                          <Monitor className="h-3 w-3 mr-1" />
                          {getBrowserInfo(log.user_agent)}
                        </span>
                      </div>
                      {!log.login_successful && log.failure_reason && (
                        <p className="text-sm text-red-600 mt-1">Reason: {log.failure_reason}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{formatDate(log.login_timestamp)}</p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
