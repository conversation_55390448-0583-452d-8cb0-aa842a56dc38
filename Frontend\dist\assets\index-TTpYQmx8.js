import{S as k,f as N,h as P,E as F,P as U,b as l,T as H,V as g}from"./main-CLJ81jXo.js";let v;const O=new Uint8Array(16);function L(){if(!v&&(v=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!v))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return v(O)}const c=[];for(let i=0;i<256;++i)c.push((i+256).toString(16).slice(1));function R(i,t=0){return c[i[t+0]]+c[i[t+1]]+c[i[t+2]]+c[i[t+3]]+"-"+c[i[t+4]]+c[i[t+5]]+"-"+c[i[t+6]]+c[i[t+7]]+"-"+c[i[t+8]]+c[i[t+9]]+"-"+c[i[t+10]]+c[i[t+11]]+c[i[t+12]]+c[i[t+13]]+c[i[t+14]]+c[i[t+15]]}const $=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),z={randomUUID:$};function D(i,t,e){if(z.randomUUID&&!i)return z.randomUUID();i=i||{};const n=i.random||(i.rng||L)();return n[6]=n[6]&15|64,n[8]=n[8]&63|128,R(n)}function x(i){return i.version===void 0}function M(i){return x(i)?i.serialize({verifySignatures:!1,requireAllSignatures:!1}):i.serialize()}var W=function(i,t,e,n){function s(r){return r instanceof e?r:new e(function(a){a(r)})}return new(e||(e=Promise))(function(r,a){function f(o){try{h(n.next(o))}catch(u){a(u)}}function p(o){try{h(n.throw(o))}catch(u){a(u)}}function h(o){o.done?r(o.value):s(o.value).then(f,p)}h((n=n.apply(i,t||[])).next())})};function T(i){return W(this,void 0,void 0,function*(){try{return yield i.request({method:"wallet_getSnaps"}),!0}catch{return!1}})}function V(){return W(this,void 0,void 0,function*(){try{const i=window.ethereum;if(!i)return null;if(i.providers&&Array.isArray(i.providers)){const t=i.providers;for(const e of t)if(yield T(e))return e}if(i.detected&&Array.isArray(i.detected)){const t=i.detected;for(const e of t)if(yield T(e))return e}return(yield T(i))?i:null}catch(i){return console.error(i),null}})}const q="solana:mainnet",B="solana:devnet",G="solana:testnet",J="solana:localnet",j=[q,B,G,J];function I(i){return j.includes(i)}var m=function(i,t,e,n){if(e==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?i!==t||!n:!t.has(i))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e==="m"?n:e==="a"?n.call(i):n?n.value:t.get(i)},_=function(i,t,e,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?i!==t||!s:!t.has(i))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(i,e):s?s.value=e:t.set(i,e),e},w,y,b,S,E,A;const Q=j,X=[k,N,P];class C{get address(){return m(this,w,"f")}get publicKey(){return m(this,y,"f").slice()}get chains(){return m(this,b,"f").slice()}get features(){return m(this,S,"f").slice()}get label(){return m(this,E,"f")}get icon(){return m(this,A,"f")}constructor({address:t,publicKey:e,label:n,icon:s}){w.set(this,void 0),y.set(this,void 0),b.set(this,void 0),S.set(this,void 0),E.set(this,void 0),A.set(this,void 0),new.target===C&&Object.freeze(this),_(this,w,t,"f"),_(this,y,e,"f"),_(this,b,Q,"f"),_(this,S,X,"f"),_(this,E,n,"f"),_(this,A,s,"f")}}w=new WeakMap,y=new WeakMap,b=new WeakMap,S=new WeakMap,E=new WeakMap,A=new WeakMap;var d=function(i,t,e,n){function s(r){return r instanceof e?r:new e(function(a){a(r)})}return new(e||(e=Promise))(function(r,a){function f(o){try{h(n.next(o))}catch(u){a(u)}}function p(o){try{h(n.throw(o))}catch(u){a(u)}}function h(o){o.done?r(o.value):s(o.value).then(f,p)}h((n=n.apply(i,t||[])).next())})};class K extends F{constructor(t){super(),this._network="mainnet-beta",this._iframeParams={},this._element=null,this._iframe=null,this._publicKey=null,this._account=null,this._isConnected=!1,this._connectHandler=null,this._messageHandlers={},this._handleEvent=e=>{var n,s;switch(e.type){case"connect":{this._collapseIframe(),!((n=e.data)===null||n===void 0)&&n.publicKey?(this._publicKey=e.data.publicKey,this._isConnected=!0,this._connectHandler&&(this._connectHandler.resolve(),this._connectHandler=null),this._connected()):(this._connectHandler&&(this._connectHandler.reject(),this._connectHandler=null),this._disconnected());return}case"disconnect":{this._connectHandler&&(this._connectHandler.reject(),this._connectHandler=null),this._disconnected();return}case"accountChanged":{!((s=e.data)===null||s===void 0)&&s.publicKey?(this._publicKey=e.data.publicKey,this.emit("accountChanged",this.publicKey),this._standardConnected()):(this.emit("accountChanged",void 0),this._standardDisconnected());return}default:return}},this._handleResize=e=>{e.resizeMode==="full"?e.params.mode==="fullscreen"?this._expandIframe():e.params.mode==="hide"&&this._collapseIframe():e.resizeMode==="coordinates"&&this._resizeIframe(e.params)},this._handleMessage=e=>{var n;if(((n=e.data)===null||n===void 0?void 0:n.channel)!=="solflareIframeToWalletAdapter")return;const s=e.data.data||{};if(s.type==="event")this._handleEvent(s.event);else if(s.type==="resize")this._handleResize(s);else if(s.type==="response"&&this._messageHandlers[s.id]){const{resolve:r,reject:a}=this._messageHandlers[s.id];delete this._messageHandlers[s.id],s.error?a(s.error):r(s.result)}},this._removeElement=()=>{this._element&&(this._element.remove(),this._element=null)},this._removeDanglingElements=()=>{const e=document.getElementsByClassName("solflare-metamask-wallet-adapter-iframe");for(const n of e)n.parentElement&&n.remove()},this._injectElement=()=>{this._removeElement(),this._removeDanglingElements();const e=Object.assign(Object.assign({},this._iframeParams),{mm:!0,v:1,cluster:this._network||"mainnet-beta",origin:window.location.origin||"",title:document.title||""}),n=Object.keys(e).map(r=>`${r}=${encodeURIComponent(e[r])}`).join("&"),s=`${K.IFRAME_URL}?${n}`;this._element=document.createElement("div"),this._element.className="solflare-metamask-wallet-adapter-iframe",this._element.innerHTML=`
      <iframe src='${s}' style='position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; border: none; border-radius: 0; z-index: 99999; color-scheme: auto;' allowtransparency='true'></iframe>
    `,document.body.appendChild(this._element),this._iframe=this._element.querySelector("iframe"),window.addEventListener("message",this._handleMessage,!1)},this._collapseIframe=()=>{this._iframe&&(this._iframe.style.top="",this._iframe.style.right="",this._iframe.style.height="2px",this._iframe.style.width="2px")},this._expandIframe=()=>{this._iframe&&(this._iframe.style.top="0px",this._iframe.style.bottom="0px",this._iframe.style.left="0px",this._iframe.style.right="0px",this._iframe.style.width="100%",this._iframe.style.height="100%")},this._resizeIframe=e=>{this._iframe&&(this._iframe.style.top=isFinite(e.top)?`${e.top}px`:"",this._iframe.style.bottom=isFinite(e.bottom)?`${e.bottom}px`:"",this._iframe.style.left=isFinite(e.left)?`${e.left}px`:"",this._iframe.style.right=isFinite(e.right)?`${e.right}px`:"",this._iframe.style.width=isFinite(e.width)?`${e.width}px`:e.width,this._iframe.style.height=isFinite(e.height)?`${e.height}px`:e.height)},this._sendIframeMessage=e=>{if(!this.connected||!this.publicKey)throw new Error("Wallet not connected");return new Promise((n,s)=>{var r,a;const f=D();this._messageHandlers[f]={resolve:n,reject:s},(a=(r=this._iframe)===null||r===void 0?void 0:r.contentWindow)===null||a===void 0||a.postMessage({channel:"solflareWalletAdapterToIframe",data:Object.assign({id:f},e)},"*")})},this._connected=()=>{this._isConnected=!0,this.emit("connect",this.publicKey),this._standardConnected()},this._disconnected=()=>{this._publicKey=null,this._isConnected=!1,window.removeEventListener("message",this._handleMessage,!1),this._removeElement(),this.emit("disconnect"),this._standardDisconnected()},this._standardConnected=()=>{if(!this.publicKey)return;const e=this.publicKey.toString();(!this._account||this._account.address!==e)&&(this._account=new C({address:e,publicKey:this.publicKey.toBytes()}),this.emit("standard_change",{accounts:this.standardAccounts}))},this._standardDisconnected=()=>{this._account&&(this._account=null,this.emit("standard_change",{accounts:this.standardAccounts}))},t?.network&&(this._network=t?.network),window.SolflareMetaMaskParams&&(this._iframeParams=Object.assign(Object.assign({},this._iframeParams),window.SolflareMetaMaskParams)),t?.params&&(this._iframeParams=Object.assign(Object.assign({},this._iframeParams),t?.params))}get publicKey(){return this._publicKey?new U(this._publicKey):null}get standardAccount(){return this._account}get standardAccounts(){return this._account?[this._account]:[]}get isConnected(){return this._isConnected}get connected(){return this.isConnected}get autoApprove(){return!1}connect(){return d(this,void 0,void 0,function*(){this.connected||(this._injectElement(),yield new Promise((t,e)=>{this._connectHandler={resolve:t,reject:e}}))})}disconnect(){return d(this,void 0,void 0,function*(){yield this._sendIframeMessage({method:"disconnect"}),this._disconnected()})}signTransaction(t){var e;return d(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw new Error("Wallet not connected");try{const n=M(t),s=yield this._sendIframeMessage({method:"signTransactionV2",params:{transaction:l.encode(n)}}),{transaction:r}=s;return x(t)?H.from(l.decode(r)):g.deserialize(l.decode(r))}catch(n){throw new Error(((e=n?.toString)===null||e===void 0?void 0:e.call(n))||"Failed to sign transaction")}})}signAllTransactions(t){var e;return d(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw new Error("Wallet not connected");try{const n=t.map(r=>M(r)),{transactions:s}=yield this._sendIframeMessage({method:"signAllTransactionsV2",params:{transactions:n.map(r=>l.encode(r))}});return s.map((r,a)=>x(t[a])?H.from(l.decode(r)):g.deserialize(l.decode(r)))}catch(n){throw new Error(((e=n?.toString)===null||e===void 0?void 0:e.call(n))||"Failed to sign transactions")}})}signAndSendTransaction(t,e){var n;return d(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw new Error("Wallet not connected");try{const s=M(t),{signature:r}=yield this._sendIframeMessage({method:"signAndSendTransaction",params:{transaction:l.encode(s),options:e}});return r}catch(s){throw new Error(((n=s?.toString)===null||n===void 0?void 0:n.call(s))||"Failed to sign and send transaction")}})}signMessage(t,e="utf8"){var n;return d(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw new Error("Wallet not connected");try{const{signature:s}=yield this._sendIframeMessage({method:"signMessage",params:{data:l.encode(t),display:e}});return Uint8Array.from(l.decode(s))}catch(s){throw new Error(((n=s?.toString)===null||n===void 0?void 0:n.call(s))||"Failed to sign message")}})}sign(t,e="utf8"){return d(this,void 0,void 0,function*(){return yield this.signMessage(t,e)})}static isSupported(){return d(this,void 0,void 0,function*(){return!!(yield V())})}standardSignAndSendTransaction(...t){return d(this,void 0,void 0,function*(){if(!this.connected)throw new Error("not connected");const e=[];if(t.length===1){const{transaction:n,account:s,chain:r,options:a}=t[0],{minContextSlot:f,preflightCommitment:p,skipPreflight:h,maxRetries:o}=a||{};if(s!==this._account)throw new Error("invalid account");if(!I(r))throw new Error("invalid chain");const u=yield this.signAndSendTransaction(g.deserialize(n),{preflightCommitment:p,minContextSlot:f,maxRetries:o,skipPreflight:h});e.push({signature:l.decode(u)})}else if(t.length>1)for(const n of t)e.push(...yield this.standardSignAndSendTransaction(n));return e})}standardSignTransaction(...t){return d(this,void 0,void 0,function*(){if(!this.connected)throw new Error("not connected");const e=[];if(t.length===1){const{transaction:n,account:s,chain:r}=t[0];if(s!==this._account)throw new Error("invalid account");if(r&&!I(r))throw new Error("invalid chain");const a=yield this.signTransaction(g.deserialize(n));e.push({signedTransaction:a.serialize()})}else if(t.length>1){let n;for(const a of t){if(a.account!==this._account)throw new Error("invalid account");if(a.chain){if(!I(a.chain))throw new Error("invalid chain");if(n){if(a.chain!==n)throw new Error("conflicting chain")}else n=a.chain}}const s=t.map(({transaction:a})=>g.deserialize(a)),r=yield this.signAllTransactions(s);e.push(...r.map(a=>({signedTransaction:a.serialize()})))}return e})}standardSignMessage(...t){return d(this,void 0,void 0,function*(){if(!this.connected)throw new Error("not connected");const e=[];if(t.length===1){const{message:n,account:s}=t[0];if(s!==this._account)throw new Error("invalid account");const r=yield this.signMessage(n);e.push({signedMessage:n,signature:r})}else if(t.length>1)for(const n of t)e.push(...yield this.standardSignMessage(n));return e})}}K.IFRAME_URL="https://widget.solflare.com/";export{C as StandardSolflareMetaMaskWalletAccount,K as default};
//# sourceMappingURL=index-TTpYQmx8.js.map
