import { useState, useEffect } from 'react';
import { <PERSON>ert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, Mail } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from '@/hooks/useTranslation';

interface DeactivatedAccountAlertProps {
  className?: string;
}

export function DeactivatedAccountAlert({ className }: DeactivatedAccountAlertProps) {
  const { user } = useAuth();
  const { t } = useTranslation();
  const [isDeactivated, setIsDeactivated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAccountStatus = async () => {
      try {
        const token = localStorage.getItem('access_token');
        if (!token) {
          setLoading(false);
          return;
        }

        // Check user status via the profile endpoint
        const response = await fetch('/api/users/profile', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          const userIsActive = data.user?.is_active;
          
          // If user is not active, show the deactivation warning
          if (userIsActive === false) {
            setIsDeactivated(true);
          } else {
            setIsDeactivated(false);
          }
        } else if (response.status === 401) {
          // If we get 401, it might be because the account is deactivated
          // Check the error message
          const errorData = await response.json().catch(() => ({}));
          if (errorData.error === 'user_inactive' || errorData.message?.includes('inactive')) {
            setIsDeactivated(true);
          }
        }
      } catch (error) {
        console.error('Error checking account status:', error);
      } finally {
        setLoading(false);
      }
    };

    // Only check if user is logged in
    if (user) {
      checkAccountStatus();
    } else {
      setLoading(false);
    }
  }, [user]);

  // Don't render anything if loading or account is active
  if (loading || !isDeactivated) {
    return null;
  }

  return (
    <div className={className}>
      <Alert variant="destructive" className="border-red-300 bg-red-50 dark:bg-red-900/20 mb-6">
        <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
        <AlertTitle className="text-red-800 dark:text-red-200 font-semibold text-lg">
          {t('account.deactivated.title', 'Account Deactivated')}
        </AlertTitle>
        <AlertDescription className="text-red-700 dark:text-red-300 mt-2">
          <div className="space-y-3">
            <p className="font-medium">
              {t('account.deactivated.message', 'Your account has been deactivated and access to trading features is restricted.')}
            </p>
            
            <div className="bg-red-100 dark:bg-red-800/30 rounded-lg p-4 border border-red-200 dark:border-red-700">
              <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                {t('account.deactivated.nextSteps', 'What to do next:')}
              </h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <span className="text-red-600 dark:text-red-400 mt-0.5">•</span>
                  <span>{t('account.deactivated.step1', 'Contact our support team for assistance')}</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-red-600 dark:text-red-400 mt-0.5">•</span>
                  <span>{t('account.deactivated.step2', 'Provide your account details and reason for deactivation')}</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-red-600 dark:text-red-400 mt-0.5">•</span>
                  <span>{t('account.deactivated.step3', 'Wait for account review and reactivation')}</span>
                </li>
              </ul>
            </div>

            <div className="flex pt-2">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 text-sm font-medium"
              >
                <Mail className="h-4 w-4" />
                {t('account.deactivated.emailSupport', 'Email Support')}
              </a>
            </div>

            <div className="text-xs text-red-600 dark:text-red-400 pt-2 border-t border-red-200 dark:border-red-700">
              <p>
                <strong>{t('account.deactivated.note', 'Note:')}</strong>{' '}
                {t('account.deactivated.noteText', 'Your account data is preserved and will be restored upon reactivation. Trading activities are temporarily suspended for security reasons.')}
              </p>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
}

export default DeactivatedAccountAlert;
