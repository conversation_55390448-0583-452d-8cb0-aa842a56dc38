/**
 * Translation Validation Utilities
 * 
 * Utilities to help developers validate and manage translations across all supported languages.
 */

import enCommon from '../locales/en/common';
import esCommon from '../locales/es/common';
import ptCommon from '../locales/pt/common';
import koCommon from '../locales/ko/common';
import jaCommon from '../locales/ja/common';
import deCommon from '../locales/de/common';
import frCommon from '../locales/fr/common';
import zhCommon from '../locales/zh/common';

type TranslationObject = Record<string, any>;

const translations = {
  en: enCommon,
  es: esCommon,
  pt: ptCommon,
  ko: koCommon,
  ja: jaCommon,
  de: deCommon,
  fr: frCommon,
  zh: zhCommon,
};

/**
 * Flattens a nested object into dot notation keys
 */
function flattenObject(obj: TranslationObject, prefix = ''): Record<string, string> {
  const flattened: Record<string, string> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }
  
  return flattened;
}

/**
 * Gets all translation keys from the English (reference) translation
 */
export function getAllTranslationKeys(): string[] {
  const flattened = flattenObject(translations.en);
  return Object.keys(flattened).sort();
}

/**
 * Finds missing translation keys for a specific language
 */
export function findMissingKeys(languageCode: keyof typeof translations): string[] {
  const englishKeys = new Set(getAllTranslationKeys());
  const languageKeys = new Set(Object.keys(flattenObject(translations[languageCode])));
  
  return Array.from(englishKeys).filter(key => !languageKeys.has(key));
}

/**
 * Finds extra translation keys (keys that exist in a language but not in English)
 */
export function findExtraKeys(languageCode: keyof typeof translations): string[] {
  const englishKeys = new Set(getAllTranslationKeys());
  const languageKeys = new Set(Object.keys(flattenObject(translations[languageCode])));
  
  return Array.from(languageKeys).filter(key => !englishKeys.has(key));
}

/**
 * Validates all translations and returns a comprehensive report
 */
export function validateAllTranslations(): {
  isValid: boolean;
  totalKeys: number;
  languages: Record<string, {
    missingKeys: string[];
    extraKeys: string[];
    completeness: number;
  }>;
} {
  const totalKeys = getAllTranslationKeys().length;
  const languages: Record<string, any> = {};
  let isValid = true;
  
  for (const langCode of Object.keys(translations) as Array<keyof typeof translations>) {
    if (langCode === 'en') continue; // Skip English as it's the reference
    
    const missingKeys = findMissingKeys(langCode);
    const extraKeys = findExtraKeys(langCode);
    const completeness = ((totalKeys - missingKeys.length) / totalKeys) * 100;
    
    languages[langCode] = {
      missingKeys,
      extraKeys,
      completeness: Math.round(completeness * 100) / 100,
    };
    
    if (missingKeys.length > 0 || extraKeys.length > 0) {
      isValid = false;
    }
  }
  
  return {
    isValid,
    totalKeys,
    languages,
  };
}

/**
 * Generates a translation template for a new language
 */
export function generateTranslationTemplate(): TranslationObject {
  function createTemplate(obj: TranslationObject): TranslationObject {
    const template: TranslationObject = {};
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          template[key] = createTemplate(obj[key]);
        } else if (Array.isArray(obj[key])) {
          template[key] = obj[key].map(() => '[TRANSLATE]');
        } else {
          template[key] = '[TRANSLATE]';
        }
      }
    }
    
    return template;
  }
  
  return createTemplate(translations.en);
}

/**
 * Finds potentially untranslated content (still contains English text)
 */
export function findUntranslatedContent(languageCode: keyof typeof translations): string[] {
  if (languageCode === 'en') return [];
  
  const flattened = flattenObject(translations[languageCode]);
  const englishFlattened = flattenObject(translations.en);
  const untranslated: string[] = [];
  
  for (const key in flattened) {
    const value = flattened[key];
    const englishValue = englishFlattened[key];
    
    // Check if the value is exactly the same as English (potential untranslated content)
    if (typeof value === 'string' && typeof englishValue === 'string' && value === englishValue) {
      // Skip common words that might be the same across languages
      const commonWords = ['API', 'NFT', 'USD', 'USDT', 'BTC', 'ETH', 'OK', 'VIP'];
      if (!commonWords.some(word => value.includes(word))) {
        untranslated.push(key);
      }
    }
  }
  
  return untranslated;
}

/**
 * Console logging utilities for development
 */
export const TranslationLogger = {
  /**
   * Logs validation results to console
   */
  logValidationResults(): void {
    const results = validateAllTranslations();
    
    console.group('🌍 Translation Validation Results');
    console.log(`Total translation keys: ${results.totalKeys}`);
    console.log(`Overall status: ${results.isValid ? '✅ Valid' : '❌ Issues found'}`);
    
    for (const [langCode, data] of Object.entries(results.languages)) {
      console.group(`${langCode.toUpperCase()} - ${data.completeness}% complete`);
      
      if (data.missingKeys.length > 0) {
        console.warn(`Missing keys (${data.missingKeys.length}):`, data.missingKeys);
      }
      
      if (data.extraKeys.length > 0) {
        console.warn(`Extra keys (${data.extraKeys.length}):`, data.extraKeys);
      }
      
      if (data.missingKeys.length === 0 && data.extraKeys.length === 0) {
        console.log('✅ No issues found');
      }
      
      console.groupEnd();
    }
    
    console.groupEnd();
  },
  
  /**
   * Logs potentially untranslated content
   */
  logUntranslatedContent(): void {
    console.group('🔍 Potentially Untranslated Content');
    
    for (const langCode of Object.keys(translations) as Array<keyof typeof translations>) {
      if (langCode === 'en') continue;
      
      const untranslated = findUntranslatedContent(langCode);
      if (untranslated.length > 0) {
        console.warn(`${langCode.toUpperCase()} (${untranslated.length} items):`, untranslated);
      } else {
        console.log(`${langCode.toUpperCase()}: ✅ No untranslated content found`);
      }
    }
    
    console.groupEnd();
  },
  
  /**
   * Logs all available translation keys
   */
  logAllKeys(): void {
    const keys = getAllTranslationKeys();
    console.group(`📝 All Translation Keys (${keys.length})`);
    keys.forEach(key => console.log(key));
    console.groupEnd();
  }
};

/**
 * Development helper - run validation in development mode
 */
if (process.env.NODE_ENV === 'development') {
  // Expose utilities to window for easy access in browser console
  (window as any).translationUtils = {
    validate: validateAllTranslations,
    findMissing: findMissingKeys,
    findExtra: findExtraKeys,
    findUntranslated: findUntranslatedContent,
    getAllKeys: getAllTranslationKeys,
    generateTemplate: generateTranslationTemplate,
    logger: TranslationLogger,
  };
  
  // Auto-run validation on load
  setTimeout(() => {
    TranslationLogger.logValidationResults();
  }, 1000);
}

export default {
  getAllTranslationKeys,
  findMissingKeys,
  findExtraKeys,
  validateAllTranslations,
  generateTranslationTemplate,
  findUntranslatedContent,
  TranslationLogger,
};
