/**
 * Paper Trading Dashboard Component
 * 
 * Displays paper trading performance, virtual balance, and simulated trade history
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { RefreshCw, TrendingUp, TrendingDown, RotateCcw, BarChart3 } from 'lucide-react';
import { paperTradingApi } from '../services/paperTradingApi';
import { usePaperTrading } from '../hooks/usePaperTrading';
import { useTranslation } from '@/hooks/useTranslation';
import { useMobile } from '../hooks/useResponsiveDesign';
import { toast } from '@/components/ui/use-toast';
import PaperTradingAnalytics from './PaperTradingAnalytics';
import PaperTradingReset from './PaperTradingReset';

interface PaperTradingDashboardProps {
  className?: string;
}

export const PaperTradingDashboard: React.FC<PaperTradingDashboardProps> = ({ className = '' }) => {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const { isPaperMode, account, balance } = usePaperTrading();
  
  const [analytics, setAnalytics] = useState<any>(null);
  const [recentTrades, setRecentTrades] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);

  // Load paper trading data
  const loadPaperTradingData = async () => {
    if (!isPaperMode) return;
    
    try {
      setIsLoading(true);
      
      const [analyticsData, historyData] = await Promise.all([
        paperTradingApi.getPaperTradingAnalytics(30),
        paperTradingApi.getPaperTradingHistory(10, 0)
      ]);
      
      setAnalytics(analyticsData);
      setRecentTrades(historyData.trades || []);
    } catch (error) {
      console.error('Error loading paper trading data:', error);
      toast({
        title: "Error",
        description: t('paperTradingDashboard.errors.loadFailed'),
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle reset modal close
  const handleResetModalClose = () => {
    setShowResetModal(false);
    // Refresh data after reset
    loadPaperTradingData();
  };

  // Load data when component mounts or paper mode changes
  useEffect(() => {
    loadPaperTradingData();
  }, [isPaperMode]);

  // Don't render if not in paper mode
  if (!isPaperMode) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getPerformanceColor = (value: number) => {
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <div className={`space-y-6 w-full min-w-0 max-w-full overflow-hidden ${className}`}>
      {/* Paper Trading Performance Overview */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardHeader className={`${isMobile ? 'flex flex-col space-y-2' : 'flex flex-row items-center justify-between space-y-0'} pb-2`}>
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <CardTitle className={`${isMobile ? 'text-base' : 'text-xl'} font-semibold truncate`}>
              {t('paperTradingDashboard.performance.title')}
            </CardTitle>
            <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded flex-shrink-0">
              {t('trading.paperTradingLabel')}
            </span>
          </div>
          <div className={`flex items-center ${isMobile ? 'gap-1' : 'space-x-2'} flex-shrink-0`}>
            <Button
              variant="outline"
              size="sm"
              onClick={loadPaperTradingData}
              disabled={isLoading}
              className={isMobile ? 'text-xs px-2 py-1' : ''}
            >
              <RefreshCw className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} ${isLoading ? 'animate-spin' : ''}`} />
              {!isMobile && <span className="ml-1">{t('paperTradingDashboard.performance.refresh')}</span>}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowResetModal(true)}
              className={`${isMobile ? 'text-xs px-2 py-1' : ''} text-orange-600 border-orange-300 hover:bg-orange-50`}
            >
              <RotateCcw className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
              {!isMobile && <span className="ml-1">{t('paperTradingDashboard.performance.reset')}</span>}
            </Button>
          </div>
        </CardHeader>
        <CardContent className={`${isMobile ? 'p-3' : 'p-6'} w-full min-w-0 max-w-full overflow-hidden`}>
          <div className={`grid ${isMobile ? 'grid-cols-2 gap-2' : 'grid-cols-4 gap-4'} w-full`}>
            {/* Virtual Balance */}
            <div className="text-center min-w-0">
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground truncate`}>{t('trading.virtualBalance')}</p>
              <p className={`${isMobile ? 'text-sm' : 'text-2xl'} font-bold truncate`}>
                {isMobile ? `$${(balance || 0).toLocaleString()}` : formatCurrency(balance || 0)}
              </p>
            </div>

            {/* Total P&L */}
            <div className="text-center min-w-0">
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground truncate`}>{t('paperTradingAnalytics.metrics.totalPnl')}</p>
              <p className={`${isMobile ? 'text-sm' : 'text-2xl'} font-bold ${getPerformanceColor(account?.total_pnl || 0)} truncate`}>
                {isMobile ? `$${(account?.total_pnl || 0).toLocaleString()}` : formatCurrency(account?.total_pnl || 0)}
              </p>
            </div>

            {/* Win Rate */}
            <div className="text-center min-w-0">
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground truncate`}>{t('paperTradingAnalytics.metrics.winRate')}</p>
              <p className={`${isMobile ? 'text-sm' : 'text-2xl'} font-bold truncate`}>
                {(account?.win_rate || 0).toFixed(1)}%
              </p>
            </div>

            {/* Total Trades */}
            <div className="text-center min-w-0">
              <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground truncate`}>{t('paperTradingAnalytics.metrics.totalTrades')}</p>
              <p className={`${isMobile ? 'text-sm' : 'text-2xl'} font-bold truncate`}>
                {account?.total_trades_count || 0}
              </p>
            </div>
          </div>
          
          {/* Return Percentage */}
          {account && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center justify-center space-x-2">
                <span className="text-sm text-muted-foreground">{t('paperTradingDashboard.performance.totalReturn')}:</span>
                <span className={`text-lg font-semibold ${getPerformanceColor(((balance || 0) - (account.initial_balance || 0)))}`}>
                  {formatPercentage(((balance || 0) - (account.initial_balance || 0)) / (account.initial_balance || 1) * 100)}
                </span>
                {((balance || 0) - (account.initial_balance || 0)) > 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-600" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-600" />
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Paper Trades */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardHeader className={`${isMobile ? 'p-3' : 'p-6'}`}>
          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'items-center justify-between'}`}>
            <CardTitle className={`${isMobile ? 'text-base' : 'text-xl'} font-semibold flex items-center min-w-0`}>
              <BarChart3 className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2 flex-shrink-0`} />
              <span className="truncate">{t('trading.recentPaperTrades')}</span>
            </CardTitle>
            <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded flex-shrink-0">
              {t('trading.paperTradingLabel')}
            </span>
          </div>
        </CardHeader>
        <CardContent className={`${isMobile ? 'p-3' : 'p-6'} w-full min-w-0 max-w-full overflow-hidden`}>
          {recentTrades.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">{t('trading.noPaperTrades')}</p>
              <p className="text-sm text-muted-foreground mt-1">
                {t('trading.paperTradingAutoGenerate')}
              </p>
            </div>
          ) : (
            <div className="space-y-3 w-full">
              {recentTrades.slice(0, 5).map((trade) => (
                <div
                  key={trade.id}
                  className={`flex items-center justify-between ${isMobile ? 'p-2' : 'p-3'} bg-gray-50 dark:bg-gray-800 rounded-lg w-full min-w-0 max-w-full overflow-hidden`}
                >
                  <div className="flex items-center space-x-3 min-w-0 flex-1">
                    <div className={`w-2 h-2 rounded-full flex-shrink-0 ${trade.pnl > 0 ? 'bg-green-500' : trade.pnl < 0 ? 'bg-red-500' : 'bg-gray-400'}`}></div>
                    <div className="min-w-0 flex-1">
                      <p className={`${isMobile ? 'text-xs' : 'text-base'} font-medium truncate`}>
                        {trade.symbol} {trade.side.toUpperCase()}
                      </p>
                      <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground truncate`}>
                        {new Date(trade.entry_time).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right flex-shrink-0">
                    <p className={`${isMobile ? 'text-xs' : 'text-base'} font-medium ${getPerformanceColor(trade.pnl || 0)}`}>
                      {trade.pnl ? formatCurrency(trade.pnl) : '--'}
                    </p>
                    <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
                      {trade.status}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Analytics Summary */}
      {analytics && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold`}>
                {t('paperTradingDashboard.analytics.thirtyDay')}
              </CardTitle>
              <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded">
                {t('trading.paperTradingLabel')}
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className={`grid ${isMobile ? 'grid-cols-2 gap-3' : 'grid-cols-3 gap-4'}`}>
              <div className="text-center">
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>Avg Win</p>
                <p className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold text-green-600`}>
                  {formatCurrency(analytics.summary.average_win)}
                </p>
              </div>
              <div className="text-center">
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>Avg Loss</p>
                <p className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold text-red-600`}>
                  {formatCurrency(analytics.summary.average_loss)}
                </p>
              </div>
              <div className="text-center">
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>Profit Factor</p>
                <p className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold`}>
                  {analytics.summary.profit_factor.toFixed(2)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Comprehensive Analytics */}
      <PaperTradingAnalytics />

      {/* Reset Modal */}
      <PaperTradingReset
        isOpen={showResetModal}
        onClose={handleResetModalClose}
      />
    </div>
  );
};

export default PaperTradingDashboard;
