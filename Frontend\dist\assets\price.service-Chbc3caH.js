class i{eventSource=null;subscribers=new Map;currentPrices=new Map;isConnected=!1;reconnectAttempts=0;maxReconnectAttempts=5;reconnectDelay=1e3;constructor(){}connect(){if(!this.isConnected)try{const e=localStorage.getItem("access_token");if(!e){console.error("No auth token found for price streaming");return}this.disconnect(),this.eventSource=new EventSource(`/api/realtime/price-stream?token=${e}`),this.eventSource.onopen=()=>{console.log("Price streaming connection established"),this.isConnected=!0,this.reconnectAttempts=0,this.reconnectDelay=1e3},this.eventSource.onmessage=r=>{try{const t=JSON.parse(r.data);this.handlePriceUpdate(t)}catch(t){console.error("Error parsing price update:",t)}},this.eventSource.onerror=r=>{if(console.error("Price streaming error:",r),this.isConnected=!1,this.reconnectAttempts<this.maxReconnectAttempts){this.reconnectAttempts++;const t=this.reconnectDelay*Math.pow(2,this.reconnectAttempts-1);console.log(`Attempting to reconnect in ${t}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{this.connect()},t)}else console.error("Max reconnection attempts reached. Price streaming disabled.")}}catch(e){console.error("Error establishing price streaming connection:",e)}}disconnect(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.isConnected=!1}handlePriceUpdate(e){switch(e.type){case"connected":console.log("Price streaming connected"),e.current_prices&&Object.entries(e.current_prices).forEach(([r,t])=>{this.currentPrices.set(r,t)});break;case"price_update":if(e.symbol&&e.data){const r=this.currentPrices.get(e.symbol)?.price;this.currentPrices.set(e.symbol,e.data),this.notifySubscribers(e.symbol,e.data,r)}break;case"heartbeat":break;case"error":console.error("Price streaming error:",e.message);break;default:console.warn("Unknown price update type:",e.type)}}notifySubscribers(e,r,t){(this.subscribers.get(e)||[]).forEach(s=>{try{s(e,r,t)}catch(c){console.error("Error in price update callback:",c)}})}subscribe(e,r){const t=e.toUpperCase();this.subscribers.has(t)||this.subscribers.set(t,[]),this.subscribers.get(t).push(r),this.isConnected||this.connect();const n=this.currentPrices.get(t);return n&&setTimeout(()=>r(t,n),0),()=>{const s=this.subscribers.get(t);if(s){const c=s.indexOf(r);c>-1&&s.splice(c,1),s.length===0&&this.subscribers.delete(t)}this.subscribers.size===0&&this.disconnect()}}getCurrentPrice(e){return this.currentPrices.get(e.toUpperCase())||null}getAllPrices(){const e={};return this.currentPrices.forEach((r,t)=>{e[t]=r}),e}getConnectionStatus(){return this.isConnected}reconnect(){this.disconnect(),this.reconnectAttempts=0,this.connect()}}const a=new i;export{i as PriceService,a as priceService};
//# sourceMappingURL=price.service-Chbc3caH.js.map
