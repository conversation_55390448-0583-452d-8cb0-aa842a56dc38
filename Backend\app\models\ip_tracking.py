"""
IP Tracking and Management Models for DeepTrade
Handles IP access logging, blacklisting, and rate limiting functionality.
"""

from datetime import datetime, timed<PERSON><PERSON>
from app import db
from sqlalchemy import Index


class IPAccessLog(db.Model):
    """Model for tracking IP access logs and login sessions."""
    
    __tablename__ = 'ip_access_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(36), nullable=True, index=True)  # Regular user ID
    admin_id = db.Column(db.Integer, db.<PERSON>Key('admin_user.id'), nullable=True, index=True)
    ip_address = db.Column(db.String(45), nullable=False, index=True)
    user_agent = db.Column(db.Text, nullable=True)
    login_timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    logout_timestamp = db.Column(db.DateTime, nullable=True)
    session_duration = db.Column(db.Integer, nullable=True)  # Duration in seconds
    login_successful = db.Column(db.<PERSON>, nullable=False, default=True, index=True)
    failure_reason = db.Column(db.String(255), nullable=True)
    geographic_location = db.Column(db.String(255), nullable=True)
    country_code = db.Column(db.String(2), nullable=True)
    city = db.Column(db.String(100), nullable=True)
    isp = db.Column(db.String(255), nullable=True)
    is_proxy = db.Column(db.Boolean, default=False)
    is_vpn = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    admin = db.relationship('AdminUser', backref='ip_access_logs')
    
    def __init__(self, **kwargs):
        super(IPAccessLog, self).__init__(**kwargs)
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'admin_id': self.admin_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'login_timestamp': self.login_timestamp.isoformat() if self.login_timestamp else None,
            'logout_timestamp': self.logout_timestamp.isoformat() if self.logout_timestamp else None,
            'session_duration': self.session_duration,
            'login_successful': self.login_successful,
            'failure_reason': self.failure_reason,
            'geographic_location': self.geographic_location,
            'country_code': self.country_code,
            'city': self.city,
            'isp': self.isp,
            'is_proxy': self.is_proxy,
            'is_vpn': self.is_vpn,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def log_access(cls, user_id=None, admin_id=None, ip_address=None, user_agent=None, 
                   login_successful=True, failure_reason=None, **kwargs):
        """Create a new IP access log entry."""
        log_entry = cls(
            user_id=user_id,
            admin_id=admin_id,
            ip_address=ip_address,
            user_agent=user_agent,
            login_successful=login_successful,
            failure_reason=failure_reason,
            **kwargs
        )
        db.session.add(log_entry)
        db.session.commit()
        return log_entry
    
    @classmethod
    def get_user_ip_history(cls, user_id, limit=50):
        """Get IP access history for a specific user."""
        return cls.query.filter_by(user_id=user_id).order_by(cls.login_timestamp.desc()).limit(limit).all()
    
    @classmethod
    def get_admin_ip_history(cls, admin_id, limit=50):
        """Get IP access history for a specific admin."""
        return cls.query.filter_by(admin_id=admin_id).order_by(cls.login_timestamp.desc()).limit(limit).all()
    
    @classmethod
    def get_ip_activity(cls, ip_address, limit=50):
        """Get all activity for a specific IP address."""
        return cls.query.filter_by(ip_address=ip_address).order_by(cls.login_timestamp.desc()).limit(limit).all()


class IPBlacklist(db.Model):
    """Model for managing banned IP addresses."""
    
    __tablename__ = 'ip_blacklist'
    
    id = db.Column(db.Integer, primary_key=True)
    ip_address = db.Column(db.String(45), nullable=False, unique=True, index=True)
    ban_reason = db.Column(db.Text, nullable=False)
    banned_by_admin_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=False)
    banned_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    expires_at = db.Column(db.DateTime, nullable=True, index=True)
    is_active = db.Column(db.Boolean, nullable=False, default=True, index=True)
    ban_type = db.Column(db.Enum('manual', 'automatic', 'rate_limit'), default='manual')
    attempt_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    banned_by = db.relationship('AdminUser', backref='banned_ips')
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'ip_address': self.ip_address,
            'ban_reason': self.ban_reason,
            'banned_by_admin_id': self.banned_by_admin_id,
            'banned_by_username': self.banned_by.username if self.banned_by else None,
            'banned_at': self.banned_at.isoformat() if self.banned_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active,
            'ban_type': self.ban_type,
            'attempt_count': self.attempt_count,
            'is_expired': self.is_expired()
        }
    
    def is_expired(self):
        """Check if the ban has expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def is_currently_banned(self):
        """Check if the IP is currently banned (active and not expired)."""
        return self.is_active and not self.is_expired()
    
    @classmethod
    def is_ip_banned(cls, ip_address):
        """Check if an IP address is currently banned."""
        ban = cls.query.filter_by(ip_address=ip_address, is_active=True).first()
        if not ban:
            return False
        return ban.is_currently_banned()
    
    @classmethod
    def ban_ip(cls, ip_address, reason, banned_by_admin_id, expires_at=None, ban_type='manual'):
        """Ban an IP address."""
        # Check if already banned
        existing_ban = cls.query.filter_by(ip_address=ip_address).first()
        if existing_ban:
            # Update existing ban
            existing_ban.ban_reason = reason
            existing_ban.banned_by_admin_id = banned_by_admin_id
            existing_ban.banned_at = datetime.utcnow()
            existing_ban.expires_at = expires_at
            existing_ban.is_active = True
            existing_ban.ban_type = ban_type
        else:
            # Create new ban
            ban = cls(
                ip_address=ip_address,
                ban_reason=reason,
                banned_by_admin_id=banned_by_admin_id,
                expires_at=expires_at,
                ban_type=ban_type
            )
            db.session.add(ban)
        
        db.session.commit()
        return existing_ban if existing_ban else ban
    
    @classmethod
    def unban_ip(cls, ip_address):
        """Unban an IP address."""
        ban = cls.query.filter_by(ip_address=ip_address).first()
        if ban:
            ban.is_active = False
            db.session.commit()
        return ban


class IPRateLimit(db.Model):
    """Model for tracking IP rate limiting."""
    
    __tablename__ = 'ip_rate_limits'
    
    id = db.Column(db.Integer, primary_key=True)
    ip_address = db.Column(db.String(45), nullable=False, unique=True, index=True)
    attempt_count = db.Column(db.Integer, nullable=False, default=1)
    first_attempt_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    last_attempt_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    blocked_until = db.Column(db.DateTime, nullable=True, index=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'ip_address': self.ip_address,
            'attempt_count': self.attempt_count,
            'first_attempt_at': self.first_attempt_at.isoformat() if self.first_attempt_at else None,
            'last_attempt_at': self.last_attempt_at.isoformat() if self.last_attempt_at else None,
            'blocked_until': self.blocked_until.isoformat() if self.blocked_until else None,
            'is_currently_blocked': self.is_currently_blocked()
        }
    
    def is_currently_blocked(self):
        """Check if the IP is currently rate limited."""
        if not self.blocked_until:
            return False
        return datetime.utcnow() < self.blocked_until
    
    @classmethod
    def record_attempt(cls, ip_address, max_attempts=None, block_duration_minutes=None):
        """Record a login attempt and apply rate limiting if needed."""
        from flask import current_app

        # Use configuration values if not provided
        if max_attempts is None:
            max_attempts = current_app.config.get('RATE_LIMIT_MAX_ATTEMPTS', 10)
        if block_duration_minutes is None:
            block_duration_minutes = current_app.config.get('RATE_LIMIT_BLOCK_DURATION_MINUTES', 10)

        reset_hours = current_app.config.get('RATE_LIMIT_RESET_HOURS', 2)

        rate_limit = cls.query.filter_by(ip_address=ip_address).first()

        if not rate_limit:
            # First attempt
            rate_limit = cls(ip_address=ip_address)
            db.session.add(rate_limit)
        else:
            # Check if we should reset the counter
            if datetime.utcnow() - rate_limit.first_attempt_at > timedelta(hours=reset_hours):
                rate_limit.attempt_count = 1
                rate_limit.first_attempt_at = datetime.utcnow()
                rate_limit.blocked_until = None
            else:
                rate_limit.attempt_count += 1

            rate_limit.last_attempt_at = datetime.utcnow()

            # Apply rate limiting if exceeded
            if rate_limit.attempt_count >= max_attempts:
                rate_limit.blocked_until = datetime.utcnow() + timedelta(minutes=block_duration_minutes)

        db.session.commit()
        return rate_limit
    
    @classmethod
    def is_ip_rate_limited(cls, ip_address):
        """Check if an IP is currently rate limited."""
        rate_limit = cls.query.filter_by(ip_address=ip_address).first()
        if not rate_limit:
            return False
        return rate_limit.is_currently_blocked()
    
    @classmethod
    def reset_rate_limit(cls, ip_address):
        """Reset rate limiting for an IP address."""
        rate_limit = cls.query.filter_by(ip_address=ip_address).first()
        if rate_limit:
            rate_limit.attempt_count = 0
            rate_limit.blocked_until = None
            db.session.commit()
        return rate_limit
