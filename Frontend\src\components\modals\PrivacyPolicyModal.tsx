/**
 * Privacy Policy Modal Component for DeepTrade
 *
 * Displays the Privacy Policy in a modal dialog with proper scrolling,
 * accessibility features, and responsive design.
 * Always displays in English regardless of language selector setting.
 */

import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, Shield } from 'lucide-react';
import { Button } from '../ui/Button';
import { cn } from '@/lib/utils';

interface PrivacyPolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const PrivacyPolicyModal: React.FC<PrivacyPolicyModalProps> = ({
  isOpen,
  onClose,
  className,
}) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div
        className={cn(
          "relative bg-white dark:bg-gray-800 rounded-lg shadow-2xl",
          "w-full max-w-4xl max-h-[90vh] flex flex-col",
          "border border-gray-200 dark:border-gray-700",
          className
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby="privacy-modal-title"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Shield className="w-6 h-6 text-primary" />
            <h2 id="privacy-modal-title" className="text-xl font-semibold text-gray-900 dark:text-white">
              Privacy Policy
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="rounded-full"
            aria-label="Close Privacy Policy"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="prose prose-gray dark:prose-invert max-w-none">
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              Last updated: January 15, 2025
            </div>

            <p>This Privacy Policy explains how DeepTrade collects, uses, and protects your personal data.</p>

            <h3>1. Information We Collect</h3>
            <p>We collect the following:</p>
            <ul>
              <li><strong>Account Data:</strong> Email address, name, authentication (OAuth or manual).</li>
              <li><strong>Trading Connection Data:</strong> API credentials (read/write), exchange ID, strategies used.</li>
              <li><strong>Usage Data:</strong> IP address, device/browser info, access times, feature interactions.</li>
            </ul>

            <h3>2. Use of Information</h3>
            <p>We use your information to:</p>
            <ul>
              <li>Operate and maintain the platform.</li>
              <li>Communicate updates or service alerts.</li>
              <li>Provide customer support.</li>
              <li>Monitor and improve performance and security.</li>
            </ul>

            <h3>3. Data Security</h3>
            <p>
              We implement encryption, access control, and security protocols to protect your data. However, no system is 100% secure, and by using DeepTrade you acknowledge this risk.
            </p>

            <h3>4. Sharing of Data</h3>
            <p>We do <strong>not sell or rent</strong> your data. We only share data with:</p>
            <ul>
              <li>Service providers (e.g., hosting, email delivery).</li>
              <li>Legal authorities, if compelled under applicable laws.</li>
              <li>Successors in the case of merger or acquisition.</li>
            </ul>

            <h3>5. Your Rights</h3>
            <p>Depending on your jurisdiction, you may:</p>
            <ul>
              <li>Request access to your data.</li>
              <li>Request deletion or correction.</li>
              <li>Object to certain uses of your data.</li>
              <li>Withdraw consent at any time.</li>
            </ul>

            <h3>6. Cookies</h3>
            <p>
              We use cookies and analytics tools to enhance user experience. You may disable cookies via browser settings, though some features may stop working.
            </p>

            <h3>7. Data Retention</h3>
            <p>
              We retain your data only as long as necessary to provide services, comply with legal obligations, and enforce our policies.
            </p>

            <h3>8. Children</h3>
            <p>
              DeepTrade is not intended for users under 18 years old. We do not knowingly collect data from minors.
            </p>

            <h3>9. International Transfers</h3>
            <p>
              Your data may be processed outside of your home country. By using the Platform, you consent to such transfer.
            </p>

            <h3>10. Changes</h3>
            <p>
              We may modify this policy. Updates will be posted on this page.
            </p>

            <h3>11. Contact</h3>
            <p>
              For privacy questions: <strong><EMAIL></strong>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <Button onClick={onClose} className="min-w-[100px]">
            Close
          </Button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default PrivacyPolicyModal;
