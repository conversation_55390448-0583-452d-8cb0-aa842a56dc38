"""
Paper Trading Service

This service manages paper trading operations including account management,
trade execution, performance tracking, and analytics.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional
from flask import current_app
from sqlalchemy import desc

from app import db
from app.models.paper_trading import (
    PaperTradingAccount, PaperTrade, PaperTradingSession, 
    PaperBalanceSnapshot, PaperTradeStatus, PaperTradingStatus
)
from app.models.user import User
from app.services.paper_exchange_service import PaperExchangeService


class PaperTradingService:
    """Service for managing paper trading operations."""
    
    @staticmethod
    def get_or_create_paper_account(user_id: str) -> PaperTradingAccount:
        """Get existing paper trading account or create new one."""
        account = PaperTradingAccount.query.filter_by(user_id=user_id).first()
        
        if not account:
            initial_balance = current_app.config.get('PAPER_TRADING_INITIAL_BALANCE', 10000)
            account = PaperTradingAccount(
                user_id=user_id,
                initial_balance=initial_balance
            )
            db.session.add(account)
            db.session.commit()
            current_app.logger.info(f"Created paper trading account for user {user_id}")
        
        return account
    
    @staticmethod
    def switch_trading_mode(user_id: str, paper_mode: bool) -> Dict:
        """Switch user between paper and live trading modes."""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'error': 'User not found', 'success': False}
            
            # Update user's trading mode
            user.paper_trading_mode = paper_mode
            
            # If switching to paper mode, ensure paper account exists
            if paper_mode:
                PaperTradingService.get_or_create_paper_account(user_id)
            
            db.session.commit()
            
            mode_name = "Paper Trading" if paper_mode else "Live Trading"
            current_app.logger.info(f"User {user_id} switched to {mode_name} mode")
            
            return {
                'success': True,
                'mode': 'paper' if paper_mode else 'live',
                'message': f'Switched to {mode_name} mode'
            }
            
        except Exception as e:
            current_app.logger.error(f"Error switching trading mode for user {user_id}: {e}")
            db.session.rollback()
            return {'error': str(e), 'success': False}
    
    @staticmethod
    def get_paper_account_summary(user_id: str) -> Dict:
        """Get paper trading account summary."""
        try:
            account = PaperTradingService.get_or_create_paper_account(user_id)
            
            # Get recent performance data
            recent_trades = PaperTrade.query.filter_by(
                user_id=user_id,
                status=PaperTradeStatus.CLOSED,
                archived=False
            ).order_by(desc(PaperTrade.exit_time)).limit(10).all()
            
            # Calculate additional metrics
            total_return_pct = 0
            if account.initial_balance > 0:
                total_return_pct = float((account.virtual_balance - account.initial_balance) / account.initial_balance * 100)
            
            return {
                'success': True,
                'account': account.to_dict(),
                'performance': {
                    'total_return_percentage': round(total_return_pct, 2),
                    'recent_trades_count': len(recent_trades),
                    'account_age_days': (datetime.utcnow() - account.created_at).days
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting paper account summary for user {user_id}: {e}")
            return {'error': str(e), 'success': False}
    
    @staticmethod
    def get_paper_trading_history(user_id: str, limit: int = 50, offset: int = 0) -> Dict:
        """Get paper trading history with pagination."""
        try:
            account = PaperTradingAccount.query.filter_by(user_id=user_id).first()
            if not account:
                return {'error': 'Paper trading account not found', 'success': False}
            
            # Get trades with pagination
            trades_query = PaperTrade.query.filter_by(
                user_id=user_id,
                archived=False
            ).order_by(PaperTrade.entry_time.desc())
            
            total_count = trades_query.count()
            trades = trades_query.offset(offset).limit(limit).all()
            
            return {
                'success': True,
                'trades': [trade.to_dict() for trade in trades],
                'pagination': {
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total_count
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting paper trading history for user {user_id}: {e}")
            return {'error': str(e), 'success': False}
    
    @staticmethod
    def reset_paper_account(user_id: str, new_balance: Optional[float] = None) -> Dict:
        """Reset paper trading account to initial state."""
        try:
            account = PaperTradingAccount.query.filter_by(user_id=user_id).first()
            if not account:
                return {'error': 'Paper trading account not found', 'success': False}
            
            # Check if user can reset today
            max_resets = current_app.config.get('PAPER_TRADING_MAX_RESET_PER_DAY', 3)
            if not account.can_reset_today(max_resets):
                return {
                    'error': f'Maximum {max_resets} resets per day allowed',
                    'success': False
                }
            
            # Reset account
            reset_balance = new_balance or current_app.config.get('PAPER_TRADING_INITIAL_BALANCE', 10000)
            account.reset_account(reset_balance)
            
            # Create reset snapshot
            snapshot = PaperBalanceSnapshot(
                paper_account_id=account.id,
                balance=account.virtual_balance,
                pnl_change=0,
                transaction_type='reset'
            )
            db.session.add(snapshot)
            db.session.commit()
            
            current_app.logger.info(f"Paper trading account reset for user {user_id} to balance {reset_balance}")
            
            return {
                'success': True,
                'message': 'Paper trading account reset successfully',
                'new_balance': float(account.virtual_balance),
                'reset_count': account.reset_count
            }
            
        except Exception as e:
            current_app.logger.error(f"Error resetting paper account for user {user_id}: {e}")
            db.session.rollback()
            return {'error': str(e), 'success': False}
    
    @staticmethod
    def get_paper_trading_analytics(user_id: str, days: int = 30) -> Dict:
        """Get paper trading analytics for specified period."""
        try:
            account = PaperTradingAccount.query.filter_by(user_id=user_id).first()
            if not account:
                return {'error': 'Paper trading account not found', 'success': False}
            
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Get trades in period
            trades = PaperTrade.query.filter(
                PaperTrade.user_id == user_id,
                PaperTrade.entry_time >= start_date,
                PaperTrade.entry_time <= end_date,
                PaperTrade.archived == False
            ).all()
            
            # Calculate analytics
            total_trades = len(trades)
            closed_trades = [t for t in trades if t.status == PaperTradeStatus.CLOSED]
            winning_trades = [t for t in closed_trades if t.pnl and t.pnl > 0]
            losing_trades = [t for t in closed_trades if t.pnl and t.pnl <= 0]
            
            total_pnl = sum([t.pnl for t in closed_trades if t.pnl], Decimal('0'))
            win_rate = (len(winning_trades) / len(closed_trades) * 100) if closed_trades else 0
            
            # Average trade metrics
            avg_win = sum([t.pnl for t in winning_trades if t.pnl], Decimal('0')) / len(winning_trades) if winning_trades else Decimal('0')
            avg_loss = sum([t.pnl for t in losing_trades if t.pnl], Decimal('0')) / len(losing_trades) if losing_trades else Decimal('0')
            
            # Get balance snapshots for chart data
            snapshots = PaperBalanceSnapshot.query.filter(
                PaperBalanceSnapshot.paper_account_id == account.id,
                PaperBalanceSnapshot.created_at >= start_date,
                PaperBalanceSnapshot.created_at <= end_date
            ).order_by(PaperBalanceSnapshot.created_at).all()
            
            balance_history = [
                {
                    'date': snapshot.created_at.isoformat(),
                    'balance': float(snapshot.balance),
                    'pnl_change': float(snapshot.pnl_change)
                }
                for snapshot in snapshots
            ]
            
            return {
                'success': True,
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                },
                'summary': {
                    'total_trades': total_trades,
                    'closed_trades': len(closed_trades),
                    'winning_trades': len(winning_trades),
                    'losing_trades': len(losing_trades),
                    'win_rate': round(win_rate, 2),
                    'total_pnl': float(total_pnl),
                    'average_win': float(avg_win),
                    'average_loss': float(avg_loss),
                    'profit_factor': float(avg_win / abs(avg_loss)) if avg_loss != 0 else 0
                },
                'balance_history': balance_history,
                'current_balance': float(account.virtual_balance)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting paper trading analytics for user {user_id}: {e}")
            return {'error': str(e), 'success': False}
    
    @staticmethod
    def create_paper_trading_session(user_id: str, symbol: str, leverage: int = 1, investment_percentage: int = 0) -> Dict:
        """Create a new paper trading session."""
        try:
            account = PaperTradingService.get_or_create_paper_account(user_id)
            
            session = PaperTradingSession(
                user_id=user_id,
                paper_account_id=account.id,
                symbol=symbol,
                initial_balance=account.virtual_balance,
                leverage=leverage,
                investment_percentage=investment_percentage
            )
            
            db.session.add(session)
            db.session.commit()
            
            current_app.logger.info(f"Created paper trading session for user {user_id}: {symbol}")
            
            return {
                'success': True,
                'session': session.to_dict()
            }
            
        except Exception as e:
            current_app.logger.error(f"Error creating paper trading session for user {user_id}: {e}")
            db.session.rollback()
            return {'error': str(e), 'success': False}
    
    @staticmethod
    def get_paper_exchange_service(user_id: str) -> PaperExchangeService:
        """Get paper exchange service instance for user."""
        return PaperExchangeService(user_id)
