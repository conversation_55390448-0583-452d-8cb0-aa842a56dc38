import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { Button } from '@/components/ui/Button';
import { toastSuccess } from '@/components/ui/use-toast';
import { TermsOfServiceModal } from '@/components/modals/TermsOfServiceModal';
import { PrivacyPolicyModal } from '@/components/modals/PrivacyPolicyModal';
import { API_BASE_URL } from '@/config';

export default function VerifyEmail() {
  const [searchParams] = useSearchParams();
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading');
  const [message, setMessage] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const navigate = useNavigate();

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setVerificationStatus('error');
      setMessage('Invalid verification link');
      setIsVerifying(false);
      return;
    }

    verifyEmail(token);
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/verify-email`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          token: verificationToken
        })
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.message?.includes('expired')) {
          setVerificationStatus('expired');
        } else {
          setVerificationStatus('error');
        }
        setMessage(data.message || 'Verification failed');
      } else {
        setVerificationStatus('success');
        setMessage('Email verified successfully!');
        toastSuccess({
          title: 'Email Verified!',
          description: 'Your account has been verified. You can now log in.',
        });
      }
    } catch (error: any) {
      console.error('Email verification failed:', error);
      setVerificationStatus('error');
      setMessage('Verification failed. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendVerification = async () => {
    // We need the email address for resending, but we don't have it from the token
    // For now, redirect to registration page where they can resend
    setIsResending(true);
    navigate('/register');
  };

  const getStatusIcon = () => {
    switch (verificationStatus) {
      case 'loading':
        return (
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        );
      case 'success':
        return (
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
          </div>
        );
      case 'expired':
        return (
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
            <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
            </svg>
          </div>
        );
      case 'error':
      default:
        return (
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
    }
  };

  const getStatusColor = () => {
    switch (verificationStatus) {
      case 'success':
        return 'text-green-600';
      case 'expired':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">Email Verification</h1>
        <p className="text-sm text-muted-foreground">
          {isVerifying ? 'Verifying your email address...' : 'Verification complete'}
        </p>
      </div>
      
      <div className="grid gap-6">
        <div className="flex flex-col items-center space-y-4">
          {getStatusIcon()}
          
          <div className="text-center">
            <h3 className={`text-lg font-medium ${getStatusColor()}`}>
              {verificationStatus === 'loading' ? 'Verifying...' : 
               verificationStatus === 'success' ? 'Verification Successful!' :
               verificationStatus === 'expired' ? 'Link Expired' :
               'Verification Failed'}
            </h3>
            <p className="text-sm text-muted-foreground mt-2">
              {message}
            </p>
          </div>

          <div className="w-full space-y-3">
            {verificationStatus === 'success' && (
              <Button
                onClick={() => navigate('/login')}
                className="w-full"
              >
                Continue to Login
              </Button>
            )}

            {verificationStatus === 'expired' && (
              <Button
                onClick={handleResendVerification}
                disabled={isResending}
                className="w-full"
              >
                {isResending ? 'Redirecting...' : 'Get New Verification Link'}
              </Button>
            )}

            {verificationStatus === 'error' && (
              <div className="space-y-2">
                <Button
                  onClick={handleResendVerification}
                  disabled={isResending}
                  className="w-full"
                >
                  {isResending ? 'Redirecting...' : 'Get New Verification Link'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/login')}
                  className="w-full"
                >
                  Back to Login
                </Button>
              </div>
            )}

            {verificationStatus !== 'loading' && (
              <div className="text-center space-y-2">
                <Link
                  to="/register"
                  className="text-sm text-muted-foreground hover:text-primary underline underline-offset-4"
                >
                  Need to create a new account?
                </Link>
                <p className="text-xs text-muted-foreground">
                  By using our service, you agree to our{' '}
                  <button
                    type="button"
                    onClick={() => setShowTermsModal(true)}
                    className="text-primary hover:underline"
                  >
                    Terms of Service
                  </button>{' '}
                  and{' '}
                  <button
                    type="button"
                    onClick={() => setShowPrivacyModal(true)}
                    className="text-primary hover:underline"
                  >
                    Privacy Policy
                  </button>
                  .
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Terms of Service Modal */}
      <TermsOfServiceModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
}
