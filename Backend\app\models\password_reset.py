import uuid
from datetime import datetime, timedelta
from sqlalchemy import <PERSON>umn, String, DateTime, Boolean, Text, Integer, ForeignKey
from sqlalchemy.orm import relationship
from app import db


class PasswordResetToken(db.Model):
    """Model for password reset tokens with security measures."""
    __tablename__ = 'password_reset_tokens'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    token = db.Column(db.String(255), unique=True, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    is_used = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    used_at = db.Column(db.DateTime, nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)  # IPv6 support
    user_agent = db.Column(db.Text, nullable=True)
    
    # Relationship
    user = relationship('User', backref='password_reset_tokens')
    
    def __init__(self, user_id, token, ip_address=None, user_agent=None):
        self.user_id = user_id
        self.token = token
        self.expires_at = datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
        self.ip_address = ip_address
        self.user_agent = user_agent
    
    def is_valid(self):
        """Check if token is valid (not expired and not used)."""
        return not self.is_used and datetime.utcnow() < self.expires_at
    
    def mark_as_used(self):
        """Mark token as used."""
        self.is_used = True
        self.used_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_used': self.is_used,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'used_at': self.used_at.isoformat() if self.used_at else None,
            'ip_address': self.ip_address
        }


class TwoFAResetRequest(db.Model):
    """Model for 2FA reset requests with admin approval workflow."""
    __tablename__ = 'twofa_reset_requests'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    
    # Request details
    reason = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    
    # Security verification fields
    full_name_provided = db.Column(db.String(255), nullable=False)
    email_provided = db.Column(db.String(255), nullable=False)
    last_login_date_provided = db.Column(db.String(100), nullable=True)
    account_creation_date_provided = db.Column(db.String(100), nullable=True)
    recent_activity_description = db.Column(db.Text, nullable=True)
    
    # Additional security questions
    security_question_1 = db.Column(db.Text, nullable=True)  # "When was your last successful trade happened?"
    security_answer_1 = db.Column(db.Text, nullable=True)
    security_question_2 = db.Column(db.Text, nullable=True)  # "What tier are you currently on?"
    security_answer_2 = db.Column(db.Text, nullable=True)
    security_question_3 = db.Column(db.Text, nullable=True)  # "What exchange do you primarily use?"
    security_answer_3 = db.Column(db.Text, nullable=True)
    
    # Admin workflow
    status = db.Column(db.String(20), default='pending', nullable=False)  # pending, approved, rejected, completed
    admin_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=True)
    admin_notes = db.Column(db.Text, nullable=True)
    reviewed_at = db.Column(db.DateTime, nullable=True)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    # Security flags
    risk_level = db.Column(db.String(10), default='medium', nullable=False)  # low, medium, high
    requires_additional_verification = db.Column(db.Boolean, default=False, nullable=False)
    
    # Relationships
    user = relationship('User', backref='twofa_reset_requests')
    admin = relationship('AdminUser', backref='twofa_reset_requests', foreign_keys=[admin_id])
    
    def __init__(self, user_id, reason, full_name_provided, email_provided, 
                 ip_address=None, user_agent=None):
        self.user_id = user_id
        self.reason = reason
        self.full_name_provided = full_name_provided
        self.email_provided = email_provided
        self.ip_address = ip_address
        self.user_agent = user_agent
    
    def approve(self, admin_id, admin_notes=None):
        """Approve the 2FA reset request."""
        self.status = 'approved'
        self.admin_id = admin_id
        self.admin_notes = admin_notes
        self.reviewed_at = datetime.utcnow()
    
    def reject(self, admin_id, admin_notes=None):
        """Reject the 2FA reset request."""
        self.status = 'rejected'
        self.admin_id = admin_id
        self.admin_notes = admin_notes
        self.reviewed_at = datetime.utcnow()
    
    def complete(self):
        """Mark the request as completed (2FA has been reset)."""
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
    
    def calculate_risk_level(self, user):
        """Calculate risk level based on provided information and user data."""
        risk_score = 0
        
        # Check name match
        if self.full_name_provided.lower().strip() != user.full_name.lower().strip():
            risk_score += 3
        
        # Check email match
        if self.email_provided.lower().strip() != user.email.lower().strip():
            risk_score += 3
        
        # Check if user has recent login activity
        from app.models.security_log import LoginAttempt
        recent_logins = LoginAttempt.query.filter(
            LoginAttempt.user_id == user.id,
            LoginAttempt.attempted_at >= datetime.utcnow() - timedelta(days=30),
            LoginAttempt.success == True
        ).count()
        
        if recent_logins == 0:
            risk_score += 2
        
        # Determine risk level
        if risk_score >= 5:
            self.risk_level = 'high'
            self.requires_additional_verification = True
        elif risk_score >= 2:
            self.risk_level = 'medium'
        else:
            self.risk_level = 'low'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'reason': self.reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'ip_address': self.ip_address,
            'full_name_provided': self.full_name_provided,
            'email_provided': self.email_provided,
            'last_login_date_provided': self.last_login_date_provided,
            'account_creation_date_provided': self.account_creation_date_provided,
            'recent_activity_description': self.recent_activity_description,
            'security_question_1': self.security_question_1,
            'security_answer_1': self.security_answer_1,
            'security_question_2': self.security_question_2,
            'security_answer_2': self.security_answer_2,
            'security_question_3': self.security_question_3,
            'security_answer_3': self.security_answer_3,
            'status': self.status,
            'admin_id': self.admin_id,
            'admin_notes': self.admin_notes,
            'reviewed_at': self.reviewed_at.isoformat() if self.reviewed_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'risk_level': self.risk_level,
            'requires_additional_verification': self.requires_additional_verification,
            'user': {
                'email': self.user.email,
                'full_name': self.user.full_name,
                'created_at': self.user.created_at.isoformat() if self.user.created_at else None
            } if self.user else None
        }
