#!/usr/bin/env python3
"""
Migration: Add language_preference field to users table
Date: 2025-07-18
Description: Add language preference field for email notifications localization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app import create_app
from config import Config

def run_migration():
    """Add language_preference column to users table."""
    
    print("🔄 Starting language preference migration...")
    
    # Create Flask app to get database configuration
    app = create_app()
    
    with app.app_context():
        try:
            # Create database engine
            engine = create_engine(app.config['SQLALCHEMY_DATABASE_URI'])
            
            with engine.connect() as conn:
                # Start transaction
                trans = conn.begin()
                
                try:
                    print("📋 Checking if language_preference column exists...")
                    
                    # Check if language_preference column exists
                    result = conn.execute(text("""
                        SELECT COLUMN_NAME 
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_SCHEMA = DATABASE() 
                        AND TABLE_NAME = 'users' 
                        AND COLUMN_NAME = 'language_preference'
                    """))
                    
                    language_preference_exists = result.fetchone() is not None
                    
                    if not language_preference_exists:
                        print("➕ Adding language_preference column to users table...")
                        conn.execute(text("""
                            ALTER TABLE users 
                            ADD COLUMN language_preference VARCHAR(5) DEFAULT 'en' NOT NULL 
                            AFTER account_type
                        """))
                        print("✅ language_preference column added successfully")
                        
                        # Update existing users to have 'en' as default language
                        print("🔄 Setting default language preference for existing users...")
                        result = conn.execute(text("""
                            UPDATE users 
                            SET language_preference = 'en' 
                            WHERE language_preference IS NULL OR language_preference = ''
                        """))
                        updated_count = result.rowcount
                        print(f"✅ Updated {updated_count} existing users with default language preference")
                        
                    else:
                        print("✅ language_preference column already exists")
                    
                    # Commit transaction
                    trans.commit()
                    print("✅ Migration completed successfully!")
                    
                    # Show current table structure
                    print("\n📊 Current users table structure (language-related fields):")
                    result = conn.execute(text("""
                        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_SCHEMA = DATABASE() 
                        AND TABLE_NAME = 'users' 
                        AND COLUMN_NAME IN ('language_preference', 'email', 'full_name')
                        ORDER BY ORDINAL_POSITION
                    """))
                    
                    for row in result:
                        print(f"   {row[0]}: {row[1]} (nullable: {row[2]}, default: {row[3]})")
                    
                    return True
                    
                except Exception as e:
                    trans.rollback()
                    print(f"❌ Migration failed: {str(e)}")
                    return False
                    
        except Exception as e:
            print(f"❌ Database connection failed: {str(e)}")
            return False

def verify_migration():
    """Verify the migration was successful."""
    
    print("\n🔍 Verifying migration...")
    
    app = create_app()
    
    with app.app_context():
        try:
            engine = create_engine(app.config['SQLALCHEMY_DATABASE_URI'])
            
            with engine.connect() as conn:
                # Check if column exists and has correct properties
                result = conn.execute(text("""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'language_preference'
                """))
                
                column_info = result.fetchone()
                
                if column_info:
                    print(f"✅ Column exists: {column_info[0]}")
                    print(f"   Type: {column_info[1]}")
                    print(f"   Nullable: {column_info[2]}")
                    print(f"   Default: {column_info[3]}")
                    
                    # Check if any users have the new field
                    result = conn.execute(text("""
                        SELECT COUNT(*) as total_users,
                               COUNT(CASE WHEN language_preference = 'en' THEN 1 END) as en_users,
                               COUNT(CASE WHEN language_preference != 'en' THEN 1 END) as other_lang_users
                        FROM users
                    """))
                    
                    stats = result.fetchone()
                    print(f"\n📊 User language preferences:")
                    print(f"   Total users: {stats[0]}")
                    print(f"   English (en): {stats[1]}")
                    print(f"   Other languages: {stats[2]}")
                    
                    return True
                else:
                    print("❌ Column not found after migration")
                    return False
                    
        except Exception as e:
            print(f"❌ Verification failed: {str(e)}")
            return False

if __name__ == "__main__":
    print("🚀 DeepTrade Language Preference Migration")
    print("=" * 50)
    
    # Run migration
    migration_success = run_migration()
    
    if migration_success:
        # Verify migration
        verification_success = verify_migration()
        
        if verification_success:
            print("\n🎉 Migration completed and verified successfully!")
            print("\nNext steps:")
            print("1. Update EmailService to use user language preferences")
            print("2. Create email templates for supported languages")
            print("3. Update frontend to save language preference to backend")
            sys.exit(0)
        else:
            print("\n⚠️ Migration completed but verification failed")
            sys.exit(1)
    else:
        print("\n❌ Migration failed")
        sys.exit(1)
