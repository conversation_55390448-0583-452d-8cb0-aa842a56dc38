"""
Enhanced Password Validation Module

Provides comprehensive password validation with detailed feedback
for the DeepTrade application registration system.
"""

import re
from typing import Dict, List, Tuple


class PasswordValidator:
    """Enhanced password validation with detailed feedback"""
    
    # Password requirements configuration
    MIN_LENGTH = 8
    REQUIRED_PATTERNS = {
        'uppercase': r'[A-Z]',
        'lowercase': r'[a-z]',
        'number': r'\d',
        'special': r'[!@#$%^&*(),.?":{}|<>]'
    }
    
    @classmethod
    def validate_password(cls, password: str) -> Tuple[bool, Dict[str, bool], List[str]]:
        """
        Validate password against all requirements
        
        Args:
            password: The password to validate
            
        Returns:
            Tuple containing:
            - is_valid: Boolean indicating if password meets all requirements
            - requirements_met: Dict showing which requirements are satisfied
            - error_messages: List of specific error messages for failed requirements
        """
        requirements = {
            'length': len(password) >= cls.MIN_LENGTH,
            'uppercase': bool(re.search(cls.REQUIRED_PATTERNS['uppercase'], password)),
            'lowercase': bool(re.search(cls.REQUIRED_PATTERNS['lowercase'], password)),
            'number': bool(re.search(cls.REQUIRED_PATTERNS['number'], password)),
            'special': bool(re.search(cls.REQUIRED_PATTERNS['special'], password))
        }
        
        error_messages = []
        
        if not requirements['length']:
            error_messages.append(f'Password must be at least {cls.MIN_LENGTH} characters long')
        if not requirements['uppercase']:
            error_messages.append('Password must contain at least one uppercase letter (A-Z)')
        if not requirements['lowercase']:
            error_messages.append('Password must contain at least one lowercase letter (a-z)')
        if not requirements['number']:
            error_messages.append('Password must contain at least one number (0-9)')
        if not requirements['special']:
            error_messages.append('Password must contain at least one special character (!@#$%^&*(),.?":{}|<>)')
        
        is_valid = all(requirements.values())
        
        return is_valid, requirements, error_messages
    
    @classmethod
    def get_password_strength_score(cls, password: str) -> int:
        """
        Calculate password strength score (0-100)
        
        Args:
            password: The password to evaluate
            
        Returns:
            Integer score from 0-100 representing password strength
        """
        score = 0
        
        # Length scoring (up to 40 points)
        if len(password) >= 8:
            score += 20
        if len(password) >= 12:
            score += 10
        if len(password) >= 16:
            score += 10
        
        # Character variety scoring (60 points total)
        if re.search(cls.REQUIRED_PATTERNS['lowercase'], password):
            score += 15
        if re.search(cls.REQUIRED_PATTERNS['uppercase'], password):
            score += 15
        if re.search(cls.REQUIRED_PATTERNS['number'], password):
            score += 15
        if re.search(cls.REQUIRED_PATTERNS['special'], password):
            score += 15
        
        return min(score, 100)
    
    @classmethod
    def get_strength_label(cls, score: int) -> str:
        """
        Get human-readable strength label for password score
        
        Args:
            score: Password strength score (0-100)
            
        Returns:
            String label describing password strength
        """
        if score < 40:
            return 'Weak'
        elif score < 70:
            return 'Fair'
        elif score < 90:
            return 'Good'
        else:
            return 'Strong'
    
    @classmethod
    def check_common_passwords(cls, password: str) -> bool:
        """
        Check if password is in common passwords list
        
        Args:
            password: The password to check
            
        Returns:
            True if password is common/weak, False if acceptable
        """
        # Common weak passwords to reject
        common_passwords = {
            'password', 'password123', '123456', '123456789', 'qwerty',
            'abc123', 'password1', 'admin', 'letmein', 'welcome',
            'monkey', '1234567890', 'dragon', 'master', 'login',
            'passw0rd', 'password!', 'Password1', 'Password123'
        }
        
        return password.lower() in common_passwords
    
    @classmethod
    def validate_password_comprehensive(cls, password: str) -> Dict:
        """
        Comprehensive password validation with all checks
        
        Args:
            password: The password to validate
            
        Returns:
            Dictionary containing validation results and recommendations
        """
        is_valid, requirements, error_messages = cls.validate_password(password)
        strength_score = cls.get_password_strength_score(password)
        strength_label = cls.get_strength_label(strength_score)
        is_common = cls.check_common_passwords(password)
        
        # Add common password warning
        if is_common:
            error_messages.append('Password is too common. Please choose a more unique password.')
            is_valid = False
        
        # Generate recommendations
        recommendations = []
        if not requirements['length']:
            recommendations.append(f'Use at least {cls.MIN_LENGTH} characters')
        if not requirements['uppercase']:
            recommendations.append('Add uppercase letters')
        if not requirements['lowercase']:
            recommendations.append('Add lowercase letters')
        if not requirements['number']:
            recommendations.append('Add numbers')
        if not requirements['special']:
            recommendations.append('Add special characters')
        if len(password) < 12:
            recommendations.append('Consider using 12+ characters for better security')
        if is_common:
            recommendations.append('Avoid common passwords')
        
        return {
            'is_valid': is_valid,
            'requirements_met': requirements,
            'error_messages': error_messages,
            'strength_score': strength_score,
            'strength_label': strength_label,
            'is_common_password': is_common,
            'recommendations': recommendations
        }


def validate_password_for_registration(password: str) -> Dict:
    """
    Convenience function for registration password validation
    
    Args:
        password: The password to validate
        
    Returns:
        Dictionary with validation results suitable for API responses
    """
    return PasswordValidator.validate_password_comprehensive(password)


# Export main validation function
__all__ = ['PasswordValidator', 'validate_password_for_registration']
