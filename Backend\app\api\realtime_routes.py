"""
Real-time Routes for DeepTrade Platform
Handles WebSocket/SSE connections for real-time updates of balance, positions, metrics, and live prices.
"""

from flask import Blueprint, Response, request, jsonify, current_app, g
from flask_jwt_extended import get_jwt_identity, jwt_required as flask_jwt_required
from app.auth.decorators import jwt_required
from app.models.user import User
from app.models.trade import Trade
from app.models.user_tier_status import UserTierStatus
from app.models.security_log import APICredential
from app.services.exchange_service import get_exchange_service
from app.services.price_service import price_service
import json
import time
import threading
from datetime import datetime
import queue

realtime_bp = Blueprint('realtime', __name__, url_prefix='/api/realtime')

@realtime_bp.route('/stream', methods=['GET'])
def stream_updates():
    """Server-Sent Events stream for real-time updates - SIMPLIFIED VERSION."""
    # Try to get token from query parameters since EventSource doesn't support headers
    token = request.args.get('token')
    
    if not token:
        # Try to get from Authorization header as fallback
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
    
    if not token:
        return jsonify({'error': 'Authentication required'}), 401
    
    # Manually verify the JWT token
    try:
        from flask_jwt_extended import decode_token
        decoded_token = decode_token(token)
        user_id = decoded_token['sub']
    except Exception as e:
        return jsonify({'error': 'Invalid token'}), 401
    
    def generate_updates():
        """Generate simple updates without database calls to avoid context issues."""
        try:
            # Send initial connection confirmation
            yield f"data: {json.dumps({'status': 'connected', 'timestamp': datetime.now().isoformat(), 'user_id': user_id})}\n\n"
            
            # Send periodic heartbeat
            for i in range(12):  # Send 12 updates over 1 minute, then close
                try:
                    heartbeat_data = {
                        'status': 'active',
                        'timestamp': datetime.now().isoformat(),
                        'heartbeat': i + 1
                    }
                    yield f"data: {json.dumps(heartbeat_data)}\n\n"
                    time.sleep(5)
                except GeneratorExit:
                    # Client disconnected, exit gracefully
                    return
                except Exception:
                    break
            
            # Send final message
            yield f"data: {json.dumps({'status': 'connection_closing', 'timestamp': datetime.now().isoformat()})}\n\n"
                    
        except GeneratorExit:
            # Handle generator exit gracefully
            return
        except Exception:
            try:
                yield f"data: {json.dumps({'error': 'Stream error', 'timestamp': datetime.now().isoformat()})}\n\n"
            except GeneratorExit:
                return
    
    return Response(
        generate_updates(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Authorization',
        }
    )

@realtime_bp.route('/price-stream', methods=['GET'])
def price_stream():
    """Server-Sent Events stream for real-time price updates."""
    # Try to get token from query parameters since EventSource doesn't support headers
    token = request.args.get('token')

    if not token:
        # Try to get from Authorization header as fallback
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

    if not token:
        return jsonify({'error': 'Authentication required'}), 401

    # Manually verify the JWT token
    try:
        from flask_jwt_extended import decode_token
        decoded_token = decode_token(token)
        user_id = decoded_token['sub']
    except Exception as e:
        return jsonify({'error': 'Invalid token'}), 401

    def generate_price_updates():
        """Generate real-time price updates."""
        # Create a queue for this client
        client_queue = queue.Queue()

        def price_callback(symbol: str, price_data: dict):
            """Callback function to receive price updates."""
            try:
                client_queue.put({
                    'type': 'price_update',
                    'symbol': symbol,
                    'data': price_data
                }, block=False)
            except queue.Full:
                # Queue is full, skip this update
                pass

        # Subscribe to price updates
        price_service.add_subscriber(price_callback)

        try:
            # Send initial connection confirmation with current prices
            current_prices = price_service.get_all_prices()
            yield f"data: {json.dumps({'type': 'connected', 'timestamp': datetime.now().isoformat(), 'current_prices': current_prices})}\n\n"

            # Send price updates as they come
            while True:
                try:
                    # Wait for price update with timeout
                    update = client_queue.get(timeout=30)  # 30 second timeout for heartbeat
                    yield f"data: {json.dumps(update)}\n\n"
                except queue.Empty:
                    # Send heartbeat if no updates
                    heartbeat = {
                        'type': 'heartbeat',
                        'timestamp': datetime.now().isoformat()
                    }
                    yield f"data: {json.dumps(heartbeat)}\n\n"
                except GeneratorExit:
                    # Client disconnected
                    break

        except GeneratorExit:
            # Handle generator exit gracefully
            pass
        except Exception as e:
            try:
                error_data = {
                    'type': 'error',
                    'message': 'Stream error',
                    'timestamp': datetime.now().isoformat()
                }
                yield f"data: {json.dumps(error_data)}\n\n"
            except GeneratorExit:
                pass
        finally:
            # Clean up subscription
            price_service.remove_subscriber(price_callback)

    return Response(
        generate_price_updates(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Authorization',
        }
    )

@realtime_bp.route('/price-status', methods=['GET'])
@jwt_required
def price_status():
    """Get the current status of the price service."""
    return jsonify(price_service.get_status())

# Simplified SSE implementation - no complex real-time data fetching for now
# This avoids Flask application context issues