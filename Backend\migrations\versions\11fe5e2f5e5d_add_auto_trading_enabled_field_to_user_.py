"""Add auto_trading_enabled field to User model

Revision ID: 11fe5e2f5e5d
Revises: 0482d3879ae2
Create Date: 2025-07-15 16:27:14.449577

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '11fe5e2f5e5d'
down_revision = '0482d3879ae2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # First, add the new column to users table
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('auto_trading_enabled', sa.<PERSON>an(), nullable=False, server_default='0'))

    # Then handle the foreign key constraint fix
    with op.batch_alter_table('user_2fa_email_codes', schema=None) as batch_op:
        # Drop foreign key constraint first
        batch_op.drop_constraint('user_2fa_email_codes_ibfk_1', type_='foreignkey')
        # Drop index after constraint is removed
        batch_op.drop_index('idx_user_2fa_email_codes_user_id')
        # Recreate foreign key constraint
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # First handle the foreign key constraint restoration
    with op.batch_alter_table('user_2fa_email_codes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_2fa_email_codes_ibfk_1', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_index('idx_user_2fa_email_codes_user_id', ['user_id'], unique=False)

    # Then remove the column from users table
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('auto_trading_enabled')

    # ### end Alembic commands ###
