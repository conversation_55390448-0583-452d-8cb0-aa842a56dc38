import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/Textarea';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { useTranslation } from '@/hooks/useTranslation';
import { API_BASE_URL } from '@/config';

export default function TwoFAResetRequest() {
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    reason: '',
    last_login_date: '',
    account_creation_date: '',
    recent_activity_description: '',
    security_question_1: 'When was your last successful trade happened?',
    security_answer_1: '',
    security_question_2: 'What tier are you currently on (1, 2, or 3)?',
    security_answer_2: '',
    security_question_3: 'What exchange do you primarily use for trading?',
    security_answer_3: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [requestId, setRequestId] = useState('');

  const { t } = useTranslation();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.email || !formData.full_name || !formData.reason) {
      toastError({
        title: t('common.error'),
        description: 'Please fill in all required fields (email, full name, and reason)',
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/2fa/reset-request`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to submit 2FA reset request');
      }

      setRequestId(data.request_id);
      setIsSubmitted(true);
      toastSuccess({
        title: 'Request Submitted',
        description: data.message,
      });

    } catch (error) {
      console.error('2FA reset request failed:', error);
      toastError({
        title: 'Request Failed',
        description: error instanceof Error ? error.message : 'Failed to submit 2FA reset request',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[500px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Request Submitted</h1>
          <p className="text-sm text-muted-foreground">
            Your 2FA reset request has been submitted for review.
          </p>
        </div>
        
        <div className="grid gap-6">
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
            <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">✓ Request Submitted Successfully</h3>
            <div className="text-sm text-green-800 dark:text-green-200 space-y-2">
              <p><strong>Email:</strong> {formData.email}</p>
              <p><strong>Request ID:</strong> {requestId}</p>
              <p><strong>Status:</strong> Pending Review</p>
            </div>
          </div>
          
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
            <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">What happens next?</h3>
            <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <p>1. Our security team will review your request and verification information</p>
              <p>2. We may contact you for additional verification if needed</p>
              <p>3. You will receive an email notification once processed</p>
              <p>4. If approved, your 2FA will be disabled and you can set it up again</p>
            </div>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button asChild className="w-full">
              <Link to="/login">Back to Login</Link>
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => {
                setIsSubmitted(false);
                setFormData({
                  email: '',
                  full_name: '',
                  reason: '',
                  last_login_date: '',
                  account_creation_date: '',
                  recent_activity_description: '',
                  security_question_1: 'When was your last successful trade happened?',
                  security_answer_1: '',
                  security_question_2: 'What tier are you currently on (1, 2, or 3)?',
                  security_answer_2: '',
                  security_question_3: 'What exchange do you primarily use for trading?',
                  security_answer_3: ''
                });
                setRequestId('');
              }}
              className="w-full"
            >
              Submit Another Request
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[500px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">2FA Reset Request</h1>
        <p className="text-sm text-muted-foreground">
          Lost access to your 2FA device? Fill out this form to request a reset from our security team.
        </p>
      </div>
      
      <div className="grid gap-6">
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-yellow-900/20 dark:border-yellow-800">
          <h3 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">⚠️ Important Security Notice</h3>
          <div className="text-sm text-yellow-800 dark:text-yellow-200 space-y-1">
            <p>• This process requires manual review by our security team</p>
            <p>• Provide as much accurate information as possible to speed up verification</p>
            <p>• False information will result in request rejection</p>
            <p>• Processing time: 1-3 business days</p>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Required Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Required Information</h3>
            
            <div className="grid gap-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your account email address"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={isLoading}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="full_name">Full Name *</Label>
              <Input
                id="full_name"
                type="text"
                placeholder="Enter your full name as registered"
                value={formData.full_name}
                onChange={(e) => handleInputChange('full_name', e.target.value)}
                disabled={isLoading}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="reason">Reason for 2FA Reset *</Label>
              <Textarea
                id="reason"
                placeholder="Explain why you need to reset your 2FA (e.g., lost phone, broken device, etc.)"
                value={formData.reason}
                onChange={(e) => handleInputChange('reason', e.target.value)}
                disabled={isLoading}
                rows={3}
                required
              />
            </div>
          </div>
          
          {/* Optional Verification Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Additional Verification (Optional but Recommended)</h3>
            
            <div className="grid gap-2">
              <Label htmlFor="last_login_date">Approximate Last Login Date</Label>
              <Input
                id="last_login_date"
                type="text"
                placeholder="e.g., December 15, 2024"
                value={formData.last_login_date}
                onChange={(e) => handleInputChange('last_login_date', e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="account_creation_date">Approximate Account Creation Date</Label>
              <Input
                id="account_creation_date"
                type="text"
                placeholder="e.g., November 2024"
                value={formData.account_creation_date}
                onChange={(e) => handleInputChange('account_creation_date', e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="recent_activity_description">Recent Account Activity</Label>
              <Textarea
                id="recent_activity_description"
                placeholder="Describe recent activities (trades, tier changes, payments, etc.)"
                value={formData.recent_activity_description}
                onChange={(e) => handleInputChange('recent_activity_description', e.target.value)}
                disabled={isLoading}
                rows={2}
              />
            </div>
          </div>
          
          {/* Security Questions */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Security Questions</h3>
            <p className="text-sm text-muted-foreground">
              Answer these questions to help verify your identity. Leave blank if you don't know.
            </p>
            
            <div className="grid gap-2">
              <Label htmlFor="security_answer_1">{formData.security_question_1}</Label>
              <Input
                id="security_answer_1"
                type="text"
                placeholder="Your answer"
                value={formData.security_answer_1}
                onChange={(e) => handleInputChange('security_answer_1', e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="security_answer_2">{formData.security_question_2}</Label>
              <Input
                id="security_answer_2"
                type="text"
                placeholder="Your answer"
                value={formData.security_answer_2}
                onChange={(e) => handleInputChange('security_answer_2', e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="security_answer_3">{formData.security_question_3}</Label>
              <Input
                id="security_answer_3"
                type="text"
                placeholder="Your answer"
                value={formData.security_answer_3}
                onChange={(e) => handleInputChange('security_answer_3', e.target.value)}
                disabled={isLoading}
              />
            </div>
          </div>
          
          <Button disabled={isLoading} className="w-full">
            {isLoading ? 'Submitting Request...' : 'Submit 2FA Reset Request'}
          </Button>
        </form>
        
        <div className="flex flex-col space-y-2 text-center">
          <Button asChild variant="ghost" className="w-full">
            <Link to="/login">Back to Login</Link>
          </Button>
          
          <p className="text-sm text-muted-foreground">
            Remember your 2FA device?{' '}
            <Link to="/verify-2fa" className="underline underline-offset-4 hover:text-primary">
              Try 2FA verification
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
