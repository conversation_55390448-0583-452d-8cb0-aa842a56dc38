"""
Referral API Routes
Handles referral system endpoints for generating codes, tracking referrals, and managing earnings.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta
from app import db
from app.models.user import User
from app.models.referral import Referral, ReferralEarning, ReferrerProfile
from app.models.user_tier_status import UserTierStatus
from app.auth.security import SecurityManager

referral_bp = Blueprint('referral', __name__, url_prefix='/api/referrals')


@referral_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_referrer_profile():
    """Get user's referrer profile and statistics."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get or create referrer profile
        profile = ReferrerProfile.query.filter_by(user_id=user_id).first()
        if not profile:
            profile = ReferrerProfile(user_id=user_id)
            db.session.add(profile)
            db.session.commit()
        
        # Update statistics
        profile.update_statistics()
        db.session.commit()
        
        # Get referrals made by this user
        referrals = Referral.query.filter_by(referrer_id=user_id).all()
        
        # Get recent earnings
        recent_earnings = ReferralEarning.query.join(Referral).filter(
            Referral.referrer_id == user_id
        ).order_by(ReferralEarning.created_at.desc()).limit(10).all()
        
        return jsonify({
            'profile': profile.to_dict(),
            'referrals': [r.to_dict() for r in referrals],
            'recent_earnings': [e.to_dict() for e in recent_earnings],
            'referral_link': f"{request.host_url}register?ref={profile.referral_code}"
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting referrer profile: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@referral_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_referrer_profile():
    """Update referrer profile settings."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        profile = ReferrerProfile.query.filter_by(user_id=user_id).first()
        if not profile:
            return jsonify({'error': 'Referrer profile not found'}), 404
        
        # Update allowed fields
        if 'solana_wallet_address' in data:
            # Basic validation for Solana address
            wallet_address = data['solana_wallet_address'].strip()
            if wallet_address and (len(wallet_address) < 32 or len(wallet_address) > 44):
                return jsonify({'error': 'Invalid Solana wallet address format'}), 400
            profile.solana_wallet_address = wallet_address
        
        if 'auto_payment_enabled' in data:
            profile.auto_payment_enabled = bool(data['auto_payment_enabled'])
        
        if 'minimum_payout' in data:
            min_payout = float(data['minimum_payout'])
            if min_payout < 1 or min_payout > 1000:
                return jsonify({'error': 'Minimum payout must be between $1 and $1000'}), 400
            profile.minimum_payout = min_payout
        
        if 'is_active' in data:
            profile.is_active = bool(data['is_active'])
        
        db.session.commit()
        
        # Log profile update
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='referrer_profile_updated',
            ip_address=request.remote_addr,
            details={
                'updated_fields': list(data.keys())
            },
            risk_level='low'
        )
        
        return jsonify({
            'message': 'Profile updated successfully',
            'profile': profile.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating referrer profile: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@referral_bp.route('/code/generate', methods=['POST'])
@jwt_required()
def generate_new_referral_code():
    """Generate a new referral code for user."""
    try:
        user_id = get_jwt_identity()
        
        profile = ReferrerProfile.query.filter_by(user_id=user_id).first()
        if not profile:
            profile = ReferrerProfile(user_id=user_id)
            db.session.add(profile)
        else:
            # Generate new code
            profile.referral_code = Referral._generate_referral_code()
        
        db.session.commit()
        
        return jsonify({
            'message': 'New referral code generated successfully',
            'referral_code': profile.referral_code,
            'referral_link': f"{request.host_url}register?ref={profile.referral_code}"
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error generating referral code: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@referral_bp.route('/earnings', methods=['GET'])
@jwt_required()
def get_referral_earnings():
    """Get detailed referral earnings history."""
    try:
        user_id = get_jwt_identity()
        
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        status = request.args.get('status')  # 'paid', 'unpaid', 'all'
        
        # Build query
        query = ReferralEarning.query.join(Referral).filter(Referral.referrer_id == user_id)
        
        if status == 'paid':
            query = query.filter(ReferralEarning.is_paid == True)
        elif status == 'unpaid':
            query = query.filter(ReferralEarning.is_paid == False)
        
        # Get paginated results
        earnings = query.order_by(ReferralEarning.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Calculate summary statistics
        total_earnings = db.session.query(db.func.sum(ReferralEarning.amount)).join(Referral).filter(
            Referral.referrer_id == user_id
        ).scalar() or 0
        
        total_paid = db.session.query(db.func.sum(ReferralEarning.paid_amount)).join(Referral).filter(
            Referral.referrer_id == user_id
        ).scalar() or 0
        
        return jsonify({
            'earnings': [e.to_dict() for e in earnings.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': earnings.total,
                'pages': earnings.pages,
                'has_next': earnings.has_next,
                'has_prev': earnings.has_prev
            },
            'summary': {
                'total_earnings': float(total_earnings),
                'total_paid': float(total_paid),
                'pending_payout': float(total_earnings - total_paid)
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting referral earnings: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@referral_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_referral_statistics():
    """Get comprehensive referral statistics."""
    try:
        user_id = get_jwt_identity()
        
        # Get time period from query params
        period = request.args.get('period', '30d')  # '7d', '30d', '90d', 'all'
        
        # Calculate date range
        if period == '7d':
            start_date = datetime.utcnow() - timedelta(days=7)
        elif period == '30d':
            start_date = datetime.utcnow() - timedelta(days=30)
        elif period == '90d':
            start_date = datetime.utcnow() - timedelta(days=90)
        else:
            start_date = None
        
        # Base queries
        referrals_query = Referral.query.filter_by(referrer_id=user_id)
        earnings_query = ReferralEarning.query.join(Referral).filter(Referral.referrer_id == user_id)
        
        # Apply date filter if specified
        if start_date:
            referrals_query = referrals_query.filter(Referral.created_at >= start_date)
            earnings_query = earnings_query.filter(ReferralEarning.created_at >= start_date)
        
        # Get statistics
        total_referrals = referrals_query.count()
        verified_referrals = referrals_query.filter(Referral.is_verified == True).count()
        active_referrals = referrals_query.filter(Referral.status == 'active').count()
        
        total_earnings = earnings_query.with_entities(db.func.sum(ReferralEarning.amount)).scalar() or 0
        paid_earnings = earnings_query.filter(ReferralEarning.is_paid == True).with_entities(
            db.func.sum(ReferralEarning.paid_amount)
        ).scalar() or 0
        
        # Get tier-based earning rates
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        current_tier = tier_status.get_current_tier() if tier_status else 1
        
        tier_rates = {1: 0.5, 2: 1.0, 3: 2.0}
        current_rate = tier_rates.get(current_tier, 0.5)
        
        # Get recent activity
        recent_referrals = referrals_query.order_by(Referral.created_at.desc()).limit(5).all()
        recent_earnings = earnings_query.order_by(ReferralEarning.created_at.desc()).limit(5).all()
        
        return jsonify({
            'period': period,
            'statistics': {
                'total_referrals': total_referrals,
                'verified_referrals': verified_referrals,
                'active_referrals': active_referrals,
                'verification_rate': (verified_referrals / total_referrals * 100) if total_referrals > 0 else 0,
                'total_earnings': float(total_earnings),
                'paid_earnings': float(paid_earnings),
                'pending_earnings': float(total_earnings - paid_earnings),
                'current_tier': current_tier,
                'earning_rate': current_rate,
                'average_earning_per_referral': float(total_earnings / verified_referrals) if verified_referrals > 0 else 0
            },
            'recent_activity': {
                'referrals': [r.to_dict() for r in recent_referrals],
                'earnings': [e.to_dict() for e in recent_earnings]
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting referral statistics: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@referral_bp.route('/verify/<referral_code>', methods=['GET'])
def verify_referral_code():
    """Verify if a referral code is valid (public endpoint for registration)."""
    try:
        referral_code = referral_code.upper()
        
        # Check if code exists in referrer profiles
        profile = ReferrerProfile.query.filter_by(referral_code=referral_code, is_active=True).first()
        
        if profile:
            return jsonify({
                'valid': True,
                'referrer_name': profile.user.full_name,
                'referral_code': referral_code
            }), 200
        else:
            return jsonify({
                'valid': False,
                'error': 'Invalid or inactive referral code'
            }), 404
            
    except Exception as e:
        current_app.logger.error(f"Error verifying referral code: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@referral_bp.route('/register', methods=['POST'])
def register_referral():
    """Register a new referral relationship (called during user registration)."""
    try:
        data = request.get_json()
        referral_code = data.get('referral_code', '').upper()
        referee_id = data.get('referee_id')
        
        if not referral_code or not referee_id:
            return jsonify({'error': 'Referral code and referee ID required'}), 400
        
        # Find referrer by code
        profile = ReferrerProfile.query.filter_by(referral_code=referral_code, is_active=True).first()
        
        if not profile:
            return jsonify({'error': 'Invalid referral code'}), 404
        
        # Check if referee already has a referral
        existing_referral = Referral.query.filter_by(referee_id=referee_id).first()
        if existing_referral:
            return jsonify({'error': 'User already has a referral relationship'}), 400
        
        # Create referral relationship
        referral = Referral(
            referrer_id=profile.user_id,
            referee_id=referee_id,
            referral_code=referral_code
        )
        
        db.session.add(referral)
        db.session.commit()
        
        # Log referral registration
        SecurityManager.log_security_event(
            user_id=referee_id,
            event_type='referral_registered',
            ip_address=request.remote_addr,
            details={
                'referrer_id': profile.user_id,
                'referral_code': referral_code
            },
            risk_level='low'
        )
        
        return jsonify({
            'message': 'Referral registered successfully',
            'referral': referral.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error registering referral: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@referral_bp.route('/payout/request', methods=['POST'])
@jwt_required()
def request_payout():
    """Request manual payout of referral earnings."""
    try:
        user_id = get_jwt_identity()
        
        profile = ReferrerProfile.query.filter_by(user_id=user_id).first()
        if not profile:
            return jsonify({'error': 'Referrer profile not found'}), 404
        
        if not profile.solana_wallet_address:
            return jsonify({'error': 'Solana wallet address required for payout'}), 400
        
        pending_amount = profile.get_pending_payout()
        if pending_amount < profile.minimum_payout:
            return jsonify({
                'error': f'Minimum payout amount is ${profile.minimum_payout}. Current pending: ${pending_amount}'
            }), 400
        
        # Create payout request (this would trigger the payment process)
        from app.services.solana_service import SolanaService
        from app.models.solana_payment import SolanaPaymentType
        
        solana_service = SolanaService()
        
        # Create payment request for referral payout
        payment_result = solana_service.create_payment_request(
            user_id=user_id,
            amount=pending_amount,
            payment_type=SolanaPaymentType.REFERRAL_PAYOUT.value,
            referral_earning_id=None  # This would be set to specific earnings being paid
        )
        
        if payment_result['success']:
            return jsonify({
                'message': 'Payout request created successfully',
                'payout_amount': float(pending_amount),
                'payment_details': payment_result['payment']
            }), 200
        else:
            return jsonify({'error': payment_result['error']}), 500
            
    except Exception as e:
        current_app.logger.error(f"Error requesting payout: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
