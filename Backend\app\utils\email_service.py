import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from flask import current_app

def validate_email_config():
    """Validate email configuration."""
    required_configs = ['SMTP_SERVER', 'SMTP_PORT', 'SMTP_USERNAME', 'SMTP_PASSWORD', 'FROM_EMAIL']
    missing_configs = []

    for config in required_configs:
        if not current_app.config.get(config):
            missing_configs.append(config)

    if missing_configs:
        current_app.logger.warning(f"Missing email configuration: {', '.join(missing_configs)}")
        return False, f"Missing email configuration: {', '.join(missing_configs)}"

    return True, "Email configuration is valid"


def send_email(to_email, subject, body_html):
    """Send email using SMTP with enhanced error handling."""
    # Validate email configuration
    is_valid, message = validate_email_config()
    if not is_valid:
        current_app.logger.error(f"Email configuration invalid: {message}")
        raise Exception(f"Email configuration error: {message}")

    # Get email configuration from app config
    smtp_server = current_app.config.get('SMTP_SERVER')
    smtp_port = current_app.config.get('SMTP_PORT')
    smtp_username = current_app.config.get('SMTP_USERNAME')
    smtp_password = current_app.config.get('SMTP_PASSWORD')
    from_email = current_app.config.get('FROM_EMAIL')

    # Validate email address format
    import re
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_regex, to_email):
        raise Exception(f"Invalid email address format: {to_email}")

    # Create message
    msg = MIMEMultipart('alternative')
    msg['Subject'] = subject
    msg['From'] = from_email
    msg['To'] = to_email

    # Attach HTML content
    html_part = MIMEText(body_html, 'html')
    msg.attach(html_part)

    # Send email with retry logic
    max_retries = 3
    for attempt in range(max_retries):
        try:
            current_app.logger.info(f"Sending email to {to_email} (attempt {attempt + 1}/{max_retries})")
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.sendmail(from_email, to_email, msg.as_string())
            server.quit()
            current_app.logger.info(f"Email sent successfully to {to_email}")
            return True
        except Exception as e:
            current_app.logger.error(f"Email send attempt {attempt + 1} failed: {str(e)}")
            if attempt == max_retries - 1:  # Last attempt
                current_app.logger.error(f"Failed to send email after {max_retries} attempts: {str(e)}")
                raise e
            # Wait before retry (exponential backoff)
            import time
            time.sleep(2 ** attempt)