import{a as ia,c as Ct,g as Ut,d as Jt,e as oa,P as Ii}from"./main-CLJ81jXo.js";var Sn={exports:{}},Ti;function rn(){if(Ti)return Sn.exports;Ti=1;var n=typeof Reflect=="object"?Reflect:null,e=n&&typeof n.apply=="function"?n.apply:function(N,F,G){return Function.prototype.apply.call(N,F,G)},t;n&&typeof n.ownKeys=="function"?t=n.ownKeys:Object.getOwnPropertySymbols?t=function(N){return Object.getOwnPropertyNames(N).concat(Object.getOwnPropertySymbols(N))}:t=function(N){return Object.getOwnPropertyNames(N)};function r(B){console&&console.warn&&console.warn(B)}var i=Number.isNaN||function(N){return N!==N};function o(){o.init.call(this)}Sn.exports=o,Sn.exports.once=f,o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var s=10;function u(B){if(typeof B!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof B)}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(B){if(typeof B!="number"||B<0||i(B))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+B+".");s=B}}),o.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(N){if(typeof N!="number"||N<0||i(N))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+N+".");return this._maxListeners=N,this};function h(B){return B._maxListeners===void 0?o.defaultMaxListeners:B._maxListeners}o.prototype.getMaxListeners=function(){return h(this)},o.prototype.emit=function(N){for(var F=[],G=1;G<arguments.length;G++)F.push(arguments[G]);var a=N==="error",w=this._events;if(w!==void 0)a=a&&w.error===void 0;else if(!a)return!1;if(a){var l;if(F.length>0&&(l=F[0]),l instanceof Error)throw l;var d=new Error("Unhandled error."+(l?" ("+l.message+")":""));throw d.context=l,d}var y=w[N];if(y===void 0)return!1;if(typeof y=="function")e(y,this,F);else for(var g=y.length,L=D(y,g),G=0;G<g;++G)e(L[G],this,F);return!0};function b(B,N,F,G){var a,w,l;if(u(F),w=B._events,w===void 0?(w=B._events=Object.create(null),B._eventsCount=0):(w.newListener!==void 0&&(B.emit("newListener",N,F.listener?F.listener:F),w=B._events),l=w[N]),l===void 0)l=w[N]=F,++B._eventsCount;else if(typeof l=="function"?l=w[N]=G?[F,l]:[l,F]:G?l.unshift(F):l.push(F),a=h(B),a>0&&l.length>a&&!l.warned){l.warned=!0;var d=new Error("Possible EventEmitter memory leak detected. "+l.length+" "+String(N)+" listeners added. Use emitter.setMaxListeners() to increase limit");d.name="MaxListenersExceededWarning",d.emitter=B,d.type=N,d.count=l.length,r(d)}return B}o.prototype.addListener=function(N,F){return b(this,N,F,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(N,F){return b(this,N,F,!0)};function C(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function O(B,N,F){var G={fired:!1,wrapFn:void 0,target:B,type:N,listener:F},a=C.bind(G);return a.listener=F,G.wrapFn=a,a}o.prototype.once=function(N,F){return u(F),this.on(N,O(this,N,F)),this},o.prototype.prependOnceListener=function(N,F){return u(F),this.prependListener(N,O(this,N,F)),this},o.prototype.removeListener=function(N,F){var G,a,w,l,d;if(u(F),a=this._events,a===void 0)return this;if(G=a[N],G===void 0)return this;if(G===F||G.listener===F)--this._eventsCount===0?this._events=Object.create(null):(delete a[N],a.removeListener&&this.emit("removeListener",N,G.listener||F));else if(typeof G!="function"){for(w=-1,l=G.length-1;l>=0;l--)if(G[l]===F||G[l].listener===F){d=G[l].listener,w=l;break}if(w<0)return this;w===0?G.shift():m(G,w),G.length===1&&(a[N]=G[0]),a.removeListener!==void 0&&this.emit("removeListener",N,d||F)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(N){var F,G,a;if(G=this._events,G===void 0)return this;if(G.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):G[N]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete G[N]),this;if(arguments.length===0){var w=Object.keys(G),l;for(a=0;a<w.length;++a)l=w[a],l!=="removeListener"&&this.removeAllListeners(l);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(F=G[N],typeof F=="function")this.removeListener(N,F);else if(F!==void 0)for(a=F.length-1;a>=0;a--)this.removeListener(N,F[a]);return this};function A(B,N,F){var G=B._events;if(G===void 0)return[];var a=G[N];return a===void 0?[]:typeof a=="function"?F?[a.listener||a]:[a]:F?_(a):D(a,a.length)}o.prototype.listeners=function(N){return A(this,N,!0)},o.prototype.rawListeners=function(N){return A(this,N,!1)},o.listenerCount=function(B,N){return typeof B.listenerCount=="function"?B.listenerCount(N):j.call(B,N)},o.prototype.listenerCount=j;function j(B){var N=this._events;if(N!==void 0){var F=N[B];if(typeof F=="function")return 1;if(F!==void 0)return F.length}return 0}o.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]};function D(B,N){for(var F=new Array(N),G=0;G<N;++G)F[G]=B[G];return F}function m(B,N){for(;N+1<B.length;N++)B[N]=B[N+1];B.pop()}function _(B){for(var N=new Array(B.length),F=0;F<N.length;++F)N[F]=B[F].listener||B[F];return N}function f(B,N){return new Promise(function(F,G){function a(l){B.removeListener(N,w),G(l)}function w(){typeof B.removeListener=="function"&&B.removeListener("error",a),F([].slice.call(arguments))}S(B,N,w,{once:!0}),N!=="error"&&c(B,a,{once:!0})})}function c(B,N,F){typeof B.on=="function"&&S(B,"error",N,F)}function S(B,N,F,G){if(typeof B.on=="function")G.once?B.once(N,F):B.on(N,F);else if(typeof B.addEventListener=="function")B.addEventListener(N,function a(w){G.once&&B.removeEventListener(N,a),F(w)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof B)}return Sn.exports}var sa=rn(),aa=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,ar=Math.ceil,ze=Math.floor,je="[BigNumber Error] ",xi=je+"Number primitive has more than 15 significant digits: ",Xe=1e14,pe=14,lr=9007199254740991,ur=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],gt=1e7,Ne=1e9;function Ko(n){var e,t,r,i=c.prototype={constructor:c,toString:null,valueOf:null},o=new c(1),s=20,u=4,h=-7,b=21,C=-1e7,O=1e7,A=!1,j=1,D=0,m={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},_="0123456789abcdefghijklmnopqrstuvwxyz",f=!0;function c(a,w){var l,d,y,g,L,p,R,P,E=this;if(!(E instanceof c))return new c(a,w);if(w==null){if(a&&a._isBigNumber===!0){E.s=a.s,!a.c||a.e>O?E.c=E.e=null:a.e<C?E.c=[E.e=0]:(E.e=a.e,E.c=a.c.slice());return}if((p=typeof a=="number")&&a*0==0){if(E.s=1/a<0?(a=-a,-1):1,a===~~a){for(g=0,L=a;L>=10;L/=10,g++);g>O?E.c=E.e=null:(E.e=g,E.c=[a]);return}P=String(a)}else{if(!aa.test(P=String(a)))return r(E,P,p);E.s=P.charCodeAt(0)==45?(P=P.slice(1),-1):1}(g=P.indexOf("."))>-1&&(P=P.replace(".","")),(L=P.search(/e/i))>0?(g<0&&(g=L),g+=+P.slice(L+1),P=P.substring(0,L)):g<0&&(g=P.length)}else{if(Re(w,2,_.length,"Base"),w==10&&f)return E=new c(a),F(E,s+E.e+1,u);if(P=String(a),p=typeof a=="number"){if(a*0!=0)return r(E,P,p,w);if(E.s=1/a<0?(P=P.slice(1),-1):1,c.DEBUG&&P.replace(/^0\.0*|\./,"").length>15)throw Error(xi+a)}else E.s=P.charCodeAt(0)===45?(P=P.slice(1),-1):1;for(l=_.slice(0,w),g=L=0,R=P.length;L<R;L++)if(l.indexOf(d=P.charAt(L))<0){if(d=="."){if(L>g){g=R;continue}}else if(!y&&(P==P.toUpperCase()&&(P=P.toLowerCase())||P==P.toLowerCase()&&(P=P.toUpperCase()))){y=!0,L=-1,g=0;continue}return r(E,String(a),p,w)}p=!1,P=t(P,w,10,E.s),(g=P.indexOf("."))>-1?P=P.replace(".",""):g=P.length}for(L=0;P.charCodeAt(L)===48;L++);for(R=P.length;P.charCodeAt(--R)===48;);if(P=P.slice(L,++R)){if(R-=L,p&&c.DEBUG&&R>15&&(a>lr||a!==ze(a)))throw Error(xi+E.s*a);if((g=g-L-1)>O)E.c=E.e=null;else if(g<C)E.c=[E.e=0];else{if(E.e=g,E.c=[],L=(g+1)%pe,g<0&&(L+=pe),L<R){for(L&&E.c.push(+P.slice(0,L)),R-=pe;L<R;)E.c.push(+P.slice(L,L+=pe));L=pe-(P=P.slice(L)).length}else L-=R;for(;L--;P+="0");E.c.push(+P)}}else E.c=[E.e=0]}c.clone=Ko,c.ROUND_UP=0,c.ROUND_DOWN=1,c.ROUND_CEIL=2,c.ROUND_FLOOR=3,c.ROUND_HALF_UP=4,c.ROUND_HALF_DOWN=5,c.ROUND_HALF_EVEN=6,c.ROUND_HALF_CEIL=7,c.ROUND_HALF_FLOOR=8,c.EUCLID=9,c.config=c.set=function(a){var w,l;if(a!=null)if(typeof a=="object"){if(a.hasOwnProperty(w="DECIMAL_PLACES")&&(l=a[w],Re(l,0,Ne,w),s=l),a.hasOwnProperty(w="ROUNDING_MODE")&&(l=a[w],Re(l,0,8,w),u=l),a.hasOwnProperty(w="EXPONENTIAL_AT")&&(l=a[w],l&&l.pop?(Re(l[0],-Ne,0,w),Re(l[1],0,Ne,w),h=l[0],b=l[1]):(Re(l,-Ne,Ne,w),h=-(b=l<0?-l:l))),a.hasOwnProperty(w="RANGE"))if(l=a[w],l&&l.pop)Re(l[0],-Ne,-1,w),Re(l[1],1,Ne,w),C=l[0],O=l[1];else if(Re(l,-Ne,Ne,w),l)C=-(O=l<0?-l:l);else throw Error(je+w+" cannot be zero: "+l);if(a.hasOwnProperty(w="CRYPTO"))if(l=a[w],l===!!l)if(l)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))A=l;else throw A=!l,Error(je+"crypto unavailable");else A=l;else throw Error(je+w+" not true or false: "+l);if(a.hasOwnProperty(w="MODULO_MODE")&&(l=a[w],Re(l,0,9,w),j=l),a.hasOwnProperty(w="POW_PRECISION")&&(l=a[w],Re(l,0,Ne,w),D=l),a.hasOwnProperty(w="FORMAT"))if(l=a[w],typeof l=="object")m=l;else throw Error(je+w+" not an object: "+l);if(a.hasOwnProperty(w="ALPHABET"))if(l=a[w],typeof l=="string"&&!/^.?$|[+\-.\s]|(.).*\1/.test(l))f=l.slice(0,10)=="0123456789",_=l;else throw Error(je+w+" invalid: "+l)}else throw Error(je+"Object expected: "+a);return{DECIMAL_PLACES:s,ROUNDING_MODE:u,EXPONENTIAL_AT:[h,b],RANGE:[C,O],CRYPTO:A,MODULO_MODE:j,POW_PRECISION:D,FORMAT:m,ALPHABET:_}},c.isBigNumber=function(a){if(!a||a._isBigNumber!==!0)return!1;if(!c.DEBUG)return!0;var w,l,d=a.c,y=a.e,g=a.s;e:if({}.toString.call(d)=="[object Array]"){if((g===1||g===-1)&&y>=-Ne&&y<=Ne&&y===ze(y)){if(d[0]===0){if(y===0&&d.length===1)return!0;break e}if(w=(y+1)%pe,w<1&&(w+=pe),String(d[0]).length==w){for(w=0;w<d.length;w++)if(l=d[w],l<0||l>=Xe||l!==ze(l))break e;if(l!==0)return!0}}}else if(d===null&&y===null&&(g===null||g===1||g===-1))return!0;throw Error(je+"Invalid BigNumber: "+a)},c.maximum=c.max=function(){return B(arguments,-1)},c.minimum=c.min=function(){return B(arguments,1)},c.random=function(){var a=9007199254740992,w=Math.random()*a&2097151?function(){return ze(Math.random()*a)}:function(){return(Math.random()*1073741824|0)*8388608+(Math.random()*8388608|0)};return function(l){var d,y,g,L,p,R=0,P=[],E=new c(o);if(l==null?l=s:Re(l,0,Ne),L=ar(l/pe),A)if(crypto.getRandomValues){for(d=crypto.getRandomValues(new Uint32Array(L*=2));R<L;)p=d[R]*131072+(d[R+1]>>>11),p>=9e15?(y=crypto.getRandomValues(new Uint32Array(2)),d[R]=y[0],d[R+1]=y[1]):(P.push(p%1e14),R+=2);R=L/2}else if(crypto.randomBytes){for(d=crypto.randomBytes(L*=7);R<L;)p=(d[R]&31)*281474976710656+d[R+1]*1099511627776+d[R+2]*4294967296+d[R+3]*16777216+(d[R+4]<<16)+(d[R+5]<<8)+d[R+6],p>=9e15?crypto.randomBytes(7).copy(d,R):(P.push(p%1e14),R+=7);R=L/7}else throw A=!1,Error(je+"crypto unavailable");if(!A)for(;R<L;)p=w(),p<9e15&&(P[R++]=p%1e14);for(L=P[--R],l%=pe,L&&l&&(p=ur[pe-l],P[R]=ze(L/p)*p);P[R]===0;P.pop(),R--);if(R<0)P=[g=0];else{for(g=-1;P[0]===0;P.splice(0,1),g-=pe);for(R=1,p=P[0];p>=10;p/=10,R++);R<pe&&(g-=pe-R)}return E.e=g,E.c=P,E}}(),c.sum=function(){for(var a=1,w=arguments,l=new c(w[0]);a<w.length;)l=l.plus(w[a++]);return l},t=function(){var a="0123456789";function w(l,d,y,g){for(var L,p=[0],R,P=0,E=l.length;P<E;){for(R=p.length;R--;p[R]*=d);for(p[0]+=g.indexOf(l.charAt(P++)),L=0;L<p.length;L++)p[L]>y-1&&(p[L+1]==null&&(p[L+1]=0),p[L+1]+=p[L]/y|0,p[L]%=y)}return p.reverse()}return function(l,d,y,g,L){var p,R,P,E,k,v,$,J,H=l.indexOf("."),ee=s,Q=u;for(H>=0&&(E=D,D=0,l=l.replace(".",""),J=new c(d),v=J.pow(l.length-H),D=E,J.c=w(st($e(v.c),v.e,"0"),10,y,a),J.e=J.c.length),$=w(l,d,y,L?(p=_,a):(p=a,_)),P=E=$.length;$[--E]==0;$.pop());if(!$[0])return p.charAt(0);if(H<0?--P:(v.c=$,v.e=P,v.s=g,v=e(v,J,ee,Q,y),$=v.c,k=v.r,P=v.e),R=P+ee+1,H=$[R],E=y/2,k=k||R<0||$[R+1]!=null,k=Q<4?(H!=null||k)&&(Q==0||Q==(v.s<0?3:2)):H>E||H==E&&(Q==4||k||Q==6&&$[R-1]&1||Q==(v.s<0?8:7)),R<1||!$[0])l=k?st(p.charAt(1),-ee,p.charAt(0)):p.charAt(0);else{if($.length=R,k)for(--y;++$[--R]>y;)$[R]=0,R||(++P,$=[1].concat($));for(E=$.length;!$[--E];);for(H=0,l="";H<=E;l+=p.charAt($[H++]));l=st(l,P,p.charAt(0))}return l}}(),e=function(){function a(d,y,g){var L,p,R,P,E=0,k=d.length,v=y%gt,$=y/gt|0;for(d=d.slice();k--;)R=d[k]%gt,P=d[k]/gt|0,L=$*R+P*v,p=v*R+L%gt*gt+E,E=(p/g|0)+(L/gt|0)+$*P,d[k]=p%g;return E&&(d=[E].concat(d)),d}function w(d,y,g,L){var p,R;if(g!=L)R=g>L?1:-1;else for(p=R=0;p<g;p++)if(d[p]!=y[p]){R=d[p]>y[p]?1:-1;break}return R}function l(d,y,g,L){for(var p=0;g--;)d[g]-=p,p=d[g]<y[g]?1:0,d[g]=p*L+d[g]-y[g];for(;!d[0]&&d.length>1;d.splice(0,1));}return function(d,y,g,L,p){var R,P,E,k,v,$,J,H,ee,Q,le,ue,K,Y,oe,ce,fe,M=d.s==y.s?1:-1,U=d.c,z=y.c;if(!U||!U[0]||!z||!z[0])return new c(!d.s||!y.s||(U?z&&U[0]==z[0]:!z)?NaN:U&&U[0]==0||!z?M*0:M/0);for(H=new c(M),ee=H.c=[],P=d.e-y.e,M=g+P+1,p||(p=Xe,P=Ge(d.e/pe)-Ge(y.e/pe),M=M/pe|0),E=0;z[E]==(U[E]||0);E++);if(z[E]>(U[E]||0)&&P--,M<0)ee.push(1),k=!0;else{for(Y=U.length,ce=z.length,E=0,M+=2,v=ze(p/(z[0]+1)),v>1&&(z=a(z,v,p),U=a(U,v,p),ce=z.length,Y=U.length),K=ce,Q=U.slice(0,ce),le=Q.length;le<ce;Q[le++]=0);fe=z.slice(),fe=[0].concat(fe),oe=z[0],z[1]>=p/2&&oe++;do{if(v=0,R=w(z,Q,ce,le),R<0){if(ue=Q[0],ce!=le&&(ue=ue*p+(Q[1]||0)),v=ze(ue/oe),v>1)for(v>=p&&(v=p-1),$=a(z,v,p),J=$.length,le=Q.length;w($,Q,J,le)==1;)v--,l($,ce<J?fe:z,J,p),J=$.length,R=1;else v==0&&(R=v=1),$=z.slice(),J=$.length;if(J<le&&($=[0].concat($)),l(Q,$,le,p),le=Q.length,R==-1)for(;w(z,Q,ce,le)<1;)v++,l(Q,ce<le?fe:z,le,p),le=Q.length}else R===0&&(v++,Q=[0]);ee[E++]=v,Q[0]?Q[le++]=U[K]||0:(Q=[U[K]],le=1)}while((K++<Y||Q[0]!=null)&&M--);k=Q[0]!=null,ee[0]||ee.splice(0,1)}if(p==Xe){for(E=1,M=ee[0];M>=10;M/=10,E++);F(H,g+(H.e=E+P*pe-1)+1,L,k)}else H.e=P,H.r=+k;return H}}();function S(a,w,l,d){var y,g,L,p,R;if(l==null?l=u:Re(l,0,8),!a.c)return a.toString();if(y=a.c[0],L=a.e,w==null)R=$e(a.c),R=d==1||d==2&&(L<=h||L>=b)?An(R,L):st(R,L,"0");else if(a=F(new c(a),w,l),g=a.e,R=$e(a.c),p=R.length,d==1||d==2&&(w<=g||g<=h)){for(;p<w;R+="0",p++);R=An(R,g)}else if(w-=L+(d===2&&g>L),R=st(R,g,"0"),g+1>p){if(--w>0)for(R+=".";w--;R+="0");}else if(w+=g-p,w>0)for(g+1==p&&(R+=".");w--;R+="0");return a.s<0&&y?"-"+R:R}function B(a,w){for(var l,d,y=1,g=new c(a[0]);y<a.length;y++)d=new c(a[y]),(!d.s||(l=vt(g,d))===w||l===0&&g.s===w)&&(g=d);return g}function N(a,w,l){for(var d=1,y=w.length;!w[--y];w.pop());for(y=w[0];y>=10;y/=10,d++);return(l=d+l*pe-1)>O?a.c=a.e=null:l<C?a.c=[a.e=0]:(a.e=l,a.c=w),a}r=function(){var a=/^(-?)0([xbo])(?=\w[\w.]*$)/i,w=/^([^.]+)\.$/,l=/^\.([^.]+)$/,d=/^-?(Infinity|NaN)$/,y=/^\s*\+(?=[\w.])|^\s+|\s+$/g;return function(g,L,p,R){var P,E=p?L:L.replace(y,"");if(d.test(E))g.s=isNaN(E)?null:E<0?-1:1;else{if(!p&&(E=E.replace(a,function(k,v,$){return P=($=$.toLowerCase())=="x"?16:$=="b"?2:8,!R||R==P?v:k}),R&&(P=R,E=E.replace(w,"$1").replace(l,"0.$1")),L!=E))return new c(E,P);if(c.DEBUG)throw Error(je+"Not a"+(R?" base "+R:"")+" number: "+L);g.s=null}g.c=g.e=null}}();function F(a,w,l,d){var y,g,L,p,R,P,E,k=a.c,v=ur;if(k){e:{for(y=1,p=k[0];p>=10;p/=10,y++);if(g=w-y,g<0)g+=pe,L=w,R=k[P=0],E=ze(R/v[y-L-1]%10);else if(P=ar((g+1)/pe),P>=k.length)if(d){for(;k.length<=P;k.push(0));R=E=0,y=1,g%=pe,L=g-pe+1}else break e;else{for(R=p=k[P],y=1;p>=10;p/=10,y++);g%=pe,L=g-pe+y,E=L<0?0:ze(R/v[y-L-1]%10)}if(d=d||w<0||k[P+1]!=null||(L<0?R:R%v[y-L-1]),d=l<4?(E||d)&&(l==0||l==(a.s<0?3:2)):E>5||E==5&&(l==4||d||l==6&&(g>0?L>0?R/v[y-L]:0:k[P-1])%10&1||l==(a.s<0?8:7)),w<1||!k[0])return k.length=0,d?(w-=a.e+1,k[0]=v[(pe-w%pe)%pe],a.e=-w||0):k[0]=a.e=0,a;if(g==0?(k.length=P,p=1,P--):(k.length=P+1,p=v[pe-g],k[P]=L>0?ze(R/v[y-L]%v[L])*p:0),d)for(;;)if(P==0){for(g=1,L=k[0];L>=10;L/=10,g++);for(L=k[0]+=p,p=1;L>=10;L/=10,p++);g!=p&&(a.e++,k[0]==Xe&&(k[0]=1));break}else{if(k[P]+=p,k[P]!=Xe)break;k[P--]=0,p=1}for(g=k.length;k[--g]===0;k.pop());}a.e>O?a.c=a.e=null:a.e<C&&(a.c=[a.e=0])}return a}function G(a){var w,l=a.e;return l===null?a.toString():(w=$e(a.c),w=l<=h||l>=b?An(w,l):st(w,l,"0"),a.s<0?"-"+w:w)}return i.absoluteValue=i.abs=function(){var a=new c(this);return a.s<0&&(a.s=1),a},i.comparedTo=function(a,w){return vt(this,new c(a,w))},i.decimalPlaces=i.dp=function(a,w){var l,d,y,g=this;if(a!=null)return Re(a,0,Ne),w==null?w=u:Re(w,0,8),F(new c(g),a+g.e+1,w);if(!(l=g.c))return null;if(d=((y=l.length-1)-Ge(this.e/pe))*pe,y=l[y])for(;y%10==0;y/=10,d--);return d<0&&(d=0),d},i.dividedBy=i.div=function(a,w){return e(this,new c(a,w),s,u)},i.dividedToIntegerBy=i.idiv=function(a,w){return e(this,new c(a,w),0,1)},i.exponentiatedBy=i.pow=function(a,w){var l,d,y,g,L,p,R,P,E,k=this;if(a=new c(a),a.c&&!a.isInteger())throw Error(je+"Exponent not an integer: "+G(a));if(w!=null&&(w=new c(w)),p=a.e>14,!k.c||!k.c[0]||k.c[0]==1&&!k.e&&k.c.length==1||!a.c||!a.c[0])return E=new c(Math.pow(+G(k),p?a.s*(2-vn(a)):+G(a))),w?E.mod(w):E;if(R=a.s<0,w){if(w.c?!w.c[0]:!w.s)return new c(NaN);d=!R&&k.isInteger()&&w.isInteger(),d&&(k=k.mod(w))}else{if(a.e>9&&(k.e>0||k.e<-1||(k.e==0?k.c[0]>1||p&&k.c[1]>=24e7:k.c[0]<8e13||p&&k.c[0]<=9999975e7)))return g=k.s<0&&vn(a)?-0:0,k.e>-1&&(g=1/g),new c(R?1/g:g);D&&(g=ar(D/pe+2))}for(p?(l=new c(.5),R&&(a.s=1),P=vn(a)):(y=Math.abs(+G(a)),P=y%2),E=new c(o);;){if(P){if(E=E.times(k),!E.c)break;g?E.c.length>g&&(E.c.length=g):d&&(E=E.mod(w))}if(y){if(y=ze(y/2),y===0)break;P=y%2}else if(a=a.times(l),F(a,a.e+1,1),a.e>14)P=vn(a);else{if(y=+G(a),y===0)break;P=y%2}k=k.times(k),g?k.c&&k.c.length>g&&(k.c.length=g):d&&(k=k.mod(w))}return d?E:(R&&(E=o.div(E)),w?E.mod(w):g?F(E,D,u,L):E)},i.integerValue=function(a){var w=new c(this);return a==null?a=u:Re(a,0,8),F(w,w.e+1,a)},i.isEqualTo=i.eq=function(a,w){return vt(this,new c(a,w))===0},i.isFinite=function(){return!!this.c},i.isGreaterThan=i.gt=function(a,w){return vt(this,new c(a,w))>0},i.isGreaterThanOrEqualTo=i.gte=function(a,w){return(w=vt(this,new c(a,w)))===1||w===0},i.isInteger=function(){return!!this.c&&Ge(this.e/pe)>this.c.length-2},i.isLessThan=i.lt=function(a,w){return vt(this,new c(a,w))<0},i.isLessThanOrEqualTo=i.lte=function(a,w){return(w=vt(this,new c(a,w)))===-1||w===0},i.isNaN=function(){return!this.s},i.isNegative=function(){return this.s<0},i.isPositive=function(){return this.s>0},i.isZero=function(){return!!this.c&&this.c[0]==0},i.minus=function(a,w){var l,d,y,g,L=this,p=L.s;if(a=new c(a,w),w=a.s,!p||!w)return new c(NaN);if(p!=w)return a.s=-w,L.plus(a);var R=L.e/pe,P=a.e/pe,E=L.c,k=a.c;if(!R||!P){if(!E||!k)return E?(a.s=-w,a):new c(k?L:NaN);if(!E[0]||!k[0])return k[0]?(a.s=-w,a):new c(E[0]?L:u==3?-0:0)}if(R=Ge(R),P=Ge(P),E=E.slice(),p=R-P){for((g=p<0)?(p=-p,y=E):(P=R,y=k),y.reverse(),w=p;w--;y.push(0));y.reverse()}else for(d=(g=(p=E.length)<(w=k.length))?p:w,p=w=0;w<d;w++)if(E[w]!=k[w]){g=E[w]<k[w];break}if(g&&(y=E,E=k,k=y,a.s=-a.s),w=(d=k.length)-(l=E.length),w>0)for(;w--;E[l++]=0);for(w=Xe-1;d>p;){if(E[--d]<k[d]){for(l=d;l&&!E[--l];E[l]=w);--E[l],E[d]+=Xe}E[d]-=k[d]}for(;E[0]==0;E.splice(0,1),--P);return E[0]?N(a,E,P):(a.s=u==3?-1:1,a.c=[a.e=0],a)},i.modulo=i.mod=function(a,w){var l,d,y=this;return a=new c(a,w),!y.c||!a.s||a.c&&!a.c[0]?new c(NaN):!a.c||y.c&&!y.c[0]?new c(y):(j==9?(d=a.s,a.s=1,l=e(y,a,0,3),a.s=d,l.s*=d):l=e(y,a,0,j),a=y.minus(l.times(a)),!a.c[0]&&j==1&&(a.s=y.s),a)},i.multipliedBy=i.times=function(a,w){var l,d,y,g,L,p,R,P,E,k,v,$,J,H,ee,Q=this,le=Q.c,ue=(a=new c(a,w)).c;if(!le||!ue||!le[0]||!ue[0])return!Q.s||!a.s||le&&!le[0]&&!ue||ue&&!ue[0]&&!le?a.c=a.e=a.s=null:(a.s*=Q.s,!le||!ue?a.c=a.e=null:(a.c=[0],a.e=0)),a;for(d=Ge(Q.e/pe)+Ge(a.e/pe),a.s*=Q.s,R=le.length,k=ue.length,R<k&&(J=le,le=ue,ue=J,y=R,R=k,k=y),y=R+k,J=[];y--;J.push(0));for(H=Xe,ee=gt,y=k;--y>=0;){for(l=0,v=ue[y]%ee,$=ue[y]/ee|0,L=R,g=y+L;g>y;)P=le[--L]%ee,E=le[L]/ee|0,p=$*P+E*v,P=v*P+p%ee*ee+J[g]+l,l=(P/H|0)+(p/ee|0)+$*E,J[g--]=P%H;J[g]=l}return l?++d:J.splice(0,1),N(a,J,d)},i.negated=function(){var a=new c(this);return a.s=-a.s||null,a},i.plus=function(a,w){var l,d=this,y=d.s;if(a=new c(a,w),w=a.s,!y||!w)return new c(NaN);if(y!=w)return a.s=-w,d.minus(a);var g=d.e/pe,L=a.e/pe,p=d.c,R=a.c;if(!g||!L){if(!p||!R)return new c(y/0);if(!p[0]||!R[0])return R[0]?a:new c(p[0]?d:y*0)}if(g=Ge(g),L=Ge(L),p=p.slice(),y=g-L){for(y>0?(L=g,l=R):(y=-y,l=p),l.reverse();y--;l.push(0));l.reverse()}for(y=p.length,w=R.length,y-w<0&&(l=R,R=p,p=l,w=y),y=0;w;)y=(p[--w]=p[w]+R[w]+y)/Xe|0,p[w]=Xe===p[w]?0:p[w]%Xe;return y&&(p=[y].concat(p),++L),N(a,p,L)},i.precision=i.sd=function(a,w){var l,d,y,g=this;if(a!=null&&a!==!!a)return Re(a,1,Ne),w==null?w=u:Re(w,0,8),F(new c(g),a,w);if(!(l=g.c))return null;if(y=l.length-1,d=y*pe+1,y=l[y]){for(;y%10==0;y/=10,d--);for(y=l[0];y>=10;y/=10,d++);}return a&&g.e+1>d&&(d=g.e+1),d},i.shiftedBy=function(a){return Re(a,-lr,lr),this.times("1e"+a)},i.squareRoot=i.sqrt=function(){var a,w,l,d,y,g=this,L=g.c,p=g.s,R=g.e,P=s+4,E=new c("0.5");if(p!==1||!L||!L[0])return new c(!p||p<0&&(!L||L[0])?NaN:L?g:1/0);if(p=Math.sqrt(+G(g)),p==0||p==1/0?(w=$e(L),(w.length+R)%2==0&&(w+="0"),p=Math.sqrt(+w),R=Ge((R+1)/2)-(R<0||R%2),p==1/0?w="5e"+R:(w=p.toExponential(),w=w.slice(0,w.indexOf("e")+1)+R),l=new c(w)):l=new c(p+""),l.c[0]){for(R=l.e,p=R+P,p<3&&(p=0);;)if(y=l,l=E.times(y.plus(e(g,y,P,1))),$e(y.c).slice(0,p)===(w=$e(l.c)).slice(0,p))if(l.e<R&&--p,w=w.slice(p-3,p+1),w=="9999"||!d&&w=="4999"){if(!d&&(F(y,y.e+s+2,0),y.times(y).eq(g))){l=y;break}P+=4,p+=4,d=1}else{(!+w||!+w.slice(1)&&w.charAt(0)=="5")&&(F(l,l.e+s+2,1),a=!l.times(l).eq(g));break}}return F(l,l.e+s+1,u,a)},i.toExponential=function(a,w){return a!=null&&(Re(a,0,Ne),a++),S(this,a,w,1)},i.toFixed=function(a,w){return a!=null&&(Re(a,0,Ne),a=a+this.e+1),S(this,a,w)},i.toFormat=function(a,w,l){var d,y=this;if(l==null)a!=null&&w&&typeof w=="object"?(l=w,w=null):a&&typeof a=="object"?(l=a,a=w=null):l=m;else if(typeof l!="object")throw Error(je+"Argument not an object: "+l);if(d=y.toFixed(a,w),y.c){var g,L=d.split("."),p=+l.groupSize,R=+l.secondaryGroupSize,P=l.groupSeparator||"",E=L[0],k=L[1],v=y.s<0,$=v?E.slice(1):E,J=$.length;if(R&&(g=p,p=R,R=g,J-=g),p>0&&J>0){for(g=J%p||p,E=$.substr(0,g);g<J;g+=p)E+=P+$.substr(g,p);R>0&&(E+=P+$.slice(g)),v&&(E="-"+E)}d=k?E+(l.decimalSeparator||"")+((R=+l.fractionGroupSize)?k.replace(new RegExp("\\d{"+R+"}\\B","g"),"$&"+(l.fractionGroupSeparator||"")):k):E}return(l.prefix||"")+d+(l.suffix||"")},i.toFraction=function(a){var w,l,d,y,g,L,p,R,P,E,k,v,$=this,J=$.c;if(a!=null&&(p=new c(a),!p.isInteger()&&(p.c||p.s!==1)||p.lt(o)))throw Error(je+"Argument "+(p.isInteger()?"out of range: ":"not an integer: ")+G(p));if(!J)return new c($);for(w=new c(o),P=l=new c(o),d=R=new c(o),v=$e(J),g=w.e=v.length-$.e-1,w.c[0]=ur[(L=g%pe)<0?pe+L:L],a=!a||p.comparedTo(w)>0?g>0?w:P:p,L=O,O=1/0,p=new c(v),R.c[0]=0;E=e(p,w,0,1),y=l.plus(E.times(d)),y.comparedTo(a)!=1;)l=d,d=y,P=R.plus(E.times(y=P)),R=y,w=p.minus(E.times(y=w)),p=y;return y=e(a.minus(l),d,0,1),R=R.plus(y.times(P)),l=l.plus(y.times(d)),R.s=P.s=$.s,g=g*2,k=e(P,d,g,u).minus($).abs().comparedTo(e(R,l,g,u).minus($).abs())<1?[P,d]:[R,l],O=L,k},i.toNumber=function(){return+G(this)},i.toPrecision=function(a,w){return a!=null&&Re(a,1,Ne),S(this,a,w,2)},i.toString=function(a){var w,l=this,d=l.s,y=l.e;return y===null?d?(w="Infinity",d<0&&(w="-"+w)):w="NaN":(a==null?w=y<=h||y>=b?An($e(l.c),y):st($e(l.c),y,"0"):a===10&&f?(l=F(new c(l),s+y+1,u),w=st($e(l.c),l.e,"0")):(Re(a,2,_.length,"Base"),w=t(st($e(l.c),y,"0"),10,a,d,!0)),d<0&&l.c[0]&&(w="-"+w)),w},i.valueOf=i.toJSON=function(){return G(this)},i._isBigNumber=!0,i[Symbol.toStringTag]="BigNumber",i[Symbol.for("nodejs.util.inspect.custom")]=i.valueOf,n!=null&&c.set(n),c}function Ge(n){var e=n|0;return n>0||n===e?e:e-1}function $e(n){for(var e,t,r=1,i=n.length,o=n[0]+"";r<i;){for(e=n[r++]+"",t=pe-e.length;t--;e="0"+e);o+=e}for(i=o.length;o.charCodeAt(--i)===48;);return o.slice(0,i+1||1)}function vt(n,e){var t,r,i=n.c,o=e.c,s=n.s,u=e.s,h=n.e,b=e.e;if(!s||!u)return null;if(t=i&&!i[0],r=o&&!o[0],t||r)return t?r?0:-u:s;if(s!=u)return s;if(t=s<0,r=h==b,!i||!o)return r?0:!i^t?1:-1;if(!r)return h>b^t?1:-1;for(u=(h=i.length)<(b=o.length)?h:b,s=0;s<u;s++)if(i[s]!=o[s])return i[s]>o[s]^t?1:-1;return h==b?0:h>b^t?1:-1}function Re(n,e,t,r){if(n<e||n>t||n!==ze(n))throw Error(je+(r||"Argument")+(typeof n=="number"?n<e||n>t?" out of range: ":" not an integer: ":" not a primitive number: ")+String(n))}function vn(n){var e=n.c.length-1;return Ge(n.e/pe)==e&&n.c[e]%2!=0}function An(n,e){return(n.length>1?n.charAt(0)+"."+n.slice(1):n)+(e<0?"e":"e+")+e}function st(n,e,t){var r,i;if(e<0){for(i=t+".";++e;i+=t);n=i+n}else if(r=n.length,++e>r){for(i=t,e-=r;--e;i+=t);n+=i}else e<r&&(n=n.slice(0,e)+"."+n.slice(e));return n}Ko();var fr={},Oi;function la(){if(Oi)return fr;Oi=1;var n=ia().Buffer,e=n.isEncoding||function(f){switch(f=""+f,f&&f.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function t(f){if(!f)return"utf8";for(var c;;)switch(f){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return f;default:if(c)return;f=(""+f).toLowerCase(),c=!0}}function r(f){var c=t(f);if(typeof c!="string"&&(n.isEncoding===e||!e(f)))throw new Error("Unknown encoding: "+f);return c||f}fr.StringDecoder=i;function i(f){this.encoding=r(f);var c;switch(this.encoding){case"utf16le":this.text=O,this.end=A,c=4;break;case"utf8":this.fillLast=h,c=4;break;case"base64":this.text=j,this.end=D,c=3;break;default:this.write=m,this.end=_;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(c)}i.prototype.write=function(f){if(f.length===0)return"";var c,S;if(this.lastNeed){if(c=this.fillLast(f),c===void 0)return"";S=this.lastNeed,this.lastNeed=0}else S=0;return S<f.length?c?c+this.text(f,S):this.text(f,S):c||""},i.prototype.end=C,i.prototype.text=b,i.prototype.fillLast=function(f){if(this.lastNeed<=f.length)return f.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);f.copy(this.lastChar,this.lastTotal-this.lastNeed,0,f.length),this.lastNeed-=f.length};function o(f){return f<=127?0:f>>5===6?2:f>>4===14?3:f>>3===30?4:f>>6===2?-1:-2}function s(f,c,S){var B=c.length-1;if(B<S)return 0;var N=o(c[B]);return N>=0?(N>0&&(f.lastNeed=N-1),N):--B<S||N===-2?0:(N=o(c[B]),N>=0?(N>0&&(f.lastNeed=N-2),N):--B<S||N===-2?0:(N=o(c[B]),N>=0?(N>0&&(N===2?N=0:f.lastNeed=N-3),N):0))}function u(f,c,S){if((c[0]&192)!==128)return f.lastNeed=0,"�";if(f.lastNeed>1&&c.length>1){if((c[1]&192)!==128)return f.lastNeed=1,"�";if(f.lastNeed>2&&c.length>2&&(c[2]&192)!==128)return f.lastNeed=2,"�"}}function h(f){var c=this.lastTotal-this.lastNeed,S=u(this,f);if(S!==void 0)return S;if(this.lastNeed<=f.length)return f.copy(this.lastChar,c,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);f.copy(this.lastChar,c,0,f.length),this.lastNeed-=f.length}function b(f,c){var S=s(this,f,c);if(!this.lastNeed)return f.toString("utf8",c);this.lastTotal=S;var B=f.length-(S-this.lastNeed);return f.copy(this.lastChar,0,B),f.toString("utf8",c,B)}function C(f){var c=f&&f.length?this.write(f):"";return this.lastNeed?c+"�":c}function O(f,c){if((f.length-c)%2===0){var S=f.toString("utf16le",c);if(S){var B=S.charCodeAt(S.length-1);if(B>=55296&&B<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=f[f.length-2],this.lastChar[1]=f[f.length-1],S.slice(0,-1)}return S}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=f[f.length-1],f.toString("utf16le",c,f.length-1)}function A(f){var c=f&&f.length?this.write(f):"";if(this.lastNeed){var S=this.lastTotal-this.lastNeed;return c+this.lastChar.toString("utf16le",0,S)}return c}function j(f,c){var S=(f.length-c)%3;return S===0?f.toString("base64",c):(this.lastNeed=3-S,this.lastTotal=3,S===1?this.lastChar[0]=f[f.length-1]:(this.lastChar[0]=f[f.length-2],this.lastChar[1]=f[f.length-1]),f.toString("base64",c,f.length-S))}function D(f){var c=f&&f.length?this.write(f):"";return this.lastNeed?c+this.lastChar.toString("base64",0,3-this.lastNeed):c}function m(f){return f.toString(this.encoding)}function _(f){return f&&f.length?this.write(f):""}return fr}function en(n){"@babel/helpers - typeof";return en=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},en(n)}function ua(n,e){if(en(n)!="object"||!n)return n;var t=n[Symbol.toPrimitive];if(t!==void 0){var r=t.call(n,e);if(en(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(n)}function fa(n){var e=ua(n,"string");return en(e)=="symbol"?e:e+""}function ge(n,e,t){return(e=fa(e))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Ni(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),t.push.apply(t,r)}return t}function Ke(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Ni(Object(t),!0).forEach(function(r){ge(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):Ni(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}var cr={exports:{}},dr={exports:{}},hr,Ci;function Ie(){if(Ci)return hr;Ci=1;class n extends Error{constructor(t){if(!Array.isArray(t))throw new TypeError(`Expected input to be an Array, got ${typeof t}`);let r="";for(let i=0;i<t.length;i++)r+=`    ${t[i].stack}
`;super(r),this.name="AggregateError",this.errors=t}}return hr={AggregateError:n,ArrayIsArray(e){return Array.isArray(e)},ArrayPrototypeIncludes(e,t){return e.includes(t)},ArrayPrototypeIndexOf(e,t){return e.indexOf(t)},ArrayPrototypeJoin(e,t){return e.join(t)},ArrayPrototypeMap(e,t){return e.map(t)},ArrayPrototypePop(e,t){return e.pop(t)},ArrayPrototypePush(e,t){return e.push(t)},ArrayPrototypeSlice(e,t,r){return e.slice(t,r)},Error,FunctionPrototypeCall(e,t,...r){return e.call(t,...r)},FunctionPrototypeSymbolHasInstance(e,t){return Function.prototype[Symbol.hasInstance].call(e,t)},MathFloor:Math.floor,Number,NumberIsInteger:Number.isInteger,NumberIsNaN:Number.isNaN,NumberMAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER,NumberMIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER,NumberParseInt:Number.parseInt,ObjectDefineProperties(e,t){return Object.defineProperties(e,t)},ObjectDefineProperty(e,t,r){return Object.defineProperty(e,t,r)},ObjectGetOwnPropertyDescriptor(e,t){return Object.getOwnPropertyDescriptor(e,t)},ObjectKeys(e){return Object.keys(e)},ObjectSetPrototypeOf(e,t){return Object.setPrototypeOf(e,t)},Promise,PromisePrototypeCatch(e,t){return e.catch(t)},PromisePrototypeThen(e,t,r){return e.then(t,r)},PromiseReject(e){return Promise.reject(e)},PromiseResolve(e){return Promise.resolve(e)},ReflectApply:Reflect.apply,RegExpPrototypeTest(e,t){return e.test(t)},SafeSet:Set,String,StringPrototypeSlice(e,t,r){return e.slice(t,r)},StringPrototypeToLowerCase(e){return e.toLowerCase()},StringPrototypeToUpperCase(e){return e.toUpperCase()},StringPrototypeTrim(e){return e.trim()},Symbol,SymbolFor:Symbol.for,SymbolAsyncIterator:Symbol.asyncIterator,SymbolHasInstance:Symbol.hasInstance,SymbolIterator:Symbol.iterator,SymbolDispose:Symbol.dispose||Symbol("Symbol.dispose"),SymbolAsyncDispose:Symbol.asyncDispose||Symbol("Symbol.asyncDispose"),TypedArrayPrototypeSet(e,t,r){return e.set(t,r)},Boolean,Uint8Array},hr}var gr={exports:{}},pr,Li;function Yo(){return Li||(Li=1,pr={format(n,...e){return n.replace(/%([sdifj])/g,function(...[t,r]){const i=e.shift();return r==="f"?i.toFixed(6):r==="j"?JSON.stringify(i):r==="s"&&typeof i=="object"?`${i.constructor!==Object?i.constructor.name:""} {}`.trim():i.toString()})},inspect(n){switch(typeof n){case"string":if(n.includes("'"))if(n.includes('"')){if(!n.includes("`")&&!n.includes("${"))return`\`${n}\``}else return`"${n}"`;return`'${n}'`;case"number":return isNaN(n)?"NaN":Object.is(n,-0)?String(n):n;case"bigint":return`${String(n)}n`;case"boolean":case"undefined":return String(n);case"object":return"{}"}}}),pr}var wr,Pi;function De(){if(Pi)return wr;Pi=1;const{format:n,inspect:e}=Yo(),{AggregateError:t}=Ie(),r=globalThis.AggregateError||t,i=Symbol("kIsNodeError"),o=["string","function","number","object","Function","Object","boolean","bigint","symbol"],s=/^([A-Z][a-z0-9]*)+$/,u="__node_internal_",h={};function b(_,f){if(!_)throw new h.ERR_INTERNAL_ASSERTION(f)}function C(_){let f="",c=_.length;const S=_[0]==="-"?1:0;for(;c>=S+4;c-=3)f=`_${_.slice(c-3,c)}${f}`;return`${_.slice(0,c)}${f}`}function O(_,f,c){if(typeof f=="function")return b(f.length<=c.length,`Code: ${_}; The provided arguments length (${c.length}) does not match the required ones (${f.length}).`),f(...c);const S=(f.match(/%[dfijoOs]/g)||[]).length;return b(S===c.length,`Code: ${_}; The provided arguments length (${c.length}) does not match the required ones (${S}).`),c.length===0?f:n(f,...c)}function A(_,f,c){c||(c=Error);class S extends c{constructor(...N){super(O(_,f,N))}toString(){return`${this.name} [${_}]: ${this.message}`}}Object.defineProperties(S.prototype,{name:{value:c.name,writable:!0,enumerable:!1,configurable:!0},toString:{value(){return`${this.name} [${_}]: ${this.message}`},writable:!0,enumerable:!1,configurable:!0}}),S.prototype.code=_,S.prototype[i]=!0,h[_]=S}function j(_){const f=u+_.name;return Object.defineProperty(_,"name",{value:f}),_}function D(_,f){if(_&&f&&_!==f){if(Array.isArray(f.errors))return f.errors.push(_),f;const c=new r([f,_],f.message);return c.code=f.code,c}return _||f}class m extends Error{constructor(f="The operation was aborted",c=void 0){if(c!==void 0&&typeof c!="object")throw new h.ERR_INVALID_ARG_TYPE("options","Object",c);super(f,c),this.code="ABORT_ERR",this.name="AbortError"}}return A("ERR_ASSERTION","%s",Error),A("ERR_INVALID_ARG_TYPE",(_,f,c)=>{b(typeof _=="string","'name' must be a string"),Array.isArray(f)||(f=[f]);let S="The ";_.endsWith(" argument")?S+=`${_} `:S+=`"${_}" ${_.includes(".")?"property":"argument"} `,S+="must be ";const B=[],N=[],F=[];for(const a of f)b(typeof a=="string","All expected entries have to be of type string"),o.includes(a)?B.push(a.toLowerCase()):s.test(a)?N.push(a):(b(a!=="object",'The value "object" should be written as "Object"'),F.push(a));if(N.length>0){const a=B.indexOf("object");a!==-1&&(B.splice(B,a,1),N.push("Object"))}if(B.length>0){switch(B.length){case 1:S+=`of type ${B[0]}`;break;case 2:S+=`one of type ${B[0]} or ${B[1]}`;break;default:{const a=B.pop();S+=`one of type ${B.join(", ")}, or ${a}`}}(N.length>0||F.length>0)&&(S+=" or ")}if(N.length>0){switch(N.length){case 1:S+=`an instance of ${N[0]}`;break;case 2:S+=`an instance of ${N[0]} or ${N[1]}`;break;default:{const a=N.pop();S+=`an instance of ${N.join(", ")}, or ${a}`}}F.length>0&&(S+=" or ")}switch(F.length){case 0:break;case 1:F[0].toLowerCase()!==F[0]&&(S+="an "),S+=`${F[0]}`;break;case 2:S+=`one of ${F[0]} or ${F[1]}`;break;default:{const a=F.pop();S+=`one of ${F.join(", ")}, or ${a}`}}if(c==null)S+=`. Received ${c}`;else if(typeof c=="function"&&c.name)S+=`. Received function ${c.name}`;else if(typeof c=="object"){var G;if((G=c.constructor)!==null&&G!==void 0&&G.name)S+=`. Received an instance of ${c.constructor.name}`;else{const a=e(c,{depth:-1});S+=`. Received ${a}`}}else{let a=e(c,{colors:!1});a.length>25&&(a=`${a.slice(0,25)}...`),S+=`. Received type ${typeof c} (${a})`}return S},TypeError),A("ERR_INVALID_ARG_VALUE",(_,f,c="is invalid")=>{let S=e(f);return S.length>128&&(S=S.slice(0,128)+"..."),`The ${_.includes(".")?"property":"argument"} '${_}' ${c}. Received ${S}`},TypeError),A("ERR_INVALID_RETURN_VALUE",(_,f,c)=>{var S;const B=c!=null&&(S=c.constructor)!==null&&S!==void 0&&S.name?`instance of ${c.constructor.name}`:`type ${typeof c}`;return`Expected ${_} to be returned from the "${f}" function but got ${B}.`},TypeError),A("ERR_MISSING_ARGS",(..._)=>{b(_.length>0,"At least one arg needs to be specified");let f;const c=_.length;switch(_=(Array.isArray(_)?_:[_]).map(S=>`"${S}"`).join(" or "),c){case 1:f+=`The ${_[0]} argument`;break;case 2:f+=`The ${_[0]} and ${_[1]} arguments`;break;default:{const S=_.pop();f+=`The ${_.join(", ")}, and ${S} arguments`}break}return`${f} must be specified`},TypeError),A("ERR_OUT_OF_RANGE",(_,f,c)=>{b(f,'Missing "range" argument');let S;if(Number.isInteger(c)&&Math.abs(c)>2**32)S=C(String(c));else if(typeof c=="bigint"){S=String(c);const B=BigInt(2)**BigInt(32);(c>B||c<-B)&&(S=C(S)),S+="n"}else S=e(c);return`The value of "${_}" is out of range. It must be ${f}. Received ${S}`},RangeError),A("ERR_MULTIPLE_CALLBACK","Callback called multiple times",Error),A("ERR_METHOD_NOT_IMPLEMENTED","The %s method is not implemented",Error),A("ERR_STREAM_ALREADY_FINISHED","Cannot call %s after a stream was finished",Error),A("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable",Error),A("ERR_STREAM_DESTROYED","Cannot call %s after a stream was destroyed",Error),A("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),A("ERR_STREAM_PREMATURE_CLOSE","Premature close",Error),A("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF",Error),A("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event",Error),A("ERR_STREAM_WRITE_AFTER_END","write after end",Error),A("ERR_UNKNOWN_ENCODING","Unknown encoding: %s",TypeError),wr={AbortError:m,aggregateTwoErrors:j(D),hideStackFrames:j,codes:h},wr}var Vt={exports:{}},Mi;function tn(){if(Mi)return Vt.exports;Mi=1;const{AbortController:n,AbortSignal:e}=typeof self<"u"?self:typeof window<"u"?window:void 0;return Vt.exports=n,Vt.exports.AbortSignal=e,Vt.exports.default=n,Vt.exports}var Bi;function We(){return Bi||(Bi=1,function(n){const e=Ct(),{format:t,inspect:r}=Yo(),{codes:{ERR_INVALID_ARG_TYPE:i}}=De(),{kResistStopPropagation:o,AggregateError:s,SymbolDispose:u}=Ie(),h=globalThis.AbortSignal||tn().AbortSignal,b=globalThis.AbortController||tn().AbortController,C=Object.getPrototypeOf(async function(){}).constructor,O=globalThis.Blob||e.Blob,A=typeof O<"u"?function(_){return _ instanceof O}:function(_){return!1},j=(m,_)=>{if(m!==void 0&&(m===null||typeof m!="object"||!("aborted"in m)))throw new i(_,"AbortSignal",m)},D=(m,_)=>{if(typeof m!="function")throw new i(_,"Function",m)};n.exports={AggregateError:s,kEmptyObject:Object.freeze({}),once(m){let _=!1;return function(...f){_||(_=!0,m.apply(this,f))}},createDeferredPromise:function(){let m,_;return{promise:new Promise((c,S)=>{m=c,_=S}),resolve:m,reject:_}},promisify(m){return new Promise((_,f)=>{m((c,...S)=>c?f(c):_(...S))})},debuglog(){return function(){}},format:t,inspect:r,types:{isAsyncFunction(m){return m instanceof C},isArrayBufferView(m){return ArrayBuffer.isView(m)}},isBlob:A,deprecate(m,_){return m},addAbortListener:rn().addAbortListener||function(_,f){if(_===void 0)throw new i("signal","AbortSignal",_);j(_,"signal"),D(f,"listener");let c;return _.aborted?queueMicrotask(()=>f()):(_.addEventListener("abort",f,{__proto__:null,once:!0,[o]:!0}),c=()=>{_.removeEventListener("abort",f)}),{__proto__:null,[u](){var S;(S=c)===null||S===void 0||S()}}},AbortSignalAny:h.any||function(_){if(_.length===1)return _[0];const f=new b,c=()=>f.abort();return _.forEach(S=>{j(S,"signals"),S.addEventListener("abort",c,{once:!0})}),f.signal.addEventListener("abort",()=>{_.forEach(S=>S.removeEventListener("abort",c))},{once:!0}),f.signal}},n.exports.promisify.custom=Symbol.for("nodejs.util.promisify.custom")}(gr)),gr.exports}var Rn={},br,Di;function on(){if(Di)return br;Di=1;const{ArrayIsArray:n,ArrayPrototypeIncludes:e,ArrayPrototypeJoin:t,ArrayPrototypeMap:r,NumberIsInteger:i,NumberIsNaN:o,NumberMAX_SAFE_INTEGER:s,NumberMIN_SAFE_INTEGER:u,NumberParseInt:h,ObjectPrototypeHasOwnProperty:b,RegExpPrototypeExec:C,String:O,StringPrototypeToUpperCase:A,StringPrototypeTrim:j}=Ie(),{hideStackFrames:D,codes:{ERR_SOCKET_BAD_PORT:m,ERR_INVALID_ARG_TYPE:_,ERR_INVALID_ARG_VALUE:f,ERR_OUT_OF_RANGE:c,ERR_UNKNOWN_SIGNAL:S}}=De(),{normalizeEncoding:B}=We(),{isAsyncFunction:N,isArrayBufferView:F}=We().types,G={};function a(q){return q===(q|0)}function w(q){return q===q>>>0}const l=/^[0-7]+$/,d="must be a 32-bit unsigned integer or an octal string";function y(q,X,ne){if(typeof q>"u"&&(q=ne),typeof q=="string"){if(C(l,q)===null)throw new f(X,q,d);q=h(q,8)}return p(q,X),q}const g=D((q,X,ne=u,Z=s)=>{if(typeof q!="number")throw new _(X,"number",q);if(!i(q))throw new c(X,"an integer",q);if(q<ne||q>Z)throw new c(X,`>= ${ne} && <= ${Z}`,q)}),L=D((q,X,ne=-2147483648,Z=2147483647)=>{if(typeof q!="number")throw new _(X,"number",q);if(!i(q))throw new c(X,"an integer",q);if(q<ne||q>Z)throw new c(X,`>= ${ne} && <= ${Z}`,q)}),p=D((q,X,ne=!1)=>{if(typeof q!="number")throw new _(X,"number",q);if(!i(q))throw new c(X,"an integer",q);const Z=ne?1:0,we=4294967295;if(q<Z||q>we)throw new c(X,`>= ${Z} && <= ${we}`,q)});function R(q,X){if(typeof q!="string")throw new _(X,"string",q)}function P(q,X,ne=void 0,Z){if(typeof q!="number")throw new _(X,"number",q);if(ne!=null&&q<ne||Z!=null&&q>Z||(ne!=null||Z!=null)&&o(q))throw new c(X,`${ne!=null?`>= ${ne}`:""}${ne!=null&&Z!=null?" && ":""}${Z!=null?`<= ${Z}`:""}`,q)}const E=D((q,X,ne)=>{if(!e(ne,q)){const we="must be one of: "+t(r(ne,Le=>typeof Le=="string"?`'${Le}'`:O(Le)),", ");throw new f(X,q,we)}});function k(q,X){if(typeof q!="boolean")throw new _(X,"boolean",q)}function v(q,X,ne){return q==null||!b(q,X)?ne:q[X]}const $=D((q,X,ne=null)=>{const Z=v(ne,"allowArray",!1),we=v(ne,"allowFunction",!1);if(!v(ne,"nullable",!1)&&q===null||!Z&&n(q)||typeof q!="object"&&(!we||typeof q!="function"))throw new _(X,"Object",q)}),J=D((q,X)=>{if(q!=null&&typeof q!="object"&&typeof q!="function")throw new _(X,"a dictionary",q)}),H=D((q,X,ne=0)=>{if(!n(q))throw new _(X,"Array",q);if(q.length<ne){const Z=`must be longer than ${ne}`;throw new f(X,q,Z)}});function ee(q,X){H(q,X);for(let ne=0;ne<q.length;ne++)R(q[ne],`${X}[${ne}]`)}function Q(q,X){H(q,X);for(let ne=0;ne<q.length;ne++)k(q[ne],`${X}[${ne}]`)}function le(q,X){H(q,X);for(let ne=0;ne<q.length;ne++){const Z=q[ne],we=`${X}[${ne}]`;if(Z==null)throw new _(we,"AbortSignal",Z);ce(Z,we)}}function ue(q,X="signal"){if(R(q,X),G[q]===void 0)throw G[A(q)]!==void 0?new S(q+" (signals must use all capital letters)"):new S(q)}const K=D((q,X="buffer")=>{if(!F(q))throw new _(X,["Buffer","TypedArray","DataView"],q)});function Y(q,X){const ne=B(X),Z=q.length;if(ne==="hex"&&Z%2!==0)throw new f("encoding",X,`is invalid for data of length ${Z}`)}function oe(q,X="Port",ne=!0){if(typeof q!="number"&&typeof q!="string"||typeof q=="string"&&j(q).length===0||+q!==+q>>>0||q>65535||q===0&&!ne)throw new m(X,q,ne);return q|0}const ce=D((q,X)=>{if(q!==void 0&&(q===null||typeof q!="object"||!("aborted"in q)))throw new _(X,"AbortSignal",q)}),fe=D((q,X)=>{if(typeof q!="function")throw new _(X,"Function",q)}),M=D((q,X)=>{if(typeof q!="function"||N(q))throw new _(X,"Function",q)}),U=D((q,X)=>{if(q!==void 0)throw new _(X,"undefined",q)});function z(q,X,ne){if(!e(ne,q))throw new _(X,`('${t(ne,"|")}')`,q)}const re=/^(?:<[^>]*>)(?:\s*;\s*[^;"\s]+(?:=(")?[^;"\s]*\1)?)*$/;function se(q,X){if(typeof q>"u"||!C(re,q))throw new f(X,q,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}function ie(q){if(typeof q=="string")return se(q,"hints"),q;if(n(q)){const X=q.length;let ne="";if(X===0)return ne;for(let Z=0;Z<X;Z++){const we=q[Z];se(we,"hints"),ne+=we,Z!==X-1&&(ne+=", ")}return ne}throw new f("hints",q,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}return br={isInt32:a,isUint32:w,parseFileMode:y,validateArray:H,validateStringArray:ee,validateBooleanArray:Q,validateAbortSignalArray:le,validateBoolean:k,validateBuffer:K,validateDictionary:J,validateEncoding:Y,validateFunction:fe,validateInt32:L,validateInteger:g,validateNumber:P,validateObject:$,validateOneOf:E,validatePlainFunction:M,validatePort:oe,validateSignalName:ue,validateString:R,validateUint32:p,validateUndefined:U,validateUnion:z,validateAbortSignal:ce,validateLinkHeaderValue:ie},br}var In={exports:{}},yr={exports:{}},ki;function Lt(){if(ki)return yr.exports;ki=1;var n=yr.exports={},e,t;function r(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?e=setTimeout:e=r}catch{e=r}try{typeof clearTimeout=="function"?t=clearTimeout:t=i}catch{t=i}})();function o(m){if(e===setTimeout)return setTimeout(m,0);if((e===r||!e)&&setTimeout)return e=setTimeout,setTimeout(m,0);try{return e(m,0)}catch{try{return e.call(null,m,0)}catch{return e.call(this,m,0)}}}function s(m){if(t===clearTimeout)return clearTimeout(m);if((t===i||!t)&&clearTimeout)return t=clearTimeout,clearTimeout(m);try{return t(m)}catch{try{return t.call(null,m)}catch{return t.call(this,m)}}}var u=[],h=!1,b,C=-1;function O(){!h||!b||(h=!1,b.length?u=b.concat(u):C=-1,u.length&&A())}function A(){if(!h){var m=o(O);h=!0;for(var _=u.length;_;){for(b=u,u=[];++C<_;)b&&b[C].run();C=-1,_=u.length}b=null,h=!1,s(m)}}n.nextTick=function(m){var _=new Array(arguments.length-1);if(arguments.length>1)for(var f=1;f<arguments.length;f++)_[f-1]=arguments[f];u.push(new j(m,_)),u.length===1&&!h&&o(A)};function j(m,_){this.fun=m,this.array=_}j.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={};function D(){}return n.on=D,n.addListener=D,n.once=D,n.off=D,n.removeListener=D,n.removeAllListeners=D,n.emit=D,n.prependListener=D,n.prependOnceListener=D,n.listeners=function(m){return[]},n.binding=function(m){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(m){throw new Error("process.chdir is not supported")},n.umask=function(){return 0},yr.exports}var _r,Ui;function dt(){if(Ui)return _r;Ui=1;const{SymbolAsyncIterator:n,SymbolIterator:e,SymbolFor:t}=Ie(),r=t("nodejs.stream.destroyed"),i=t("nodejs.stream.errored"),o=t("nodejs.stream.readable"),s=t("nodejs.stream.writable"),u=t("nodejs.stream.disturbed"),h=t("nodejs.webstream.isClosedPromise"),b=t("nodejs.webstream.controllerErrorFunction");function C(v,$=!1){var J;return!!(v&&typeof v.pipe=="function"&&typeof v.on=="function"&&(!$||typeof v.pause=="function"&&typeof v.resume=="function")&&(!v._writableState||((J=v._readableState)===null||J===void 0?void 0:J.readable)!==!1)&&(!v._writableState||v._readableState))}function O(v){var $;return!!(v&&typeof v.write=="function"&&typeof v.on=="function"&&(!v._readableState||(($=v._writableState)===null||$===void 0?void 0:$.writable)!==!1))}function A(v){return!!(v&&typeof v.pipe=="function"&&v._readableState&&typeof v.on=="function"&&typeof v.write=="function")}function j(v){return v&&(v._readableState||v._writableState||typeof v.write=="function"&&typeof v.on=="function"||typeof v.pipe=="function"&&typeof v.on=="function")}function D(v){return!!(v&&!j(v)&&typeof v.pipeThrough=="function"&&typeof v.getReader=="function"&&typeof v.cancel=="function")}function m(v){return!!(v&&!j(v)&&typeof v.getWriter=="function"&&typeof v.abort=="function")}function _(v){return!!(v&&!j(v)&&typeof v.readable=="object"&&typeof v.writable=="object")}function f(v){return D(v)||m(v)||_(v)}function c(v,$){return v==null?!1:$===!0?typeof v[n]=="function":$===!1?typeof v[e]=="function":typeof v[n]=="function"||typeof v[e]=="function"}function S(v){if(!j(v))return null;const $=v._writableState,J=v._readableState,H=$||J;return!!(v.destroyed||v[r]||H!=null&&H.destroyed)}function B(v){if(!O(v))return null;if(v.writableEnded===!0)return!0;const $=v._writableState;return $!=null&&$.errored?!1:typeof $?.ended!="boolean"?null:$.ended}function N(v,$){if(!O(v))return null;if(v.writableFinished===!0)return!0;const J=v._writableState;return J!=null&&J.errored?!1:typeof J?.finished!="boolean"?null:!!(J.finished||$===!1&&J.ended===!0&&J.length===0)}function F(v){if(!C(v))return null;if(v.readableEnded===!0)return!0;const $=v._readableState;return!$||$.errored?!1:typeof $?.ended!="boolean"?null:$.ended}function G(v,$){if(!C(v))return null;const J=v._readableState;return J!=null&&J.errored?!1:typeof J?.endEmitted!="boolean"?null:!!(J.endEmitted||$===!1&&J.ended===!0&&J.length===0)}function a(v){return v&&v[o]!=null?v[o]:typeof v?.readable!="boolean"?null:S(v)?!1:C(v)&&v.readable&&!G(v)}function w(v){return v&&v[s]!=null?v[s]:typeof v?.writable!="boolean"?null:S(v)?!1:O(v)&&v.writable&&!B(v)}function l(v,$){return j(v)?S(v)?!0:!($?.readable!==!1&&a(v)||$?.writable!==!1&&w(v)):null}function d(v){var $,J;return j(v)?v.writableErrored?v.writableErrored:($=(J=v._writableState)===null||J===void 0?void 0:J.errored)!==null&&$!==void 0?$:null:null}function y(v){var $,J;return j(v)?v.readableErrored?v.readableErrored:($=(J=v._readableState)===null||J===void 0?void 0:J.errored)!==null&&$!==void 0?$:null:null}function g(v){if(!j(v))return null;if(typeof v.closed=="boolean")return v.closed;const $=v._writableState,J=v._readableState;return typeof $?.closed=="boolean"||typeof J?.closed=="boolean"?$?.closed||J?.closed:typeof v._closed=="boolean"&&L(v)?v._closed:null}function L(v){return typeof v._closed=="boolean"&&typeof v._defaultKeepAlive=="boolean"&&typeof v._removedConnection=="boolean"&&typeof v._removedContLen=="boolean"}function p(v){return typeof v._sent100=="boolean"&&L(v)}function R(v){var $;return typeof v._consuming=="boolean"&&typeof v._dumped=="boolean"&&(($=v.req)===null||$===void 0?void 0:$.upgradeOrConnect)===void 0}function P(v){if(!j(v))return null;const $=v._writableState,J=v._readableState,H=$||J;return!H&&p(v)||!!(H&&H.autoDestroy&&H.emitClose&&H.closed===!1)}function E(v){var $;return!!(v&&(($=v[u])!==null&&$!==void 0?$:v.readableDidRead||v.readableAborted))}function k(v){var $,J,H,ee,Q,le,ue,K,Y,oe;return!!(v&&(($=(J=(H=(ee=(Q=(le=v[i])!==null&&le!==void 0?le:v.readableErrored)!==null&&Q!==void 0?Q:v.writableErrored)!==null&&ee!==void 0?ee:(ue=v._readableState)===null||ue===void 0?void 0:ue.errorEmitted)!==null&&H!==void 0?H:(K=v._writableState)===null||K===void 0?void 0:K.errorEmitted)!==null&&J!==void 0?J:(Y=v._readableState)===null||Y===void 0?void 0:Y.errored)!==null&&$!==void 0?$:!((oe=v._writableState)===null||oe===void 0)&&oe.errored))}return _r={isDestroyed:S,kIsDestroyed:r,isDisturbed:E,kIsDisturbed:u,isErrored:k,kIsErrored:i,isReadable:a,kIsReadable:o,kIsClosedPromise:h,kControllerErrorFunction:b,kIsWritable:s,isClosed:g,isDuplexNodeStream:A,isFinished:l,isIterable:c,isReadableNodeStream:C,isReadableStream:D,isReadableEnded:F,isReadableFinished:G,isReadableErrored:y,isNodeStream:j,isWebStream:f,isWritable:w,isWritableNodeStream:O,isWritableStream:m,isWritableEnded:B,isWritableFinished:N,isWritableErrored:d,isServerRequest:R,isServerResponse:p,willEmitClose:P,isTransformStream:_},_r}var ji;function _t(){if(ji)return In.exports;ji=1;const n=Lt(),{AbortError:e,codes:t}=De(),{ERR_INVALID_ARG_TYPE:r,ERR_STREAM_PREMATURE_CLOSE:i}=t,{kEmptyObject:o,once:s}=We(),{validateAbortSignal:u,validateFunction:h,validateObject:b,validateBoolean:C}=on(),{Promise:O,PromisePrototypeThen:A,SymbolDispose:j}=Ie(),{isClosed:D,isReadable:m,isReadableNodeStream:_,isReadableStream:f,isReadableFinished:c,isReadableErrored:S,isWritable:B,isWritableNodeStream:N,isWritableStream:F,isWritableFinished:G,isWritableErrored:a,isNodeStream:w,willEmitClose:l,kIsClosedPromise:d}=dt();let y;function g(E){return E.setHeader&&typeof E.abort=="function"}const L=()=>{};function p(E,k,v){var $,J;if(arguments.length===2?(v=k,k=o):k==null?k=o:b(k,"options"),h(v,"callback"),u(k.signal,"options.signal"),v=s(v),f(E)||F(E))return R(E,k,v);if(!w(E))throw new r("stream",["ReadableStream","WritableStream","Stream"],E);const H=($=k.readable)!==null&&$!==void 0?$:_(E),ee=(J=k.writable)!==null&&J!==void 0?J:N(E),Q=E._writableState,le=E._readableState,ue=()=>{E.writable||oe()};let K=l(E)&&_(E)===H&&N(E)===ee,Y=G(E,!1);const oe=()=>{Y=!0,E.destroyed&&(K=!1),!(K&&(!E.readable||H))&&(!H||ce)&&v.call(E)};let ce=c(E,!1);const fe=()=>{ce=!0,E.destroyed&&(K=!1),!(K&&(!E.writable||ee))&&(!ee||Y)&&v.call(E)},M=q=>{v.call(E,q)};let U=D(E);const z=()=>{U=!0;const q=a(E)||S(E);if(q&&typeof q!="boolean")return v.call(E,q);if(H&&!ce&&_(E,!0)&&!c(E,!1))return v.call(E,new i);if(ee&&!Y&&!G(E,!1))return v.call(E,new i);v.call(E)},re=()=>{U=!0;const q=a(E)||S(E);if(q&&typeof q!="boolean")return v.call(E,q);v.call(E)},se=()=>{E.req.on("finish",oe)};g(E)?(E.on("complete",oe),K||E.on("abort",z),E.req?se():E.on("request",se)):ee&&!Q&&(E.on("end",ue),E.on("close",ue)),!K&&typeof E.aborted=="boolean"&&E.on("aborted",z),E.on("end",fe),E.on("finish",oe),k.error!==!1&&E.on("error",M),E.on("close",z),U?n.nextTick(z):Q!=null&&Q.errorEmitted||le!=null&&le.errorEmitted?K||n.nextTick(re):(!H&&(!K||m(E))&&(Y||B(E)===!1)||!ee&&(!K||B(E))&&(ce||m(E)===!1)||le&&E.req&&E.aborted)&&n.nextTick(re);const ie=()=>{v=L,E.removeListener("aborted",z),E.removeListener("complete",oe),E.removeListener("abort",z),E.removeListener("request",se),E.req&&E.req.removeListener("finish",oe),E.removeListener("end",ue),E.removeListener("close",ue),E.removeListener("finish",oe),E.removeListener("end",fe),E.removeListener("error",M),E.removeListener("close",z)};if(k.signal&&!U){const q=()=>{const X=v;ie(),X.call(E,new e(void 0,{cause:k.signal.reason}))};if(k.signal.aborted)n.nextTick(q);else{y=y||We().addAbortListener;const X=y(k.signal,q),ne=v;v=s((...Z)=>{X[j](),ne.apply(E,Z)})}}return ie}function R(E,k,v){let $=!1,J=L;if(k.signal)if(J=()=>{$=!0,v.call(E,new e(void 0,{cause:k.signal.reason}))},k.signal.aborted)n.nextTick(J);else{y=y||We().addAbortListener;const ee=y(k.signal,J),Q=v;v=s((...le)=>{ee[j](),Q.apply(E,le)})}const H=(...ee)=>{$||n.nextTick(()=>v.apply(E,ee))};return A(E[d].promise,H,H),L}function P(E,k){var v;let $=!1;return k===null&&(k=o),(v=k)!==null&&v!==void 0&&v.cleanup&&(C(k.cleanup,"cleanup"),$=k.cleanup),new O((J,H)=>{const ee=p(E,k,Q=>{$&&ee(),Q?H(Q):J()})})}return In.exports=p,In.exports.finished=P,In.exports}var mr,qi;function jt(){if(qi)return mr;qi=1;const n=Lt(),{aggregateTwoErrors:e,codes:{ERR_MULTIPLE_CALLBACK:t},AbortError:r}=De(),{Symbol:i}=Ie(),{kIsDestroyed:o,isDestroyed:s,isFinished:u,isServerRequest:h}=dt(),b=i("kDestroy"),C=i("kConstruct");function O(l,d,y){l&&(l.stack,d&&!d.errored&&(d.errored=l),y&&!y.errored&&(y.errored=l))}function A(l,d){const y=this._readableState,g=this._writableState,L=g||y;return g!=null&&g.destroyed||y!=null&&y.destroyed?(typeof d=="function"&&d(),this):(O(l,g,y),g&&(g.destroyed=!0),y&&(y.destroyed=!0),L.constructed?j(this,l,d):this.once(b,function(p){j(this,e(p,l),d)}),this)}function j(l,d,y){let g=!1;function L(p){if(g)return;g=!0;const R=l._readableState,P=l._writableState;O(p,P,R),P&&(P.closed=!0),R&&(R.closed=!0),typeof y=="function"&&y(p),p?n.nextTick(D,l,p):n.nextTick(m,l)}try{l._destroy(d||null,L)}catch(p){L(p)}}function D(l,d){_(l,d),m(l)}function m(l){const d=l._readableState,y=l._writableState;y&&(y.closeEmitted=!0),d&&(d.closeEmitted=!0),(y!=null&&y.emitClose||d!=null&&d.emitClose)&&l.emit("close")}function _(l,d){const y=l._readableState,g=l._writableState;g!=null&&g.errorEmitted||y!=null&&y.errorEmitted||(g&&(g.errorEmitted=!0),y&&(y.errorEmitted=!0),l.emit("error",d))}function f(){const l=this._readableState,d=this._writableState;l&&(l.constructed=!0,l.closed=!1,l.closeEmitted=!1,l.destroyed=!1,l.errored=null,l.errorEmitted=!1,l.reading=!1,l.ended=l.readable===!1,l.endEmitted=l.readable===!1),d&&(d.constructed=!0,d.destroyed=!1,d.closed=!1,d.closeEmitted=!1,d.errored=null,d.errorEmitted=!1,d.finalCalled=!1,d.prefinished=!1,d.ended=d.writable===!1,d.ending=d.writable===!1,d.finished=d.writable===!1)}function c(l,d,y){const g=l._readableState,L=l._writableState;if(L!=null&&L.destroyed||g!=null&&g.destroyed)return this;g!=null&&g.autoDestroy||L!=null&&L.autoDestroy?l.destroy(d):d&&(d.stack,L&&!L.errored&&(L.errored=d),g&&!g.errored&&(g.errored=d),y?n.nextTick(_,l,d):_(l,d))}function S(l,d){if(typeof l._construct!="function")return;const y=l._readableState,g=l._writableState;y&&(y.constructed=!1),g&&(g.constructed=!1),l.once(C,d),!(l.listenerCount(C)>1)&&n.nextTick(B,l)}function B(l){let d=!1;function y(g){if(d){c(l,g??new t);return}d=!0;const L=l._readableState,p=l._writableState,R=p||L;L&&(L.constructed=!0),p&&(p.constructed=!0),R.destroyed?l.emit(b,g):g?c(l,g,!0):n.nextTick(N,l)}try{l._construct(g=>{n.nextTick(y,g)})}catch(g){n.nextTick(y,g)}}function N(l){l.emit(C)}function F(l){return l?.setHeader&&typeof l.abort=="function"}function G(l){l.emit("close")}function a(l,d){l.emit("error",d),n.nextTick(G,l)}function w(l,d){!l||s(l)||(!d&&!u(l)&&(d=new r),h(l)?(l.socket=null,l.destroy(d)):F(l)?l.abort():F(l.req)?l.req.abort():typeof l.destroy=="function"?l.destroy(d):typeof l.close=="function"?l.close():d?n.nextTick(a,l,d):n.nextTick(G,l),l.destroyed||(l[o]=!0))}return mr={construct:S,destroyer:w,destroy:A,undestroy:f,errorOrDestroy:c},mr}var Er,Wi;function ri(){if(Wi)return Er;Wi=1;const{ArrayIsArray:n,ObjectSetPrototypeOf:e}=Ie(),{EventEmitter:t}=rn();function r(o){t.call(this,o)}e(r.prototype,t.prototype),e(r,t),r.prototype.pipe=function(o,s){const u=this;function h(m){o.writable&&o.write(m)===!1&&u.pause&&u.pause()}u.on("data",h);function b(){u.readable&&u.resume&&u.resume()}o.on("drain",b),!o._isStdio&&(!s||s.end!==!1)&&(u.on("end",O),u.on("close",A));let C=!1;function O(){C||(C=!0,o.end())}function A(){C||(C=!0,typeof o.destroy=="function"&&o.destroy())}function j(m){D(),t.listenerCount(this,"error")===0&&this.emit("error",m)}i(u,"error",j),i(o,"error",j);function D(){u.removeListener("data",h),o.removeListener("drain",b),u.removeListener("end",O),u.removeListener("close",A),u.removeListener("error",j),o.removeListener("error",j),u.removeListener("end",D),u.removeListener("close",D),o.removeListener("close",D)}return u.on("end",D),u.on("close",D),o.on("close",D),o.emit("pipe",u),o};function i(o,s,u){if(typeof o.prependListener=="function")return o.prependListener(s,u);!o._events||!o._events[s]?o.on(s,u):n(o._events[s])?o._events[s].unshift(u):o._events[s]=[u,o._events[s]]}return Er={Stream:r,prependListener:i},Er}var Sr={exports:{}},Fi;function kn(){return Fi||(Fi=1,function(n){const{SymbolDispose:e}=Ie(),{AbortError:t,codes:r}=De(),{isNodeStream:i,isWebStream:o,kControllerErrorFunction:s}=dt(),u=_t(),{ERR_INVALID_ARG_TYPE:h}=r;let b;const C=(O,A)=>{if(typeof O!="object"||!("aborted"in O))throw new h(A,"AbortSignal",O)};n.exports.addAbortSignal=function(A,j){if(C(A,"signal"),!i(j)&&!o(j))throw new h("stream",["ReadableStream","WritableStream","Stream"],j);return n.exports.addAbortSignalNoValidate(A,j)},n.exports.addAbortSignalNoValidate=function(O,A){if(typeof O!="object"||!("aborted"in O))return A;const j=i(A)?()=>{A.destroy(new t(void 0,{cause:O.reason}))}:()=>{A[s](new t(void 0,{cause:O.reason}))};if(O.aborted)j();else{b=b||We().addAbortListener;const D=b(O,j);u(A,D[e])}return A}}(Sr)),Sr.exports}var vr,$i;function ca(){if($i)return vr;$i=1;const{StringPrototypeSlice:n,SymbolIterator:e,TypedArrayPrototypeSet:t,Uint8Array:r}=Ie(),{Buffer:i}=Ct(),{inspect:o}=We();return vr=class{constructor(){this.head=null,this.tail=null,this.length=0}push(u){const h={data:u,next:null};this.length>0?this.tail.next=h:this.head=h,this.tail=h,++this.length}unshift(u){const h={data:u,next:this.head};this.length===0&&(this.tail=h),this.head=h,++this.length}shift(){if(this.length===0)return;const u=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,u}clear(){this.head=this.tail=null,this.length=0}join(u){if(this.length===0)return"";let h=this.head,b=""+h.data;for(;(h=h.next)!==null;)b+=u+h.data;return b}concat(u){if(this.length===0)return i.alloc(0);const h=i.allocUnsafe(u>>>0);let b=this.head,C=0;for(;b;)t(h,b.data,C),C+=b.data.length,b=b.next;return h}consume(u,h){const b=this.head.data;if(u<b.length){const C=b.slice(0,u);return this.head.data=b.slice(u),C}return u===b.length?this.shift():h?this._getString(u):this._getBuffer(u)}first(){return this.head.data}*[e](){for(let u=this.head;u;u=u.next)yield u.data}_getString(u){let h="",b=this.head,C=0;do{const O=b.data;if(u>O.length)h+=O,u-=O.length;else{u===O.length?(h+=O,++C,b.next?this.head=b.next:this.head=this.tail=null):(h+=n(O,0,u),this.head=b,b.data=n(O,u));break}++C}while((b=b.next)!==null);return this.length-=C,h}_getBuffer(u){const h=i.allocUnsafe(u),b=u;let C=this.head,O=0;do{const A=C.data;if(u>A.length)t(h,A,b-u),u-=A.length;else{u===A.length?(t(h,A,b-u),++O,C.next?this.head=C.next:this.head=this.tail=null):(t(h,new r(A.buffer,A.byteOffset,u),b-u),this.head=C,C.data=A.slice(u));break}++O}while((C=C.next)!==null);return this.length-=O,h}[Symbol.for("nodejs.util.inspect.custom")](u,h){return o(this,{...h,depth:0,customInspect:!1})}},vr}var Ar,Hi;function Un(){if(Hi)return Ar;Hi=1;const{MathFloor:n,NumberIsInteger:e}=Ie(),{validateInteger:t}=on(),{ERR_INVALID_ARG_VALUE:r}=De().codes;let i=16*1024,o=16;function s(C,O,A){return C.highWaterMark!=null?C.highWaterMark:O?C[A]:null}function u(C){return C?o:i}function h(C,O){t(O,"value",0),C?o=O:i=O}function b(C,O,A,j){const D=s(O,j,A);if(D!=null){if(!e(D)||D<0){const m=j?`options.${A}`:"options.highWaterMark";throw new r(m,D)}return n(D)}return u(C.objectMode)}return Ar={getHighWaterMark:b,getDefaultHighWaterMark:u,setDefaultHighWaterMark:h},Ar}var Rr,zi;function Jo(){if(zi)return Rr;zi=1;const n=Lt(),{PromisePrototypeThen:e,SymbolAsyncIterator:t,SymbolIterator:r}=Ie(),{Buffer:i}=Ct(),{ERR_INVALID_ARG_TYPE:o,ERR_STREAM_NULL_VALUES:s}=De().codes;function u(h,b,C){let O;if(typeof b=="string"||b instanceof i)return new h({objectMode:!0,...C,read(){this.push(b),this.push(null)}});let A;if(b&&b[t])A=!0,O=b[t]();else if(b&&b[r])A=!1,O=b[r]();else throw new o("iterable",["Iterable"],b);const j=new h({objectMode:!0,highWaterMark:1,...C});let D=!1;j._read=function(){D||(D=!0,_())},j._destroy=function(f,c){e(m(f),()=>n.nextTick(c,f),S=>n.nextTick(c,S||f))};async function m(f){const c=f!=null,S=typeof O.throw=="function";if(c&&S){const{value:B,done:N}=await O.throw(f);if(await B,N)return}if(typeof O.return=="function"){const{value:B}=await O.return();await B}}async function _(){for(;;){try{const{value:f,done:c}=A?await O.next():O.next();if(c)j.push(null);else{const S=f&&typeof f.then=="function"?await f:f;if(S===null)throw D=!1,new s;if(j.push(S))continue;D=!1}}catch(f){j.destroy(f)}break}}return j}return Rr=u,Rr}var Ir,Gi;function jn(){if(Gi)return Ir;Gi=1;const n=Lt(),{ArrayPrototypeIndexOf:e,NumberIsInteger:t,NumberIsNaN:r,NumberParseInt:i,ObjectDefineProperties:o,ObjectKeys:s,ObjectSetPrototypeOf:u,Promise:h,SafeSet:b,SymbolAsyncDispose:C,SymbolAsyncIterator:O,Symbol:A}=Ie();Ir=Z,Z.ReadableState=ne;const{EventEmitter:j}=rn(),{Stream:D,prependListener:m}=ri(),{Buffer:_}=Ct(),{addAbortSignal:f}=kn(),c=_t();let S=We().debuglog("stream",I=>{S=I});const B=ca(),N=jt(),{getHighWaterMark:F,getDefaultHighWaterMark:G}=Un(),{aggregateTwoErrors:a,codes:{ERR_INVALID_ARG_TYPE:w,ERR_METHOD_NOT_IMPLEMENTED:l,ERR_OUT_OF_RANGE:d,ERR_STREAM_PUSH_AFTER_EOF:y,ERR_STREAM_UNSHIFT_AFTER_END_EVENT:g},AbortError:L}=De(),{validateObject:p}=on(),R=A("kPaused"),{StringDecoder:P}=la(),E=Jo();u(Z.prototype,D.prototype),u(Z,D);const k=()=>{},{errorOrDestroy:v}=N,$=1,J=2,H=4,ee=8,Q=16,le=32,ue=64,K=128,Y=256,oe=512,ce=1024,fe=2048,M=4096,U=8192,z=16384,re=32768,se=65536,ie=1<<17,q=1<<18;function X(I){return{enumerable:!1,get(){return(this.state&I)!==0},set(x){x?this.state|=I:this.state&=~I}}}o(ne.prototype,{objectMode:X($),ended:X(J),endEmitted:X(H),reading:X(ee),constructed:X(Q),sync:X(le),needReadable:X(ue),emittedReadable:X(K),readableListening:X(Y),resumeScheduled:X(oe),errorEmitted:X(ce),emitClose:X(fe),autoDestroy:X(M),destroyed:X(U),closed:X(z),closeEmitted:X(re),multiAwaitDrain:X(se),readingMore:X(ie),dataEmitted:X(q)});function ne(I,x,te){typeof te!="boolean"&&(te=x instanceof ct()),this.state=fe|M|Q|le,I&&I.objectMode&&(this.state|=$),te&&I&&I.readableObjectMode&&(this.state|=$),this.highWaterMark=I?F(this,I,"readableHighWaterMark",te):G(!1),this.buffer=new B,this.length=0,this.pipes=[],this.flowing=null,this[R]=null,I&&I.emitClose===!1&&(this.state&=~fe),I&&I.autoDestroy===!1&&(this.state&=~M),this.errored=null,this.defaultEncoding=I&&I.defaultEncoding||"utf8",this.awaitDrainWriters=null,this.decoder=null,this.encoding=null,I&&I.encoding&&(this.decoder=new P(I.encoding),this.encoding=I.encoding)}function Z(I){if(!(this instanceof Z))return new Z(I);const x=this instanceof ct();this._readableState=new ne(I,this,x),I&&(typeof I.read=="function"&&(this._read=I.read),typeof I.destroy=="function"&&(this._destroy=I.destroy),typeof I.construct=="function"&&(this._construct=I.construct),I.signal&&!x&&f(I.signal,this)),D.call(this,I),N.construct(this,()=>{this._readableState.needReadable&&Me(this,this._readableState)})}Z.prototype.destroy=N.destroy,Z.prototype._undestroy=N.undestroy,Z.prototype._destroy=function(I,x){x(I)},Z.prototype[j.captureRejectionSymbol]=function(I){this.destroy(I)},Z.prototype[C]=function(){let I;return this.destroyed||(I=this.readableEnded?null:new L,this.destroy(I)),new h((x,te)=>c(this,ae=>ae&&ae!==I?te(ae):x(null)))},Z.prototype.push=function(I,x){return we(this,I,x,!1)},Z.prototype.unshift=function(I,x){return we(this,I,x,!0)};function we(I,x,te,ae){S("readableAddChunk",x);const de=I._readableState;let Oe;if((de.state&$)===0&&(typeof x=="string"?(te=te||de.defaultEncoding,de.encoding!==te&&(ae&&de.encoding?x=_.from(x,te).toString(de.encoding):(x=_.from(x,te),te=""))):x instanceof _?te="":D._isUint8Array(x)?(x=D._uint8ArrayToBuffer(x),te=""):x!=null&&(Oe=new w("chunk",["string","Buffer","Uint8Array"],x))),Oe)v(I,Oe);else if(x===null)de.state&=~ee,Pe(I,de);else if((de.state&$)!==0||x&&x.length>0)if(ae)if((de.state&H)!==0)v(I,new g);else{if(de.destroyed||de.errored)return!1;Le(I,de,x,!0)}else if(de.ended)v(I,new y);else{if(de.destroyed||de.errored)return!1;de.state&=~ee,de.decoder&&!te?(x=de.decoder.write(x),de.objectMode||x.length!==0?Le(I,de,x,!1):Me(I,de)):Le(I,de,x,!1)}else ae||(de.state&=~ee,Me(I,de));return!de.ended&&(de.length<de.highWaterMark||de.length===0)}function Le(I,x,te,ae){x.flowing&&x.length===0&&!x.sync&&I.listenerCount("data")>0?((x.state&se)!==0?x.awaitDrainWriters.clear():x.awaitDrainWriters=null,x.dataEmitted=!0,I.emit("data",te)):(x.length+=x.objectMode?1:te.length,ae?x.buffer.unshift(te):x.buffer.push(te),(x.state&ue)!==0&&Ye(I)),Me(I,x)}Z.prototype.isPaused=function(){const I=this._readableState;return I[R]===!0||I.flowing===!1},Z.prototype.setEncoding=function(I){const x=new P(I);this._readableState.decoder=x,this._readableState.encoding=this._readableState.decoder.encoding;const te=this._readableState.buffer;let ae="";for(const de of te)ae+=x.write(de);return te.clear(),ae!==""&&te.push(ae),this._readableState.length=ae.length,this};const ye=1073741824;function Be(I){if(I>ye)throw new d("size","<= 1GiB",I);return I--,I|=I>>>1,I|=I>>>2,I|=I>>>4,I|=I>>>8,I|=I>>>16,I++,I}function xe(I,x){return I<=0||x.length===0&&x.ended?0:(x.state&$)!==0?1:r(I)?x.flowing&&x.length?x.buffer.first().length:x.length:I<=x.length?I:x.ended?x.length:0}Z.prototype.read=function(I){S("read",I),I===void 0?I=NaN:t(I)||(I=i(I,10));const x=this._readableState,te=I;if(I>x.highWaterMark&&(x.highWaterMark=Be(I)),I!==0&&(x.state&=~K),I===0&&x.needReadable&&((x.highWaterMark!==0?x.length>=x.highWaterMark:x.length>0)||x.ended))return S("read: emitReadable",x.length,x.ended),x.length===0&&x.ended?Wt(this):Ye(this),null;if(I=xe(I,x),I===0&&x.ended)return x.length===0&&Wt(this),null;let ae=(x.state&ue)!==0;if(S("need readable",ae),(x.length===0||x.length-I<x.highWaterMark)&&(ae=!0,S("length less than watermark",ae)),x.ended||x.reading||x.destroyed||x.errored||!x.constructed)ae=!1,S("reading, ended or constructing",ae);else if(ae){S("do read"),x.state|=ee|le,x.length===0&&(x.state|=ue);try{this._read(x.highWaterMark)}catch(Oe){v(this,Oe)}x.state&=~le,x.reading||(I=xe(te,x))}let de;return I>0?de=ht(I,x):de=null,de===null?(x.needReadable=x.length<=x.highWaterMark,I=0):(x.length-=I,x.multiAwaitDrain?x.awaitDrainWriters.clear():x.awaitDrainWriters=null),x.length===0&&(x.ended||(x.needReadable=!0),te!==I&&x.ended&&Wt(this)),de!==null&&!x.errorEmitted&&!x.closeEmitted&&(x.dataEmitted=!0,this.emit("data",de)),de};function Pe(I,x){if(S("onEofChunk"),!x.ended){if(x.decoder){const te=x.decoder.end();te&&te.length&&(x.buffer.push(te),x.length+=x.objectMode?1:te.length)}x.ended=!0,x.sync?Ye(I):(x.needReadable=!1,x.emittedReadable=!0,mt(I))}}function Ye(I){const x=I._readableState;S("emitReadable",x.needReadable,x.emittedReadable),x.needReadable=!1,x.emittedReadable||(S("emitReadable",x.flowing),x.emittedReadable=!0,n.nextTick(mt,I))}function mt(I){const x=I._readableState;S("emitReadable_",x.destroyed,x.length,x.ended),!x.destroyed&&!x.errored&&(x.length||x.ended)&&(I.emit("readable"),x.emittedReadable=!1),x.needReadable=!x.flowing&&!x.ended&&x.length<=x.highWaterMark,qt(I)}function Me(I,x){!x.readingMore&&x.constructed&&(x.readingMore=!0,n.nextTick(un,I,x))}function un(I,x){for(;!x.reading&&!x.ended&&(x.length<x.highWaterMark||x.flowing&&x.length===0);){const te=x.length;if(S("maybeReadMore read 0"),I.read(0),te===x.length)break}x.readingMore=!1}Z.prototype._read=function(I){throw new l("_read()")},Z.prototype.pipe=function(I,x){const te=this,ae=this._readableState;ae.pipes.length===1&&(ae.multiAwaitDrain||(ae.multiAwaitDrain=!0,ae.awaitDrainWriters=new b(ae.awaitDrainWriters?[ae.awaitDrainWriters]:[]))),ae.pipes.push(I),S("pipe count=%d opts=%j",ae.pipes.length,x);const Oe=(!x||x.end!==!1)&&I!==n.stdout&&I!==n.stderr?gn:Et;ae.endEmitted?n.nextTick(Oe):te.once("end",Oe),I.on("unpipe",ke);function ke(ot,Je){S("onunpipe"),ot===te&&Je&&Je.hasUnpiped===!1&&(Je.hasUnpiped=!0,Jn())}function gn(){S("onend"),I.end()}let Ue,pn=!1;function Jn(){S("cleanup"),I.removeListener("close",et),I.removeListener("finish",Ht),Ue&&I.removeListener("drain",Ue),I.removeListener("error",$t),I.removeListener("unpipe",ke),te.removeListener("end",gn),te.removeListener("end",Et),te.removeListener("data",bn),pn=!0,Ue&&ae.awaitDrainWriters&&(!I._writableState||I._writableState.needDrain)&&Ue()}function wn(){pn||(ae.pipes.length===1&&ae.pipes[0]===I?(S("false write response, pause",0),ae.awaitDrainWriters=I,ae.multiAwaitDrain=!1):ae.pipes.length>1&&ae.pipes.includes(I)&&(S("false write response, pause",ae.awaitDrainWriters.size),ae.awaitDrainWriters.add(I)),te.pause()),Ue||(Ue=Vn(te,I),I.on("drain",Ue))}te.on("data",bn);function bn(ot){S("ondata");const Je=I.write(ot);S("dest.write",Je),Je===!1&&wn()}function $t(ot){if(S("onerror",ot),Et(),I.removeListener("error",$t),I.listenerCount("error")===0){const Je=I._writableState||I._readableState;Je&&!Je.errorEmitted?v(I,ot):I.emit("error",ot)}}m(I,"error",$t);function et(){I.removeListener("finish",Ht),Et()}I.once("close",et);function Ht(){S("onfinish"),I.removeListener("close",et),Et()}I.once("finish",Ht);function Et(){S("unpipe"),te.unpipe(I)}return I.emit("pipe",te),I.writableNeedDrain===!0?wn():ae.flowing||(S("pipe resume"),te.resume()),I};function Vn(I,x){return function(){const ae=I._readableState;ae.awaitDrainWriters===x?(S("pipeOnDrain",1),ae.awaitDrainWriters=null):ae.multiAwaitDrain&&(S("pipeOnDrain",ae.awaitDrainWriters.size),ae.awaitDrainWriters.delete(x)),(!ae.awaitDrainWriters||ae.awaitDrainWriters.size===0)&&I.listenerCount("data")&&I.resume()}}Z.prototype.unpipe=function(I){const x=this._readableState,te={hasUnpiped:!1};if(x.pipes.length===0)return this;if(!I){const de=x.pipes;x.pipes=[],this.pause();for(let Oe=0;Oe<de.length;Oe++)de[Oe].emit("unpipe",this,{hasUnpiped:!1});return this}const ae=e(x.pipes,I);return ae===-1?this:(x.pipes.splice(ae,1),x.pipes.length===0&&this.pause(),I.emit("unpipe",this,te),this)},Z.prototype.on=function(I,x){const te=D.prototype.on.call(this,I,x),ae=this._readableState;return I==="data"?(ae.readableListening=this.listenerCount("readable")>0,ae.flowing!==!1&&this.resume()):I==="readable"&&!ae.endEmitted&&!ae.readableListening&&(ae.readableListening=ae.needReadable=!0,ae.flowing=!1,ae.emittedReadable=!1,S("on readable",ae.length,ae.reading),ae.length?Ye(this):ae.reading||n.nextTick(rt,this)),te},Z.prototype.addListener=Z.prototype.on,Z.prototype.removeListener=function(I,x){const te=D.prototype.removeListener.call(this,I,x);return I==="readable"&&n.nextTick(fn,this),te},Z.prototype.off=Z.prototype.removeListener,Z.prototype.removeAllListeners=function(I){const x=D.prototype.removeAllListeners.apply(this,arguments);return(I==="readable"||I===void 0)&&n.nextTick(fn,this),x};function fn(I){const x=I._readableState;x.readableListening=I.listenerCount("readable")>0,x.resumeScheduled&&x[R]===!1?x.flowing=!0:I.listenerCount("data")>0?I.resume():x.readableListening||(x.flowing=null)}function rt(I){S("readable nexttick read 0"),I.read(0)}Z.prototype.resume=function(){const I=this._readableState;return I.flowing||(S("resume"),I.flowing=!I.readableListening,Pt(this,I)),I[R]=!1,this};function Pt(I,x){x.resumeScheduled||(x.resumeScheduled=!0,n.nextTick(Kn,I,x))}function Kn(I,x){S("resume",x.reading),x.reading||I.read(0),x.resumeScheduled=!1,I.emit("resume"),qt(I),x.flowing&&!x.reading&&I.read(0)}Z.prototype.pause=function(){return S("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(S("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState[R]=!0,this};function qt(I){const x=I._readableState;for(S("flow",x.flowing);x.flowing&&I.read()!==null;);}Z.prototype.wrap=function(I){let x=!1;I.on("data",ae=>{!this.push(ae)&&I.pause&&(x=!0,I.pause())}),I.on("end",()=>{this.push(null)}),I.on("error",ae=>{v(this,ae)}),I.on("close",()=>{this.destroy()}),I.on("destroy",()=>{this.destroy()}),this._read=()=>{x&&I.resume&&(x=!1,I.resume())};const te=s(I);for(let ae=1;ae<te.length;ae++){const de=te[ae];this[de]===void 0&&typeof I[de]=="function"&&(this[de]=I[de].bind(I))}return this},Z.prototype[O]=function(){return cn(this)},Z.prototype.iterator=function(I){return I!==void 0&&p(I,"options"),cn(this,I)};function cn(I,x){typeof I.read!="function"&&(I=Z.wrap(I,{objectMode:!0}));const te=dn(I,x);return te.stream=I,te}async function*dn(I,x){let te=k;function ae(ke){this===I?(te(),te=k):te=ke}I.on("readable",ae);let de;const Oe=c(I,{writable:!1},ke=>{de=ke?a(de,ke):null,te(),te=k});try{for(;;){const ke=I.destroyed?null:I.read();if(ke!==null)yield ke;else{if(de)throw de;if(de===null)return;await new h(ae)}}}catch(ke){throw de=a(de,ke),de}finally{(de||x?.destroyOnReturn!==!1)&&(de===void 0||I._readableState.autoDestroy)?N.destroyer(I,null):(I.off("readable",ae),Oe())}}o(Z.prototype,{readable:{__proto__:null,get(){const I=this._readableState;return!!I&&I.readable!==!1&&!I.destroyed&&!I.errorEmitted&&!I.endEmitted},set(I){this._readableState&&(this._readableState.readable=!!I)}},readableDidRead:{__proto__:null,enumerable:!1,get:function(){return this._readableState.dataEmitted}},readableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._readableState.readable!==!1&&(this._readableState.destroyed||this._readableState.errored)&&!this._readableState.endEmitted)}},readableHighWaterMark:{__proto__:null,enumerable:!1,get:function(){return this._readableState.highWaterMark}},readableBuffer:{__proto__:null,enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}},readableFlowing:{__proto__:null,enumerable:!1,get:function(){return this._readableState.flowing},set:function(I){this._readableState&&(this._readableState.flowing=I)}},readableLength:{__proto__:null,enumerable:!1,get(){return this._readableState.length}},readableObjectMode:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.objectMode:!1}},readableEncoding:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.encoding:null}},errored:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.errored:null}},closed:{__proto__:null,get(){return this._readableState?this._readableState.closed:!1}},destroyed:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.destroyed:!1},set(I){this._readableState&&(this._readableState.destroyed=I)}},readableEnded:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.endEmitted:!1}}}),o(ne.prototype,{pipesCount:{__proto__:null,get(){return this.pipes.length}},paused:{__proto__:null,get(){return this[R]!==!1},set(I){this[R]=!!I}}}),Z._fromList=ht;function ht(I,x){if(x.length===0)return null;let te;return x.objectMode?te=x.buffer.shift():!I||I>=x.length?(x.decoder?te=x.buffer.join(""):x.buffer.length===1?te=x.buffer.first():te=x.buffer.concat(x.length),x.buffer.clear()):te=x.buffer.consume(I,x.decoder),te}function Wt(I){const x=I._readableState;S("endReadable",x.endEmitted),x.endEmitted||(x.ended=!0,n.nextTick(it,x,I))}function it(I,x){if(S("endReadableNT",I.endEmitted,I.length),!I.errored&&!I.closeEmitted&&!I.endEmitted&&I.length===0){if(I.endEmitted=!0,x.emit("end"),x.writable&&x.allowHalfOpen===!1)n.nextTick(Yn,x);else if(I.autoDestroy){const te=x._writableState;(!te||te.autoDestroy&&(te.finished||te.writable===!1))&&x.destroy()}}}function Yn(I){I.writable&&!I.writableEnded&&!I.destroyed&&I.end()}Z.from=function(I,x){return E(Z,I,x)};let Ft;function hn(){return Ft===void 0&&(Ft={}),Ft}return Z.fromWeb=function(I,x){return hn().newStreamReadableFromReadableStream(I,x)},Z.toWeb=function(I,x){return hn().newReadableStreamFromStreamReadable(I,x)},Z.wrap=function(I,x){var te,ae;return new Z({objectMode:(te=(ae=I.readableObjectMode)!==null&&ae!==void 0?ae:I.objectMode)!==null&&te!==void 0?te:!0,...x,destroy(de,Oe){N.destroyer(I,de),Oe(de)}}).wrap(I)},Ir}var Tr,Vi;function ii(){if(Vi)return Tr;Vi=1;const n=Lt(),{ArrayPrototypeSlice:e,Error:t,FunctionPrototypeSymbolHasInstance:r,ObjectDefineProperty:i,ObjectDefineProperties:o,ObjectSetPrototypeOf:s,StringPrototypeToLowerCase:u,Symbol:h,SymbolHasInstance:b}=Ie();Tr=p,p.WritableState=g;const{EventEmitter:C}=rn(),O=ri().Stream,{Buffer:A}=Ct(),j=jt(),{addAbortSignal:D}=kn(),{getHighWaterMark:m,getDefaultHighWaterMark:_}=Un(),{ERR_INVALID_ARG_TYPE:f,ERR_METHOD_NOT_IMPLEMENTED:c,ERR_MULTIPLE_CALLBACK:S,ERR_STREAM_CANNOT_PIPE:B,ERR_STREAM_DESTROYED:N,ERR_STREAM_ALREADY_FINISHED:F,ERR_STREAM_NULL_VALUES:G,ERR_STREAM_WRITE_AFTER_END:a,ERR_UNKNOWN_ENCODING:w}=De().codes,{errorOrDestroy:l}=j;s(p.prototype,O.prototype),s(p,O);function d(){}const y=h("kOnFinished");function g(M,U,z){typeof z!="boolean"&&(z=U instanceof ct()),this.objectMode=!!(M&&M.objectMode),z&&(this.objectMode=this.objectMode||!!(M&&M.writableObjectMode)),this.highWaterMark=M?m(this,M,"writableHighWaterMark",z):_(!1),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;const re=!!(M&&M.decodeStrings===!1);this.decodeStrings=!re,this.defaultEncoding=M&&M.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=v.bind(void 0,U),this.writecb=null,this.writelen=0,this.afterWriteTickInfo=null,L(this),this.pendingcb=0,this.constructed=!0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!M||M.emitClose!==!1,this.autoDestroy=!M||M.autoDestroy!==!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this[y]=[]}function L(M){M.buffered=[],M.bufferedIndex=0,M.allBuffers=!0,M.allNoop=!0}g.prototype.getBuffer=function(){return e(this.buffered,this.bufferedIndex)},i(g.prototype,"bufferedRequestCount",{__proto__:null,get(){return this.buffered.length-this.bufferedIndex}});function p(M){const U=this instanceof ct();if(!U&&!r(p,this))return new p(M);this._writableState=new g(M,this,U),M&&(typeof M.write=="function"&&(this._write=M.write),typeof M.writev=="function"&&(this._writev=M.writev),typeof M.destroy=="function"&&(this._destroy=M.destroy),typeof M.final=="function"&&(this._final=M.final),typeof M.construct=="function"&&(this._construct=M.construct),M.signal&&D(M.signal,this)),O.call(this,M),j.construct(this,()=>{const z=this._writableState;z.writing||ee(this,z),K(this,z)})}i(p,b,{__proto__:null,value:function(M){return r(this,M)?!0:this!==p?!1:M&&M._writableState instanceof g}}),p.prototype.pipe=function(){l(this,new B)};function R(M,U,z,re){const se=M._writableState;if(typeof z=="function")re=z,z=se.defaultEncoding;else{if(!z)z=se.defaultEncoding;else if(z!=="buffer"&&!A.isEncoding(z))throw new w(z);typeof re!="function"&&(re=d)}if(U===null)throw new G;if(!se.objectMode)if(typeof U=="string")se.decodeStrings!==!1&&(U=A.from(U,z),z="buffer");else if(U instanceof A)z="buffer";else if(O._isUint8Array(U))U=O._uint8ArrayToBuffer(U),z="buffer";else throw new f("chunk",["string","Buffer","Uint8Array"],U);let ie;return se.ending?ie=new a:se.destroyed&&(ie=new N("write")),ie?(n.nextTick(re,ie),l(M,ie,!0),ie):(se.pendingcb++,P(M,se,U,z,re))}p.prototype.write=function(M,U,z){return R(this,M,U,z)===!0},p.prototype.cork=function(){this._writableState.corked++},p.prototype.uncork=function(){const M=this._writableState;M.corked&&(M.corked--,M.writing||ee(this,M))},p.prototype.setDefaultEncoding=function(U){if(typeof U=="string"&&(U=u(U)),!A.isEncoding(U))throw new w(U);return this._writableState.defaultEncoding=U,this};function P(M,U,z,re,se){const ie=U.objectMode?1:z.length;U.length+=ie;const q=U.length<U.highWaterMark;return q||(U.needDrain=!0),U.writing||U.corked||U.errored||!U.constructed?(U.buffered.push({chunk:z,encoding:re,callback:se}),U.allBuffers&&re!=="buffer"&&(U.allBuffers=!1),U.allNoop&&se!==d&&(U.allNoop=!1)):(U.writelen=ie,U.writecb=se,U.writing=!0,U.sync=!0,M._write(z,re,U.onwrite),U.sync=!1),q&&!U.errored&&!U.destroyed}function E(M,U,z,re,se,ie,q){U.writelen=re,U.writecb=q,U.writing=!0,U.sync=!0,U.destroyed?U.onwrite(new N("write")):z?M._writev(se,U.onwrite):M._write(se,ie,U.onwrite),U.sync=!1}function k(M,U,z,re){--U.pendingcb,re(z),H(U),l(M,z)}function v(M,U){const z=M._writableState,re=z.sync,se=z.writecb;if(typeof se!="function"){l(M,new S);return}z.writing=!1,z.writecb=null,z.length-=z.writelen,z.writelen=0,U?(U.stack,z.errored||(z.errored=U),M._readableState&&!M._readableState.errored&&(M._readableState.errored=U),re?n.nextTick(k,M,z,U,se):k(M,z,U,se)):(z.buffered.length>z.bufferedIndex&&ee(M,z),re?z.afterWriteTickInfo!==null&&z.afterWriteTickInfo.cb===se?z.afterWriteTickInfo.count++:(z.afterWriteTickInfo={count:1,cb:se,stream:M,state:z},n.nextTick($,z.afterWriteTickInfo)):J(M,z,1,se))}function $({stream:M,state:U,count:z,cb:re}){return U.afterWriteTickInfo=null,J(M,U,z,re)}function J(M,U,z,re){for(!U.ending&&!M.destroyed&&U.length===0&&U.needDrain&&(U.needDrain=!1,M.emit("drain"));z-- >0;)U.pendingcb--,re();U.destroyed&&H(U),K(M,U)}function H(M){if(M.writing)return;for(let se=M.bufferedIndex;se<M.buffered.length;++se){var U;const{chunk:ie,callback:q}=M.buffered[se],X=M.objectMode?1:ie.length;M.length-=X,q((U=M.errored)!==null&&U!==void 0?U:new N("write"))}const z=M[y].splice(0);for(let se=0;se<z.length;se++){var re;z[se]((re=M.errored)!==null&&re!==void 0?re:new N("end"))}L(M)}function ee(M,U){if(U.corked||U.bufferProcessing||U.destroyed||!U.constructed)return;const{buffered:z,bufferedIndex:re,objectMode:se}=U,ie=z.length-re;if(!ie)return;let q=re;if(U.bufferProcessing=!0,ie>1&&M._writev){U.pendingcb-=ie-1;const X=U.allNoop?d:Z=>{for(let we=q;we<z.length;++we)z[we].callback(Z)},ne=U.allNoop&&q===0?z:e(z,q);ne.allBuffers=U.allBuffers,E(M,U,!0,U.length,ne,"",X),L(U)}else{do{const{chunk:X,encoding:ne,callback:Z}=z[q];z[q++]=null;const we=se?1:X.length;E(M,U,!1,we,X,ne,Z)}while(q<z.length&&!U.writing);q===z.length?L(U):q>256?(z.splice(0,q),U.bufferedIndex=0):U.bufferedIndex=q}U.bufferProcessing=!1}p.prototype._write=function(M,U,z){if(this._writev)this._writev([{chunk:M,encoding:U}],z);else throw new c("_write()")},p.prototype._writev=null,p.prototype.end=function(M,U,z){const re=this._writableState;typeof M=="function"?(z=M,M=null,U=null):typeof U=="function"&&(z=U,U=null);let se;if(M!=null){const ie=R(this,M,U);ie instanceof t&&(se=ie)}return re.corked&&(re.corked=1,this.uncork()),se||(!re.errored&&!re.ending?(re.ending=!0,K(this,re,!0),re.ended=!0):re.finished?se=new F("end"):re.destroyed&&(se=new N("end"))),typeof z=="function"&&(se||re.finished?n.nextTick(z,se):re[y].push(z)),this};function Q(M){return M.ending&&!M.destroyed&&M.constructed&&M.length===0&&!M.errored&&M.buffered.length===0&&!M.finished&&!M.writing&&!M.errorEmitted&&!M.closeEmitted}function le(M,U){let z=!1;function re(se){if(z){l(M,se??S());return}if(z=!0,U.pendingcb--,se){const ie=U[y].splice(0);for(let q=0;q<ie.length;q++)ie[q](se);l(M,se,U.sync)}else Q(U)&&(U.prefinished=!0,M.emit("prefinish"),U.pendingcb++,n.nextTick(Y,M,U))}U.sync=!0,U.pendingcb++;try{M._final(re)}catch(se){re(se)}U.sync=!1}function ue(M,U){!U.prefinished&&!U.finalCalled&&(typeof M._final=="function"&&!U.destroyed?(U.finalCalled=!0,le(M,U)):(U.prefinished=!0,M.emit("prefinish")))}function K(M,U,z){Q(U)&&(ue(M,U),U.pendingcb===0&&(z?(U.pendingcb++,n.nextTick((re,se)=>{Q(se)?Y(re,se):se.pendingcb--},M,U)):Q(U)&&(U.pendingcb++,Y(M,U))))}function Y(M,U){U.pendingcb--,U.finished=!0;const z=U[y].splice(0);for(let re=0;re<z.length;re++)z[re]();if(M.emit("finish"),U.autoDestroy){const re=M._readableState;(!re||re.autoDestroy&&(re.endEmitted||re.readable===!1))&&M.destroy()}}o(p.prototype,{closed:{__proto__:null,get(){return this._writableState?this._writableState.closed:!1}},destroyed:{__proto__:null,get(){return this._writableState?this._writableState.destroyed:!1},set(M){this._writableState&&(this._writableState.destroyed=M)}},writable:{__proto__:null,get(){const M=this._writableState;return!!M&&M.writable!==!1&&!M.destroyed&&!M.errored&&!M.ending&&!M.ended},set(M){this._writableState&&(this._writableState.writable=!!M)}},writableFinished:{__proto__:null,get(){return this._writableState?this._writableState.finished:!1}},writableObjectMode:{__proto__:null,get(){return this._writableState?this._writableState.objectMode:!1}},writableBuffer:{__proto__:null,get(){return this._writableState&&this._writableState.getBuffer()}},writableEnded:{__proto__:null,get(){return this._writableState?this._writableState.ending:!1}},writableNeedDrain:{__proto__:null,get(){const M=this._writableState;return M?!M.destroyed&&!M.ending&&M.needDrain:!1}},writableHighWaterMark:{__proto__:null,get(){return this._writableState&&this._writableState.highWaterMark}},writableCorked:{__proto__:null,get(){return this._writableState?this._writableState.corked:0}},writableLength:{__proto__:null,get(){return this._writableState&&this._writableState.length}},errored:{__proto__:null,enumerable:!1,get(){return this._writableState?this._writableState.errored:null}},writableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._writableState.writable!==!1&&(this._writableState.destroyed||this._writableState.errored)&&!this._writableState.finished)}}});const oe=j.destroy;p.prototype.destroy=function(M,U){const z=this._writableState;return!z.destroyed&&(z.bufferedIndex<z.buffered.length||z[y].length)&&n.nextTick(H,z),oe.call(this,M,U),this},p.prototype._undestroy=j.undestroy,p.prototype._destroy=function(M,U){U(M)},p.prototype[C.captureRejectionSymbol]=function(M){this.destroy(M)};let ce;function fe(){return ce===void 0&&(ce={}),ce}return p.fromWeb=function(M,U){return fe().newStreamWritableFromWritableStream(M,U)},p.toWeb=function(M){return fe().newWritableStreamFromStreamWritable(M)},Tr}var xr,Ki;function da(){if(Ki)return xr;Ki=1;const n=Lt(),e=Ct(),{isReadable:t,isWritable:r,isIterable:i,isNodeStream:o,isReadableNodeStream:s,isWritableNodeStream:u,isDuplexNodeStream:h,isReadableStream:b,isWritableStream:C}=dt(),O=_t(),{AbortError:A,codes:{ERR_INVALID_ARG_TYPE:j,ERR_INVALID_RETURN_VALUE:D}}=De(),{destroyer:m}=jt(),_=ct(),f=jn(),c=ii(),{createDeferredPromise:S}=We(),B=Jo(),N=globalThis.Blob||e.Blob,F=typeof N<"u"?function(g){return g instanceof N}:function(g){return!1},G=globalThis.AbortController||tn().AbortController,{FunctionPrototypeCall:a}=Ie();class w extends _{constructor(g){super(g),g?.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),g?.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}}xr=function y(g,L){if(h(g))return g;if(s(g))return d({readable:g});if(u(g))return d({writable:g});if(o(g))return d({writable:!1,readable:!1});if(b(g))return d({readable:f.fromWeb(g)});if(C(g))return d({writable:c.fromWeb(g)});if(typeof g=="function"){const{value:R,write:P,final:E,destroy:k}=l(g);if(i(R))return B(w,R,{objectMode:!0,write:P,final:E,destroy:k});const v=R?.then;if(typeof v=="function"){let $;const J=a(v,R,H=>{if(H!=null)throw new D("nully","body",H)},H=>{m($,H)});return $=new w({objectMode:!0,readable:!1,write:P,final(H){E(async()=>{try{await J,n.nextTick(H,null)}catch(ee){n.nextTick(H,ee)}})},destroy:k})}throw new D("Iterable, AsyncIterable or AsyncFunction",L,R)}if(F(g))return y(g.arrayBuffer());if(i(g))return B(w,g,{objectMode:!0,writable:!1});if(b(g?.readable)&&C(g?.writable))return w.fromWeb(g);if(typeof g?.writable=="object"||typeof g?.readable=="object"){const R=g!=null&&g.readable?s(g?.readable)?g?.readable:y(g.readable):void 0,P=g!=null&&g.writable?u(g?.writable)?g?.writable:y(g.writable):void 0;return d({readable:R,writable:P})}const p=g?.then;if(typeof p=="function"){let R;return a(p,g,P=>{P!=null&&R.push(P),R.push(null)},P=>{m(R,P)}),R=new w({objectMode:!0,writable:!1,read(){}})}throw new j(L,["Blob","ReadableStream","WritableStream","Stream","Iterable","AsyncIterable","Function","{ readable, writable } pair","Promise"],g)};function l(y){let{promise:g,resolve:L}=S();const p=new G,R=p.signal;return{value:y(async function*(){for(;;){const E=g;g=null;const{chunk:k,done:v,cb:$}=await E;if(n.nextTick($),v)return;if(R.aborted)throw new A(void 0,{cause:R.reason});({promise:g,resolve:L}=S()),yield k}}(),{signal:R}),write(E,k,v){const $=L;L=null,$({chunk:E,done:!1,cb:v})},final(E){const k=L;L=null,k({done:!0,cb:E})},destroy(E,k){p.abort(),k(E)}}}function d(y){const g=y.readable&&typeof y.readable.read!="function"?f.wrap(y.readable):y.readable,L=y.writable;let p=!!t(g),R=!!r(L),P,E,k,v,$;function J(H){const ee=v;v=null,ee?ee(H):H&&$.destroy(H)}return $=new w({readableObjectMode:!!(g!=null&&g.readableObjectMode),writableObjectMode:!!(L!=null&&L.writableObjectMode),readable:p,writable:R}),R&&(O(L,H=>{R=!1,H&&m(g,H),J(H)}),$._write=function(H,ee,Q){L.write(H,ee)?Q():P=Q},$._final=function(H){L.end(),E=H},L.on("drain",function(){if(P){const H=P;P=null,H()}}),L.on("finish",function(){if(E){const H=E;E=null,H()}})),p&&(O(g,H=>{p=!1,H&&m(g,H),J(H)}),g.on("readable",function(){if(k){const H=k;k=null,H()}}),g.on("end",function(){$.push(null)}),$._read=function(){for(;;){const H=g.read();if(H===null){k=$._read;return}if(!$.push(H))return}}),$._destroy=function(H,ee){!H&&v!==null&&(H=new A),k=null,P=null,E=null,v===null?ee(H):(v=ee,m(L,H),m(g,H))},$}return xr}var Or,Yi;function ct(){if(Yi)return Or;Yi=1;const{ObjectDefineProperties:n,ObjectGetOwnPropertyDescriptor:e,ObjectKeys:t,ObjectSetPrototypeOf:r}=Ie();Or=s;const i=jn(),o=ii();r(s.prototype,i.prototype),r(s,i);{const C=t(o.prototype);for(let O=0;O<C.length;O++){const A=C[O];s.prototype[A]||(s.prototype[A]=o.prototype[A])}}function s(C){if(!(this instanceof s))return new s(C);i.call(this,C),o.call(this,C),C?(this.allowHalfOpen=C.allowHalfOpen!==!1,C.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),C.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)):this.allowHalfOpen=!0}n(s.prototype,{writable:{__proto__:null,...e(o.prototype,"writable")},writableHighWaterMark:{__proto__:null,...e(o.prototype,"writableHighWaterMark")},writableObjectMode:{__proto__:null,...e(o.prototype,"writableObjectMode")},writableBuffer:{__proto__:null,...e(o.prototype,"writableBuffer")},writableLength:{__proto__:null,...e(o.prototype,"writableLength")},writableFinished:{__proto__:null,...e(o.prototype,"writableFinished")},writableCorked:{__proto__:null,...e(o.prototype,"writableCorked")},writableEnded:{__proto__:null,...e(o.prototype,"writableEnded")},writableNeedDrain:{__proto__:null,...e(o.prototype,"writableNeedDrain")},destroyed:{__proto__:null,get(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set(C){this._readableState&&this._writableState&&(this._readableState.destroyed=C,this._writableState.destroyed=C)}}});let u;function h(){return u===void 0&&(u={}),u}s.fromWeb=function(C,O){return h().newStreamDuplexFromReadableWritablePair(C,O)},s.toWeb=function(C){return h().newReadableWritablePairFromDuplex(C)};let b;return s.from=function(C){return b||(b=da()),b(C,"body")},Or}var Nr,Ji;function Zo(){if(Ji)return Nr;Ji=1;const{ObjectSetPrototypeOf:n,Symbol:e}=Ie();Nr=s;const{ERR_METHOD_NOT_IMPLEMENTED:t}=De().codes,r=ct(),{getHighWaterMark:i}=Un();n(s.prototype,r.prototype),n(s,r);const o=e("kCallback");function s(b){if(!(this instanceof s))return new s(b);const C=b?i(this,b,"readableHighWaterMark",!0):null;C===0&&(b={...b,highWaterMark:null,readableHighWaterMark:C,writableHighWaterMark:b.writableHighWaterMark||0}),r.call(this,b),this._readableState.sync=!1,this[o]=null,b&&(typeof b.transform=="function"&&(this._transform=b.transform),typeof b.flush=="function"&&(this._flush=b.flush)),this.on("prefinish",h)}function u(b){typeof this._flush=="function"&&!this.destroyed?this._flush((C,O)=>{if(C){b?b(C):this.destroy(C);return}O!=null&&this.push(O),this.push(null),b&&b()}):(this.push(null),b&&b())}function h(){this._final!==u&&u.call(this)}return s.prototype._final=u,s.prototype._transform=function(b,C,O){throw new t("_transform()")},s.prototype._write=function(b,C,O){const A=this._readableState,j=this._writableState,D=A.length;this._transform(b,C,(m,_)=>{if(m){O(m);return}_!=null&&this.push(_),j.ended||D===A.length||A.length<A.highWaterMark?O():this[o]=O})},s.prototype._read=function(){if(this[o]){const b=this[o];this[o]=null,b()}},Nr}var Cr,Zi;function Xo(){if(Zi)return Cr;Zi=1;const{ObjectSetPrototypeOf:n}=Ie();Cr=t;const e=Zo();n(t.prototype,e.prototype),n(t,e);function t(r){if(!(this instanceof t))return new t(r);e.call(this,r)}return t.prototype._transform=function(r,i,o){o(null,r)},Cr}var Lr,Xi;function oi(){if(Xi)return Lr;Xi=1;const n=Lt(),{ArrayIsArray:e,Promise:t,SymbolAsyncIterator:r,SymbolDispose:i}=Ie(),o=_t(),{once:s}=We(),u=jt(),h=ct(),{aggregateTwoErrors:b,codes:{ERR_INVALID_ARG_TYPE:C,ERR_INVALID_RETURN_VALUE:O,ERR_MISSING_ARGS:A,ERR_STREAM_DESTROYED:j,ERR_STREAM_PREMATURE_CLOSE:D},AbortError:m}=De(),{validateFunction:_,validateAbortSignal:f}=on(),{isIterable:c,isReadable:S,isReadableNodeStream:B,isNodeStream:N,isTransformStream:F,isWebStream:G,isReadableStream:a,isReadableFinished:w}=dt(),l=globalThis.AbortController||tn().AbortController;let d,y,g;function L(H,ee,Q){let le=!1;H.on("close",()=>{le=!0});const ue=o(H,{readable:ee,writable:Q},K=>{le=!K});return{destroy:K=>{le||(le=!0,u.destroyer(H,K||new j("pipe")))},cleanup:ue}}function p(H){return _(H[H.length-1],"streams[stream.length - 1]"),H.pop()}function R(H){if(c(H))return H;if(B(H))return P(H);throw new C("val",["Readable","Iterable","AsyncIterable"],H)}async function*P(H){y||(y=jn()),yield*y.prototype[r].call(H)}async function E(H,ee,Q,{end:le}){let ue,K=null;const Y=fe=>{if(fe&&(ue=fe),K){const M=K;K=null,M()}},oe=()=>new t((fe,M)=>{ue?M(ue):K=()=>{ue?M(ue):fe()}});ee.on("drain",Y);const ce=o(ee,{readable:!1},Y);try{ee.writableNeedDrain&&await oe();for await(const fe of H)ee.write(fe)||await oe();le&&(ee.end(),await oe()),Q()}catch(fe){Q(ue!==fe?b(ue,fe):fe)}finally{ce(),ee.off("drain",Y)}}async function k(H,ee,Q,{end:le}){F(ee)&&(ee=ee.writable);const ue=ee.getWriter();try{for await(const K of H)await ue.ready,ue.write(K).catch(()=>{});await ue.ready,le&&await ue.close(),Q()}catch(K){try{await ue.abort(K),Q(K)}catch(Y){Q(Y)}}}function v(...H){return $(H,s(p(H)))}function $(H,ee,Q){if(H.length===1&&e(H[0])&&(H=H[0]),H.length<2)throw new A("streams");const le=new l,ue=le.signal,K=Q?.signal,Y=[];f(K,"options.signal");function oe(){se(new m)}g=g||We().addAbortListener;let ce;K&&(ce=g(K,oe));let fe,M;const U=[];let z=0;function re(ne){se(ne,--z===0)}function se(ne,Z){var we;if(ne&&(!fe||fe.code==="ERR_STREAM_PREMATURE_CLOSE")&&(fe=ne),!(!fe&&!Z)){for(;U.length;)U.shift()(fe);(we=ce)===null||we===void 0||we[i](),le.abort(),Z&&(fe||Y.forEach(Le=>Le()),n.nextTick(ee,fe,M))}}let ie;for(let ne=0;ne<H.length;ne++){const Z=H[ne],we=ne<H.length-1,Le=ne>0,ye=we||Q?.end!==!1,Be=ne===H.length-1;if(N(Z)){let xe=function(Pe){Pe&&Pe.name!=="AbortError"&&Pe.code!=="ERR_STREAM_PREMATURE_CLOSE"&&re(Pe)};if(ye){const{destroy:Pe,cleanup:Ye}=L(Z,we,Le);U.push(Pe),S(Z)&&Be&&Y.push(Ye)}Z.on("error",xe),S(Z)&&Be&&Y.push(()=>{Z.removeListener("error",xe)})}if(ne===0)if(typeof Z=="function"){if(ie=Z({signal:ue}),!c(ie))throw new O("Iterable, AsyncIterable or Stream","source",ie)}else c(Z)||B(Z)||F(Z)?ie=Z:ie=h.from(Z);else if(typeof Z=="function"){if(F(ie)){var q;ie=R((q=ie)===null||q===void 0?void 0:q.readable)}else ie=R(ie);if(ie=Z(ie,{signal:ue}),we){if(!c(ie,!0))throw new O("AsyncIterable",`transform[${ne-1}]`,ie)}else{var X;d||(d=Xo());const xe=new d({objectMode:!0}),Pe=(X=ie)===null||X===void 0?void 0:X.then;if(typeof Pe=="function")z++,Pe.call(ie,Me=>{M=Me,Me!=null&&xe.write(Me),ye&&xe.end(),n.nextTick(re)},Me=>{xe.destroy(Me),n.nextTick(re,Me)});else if(c(ie,!0))z++,E(ie,xe,re,{end:ye});else if(a(ie)||F(ie)){const Me=ie.readable||ie;z++,E(Me,xe,re,{end:ye})}else throw new O("AsyncIterable or Promise","destination",ie);ie=xe;const{destroy:Ye,cleanup:mt}=L(ie,!1,!0);U.push(Ye),Be&&Y.push(mt)}}else if(N(Z)){if(B(ie)){z+=2;const xe=J(ie,Z,re,{end:ye});S(Z)&&Be&&Y.push(xe)}else if(F(ie)||a(ie)){const xe=ie.readable||ie;z++,E(xe,Z,re,{end:ye})}else if(c(ie))z++,E(ie,Z,re,{end:ye});else throw new C("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],ie);ie=Z}else if(G(Z)){if(B(ie))z++,k(R(ie),Z,re,{end:ye});else if(a(ie)||c(ie))z++,k(ie,Z,re,{end:ye});else if(F(ie))z++,k(ie.readable,Z,re,{end:ye});else throw new C("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],ie);ie=Z}else ie=h.from(Z)}return(ue!=null&&ue.aborted||K!=null&&K.aborted)&&n.nextTick(oe),ie}function J(H,ee,Q,{end:le}){let ue=!1;if(ee.on("close",()=>{ue||Q(new D)}),H.pipe(ee,{end:!1}),le){let K=function(){ue=!0,ee.end()};w(H)?n.nextTick(K):H.once("end",K)}else Q();return o(H,{readable:!0,writable:!1},K=>{const Y=H._readableState;K&&K.code==="ERR_STREAM_PREMATURE_CLOSE"&&Y&&Y.ended&&!Y.errored&&!Y.errorEmitted?H.once("end",Q).once("error",Q):Q(K)}),o(ee,{readable:!1,writable:!0},Q)}return Lr={pipelineImpl:$,pipeline:v},Lr}var Pr,Qi;function Qo(){if(Qi)return Pr;Qi=1;const{pipeline:n}=oi(),e=ct(),{destroyer:t}=jt(),{isNodeStream:r,isReadable:i,isWritable:o,isWebStream:s,isTransformStream:u,isWritableStream:h,isReadableStream:b}=dt(),{AbortError:C,codes:{ERR_INVALID_ARG_VALUE:O,ERR_MISSING_ARGS:A}}=De(),j=_t();return Pr=function(...m){if(m.length===0)throw new A("streams");if(m.length===1)return e.from(m[0]);const _=[...m];if(typeof m[0]=="function"&&(m[0]=e.from(m[0])),typeof m[m.length-1]=="function"){const d=m.length-1;m[d]=e.from(m[d])}for(let d=0;d<m.length;++d)if(!(!r(m[d])&&!s(m[d]))){if(d<m.length-1&&!(i(m[d])||b(m[d])||u(m[d])))throw new O(`streams[${d}]`,_[d],"must be readable");if(d>0&&!(o(m[d])||h(m[d])||u(m[d])))throw new O(`streams[${d}]`,_[d],"must be writable")}let f,c,S,B,N;function F(d){const y=B;B=null,y?y(d):d?N.destroy(d):!l&&!w&&N.destroy()}const G=m[0],a=n(m,F),w=!!(o(G)||h(G)||u(G)),l=!!(i(a)||b(a)||u(a));if(N=new e({writableObjectMode:!!(G!=null&&G.writableObjectMode),readableObjectMode:!!(a!=null&&a.readableObjectMode),writable:w,readable:l}),w){if(r(G))N._write=function(y,g,L){G.write(y,g)?L():f=L},N._final=function(y){G.end(),c=y},G.on("drain",function(){if(f){const y=f;f=null,y()}});else if(s(G)){const g=(u(G)?G.writable:G).getWriter();N._write=async function(L,p,R){try{await g.ready,g.write(L).catch(()=>{}),R()}catch(P){R(P)}},N._final=async function(L){try{await g.ready,g.close().catch(()=>{}),c=L}catch(p){L(p)}}}const d=u(a)?a.readable:a;j(d,()=>{if(c){const y=c;c=null,y()}})}if(l){if(r(a))a.on("readable",function(){if(S){const d=S;S=null,d()}}),a.on("end",function(){N.push(null)}),N._read=function(){for(;;){const d=a.read();if(d===null){S=N._read;return}if(!N.push(d))return}};else if(s(a)){const y=(u(a)?a.readable:a).getReader();N._read=async function(){for(;;)try{const{value:g,done:L}=await y.read();if(!N.push(g))return;if(L){N.push(null);return}}catch{return}}}}return N._destroy=function(d,y){!d&&B!==null&&(d=new C),S=null,f=null,c=null,B===null?y(d):(B=y,r(a)&&t(a,d))},N},Pr}var eo;function ha(){if(eo)return Rn;eo=1;const n=globalThis.AbortController||tn().AbortController,{codes:{ERR_INVALID_ARG_VALUE:e,ERR_INVALID_ARG_TYPE:t,ERR_MISSING_ARGS:r,ERR_OUT_OF_RANGE:i},AbortError:o}=De(),{validateAbortSignal:s,validateInteger:u,validateObject:h}=on(),b=Ie().Symbol("kWeak"),C=Ie().Symbol("kResistStopPropagation"),{finished:O}=_t(),A=Qo(),{addAbortSignalNoValidate:j}=kn(),{isWritable:D,isNodeStream:m}=dt(),{deprecate:_}=We(),{ArrayPrototypePush:f,Boolean:c,MathFloor:S,Number:B,NumberIsNaN:N,Promise:F,PromiseReject:G,PromiseResolve:a,PromisePrototypeThen:w,Symbol:l}=Ie(),d=l("kEmpty"),y=l("kEof");function g(K,Y){if(Y!=null&&h(Y,"options"),Y?.signal!=null&&s(Y.signal,"options.signal"),m(K)&&!D(K))throw new e("stream",K,"must be writable");const oe=A(this,K);return Y!=null&&Y.signal&&j(Y.signal,oe),oe}function L(K,Y){if(typeof K!="function")throw new t("fn",["Function","AsyncFunction"],K);Y!=null&&h(Y,"options"),Y?.signal!=null&&s(Y.signal,"options.signal");let oe=1;Y?.concurrency!=null&&(oe=S(Y.concurrency));let ce=oe-1;return Y?.highWaterMark!=null&&(ce=S(Y.highWaterMark)),u(oe,"options.concurrency",1),u(ce,"options.highWaterMark",0),ce+=oe,(async function*(){const M=We().AbortSignalAny([Y?.signal].filter(c)),U=this,z=[],re={signal:M};let se,ie,q=!1,X=0;function ne(){q=!0,Z()}function Z(){X-=1,we()}function we(){ie&&!q&&X<oe&&z.length<ce&&(ie(),ie=null)}async function Le(){try{for await(let ye of U){if(q)return;if(M.aborted)throw new o;try{if(ye=K(ye,re),ye===d)continue;ye=a(ye)}catch(Be){ye=G(Be)}X+=1,w(ye,Z,ne),z.push(ye),se&&(se(),se=null),!q&&(z.length>=ce||X>=oe)&&await new F(Be=>{ie=Be})}z.push(y)}catch(ye){const Be=G(ye);w(Be,Z,ne),z.push(Be)}finally{q=!0,se&&(se(),se=null)}}Le();try{for(;;){for(;z.length>0;){const ye=await z[0];if(ye===y)return;if(M.aborted)throw new o;ye!==d&&(yield ye),z.shift(),we()}await new F(ye=>{se=ye})}}finally{q=!0,ie&&(ie(),ie=null)}}).call(this)}function p(K=void 0){return K!=null&&h(K,"options"),K?.signal!=null&&s(K.signal,"options.signal"),(async function*(){let oe=0;for await(const fe of this){var ce;if(K!=null&&(ce=K.signal)!==null&&ce!==void 0&&ce.aborted)throw new o({cause:K.signal.reason});yield[oe++,fe]}}).call(this)}async function R(K,Y=void 0){for await(const oe of v.call(this,K,Y))return!0;return!1}async function P(K,Y=void 0){if(typeof K!="function")throw new t("fn",["Function","AsyncFunction"],K);return!await R.call(this,async(...oe)=>!await K(...oe),Y)}async function E(K,Y){for await(const oe of v.call(this,K,Y))return oe}async function k(K,Y){if(typeof K!="function")throw new t("fn",["Function","AsyncFunction"],K);async function oe(ce,fe){return await K(ce,fe),d}for await(const ce of L.call(this,oe,Y));}function v(K,Y){if(typeof K!="function")throw new t("fn",["Function","AsyncFunction"],K);async function oe(ce,fe){return await K(ce,fe)?ce:d}return L.call(this,oe,Y)}class $ extends r{constructor(){super("reduce"),this.message="Reduce of an empty stream requires an initial value"}}async function J(K,Y,oe){var ce;if(typeof K!="function")throw new t("reducer",["Function","AsyncFunction"],K);oe!=null&&h(oe,"options"),oe?.signal!=null&&s(oe.signal,"options.signal");let fe=arguments.length>1;if(oe!=null&&(ce=oe.signal)!==null&&ce!==void 0&&ce.aborted){const se=new o(void 0,{cause:oe.signal.reason});throw this.once("error",()=>{}),await O(this.destroy(se)),se}const M=new n,U=M.signal;if(oe!=null&&oe.signal){const se={once:!0,[b]:this,[C]:!0};oe.signal.addEventListener("abort",()=>M.abort(),se)}let z=!1;try{for await(const se of this){var re;if(z=!0,oe!=null&&(re=oe.signal)!==null&&re!==void 0&&re.aborted)throw new o;fe?Y=await K(Y,se,{signal:U}):(Y=se,fe=!0)}if(!z&&!fe)throw new $}finally{M.abort()}return Y}async function H(K){K!=null&&h(K,"options"),K?.signal!=null&&s(K.signal,"options.signal");const Y=[];for await(const ce of this){var oe;if(K!=null&&(oe=K.signal)!==null&&oe!==void 0&&oe.aborted)throw new o(void 0,{cause:K.signal.reason});f(Y,ce)}return Y}function ee(K,Y){const oe=L.call(this,K,Y);return(async function*(){for await(const fe of oe)yield*fe}).call(this)}function Q(K){if(K=B(K),N(K))return 0;if(K<0)throw new i("number",">= 0",K);return K}function le(K,Y=void 0){return Y!=null&&h(Y,"options"),Y?.signal!=null&&s(Y.signal,"options.signal"),K=Q(K),(async function*(){var ce;if(Y!=null&&(ce=Y.signal)!==null&&ce!==void 0&&ce.aborted)throw new o;for await(const M of this){var fe;if(Y!=null&&(fe=Y.signal)!==null&&fe!==void 0&&fe.aborted)throw new o;K--<=0&&(yield M)}}).call(this)}function ue(K,Y=void 0){return Y!=null&&h(Y,"options"),Y?.signal!=null&&s(Y.signal,"options.signal"),K=Q(K),(async function*(){var ce;if(Y!=null&&(ce=Y.signal)!==null&&ce!==void 0&&ce.aborted)throw new o;for await(const M of this){var fe;if(Y!=null&&(fe=Y.signal)!==null&&fe!==void 0&&fe.aborted)throw new o;if(K-- >0&&(yield M),K<=0)return}}).call(this)}return Rn.streamReturningOperators={asIndexedPairs:_(p,"readable.asIndexedPairs will be removed in a future version."),drop:le,filter:v,flatMap:ee,map:L,take:ue,compose:g},Rn.promiseReturningOperators={every:P,forEach:k,reduce:J,toArray:H,some:R,find:E},Rn}var Mr,to;function es(){if(to)return Mr;to=1;const{ArrayPrototypePop:n,Promise:e}=Ie(),{isIterable:t,isNodeStream:r,isWebStream:i}=dt(),{pipelineImpl:o}=oi(),{finished:s}=_t();ts();function u(...h){return new e((b,C)=>{let O,A;const j=h[h.length-1];if(j&&typeof j=="object"&&!r(j)&&!t(j)&&!i(j)){const D=n(h);O=D.signal,A=D.end}o(h,(D,m)=>{D?C(D):b(m)},{signal:O,end:A})})}return Mr={finished:s,pipeline:u},Mr}var no;function ts(){if(no)return dr.exports;no=1;const{Buffer:n}=Ct(),{ObjectDefineProperty:e,ObjectKeys:t,ReflectApply:r}=Ie(),{promisify:{custom:i}}=We(),{streamReturningOperators:o,promiseReturningOperators:s}=ha(),{codes:{ERR_ILLEGAL_CONSTRUCTOR:u}}=De(),h=Qo(),{setDefaultHighWaterMark:b,getDefaultHighWaterMark:C}=Un(),{pipeline:O}=oi(),{destroyer:A}=jt(),j=_t(),D=es(),m=dt(),_=dr.exports=ri().Stream;_.isDestroyed=m.isDestroyed,_.isDisturbed=m.isDisturbed,_.isErrored=m.isErrored,_.isReadable=m.isReadable,_.isWritable=m.isWritable,_.Readable=jn();for(const c of t(o)){let B=function(...N){if(new.target)throw u();return _.Readable.from(r(S,this,N))};const S=o[c];e(B,"name",{__proto__:null,value:S.name}),e(B,"length",{__proto__:null,value:S.length}),e(_.Readable.prototype,c,{__proto__:null,value:B,enumerable:!1,configurable:!0,writable:!0})}for(const c of t(s)){let B=function(...N){if(new.target)throw u();return r(S,this,N)};const S=s[c];e(B,"name",{__proto__:null,value:S.name}),e(B,"length",{__proto__:null,value:S.length}),e(_.Readable.prototype,c,{__proto__:null,value:B,enumerable:!1,configurable:!0,writable:!0})}_.Writable=ii(),_.Duplex=ct(),_.Transform=Zo(),_.PassThrough=Xo(),_.pipeline=O;const{addAbortSignal:f}=kn();return _.addAbortSignal=f,_.finished=j,_.destroy=A,_.compose=h,_.setDefaultHighWaterMark=b,_.getDefaultHighWaterMark=C,e(_,"promises",{__proto__:null,configurable:!0,enumerable:!0,get(){return D}}),e(O,i,{__proto__:null,enumerable:!0,get(){return D.pipeline}}),e(j,i,{__proto__:null,enumerable:!0,get(){return D.finished}}),_.Stream=_,_._isUint8Array=function(S){return S instanceof Uint8Array},_._uint8ArrayToBuffer=function(S){return n.from(S.buffer,S.byteOffset,S.byteLength)},dr.exports}var ro;function ga(){return ro||(ro=1,function(n){const e=ts(),t=es(),r=e.Readable.destroy;n.exports=e.Readable,n.exports._uint8ArrayToBuffer=e._uint8ArrayToBuffer,n.exports._isUint8Array=e._isUint8Array,n.exports.isDisturbed=e.isDisturbed,n.exports.isErrored=e.isErrored,n.exports.isReadable=e.isReadable,n.exports.Readable=e.Readable,n.exports.Writable=e.Writable,n.exports.Duplex=e.Duplex,n.exports.Transform=e.Transform,n.exports.PassThrough=e.PassThrough,n.exports.addAbortSignal=e.addAbortSignal,n.exports.finished=e.finished,n.exports.destroy=e.destroy,n.exports.destroy=r,n.exports.pipeline=e.pipeline,n.exports.compose=e.compose,Object.defineProperty(e,"promises",{configurable:!0,enumerable:!0,get(){return t}}),n.exports.Stream=e.Stream,n.exports.default=n.exports}(cr)),cr.exports}var qn=ga(),Br,io;function ns(){if(io)return Br;io=1,Br=o,o.default=o,o.stable=b,o.stableStringify=b;var n="[...]",e="[Circular]",t=[],r=[];function i(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(A,j,D,m){typeof m>"u"&&(m=i()),u(A,"",0,[],void 0,0,m);var _;try{r.length===0?_=JSON.stringify(A,j,D):_=JSON.stringify(A,O(j),D)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;t.length!==0;){var f=t.pop();f.length===4?Object.defineProperty(f[0],f[1],f[3]):f[0][f[1]]=f[2]}}return _}function s(A,j,D,m){var _=Object.getOwnPropertyDescriptor(m,D);_.get!==void 0?_.configurable?(Object.defineProperty(m,D,{value:A}),t.push([m,D,j,_])):r.push([j,D,A]):(m[D]=A,t.push([m,D,j]))}function u(A,j,D,m,_,f,c){f+=1;var S;if(typeof A=="object"&&A!==null){for(S=0;S<m.length;S++)if(m[S]===A){s(e,A,j,_);return}if(typeof c.depthLimit<"u"&&f>c.depthLimit){s(n,A,j,_);return}if(typeof c.edgesLimit<"u"&&D+1>c.edgesLimit){s(n,A,j,_);return}if(m.push(A),Array.isArray(A))for(S=0;S<A.length;S++)u(A[S],S,S,m,A,f,c);else{var B=Object.keys(A);for(S=0;S<B.length;S++){var N=B[S];u(A[N],N,S,m,A,f,c)}}m.pop()}}function h(A,j){return A<j?-1:A>j?1:0}function b(A,j,D,m){typeof m>"u"&&(m=i());var _=C(A,"",0,[],void 0,0,m)||A,f;try{r.length===0?f=JSON.stringify(_,j,D):f=JSON.stringify(_,O(j),D)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;t.length!==0;){var c=t.pop();c.length===4?Object.defineProperty(c[0],c[1],c[3]):c[0][c[1]]=c[2]}}return f}function C(A,j,D,m,_,f,c){f+=1;var S;if(typeof A=="object"&&A!==null){for(S=0;S<m.length;S++)if(m[S]===A){s(e,A,j,_);return}try{if(typeof A.toJSON=="function")return}catch{return}if(typeof c.depthLimit<"u"&&f>c.depthLimit){s(n,A,j,_);return}if(typeof c.edgesLimit<"u"&&D+1>c.edgesLimit){s(n,A,j,_);return}if(m.push(A),Array.isArray(A))for(S=0;S<A.length;S++)C(A[S],S,S,m,A,f,c);else{var B={},N=Object.keys(A).sort(h);for(S=0;S<N.length;S++){var F=N[S];C(A[F],F,S,m,A,f,c),B[F]=A[F]}if(typeof _<"u")t.push([_,j,A]),_[j]=B;else return B}m.pop()}}function O(A){return A=typeof A<"u"?A:function(j,D){return D},function(j,D){if(r.length>0)for(var m=0;m<r.length;m++){var _=r[m];if(_[1]===j&&_[0]===D){D=_[2],r.splice(m,1);break}}return A.call(this,j,D)}}return Br}var pa=ns();const wa=Ut(pa);var Tn={exports:{}},Dr,oo;function ba(){if(oo)return Dr;oo=1,Dr=n;function n(e,t){if(e&&t)return n(e)(t);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(i){r[i]=e[i]}),r;function r(){for(var i=new Array(arguments.length),o=0;o<i.length;o++)i[o]=arguments[o];var s=e.apply(this,i),u=i[i.length-1];return typeof s=="function"&&s!==u&&Object.keys(u).forEach(function(h){s[h]=u[h]}),s}}return Dr}var so;function si(){if(so)return Tn.exports;so=1;var n=ba();Tn.exports=n(e),Tn.exports.strict=n(t),e.proto=e(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return e(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return t(this)},configurable:!0})});function e(r){var i=function(){return i.called?i.value:(i.called=!0,i.value=r.apply(this,arguments))};return i.called=!1,i}function t(r){var i=function(){if(i.called)throw new Error(i.onceError);return i.called=!0,i.value=r.apply(this,arguments)},o=r.name||"Function wrapped with `once`";return i.onceError=o+" shouldn't be called more than once",i.called=!1,i}return Tn.exports}var kr,ao;function rs(){if(ao)return kr;ao=1;var n=si(),e=function(){},t=Jt.Bare?queueMicrotask:process.nextTick.bind(process),r=function(s){return s.setHeader&&typeof s.abort=="function"},i=function(s){return s.stdio&&Array.isArray(s.stdio)&&s.stdio.length===3},o=function(s,u,h){if(typeof u=="function")return o(s,null,u);u||(u={}),h=n(h||e);var b=s._writableState,C=s._readableState,O=u.readable||u.readable!==!1&&s.readable,A=u.writable||u.writable!==!1&&s.writable,j=!1,D=function(){s.writable||m()},m=function(){A=!1,O||h.call(s)},_=function(){O=!1,A||h.call(s)},f=function(F){h.call(s,F?new Error("exited with error code: "+F):null)},c=function(F){h.call(s,F)},S=function(){t(B)},B=function(){if(!j){if(O&&!(C&&C.ended&&!C.destroyed))return h.call(s,new Error("premature close"));if(A&&!(b&&b.ended&&!b.destroyed))return h.call(s,new Error("premature close"))}},N=function(){s.req.on("finish",m)};return r(s)?(s.on("complete",m),s.on("abort",S),s.req?N():s.on("request",N)):A&&!b&&(s.on("end",D),s.on("close",D)),i(s)&&s.on("exit",f),s.on("end",_),s.on("finish",m),u.error!==!1&&s.on("error",c),s.on("close",S),function(){j=!0,s.removeListener("complete",m),s.removeListener("abort",S),s.removeListener("request",N),s.req&&s.req.removeListener("finish",m),s.removeListener("end",D),s.removeListener("close",D),s.removeListener("finish",m),s.removeListener("exit",f),s.removeListener("end",_),s.removeListener("error",c),s.removeListener("close",S)}};return kr=o,kr}var ya=rs();const lo=Ut(ya);var _a=si();const ma=Ut(_a);var Ur,uo;function Ea(){if(uo)return Ur;uo=1;var n=si(),e=rs(),t;try{t=oa}catch{}var r=function(){},i=typeof process>"u"?!1:/^v?\.0/.test(process.version),o=function(A){return typeof A=="function"},s=function(A){return!i||!t?!1:(A instanceof(t.ReadStream||r)||A instanceof(t.WriteStream||r))&&o(A.close)},u=function(A){return A.setHeader&&o(A.abort)},h=function(A,j,D,m){m=n(m);var _=!1;A.on("close",function(){_=!0}),e(A,{readable:j,writable:D},function(c){if(c)return m(c);_=!0,m()});var f=!1;return function(c){if(!_&&!f){if(f=!0,s(A))return A.close(r);if(u(A))return A.abort();if(o(A.destroy))return A.destroy();m(c||new Error("stream was destroyed"))}}},b=function(A){A()},C=function(A,j){return A.pipe(j)},O=function(){var A=Array.prototype.slice.call(arguments),j=o(A[A.length-1]||r)&&A.pop()||r;if(Array.isArray(A[0])&&(A=A[0]),A.length<2)throw new Error("pump requires two streams per minimum");var D,m=A.map(function(_,f){var c=f<A.length-1,S=f>0;return h(_,c,S,function(B){D||(D=B),B&&m.forEach(b),!c&&(m.forEach(b),j(D))})});return A.reduce(C)};return Ur=O,Ur}var Sa=Ea();const fo=Ut(Sa);function xn(){}const co="SYN",jr="ACK",ho="BRK";class go extends qn.Duplex{constructor(e){let{name:t,target:r,targetWindow:i=window,targetOrigin:o="*"}=e;if(super({objectMode:!0}),ge(this,"_init",void 0),ge(this,"_haveSyn",void 0),ge(this,"_name",void 0),ge(this,"_target",void 0),ge(this,"_targetWindow",void 0),ge(this,"_targetOrigin",void 0),ge(this,"_onMessage",void 0),ge(this,"_synIntervalId",void 0),!t||!r)throw new Error("Invalid input.");this._init=!1,this._haveSyn=!1,this._name=t,this._target=r,this._targetWindow=i,this._targetOrigin=o,this._onMessage=this.onMessage.bind(this),this._synIntervalId=null,window.addEventListener("message",this._onMessage,!1),this._handShake()}_break(){this.cork(),this._write(ho,null,xn),this._haveSyn=!1,this._init=!1}_handShake(){this._write(co,null,xn),this.cork()}_onData(e){if(!this._init)e===co?(this._haveSyn=!0,this._write(jr,null,xn)):e===jr&&(this._init=!0,this._haveSyn||this._write(jr,null,xn),this.uncork());else if(e===ho)this._break();else try{this.push(e)}catch(t){this.emit("error",t)}}_postMessage(e){const t=this._targetOrigin;this._targetWindow.postMessage({target:this._target,data:e},t)}onMessage(e){const t=e.data;this._targetOrigin!=="*"&&e.origin!==this._targetOrigin||e.source!==this._targetWindow||typeof t!="object"||t.target!==this._name||!t.data||this._onData(t.data)}_read(){}_write(e,t,r){this._postMessage(e),r()}_destroy(){window.removeEventListener("message",this._onMessage,!1)}}const va={rpc:{internal:-32603}},po={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}},wo=va.rpc.internal,Aa="Unspecified error message. This is a bug, please report it.";function is(n){return Number.isInteger(n)}function bo(n){return typeof n=="string"&&n.length>0}function yo(n){return!!n&&typeof n=="object"&&!Array.isArray(n)}function os(n){const e=n;return!(!e||!is(e.code)||!bo(e.message)||e.stack&&!bo(e.stack))}function Ra(n){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Aa;if(is(n)){const t=n.toString();if(Object.hasOwn(po,t))return po[t].message}return e}const Ia={code:wo,message:Ra(wo)};function Yr(n){try{JSON.parse(JSON.stringify(n,(e,t)=>{if(e==="__proto__"||e==="constructor")throw new Error("Not valid json");if(typeof t=="function"||typeof t=="symbol")throw new Error("Not valid json");return t}),(e,t)=>{if(!(e==="__proto__"||e==="constructor"))return t})}catch{return!1}return!0}function _o(n){return Object.getOwnPropertyNames(n).reduce((e,t)=>{const r=n[t];return Yr(r)&&(e[t]=r),e},{})}function Ta(n){return Array.isArray(n)?n.map(e=>Yr(e)?e:yo(e)?_o(e):null):yo(n)?_o(n):Yr(n)?n:null}function xa(n,e){if(n&&typeof n=="object"&&"serialize"in n&&typeof n.serialize=="function")return n.serialize();if(os(n))return n;const t=Ta(n);return Ke(Ke({},e),{},{data:{cause:t}})}function mo(n){let{fallbackError:e=Ia,shouldIncludeStack:t=!0}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!os(e))throw new Error("Must provide fallback error with integer number code and string message.");const r=xa(n,e);return t||delete r.stack,r}function Eo(n,e,t){try{Reflect.apply(n,e,t)}catch(r){setTimeout(()=>{throw r})}}function Oa(n){const e=n.length,t=new Array(e);for(let r=0;r<e;r+=1)t[r]=n[r];return t}class Wn extends sa.EventEmitter{emit(e){let t=e==="error";const r=this._events;if(r!==void 0)t=t&&r.error===void 0;else if(!t)return!1;for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];if(t){let h;if(o.length>0&&([h]=o),h instanceof Error)throw h;const b=new Error(`Unhandled error.${h?` (${h.message})`:""}`);throw b.context=h,b}const u=r[e];if(u===void 0)return!1;if(typeof u=="function")Eo(u,this,o);else{const h=u.length,b=Oa(u);for(let C=0;C<h;C+=1)Eo(b[C],this,o)}return!0}}class Kt extends Error{constructor(e){let{code:t,message:r,data:i}=e;if(!Number.isInteger(t))throw new Error("code must be an integer");if(!r||typeof r!="string")throw new Error("message must be string");super(r),ge(this,"code",void 0),ge(this,"data",void 0),this.code=t,i!==void 0&&(this.data=i)}toString(){return wa({code:this.code,message:this.message,data:this.data,stack:this.stack})}}const Jr=function(n,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return(r,i)=>{r||i.error?e(r||i.error):!t||Array.isArray(i)?n(i):n(i.result)}};function Na(){const n={};function e(){return!1}const t=new Wn;function r(h){const b=n[h.id];if(!b)throw new Error(`StreamMiddleware - Unknown response id "${h.id}"`);delete n[h.id],Object.assign(b.res,h),setTimeout(b.end)}function i(h){t.emit("notification",h)}function o(h,b,C){let O;try{!h.id?i(h):r(h)}catch(A){O=A}C(O)}const s=new qn.Duplex({objectMode:!0,read:e,write:o});return{events:t,middleware:(h,b,C,O)=>{s.push(h),n[h.id]={req:h,res:b,next:C,end:O}},stream:s}}function Ca(){return(n,e,t,r)=>{const i=n.id,o=Math.random().toString(36).slice(2);n.id=o,e.id=o,t(s=>{n.id=i,e.id=i,s()})}}class lt extends Wn{constructor(){super(),ge(this,"_middleware",void 0),this._middleware=[]}static async _runAllMiddleware(e,t,r){const i=[];let o=null,s=!1;for(const u of r)if([o,s]=await lt._runMiddleware(e,t,u,i),s)break;return[o,s,i.reverse()]}static _runMiddleware(e,t,r,i){return new Promise(o=>{const s=h=>{const b=h||t.error;b&&(typeof b=="object"&&Object.keys(b).includes("stack")===!1&&(b.stack="Stack trace is not available."),t.error=mo(b,{shouldIncludeStack:!0,fallbackError:{message:b?.message||b?.toString(),code:b?.code||-32603,stack:b?.stack||"Stack trace is not available.",data:b?.data||b?.message||b?.toString()}})),o([b,!0])},u=h=>{t.error?s(t.error):(h&&(typeof h!="function"&&s(new Kt({code:-32603,message:"JRPCEngine: 'next' return handlers must be functions"})),i.push(h)),o([null,!1]))};try{r(e,t,u,s)}catch(h){s(h)}})}static async _runReturnHandlers(e){for(const t of e)await new Promise((r,i)=>{t(o=>o?i(o):r())})}static _checkForCompletion(e,t,r){if(!("result"in t)&&!("error"in t))throw new Kt({code:-32603,message:"Response has no error or result for request"});if(!r)throw new Kt({code:-32603,message:"Nothing ended request"})}push(e){this._middleware.push(e)}handle(e,t){if(t&&typeof t!="function")throw new Error('"callback" must be a function if provided.');return Array.isArray(e)?t?this._handleBatch(e,t):this._handleBatch(e):t?this._handle(e,t):this._promiseHandle(e)}asMiddleware(){return async(e,t,r,i)=>{try{const[o,s,u]=await lt._runAllMiddleware(e,t,this._middleware);return s?(await lt._runReturnHandlers(u),i(o)):r(async h=>{try{await lt._runReturnHandlers(u)}catch(b){return h(b)}return h()})}catch(o){return i(o)}}}async _handleBatch(e,t){try{const r=await Promise.all(e.map(this._promiseHandle.bind(this)));return t?t(null,r):r}catch(r){if(t)return t(r);throw r}}_promiseHandle(e){return new Promise((t,r)=>{this._handle(e,(i,o)=>{i&&o===void 0?r(i):t(o)}).catch(r)})}async _handle(e,t){if(!e||Array.isArray(e)||typeof e!="object"){const j=new Kt({code:-32603,message:"request must be plain object"});return t(j,{id:void 0,jsonrpc:"2.0",error:j})}if(typeof e.method!="string"){const j=new Kt({code:-32603,message:"method must be string"});return t(j,{id:e.id,jsonrpc:"2.0",error:j})}const r=Ke({},e),i={id:r.id,jsonrpc:r.jsonrpc};let o=null;try{await this._processRequest(r,i)}catch(j){o=j}if(o&&(delete i.result,!i.error)){var s,u,h,b,C,O,A;typeof o=="object"&&Object.keys(o).includes("stack")===!1&&(o.stack="Stack trace is not available."),i.error=mo(o,{shouldIncludeStack:!0,fallbackError:{message:((s=o)===null||s===void 0?void 0:s.message)||((u=o)===null||u===void 0?void 0:u.toString()),code:((h=o)===null||h===void 0?void 0:h.code)||-32603,stack:((b=o)===null||b===void 0?void 0:b.stack)||"Stack trace is not available.",data:((C=o)===null||C===void 0?void 0:C.data)||((O=o)===null||O===void 0?void 0:O.message)||((A=o)===null||A===void 0?void 0:A.toString())}})}return t(o,i)}async _processRequest(e,t){const[r,i,o]=await lt._runAllMiddleware(e,t,this._middleware);if(lt._checkForCompletion(e,t,i),await lt._runReturnHandlers(o),r)throw r}}class La extends qn.Duplex{constructor(e){let{parent:t,name:r}=e;super({objectMode:!0}),ge(this,"_parent",void 0),ge(this,"_name",void 0),this._parent=t,this._name=r}_read(){}_write(e,t,r){this._parent.push({name:this._name,data:e}),r()}}const So=Symbol("IGNORE_SUBSTREAM");class Pa extends qn.Duplex{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(Ke(Ke({},e),{},{objectMode:!0})),ge(this,"_substreams",void 0),ge(this,"getStream",void 0),this._substreams={}}createStream(e){if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);const t=new La({parent:this,name:e});return this._substreams[e]=t,Ma(this,r=>t.destroy(r||void 0)),t}ignoreStream(e){if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);this._substreams[e]=So}_read(){}_write(e,t,r){const{name:i,data:o}=e;if(!i)return window.console.warn(`ObjectMultiplex - malformed chunk without name "${e}"`),r();const s=this._substreams[i];return s?(s!==So&&s.push(o),r()):(window.console.warn(`ObjectMultiplex - orphaned data for stream "${i}"`),r())}}function Ma(n,e){const t=ma(e);lo(n,{readable:!1},t),lo(n,{writable:!1},t)}var Zt={exports:{}};Zt.exports;var vo;function Ba(){return vo||(vo=1,function(n,e){var t=200,r="__lodash_hash_undefined__",i=800,o=16,s=9007199254740991,u="[object Arguments]",h="[object Array]",b="[object AsyncFunction]",C="[object Boolean]",O="[object Date]",A="[object Error]",j="[object Function]",D="[object GeneratorFunction]",m="[object Map]",_="[object Number]",f="[object Null]",c="[object Object]",S="[object Proxy]",B="[object RegExp]",N="[object Set]",F="[object String]",G="[object Undefined]",a="[object WeakMap]",w="[object ArrayBuffer]",l="[object DataView]",d="[object Float32Array]",y="[object Float64Array]",g="[object Int8Array]",L="[object Int16Array]",p="[object Int32Array]",R="[object Uint8Array]",P="[object Uint8ClampedArray]",E="[object Uint16Array]",k="[object Uint32Array]",v=/[\\^$.*+?()[\]{}|]/g,$=/^\[object .+?Constructor\]$/,J=/^(?:0|[1-9]\d*)$/,H={};H[d]=H[y]=H[g]=H[L]=H[p]=H[R]=H[P]=H[E]=H[k]=!0,H[u]=H[h]=H[w]=H[C]=H[l]=H[O]=H[A]=H[j]=H[m]=H[_]=H[c]=H[B]=H[N]=H[F]=H[a]=!1;var ee=typeof Jt=="object"&&Jt&&Jt.Object===Object&&Jt,Q=typeof self=="object"&&self&&self.Object===Object&&self,le=ee||Q||Function("return this")(),ue=e&&!e.nodeType&&e,K=ue&&!0&&n&&!n.nodeType&&n,Y=K&&K.exports===ue,oe=Y&&ee.process,ce=function(){try{var T=K&&K.require&&K.require("util").types;return T||oe&&oe.binding&&oe.binding("util")}catch{}}(),fe=ce&&ce.isTypedArray;function M(T,W,V){switch(V.length){case 0:return T.call(W);case 1:return T.call(W,V[0]);case 2:return T.call(W,V[0],V[1]);case 3:return T.call(W,V[0],V[1],V[2])}return T.apply(W,V)}function U(T,W){for(var V=-1,he=Array(T);++V<T;)he[V]=W(V);return he}function z(T){return function(W){return T(W)}}function re(T,W){return T?.[W]}function se(T,W){return function(V){return T(W(V))}}var ie=Array.prototype,q=Function.prototype,X=Object.prototype,ne=le["__core-js_shared__"],Z=q.toString,we=X.hasOwnProperty,Le=function(){var T=/[^.]+$/.exec(ne&&ne.keys&&ne.keys.IE_PROTO||"");return T?"Symbol(src)_1."+T:""}(),ye=X.toString,Be=Z.call(Object),xe=RegExp("^"+Z.call(we).replace(v,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Pe=Y?le.Buffer:void 0,Ye=le.Symbol,mt=le.Uint8Array;Pe&&Pe.allocUnsafe;var Me=se(Object.getPrototypeOf,Object),un=Object.create,Vn=X.propertyIsEnumerable,fn=ie.splice,rt=Ye?Ye.toStringTag:void 0,Pt=function(){try{var T=Qn(Object,"defineProperty");return T({},"",{}),T}catch{}}(),Kn=Pe?Pe.isBuffer:void 0,qt=Math.max,cn=Date.now,dn=Qn(le,"Map"),ht=Qn(Object,"create"),Wt=function(){function T(){}return function(W){if(!St(W))return{};if(un)return un(W);T.prototype=W;var V=new T;return T.prototype=void 0,V}}();function it(T){var W=-1,V=T==null?0:T.length;for(this.clear();++W<V;){var he=T[W];this.set(he[0],he[1])}}function Yn(){this.__data__=ht?ht(null):{},this.size=0}function Ft(T){var W=this.has(T)&&delete this.__data__[T];return this.size-=W?1:0,W}function hn(T){var W=this.__data__;if(ht){var V=W[T];return V===r?void 0:V}return we.call(W,T)?W[T]:void 0}function I(T){var W=this.__data__;return ht?W[T]!==void 0:we.call(W,T)}function x(T,W){var V=this.__data__;return this.size+=this.has(T)?0:1,V[T]=ht&&W===void 0?r:W,this}it.prototype.clear=Yn,it.prototype.delete=Ft,it.prototype.get=hn,it.prototype.has=I,it.prototype.set=x;function te(T){var W=-1,V=T==null?0:T.length;for(this.clear();++W<V;){var he=T[W];this.set(he[0],he[1])}}function ae(){this.__data__=[],this.size=0}function de(T){var W=this.__data__,V=yn(W,T);if(V<0)return!1;var he=W.length-1;return V==he?W.pop():fn.call(W,V,1),--this.size,!0}function Oe(T){var W=this.__data__,V=yn(W,T);return V<0?void 0:W[V][1]}function ke(T){return yn(this.__data__,T)>-1}function gn(T,W){var V=this.__data__,he=yn(V,T);return he<0?(++this.size,V.push([T,W])):V[he][1]=W,this}te.prototype.clear=ae,te.prototype.delete=de,te.prototype.get=Oe,te.prototype.has=ke,te.prototype.set=gn;function Ue(T){var W=-1,V=T==null?0:T.length;for(this.clear();++W<V;){var he=T[W];this.set(he[0],he[1])}}function pn(){this.size=0,this.__data__={hash:new it,map:new(dn||te),string:new it}}function Jn(T){var W=mn(this,T).delete(T);return this.size-=W?1:0,W}function wn(T){return mn(this,T).get(T)}function bn(T){return mn(this,T).has(T)}function $t(T,W){var V=mn(this,T),he=V.size;return V.set(T,W),this.size+=V.size==he?0:1,this}Ue.prototype.clear=pn,Ue.prototype.delete=Jn,Ue.prototype.get=wn,Ue.prototype.has=bn,Ue.prototype.set=$t;function et(T){var W=this.__data__=new te(T);this.size=W.size}function Ht(){this.__data__=new te,this.size=0}function Et(T){var W=this.__data__,V=W.delete(T);return this.size=W.size,V}function ot(T){return this.__data__.get(T)}function Je(T){return this.__data__.has(T)}function As(T,W){var V=this.__data__;if(V instanceof te){var he=V.__data__;if(!dn||he.length<t-1)return he.push([T,W]),this.size=++V.size,this;V=this.__data__=new Ue(he)}return V.set(T,W),this.size=V.size,this}et.prototype.clear=Ht,et.prototype.delete=Et,et.prototype.get=ot,et.prototype.has=Je,et.prototype.set=As;function Rs(T,W){var V=nr(T),he=!V&&tr(T),be=!V&&!he&&mi(T),me=!V&&!he&&!be&&Si(T),ve=V||he||be||me,_e=ve?U(T.length,String):[],Ae=_e.length;for(var Ze in T)ve&&(Ze=="length"||be&&(Ze=="offset"||Ze=="parent")||me&&(Ze=="buffer"||Ze=="byteLength"||Ze=="byteOffset")||yi(Ze,Ae))||_e.push(Ze);return _e}function Zn(T,W,V){(V!==void 0&&!En(T[W],V)||V===void 0&&!(W in T))&&Xn(T,W,V)}function Is(T,W,V){var he=T[W];(!(we.call(T,W)&&En(he,V))||V===void 0&&!(W in T))&&Xn(T,W,V)}function yn(T,W){for(var V=T.length;V--;)if(En(T[V][0],W))return V;return-1}function Xn(T,W,V){W=="__proto__"&&Pt?Pt(T,W,{configurable:!0,enumerable:!0,value:V,writable:!0}):T[W]=V}var Ts=qs();function _n(T){return T==null?T===void 0?G:f:rt&&rt in Object(T)?Ws(T):Vs(T)}function wi(T){return zt(T)&&_n(T)==u}function xs(T){if(!St(T)||zs(T))return!1;var W=ir(T)?xe:$;return W.test(Zs(T))}function Os(T){return zt(T)&&Ei(T.length)&&!!H[_n(T)]}function Ns(T){if(!St(T))return Gs(T);var W=_i(T),V=[];for(var he in T)he=="constructor"&&(W||!we.call(T,he))||V.push(he);return V}function bi(T,W,V,he,be){T!==W&&Ts(W,function(me,ve){if(be||(be=new et),St(me))Cs(T,W,ve,V,bi,he,be);else{var _e=he?he(er(T,ve),me,ve+"",T,W,be):void 0;_e===void 0&&(_e=me),Zn(T,ve,_e)}},vi)}function Cs(T,W,V,he,be,me,ve){var _e=er(T,V),Ae=er(W,V),Ze=ve.get(Ae);if(Ze){Zn(T,V,Ze);return}var Fe=me?me(_e,Ae,V+"",T,W,ve):void 0,Gt=Fe===void 0;if(Gt){var or=nr(Ae),sr=!or&&mi(Ae),Ri=!or&&!sr&&Si(Ae);Fe=Ae,or||sr||Ri?nr(_e)?Fe=_e:Xs(_e)?Fe=ks(_e):sr?(Gt=!1,Fe=Ms(Ae)):Ri?(Gt=!1,Fe=Ds(Ae)):Fe=[]:Qs(Ae)||tr(Ae)?(Fe=_e,tr(_e)?Fe=ea(_e):(!St(_e)||ir(_e))&&(Fe=Fs(Ae))):Gt=!1}Gt&&(ve.set(Ae,Fe),be(Fe,Ae,he,me,ve),ve.delete(Ae)),Zn(T,V,Fe)}function Ls(T,W){return Ys(Ks(T,W,Ai),T+"")}var Ps=Pt?function(T,W){return Pt(T,"toString",{configurable:!0,enumerable:!1,value:na(W),writable:!0})}:Ai;function Ms(T,W){return T.slice()}function Bs(T){var W=new T.constructor(T.byteLength);return new mt(W).set(new mt(T)),W}function Ds(T,W){var V=Bs(T.buffer);return new T.constructor(V,T.byteOffset,T.length)}function ks(T,W){var V=-1,he=T.length;for(W||(W=Array(he));++V<he;)W[V]=T[V];return W}function Us(T,W,V,he){var be=!V;V||(V={});for(var me=-1,ve=W.length;++me<ve;){var _e=W[me],Ae=void 0;Ae===void 0&&(Ae=T[_e]),be?Xn(V,_e,Ae):Is(V,_e,Ae)}return V}function js(T){return Ls(function(W,V){var he=-1,be=V.length,me=be>1?V[be-1]:void 0,ve=be>2?V[2]:void 0;for(me=T.length>3&&typeof me=="function"?(be--,me):void 0,ve&&$s(V[0],V[1],ve)&&(me=be<3?void 0:me,be=1),W=Object(W);++he<be;){var _e=V[he];_e&&T(W,_e,he,me)}return W})}function qs(T){return function(W,V,he){for(var be=-1,me=Object(W),ve=he(W),_e=ve.length;_e--;){var Ae=ve[++be];if(V(me[Ae],Ae,me)===!1)break}return W}}function mn(T,W){var V=T.__data__;return Hs(W)?V[typeof W=="string"?"string":"hash"]:V.map}function Qn(T,W){var V=re(T,W);return xs(V)?V:void 0}function Ws(T){var W=we.call(T,rt),V=T[rt];try{T[rt]=void 0;var he=!0}catch{}var be=ye.call(T);return he&&(W?T[rt]=V:delete T[rt]),be}function Fs(T){return typeof T.constructor=="function"&&!_i(T)?Wt(Me(T)):{}}function yi(T,W){var V=typeof T;return W=W??s,!!W&&(V=="number"||V!="symbol"&&J.test(T))&&T>-1&&T%1==0&&T<W}function $s(T,W,V){if(!St(V))return!1;var he=typeof W;return(he=="number"?rr(V)&&yi(W,V.length):he=="string"&&W in V)?En(V[W],T):!1}function Hs(T){var W=typeof T;return W=="string"||W=="number"||W=="symbol"||W=="boolean"?T!=="__proto__":T===null}function zs(T){return!!Le&&Le in T}function _i(T){var W=T&&T.constructor,V=typeof W=="function"&&W.prototype||X;return T===V}function Gs(T){var W=[];if(T!=null)for(var V in Object(T))W.push(V);return W}function Vs(T){return ye.call(T)}function Ks(T,W,V){return W=qt(W===void 0?T.length-1:W,0),function(){for(var he=arguments,be=-1,me=qt(he.length-W,0),ve=Array(me);++be<me;)ve[be]=he[W+be];be=-1;for(var _e=Array(W+1);++be<W;)_e[be]=he[be];return _e[W]=V(ve),M(T,this,_e)}}function er(T,W){if(!(W==="constructor"&&typeof T[W]=="function")&&W!="__proto__")return T[W]}var Ys=Js(Ps);function Js(T){var W=0,V=0;return function(){var he=cn(),be=o-(he-V);if(V=he,be>0){if(++W>=i)return arguments[0]}else W=0;return T.apply(void 0,arguments)}}function Zs(T){if(T!=null){try{return Z.call(T)}catch{}try{return T+""}catch{}}return""}function En(T,W){return T===W||T!==T&&W!==W}var tr=wi(function(){return arguments}())?wi:function(T){return zt(T)&&we.call(T,"callee")&&!Vn.call(T,"callee")},nr=Array.isArray;function rr(T){return T!=null&&Ei(T.length)&&!ir(T)}function Xs(T){return zt(T)&&rr(T)}var mi=Kn||ra;function ir(T){if(!St(T))return!1;var W=_n(T);return W==j||W==D||W==b||W==S}function Ei(T){return typeof T=="number"&&T>-1&&T%1==0&&T<=s}function St(T){var W=typeof T;return T!=null&&(W=="object"||W=="function")}function zt(T){return T!=null&&typeof T=="object"}function Qs(T){if(!zt(T)||_n(T)!=c)return!1;var W=Me(T);if(W===null)return!0;var V=we.call(W,"constructor")&&W.constructor;return typeof V=="function"&&V instanceof V&&Z.call(V)==Be}var Si=fe?z(fe):Os;function ea(T){return Us(T,vi(T))}function vi(T){return rr(T)?Rs(T):Ns(T)}var ta=js(function(T,W,V){bi(T,W,V)});function na(T){return function(){return T}}function Ai(T){return T}function ra(){return!1}n.exports=ta}(Zt,Zt.exports)),Zt.exports}Ba();var Mn={exports:{}},Da=Mn.exports,Ao;function ka(){return Ao||(Ao=1,function(n){(function(e,t){n.exports?n.exports=t():e.log=t()})(Da,function(){var e=function(){},t="undefined",r=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),i=["trace","debug","info","warn","error"],o={},s=null;function u(m,_){var f=m[_];if(typeof f.bind=="function")return f.bind(m);try{return Function.prototype.bind.call(f,m)}catch{return function(){return Function.prototype.apply.apply(f,[m,arguments])}}}function h(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function b(m){return m==="debug"&&(m="log"),typeof console===t?!1:m==="trace"&&r?h:console[m]!==void 0?u(console,m):console.log!==void 0?u(console,"log"):e}function C(){for(var m=this.getLevel(),_=0;_<i.length;_++){var f=i[_];this[f]=_<m?e:this.methodFactory(f,m,this.name)}if(this.log=this.debug,typeof console===t&&m<this.levels.SILENT)return"No console available for logging"}function O(m){return function(){typeof console!==t&&(C.call(this),this[m].apply(this,arguments))}}function A(m,_,f){return b(m)||O.apply(this,arguments)}function j(m,_){var f=this,c,S,B,N="loglevel";typeof m=="string"?N+=":"+m:typeof m=="symbol"&&(N=void 0);function F(d){var y=(i[d]||"silent").toUpperCase();if(!(typeof window===t||!N)){try{window.localStorage[N]=y;return}catch{}try{window.document.cookie=encodeURIComponent(N)+"="+y+";"}catch{}}}function G(){var d;if(!(typeof window===t||!N)){try{d=window.localStorage[N]}catch{}if(typeof d===t)try{var y=window.document.cookie,g=encodeURIComponent(N),L=y.indexOf(g+"=");L!==-1&&(d=/^([^;]+)/.exec(y.slice(L+g.length+1))[1])}catch{}return f.levels[d]===void 0&&(d=void 0),d}}function a(){if(!(typeof window===t||!N)){try{window.localStorage.removeItem(N)}catch{}try{window.document.cookie=encodeURIComponent(N)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch{}}}function w(d){var y=d;if(typeof y=="string"&&f.levels[y.toUpperCase()]!==void 0&&(y=f.levels[y.toUpperCase()]),typeof y=="number"&&y>=0&&y<=f.levels.SILENT)return y;throw new TypeError("log.setLevel() called with invalid level: "+d)}f.name=m,f.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},f.methodFactory=_||A,f.getLevel=function(){return B??S??c},f.setLevel=function(d,y){return B=w(d),y!==!1&&F(B),C.call(f)},f.setDefaultLevel=function(d){S=w(d),G()||f.setLevel(d,!1)},f.resetLevel=function(){B=null,a(),C.call(f)},f.enableAll=function(d){f.setLevel(f.levels.TRACE,d)},f.disableAll=function(d){f.setLevel(f.levels.SILENT,d)},f.rebuild=function(){if(s!==f&&(c=w(s.getLevel())),C.call(f),s===f)for(var d in o)o[d].rebuild()},c=w(s?s.getLevel():"WARN");var l=G();l!=null&&(B=w(l)),C.call(f)}s=new j,s.getLogger=function(_){if(typeof _!="symbol"&&typeof _!="string"||_==="")throw new TypeError("You must supply a name when creating a logger.");var f=o[_];return f||(f=o[_]=new j(_,s.methodFactory)),f};var D=typeof window!==t?window.log:void 0;return s.noConflict=function(){return typeof window!==t&&window.log===s&&(window.log=D),s},s.getLoggers=function(){return o},s.default=s,s})}(Mn)),Mn.exports}var ss=ka();const Bn=Ut(ss),Ua=Bn.getLogger("http-helpers");Ua.setLevel(ss.levels.INFO);function Ro(n){if(!Number.isSafeInteger(n)||n<0)throw new Error(`positive integer expected, not ${n}`)}function ja(n){return n instanceof Uint8Array||n!=null&&typeof n=="object"&&n.constructor.name==="Uint8Array"}function Fn(n,...e){if(!ja(n))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(n.length))throw new Error(`Uint8Array expected of length ${e}, not of length=${n.length}`)}function qa(n){if(typeof n!="function"||typeof n.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Ro(n.outputLen),Ro(n.blockLen)}function Dn(n,e=!0){if(n.destroyed)throw new Error("Hash instance has been destroyed");if(e&&n.finished)throw new Error("Hash#digest() has already been called")}function Wa(n,e){Fn(n);const t=e.outputLen;if(n.length<t)throw new Error(`digestInto() expects output buffer of length at least ${t}`)}const qr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Wr=n=>new DataView(n.buffer,n.byteOffset,n.byteLength),tt=(n,e)=>n<<32-e|n>>>e;new Uint8Array(new Uint32Array([287454020]).buffer)[0];function Fa(n){if(typeof n!="string")throw new Error(`utf8ToBytes expected string, got ${typeof n}`);return new Uint8Array(new TextEncoder().encode(n))}function ai(n){return typeof n=="string"&&(n=Fa(n)),Fn(n),n}function $a(...n){let e=0;for(let r=0;r<n.length;r++){const i=n[r];Fn(i),e+=i.length}const t=new Uint8Array(e);for(let r=0,i=0;r<n.length;r++){const o=n[r];t.set(o,i),i+=o.length}return t}class as{clone(){return this._cloneInto()}}function Ha(n){const e=r=>n().update(ai(r)).digest(),t=n();return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=()=>n(),e}function za(n=32){if(qr&&typeof qr.getRandomValues=="function")return qr.getRandomValues(new Uint8Array(n));throw new Error("crypto.getRandomValues must be defined")}function Ga(n,e,t,r){if(typeof n.setBigUint64=="function")return n.setBigUint64(e,t,r);const i=BigInt(32),o=BigInt(4294967295),s=Number(t>>i&o),u=Number(t&o),h=r?4:0,b=r?0:4;n.setUint32(e+h,s,r),n.setUint32(e+b,u,r)}const Va=(n,e,t)=>n&e^~n&t,Ka=(n,e,t)=>n&e^n&t^e&t;class Ya extends as{constructor(e,t,r,i){super(),this.blockLen=e,this.outputLen=t,this.padOffset=r,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=Wr(this.buffer)}update(e){Dn(this);const{view:t,buffer:r,blockLen:i}=this;e=ai(e);const o=e.length;for(let s=0;s<o;){const u=Math.min(i-this.pos,o-s);if(u===i){const h=Wr(e);for(;i<=o-s;s+=i)this.process(h,s);continue}r.set(e.subarray(s,s+u),this.pos),this.pos+=u,s+=u,this.pos===i&&(this.process(t,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){Dn(this),Wa(e,this),this.finished=!0;const{buffer:t,view:r,blockLen:i,isLE:o}=this;let{pos:s}=this;t[s++]=128,this.buffer.subarray(s).fill(0),this.padOffset>i-s&&(this.process(r,0),s=0);for(let O=s;O<i;O++)t[O]=0;Ga(r,i-8,BigInt(this.length*8),o),this.process(r,0);const u=Wr(e),h=this.outputLen;if(h%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const b=h/4,C=this.get();if(b>C.length)throw new Error("_sha2: outputLen bigger than state");for(let O=0;O<b;O++)u.setUint32(4*O,C[O],o)}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const r=e.slice(0,t);return this.destroy(),r}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:t,buffer:r,length:i,finished:o,destroyed:s,pos:u}=this;return e.length=i,e.pos=u,e.finished=o,e.destroyed=s,i%t&&e.buffer.set(r),e}}const Ja=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),pt=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),wt=new Uint32Array(64);class Za extends Ya{constructor(){super(64,32,8,!1),this.A=pt[0]|0,this.B=pt[1]|0,this.C=pt[2]|0,this.D=pt[3]|0,this.E=pt[4]|0,this.F=pt[5]|0,this.G=pt[6]|0,this.H=pt[7]|0}get(){const{A:e,B:t,C:r,D:i,E:o,F:s,G:u,H:h}=this;return[e,t,r,i,o,s,u,h]}set(e,t,r,i,o,s,u,h){this.A=e|0,this.B=t|0,this.C=r|0,this.D=i|0,this.E=o|0,this.F=s|0,this.G=u|0,this.H=h|0}process(e,t){for(let O=0;O<16;O++,t+=4)wt[O]=e.getUint32(t,!1);for(let O=16;O<64;O++){const A=wt[O-15],j=wt[O-2],D=tt(A,7)^tt(A,18)^A>>>3,m=tt(j,17)^tt(j,19)^j>>>10;wt[O]=m+wt[O-7]+D+wt[O-16]|0}let{A:r,B:i,C:o,D:s,E:u,F:h,G:b,H:C}=this;for(let O=0;O<64;O++){const A=tt(u,6)^tt(u,11)^tt(u,25),j=C+A+Va(u,h,b)+Ja[O]+wt[O]|0,m=(tt(r,2)^tt(r,13)^tt(r,22))+Ka(r,i,o)|0;C=b,b=h,h=u,u=s+j|0,s=o,o=i,i=r,r=j+m|0}r=r+this.A|0,i=i+this.B|0,o=o+this.C|0,s=s+this.D|0,u=u+this.E|0,h=h+this.F|0,b=b+this.G|0,C=C+this.H|0,this.set(r,i,o,s,u,h,b,C)}roundClean(){wt.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const Xa=Ha(()=>new Za);class ls extends as{constructor(e,t){super(),this.finished=!1,this.destroyed=!1,qa(e);const r=ai(t);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const i=this.blockLen,o=new Uint8Array(i);o.set(r.length>i?e.create().update(r).digest():r);for(let s=0;s<o.length;s++)o[s]^=54;this.iHash.update(o),this.oHash=e.create();for(let s=0;s<o.length;s++)o[s]^=106;this.oHash.update(o),o.fill(0)}update(e){return Dn(this),this.iHash.update(e),this}digestInto(e){Dn(this),Fn(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:t,iHash:r,finished:i,destroyed:o,blockLen:s,outputLen:u}=this;return e=e,e.finished=i,e.destroyed=o,e.blockLen=s,e.outputLen=u,e.oHash=t._cloneInto(e.oHash),e.iHash=r._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const us=(n,e,t)=>new ls(n,e).update(t).digest();us.create=(n,e)=>new ls(n,e);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const fs=BigInt(0),$n=BigInt(1),Qa=BigInt(2);function Ot(n){return n instanceof Uint8Array||n!=null&&typeof n=="object"&&n.constructor.name==="Uint8Array"}function sn(n){if(!Ot(n))throw new Error("Uint8Array expected")}const el=Array.from({length:256},(n,e)=>e.toString(16).padStart(2,"0"));function Bt(n){sn(n);let e="";for(let t=0;t<n.length;t++)e+=el[n[t]];return e}function cs(n){const e=n.toString(16);return e.length&1?`0${e}`:e}function li(n){if(typeof n!="string")throw new Error("hex string expected, got "+typeof n);return BigInt(n===""?"0":`0x${n}`)}const at={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function Io(n){if(n>=at._0&&n<=at._9)return n-at._0;if(n>=at._A&&n<=at._F)return n-(at._A-10);if(n>=at._a&&n<=at._f)return n-(at._a-10)}function Dt(n){if(typeof n!="string")throw new Error("hex string expected, got "+typeof n);const e=n.length,t=e/2;if(e%2)throw new Error("padded hex string expected, got unpadded hex of length "+e);const r=new Uint8Array(t);for(let i=0,o=0;i<t;i++,o+=2){const s=Io(n.charCodeAt(o)),u=Io(n.charCodeAt(o+1));if(s===void 0||u===void 0){const h=n[o]+n[o+1];throw new Error('hex string expected, got non-hex character "'+h+'" at index '+o)}r[i]=s*16+u}return r}function xt(n){return li(Bt(n))}function ui(n){return sn(n),li(Bt(Uint8Array.from(n).reverse()))}function kt(n,e){return Dt(n.toString(16).padStart(e*2,"0"))}function fi(n,e){return kt(n,e).reverse()}function tl(n){return Dt(cs(n))}function Qe(n,e,t){let r;if(typeof e=="string")try{r=Dt(e)}catch(o){throw new Error(`${n} must be valid hex string, got "${e}". Cause: ${o}`)}else if(Ot(e))r=Uint8Array.from(e);else throw new Error(`${n} must be hex string or Uint8Array`);const i=r.length;if(typeof t=="number"&&i!==t)throw new Error(`${n} expected ${t} bytes, got ${i}`);return r}function nn(...n){let e=0;for(let r=0;r<n.length;r++){const i=n[r];sn(i),e+=i.length}const t=new Uint8Array(e);for(let r=0,i=0;r<n.length;r++){const o=n[r];t.set(o,i),i+=o.length}return t}function nl(n,e){if(n.length!==e.length)return!1;let t=0;for(let r=0;r<n.length;r++)t|=n[r]^e[r];return t===0}function rl(n){if(typeof n!="string")throw new Error(`utf8ToBytes expected string, got ${typeof n}`);return new Uint8Array(new TextEncoder().encode(n))}function il(n){let e;for(e=0;n>fs;n>>=$n,e+=1);return e}function ol(n,e){return n>>BigInt(e)&$n}function sl(n,e,t){return n|(t?$n:fs)<<BigInt(e)}const ci=n=>(Qa<<BigInt(n-1))-$n,Fr=n=>new Uint8Array(n),To=n=>Uint8Array.from(n);function ds(n,e,t){if(typeof n!="number"||n<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof t!="function")throw new Error("hmacFn must be a function");let r=Fr(n),i=Fr(n),o=0;const s=()=>{r.fill(1),i.fill(0),o=0},u=(...O)=>t(i,r,...O),h=(O=Fr())=>{i=u(To([0]),O),r=u(),O.length!==0&&(i=u(To([1]),O),r=u())},b=()=>{if(o++>=1e3)throw new Error("drbg: tried 1000 values");let O=0;const A=[];for(;O<e;){r=u();const j=r.slice();A.push(j),O+=r.length}return nn(...A)};return(O,A)=>{s(),h(O);let j;for(;!(j=A(b()));)h();return s(),j}}const al={bigint:n=>typeof n=="bigint",function:n=>typeof n=="function",boolean:n=>typeof n=="boolean",string:n=>typeof n=="string",stringOrUint8Array:n=>typeof n=="string"||Ot(n),isSafeInteger:n=>Number.isSafeInteger(n),array:n=>Array.isArray(n),field:(n,e)=>e.Fp.isValid(n),hash:n=>typeof n=="function"&&Number.isSafeInteger(n.outputLen)};function an(n,e,t={}){const r=(i,o,s)=>{const u=al[o];if(typeof u!="function")throw new Error(`Invalid validator "${o}", expected function`);const h=n[i];if(!(s&&h===void 0)&&!u(h,n))throw new Error(`Invalid param ${String(i)}=${h} (${typeof h}), expected ${o}`)};for(const[i,o]of Object.entries(e))r(i,o,!1);for(const[i,o]of Object.entries(t))r(i,o,!0);return n}const ll=Object.freeze(Object.defineProperty({__proto__:null,abytes:sn,bitGet:ol,bitLen:il,bitMask:ci,bitSet:sl,bytesToHex:Bt,bytesToNumberBE:xt,bytesToNumberLE:ui,concatBytes:nn,createHmacDrbg:ds,ensureBytes:Qe,equalBytes:nl,hexToBytes:Dt,hexToNumber:li,isBytes:Ot,numberToBytesBE:kt,numberToBytesLE:fi,numberToHexUnpadded:cs,numberToVarBytesBE:tl,utf8ToBytes:rl,validateObject:an},Symbol.toStringTag,{value:"Module"}));/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Ce=BigInt(0),Te=BigInt(1),It=BigInt(2),ul=BigInt(3),Zr=BigInt(4),xo=BigInt(5),Oo=BigInt(8);BigInt(9);BigInt(16);function qe(n,e){const t=n%e;return t>=Ce?t:e+t}function fl(n,e,t){if(t<=Ce||e<Ce)throw new Error("Expected power/modulo > 0");if(t===Te)return Ce;let r=Te;for(;e>Ce;)e&Te&&(r=r*n%t),n=n*n%t,e>>=Te;return r}function He(n,e,t){let r=n;for(;e-- >Ce;)r*=r,r%=t;return r}function Xr(n,e){if(n===Ce||e<=Ce)throw new Error(`invert: expected positive integers, got n=${n} mod=${e}`);let t=qe(n,e),r=e,i=Ce,o=Te;for(;t!==Ce;){const u=r/t,h=r%t,b=i-o*u;r=t,t=h,i=o,o=b}if(r!==Te)throw new Error("invert: does not exist");return qe(i,e)}function cl(n){const e=(n-Te)/It;let t,r,i;for(t=n-Te,r=0;t%It===Ce;t/=It,r++);for(i=It;i<n&&fl(i,e,n)!==n-Te;i++);if(r===1){const s=(n+Te)/Zr;return function(h,b){const C=h.pow(b,s);if(!h.eql(h.sqr(C),b))throw new Error("Cannot find square root");return C}}const o=(t+Te)/It;return function(u,h){if(u.pow(h,e)===u.neg(u.ONE))throw new Error("Cannot find square root");let b=r,C=u.pow(u.mul(u.ONE,i),t),O=u.pow(h,o),A=u.pow(h,t);for(;!u.eql(A,u.ONE);){if(u.eql(A,u.ZERO))return u.ZERO;let j=1;for(let m=u.sqr(A);j<b&&!u.eql(m,u.ONE);j++)m=u.sqr(m);const D=u.pow(C,Te<<BigInt(b-j-1));C=u.sqr(D),O=u.mul(O,D),A=u.mul(A,C),b=j}return O}}function dl(n){if(n%Zr===ul){const e=(n+Te)/Zr;return function(r,i){const o=r.pow(i,e);if(!r.eql(r.sqr(o),i))throw new Error("Cannot find square root");return o}}if(n%Oo===xo){const e=(n-xo)/Oo;return function(r,i){const o=r.mul(i,It),s=r.pow(o,e),u=r.mul(i,s),h=r.mul(r.mul(u,It),s),b=r.mul(u,r.sub(h,r.ONE));if(!r.eql(r.sqr(b),i))throw new Error("Cannot find square root");return b}}return cl(n)}const hl=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function gl(n){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},t=hl.reduce((r,i)=>(r[i]="function",r),e);return an(n,t)}function pl(n,e,t){if(t<Ce)throw new Error("Expected power > 0");if(t===Ce)return n.ONE;if(t===Te)return e;let r=n.ONE,i=e;for(;t>Ce;)t&Te&&(r=n.mul(r,i)),i=n.sqr(i),t>>=Te;return r}function wl(n,e){const t=new Array(e.length),r=e.reduce((o,s,u)=>n.is0(s)?o:(t[u]=o,n.mul(o,s)),n.ONE),i=n.inv(r);return e.reduceRight((o,s,u)=>n.is0(s)?o:(t[u]=n.mul(o,t[u]),n.mul(o,s)),i),t}function hs(n,e){const t=e!==void 0?e:n.toString(2).length,r=Math.ceil(t/8);return{nBitLength:t,nByteLength:r}}function bl(n,e,t=!1,r={}){if(n<=Ce)throw new Error(`Expected Field ORDER > 0, got ${n}`);const{nBitLength:i,nByteLength:o}=hs(n,e);if(o>2048)throw new Error("Field lengths over 2048 bytes are not supported");const s=dl(n),u=Object.freeze({ORDER:n,BITS:i,BYTES:o,MASK:ci(i),ZERO:Ce,ONE:Te,create:h=>qe(h,n),isValid:h=>{if(typeof h!="bigint")throw new Error(`Invalid field element: expected bigint, got ${typeof h}`);return Ce<=h&&h<n},is0:h=>h===Ce,isOdd:h=>(h&Te)===Te,neg:h=>qe(-h,n),eql:(h,b)=>h===b,sqr:h=>qe(h*h,n),add:(h,b)=>qe(h+b,n),sub:(h,b)=>qe(h-b,n),mul:(h,b)=>qe(h*b,n),pow:(h,b)=>pl(u,h,b),div:(h,b)=>qe(h*Xr(b,n),n),sqrN:h=>h*h,addN:(h,b)=>h+b,subN:(h,b)=>h-b,mulN:(h,b)=>h*b,inv:h=>Xr(h,n),sqrt:r.sqrt||(h=>s(u,h)),invertBatch:h=>wl(u,h),cmov:(h,b,C)=>C?b:h,toBytes:h=>t?fi(h,o):kt(h,o),fromBytes:h=>{if(h.length!==o)throw new Error(`Fp.fromBytes: expected ${o}, got ${h.length}`);return t?ui(h):xt(h)}});return Object.freeze(u)}function gs(n){if(typeof n!="bigint")throw new Error("field order must be bigint");const e=n.toString(2).length;return Math.ceil(e/8)}function ps(n){const e=gs(n);return e+Math.ceil(e/2)}function yl(n,e,t=!1){const r=n.length,i=gs(e),o=ps(e);if(r<16||r<o||r>1024)throw new Error(`expected ${o}-1024 bytes of input, got ${r}`);const s=t?xt(n):ui(n),u=qe(s,e-Te)+Te;return t?fi(u,i):kt(u,i)}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const _l=BigInt(0),$r=BigInt(1);function ml(n,e){const t=(i,o)=>{const s=o.negate();return i?s:o},r=i=>{const o=Math.ceil(e/i)+1,s=2**(i-1);return{windows:o,windowSize:s}};return{constTimeNegate:t,unsafeLadder(i,o){let s=n.ZERO,u=i;for(;o>_l;)o&$r&&(s=s.add(u)),u=u.double(),o>>=$r;return s},precomputeWindow(i,o){const{windows:s,windowSize:u}=r(o),h=[];let b=i,C=b;for(let O=0;O<s;O++){C=b,h.push(C);for(let A=1;A<u;A++)C=C.add(b),h.push(C);b=C.double()}return h},wNAF(i,o,s){const{windows:u,windowSize:h}=r(i);let b=n.ZERO,C=n.BASE;const O=BigInt(2**i-1),A=2**i,j=BigInt(i);for(let D=0;D<u;D++){const m=D*h;let _=Number(s&O);s>>=j,_>h&&(_-=A,s+=$r);const f=m,c=m+Math.abs(_)-1,S=D%2!==0,B=_<0;_===0?C=C.add(t(S,o[f])):b=b.add(t(B,o[c]))}return{p:b,f:C}},wNAFCached(i,o,s,u){const h=i._WINDOW_SIZE||1;let b=o.get(i);return b||(b=this.precomputeWindow(i,h),h!==1&&o.set(i,u(b))),this.wNAF(h,b,s)}}}function ws(n){return gl(n.Fp),an(n,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...hs(n.n,n.nBitLength),...n,p:n.Fp.ORDER})}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function El(n){const e=ws(n);an(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:t,Fp:r,a:i}=e;if(t){if(!r.eql(i,r.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if(typeof t!="object"||typeof t.beta!="bigint"||typeof t.splitScalar!="function")throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...e})}const{bytesToNumberBE:Sl,hexToBytes:vl}=ll,Tt={Err:class extends Error{constructor(e=""){super(e)}},_parseInt(n){const{Err:e}=Tt;if(n.length<2||n[0]!==2)throw new e("Invalid signature integer tag");const t=n[1],r=n.subarray(2,t+2);if(!t||r.length!==t)throw new e("Invalid signature integer: wrong length");if(r[0]&128)throw new e("Invalid signature integer: negative");if(r[0]===0&&!(r[1]&128))throw new e("Invalid signature integer: unnecessary leading zero");return{d:Sl(r),l:n.subarray(t+2)}},toSig(n){const{Err:e}=Tt,t=typeof n=="string"?vl(n):n;sn(t);let r=t.length;if(r<2||t[0]!=48)throw new e("Invalid signature tag");if(t[1]!==r-2)throw new e("Invalid signature: incorrect length");const{d:i,l:o}=Tt._parseInt(t.subarray(2)),{d:s,l:u}=Tt._parseInt(o);if(u.length)throw new e("Invalid signature: left bytes after parsing");return{r:i,s}},hexFromSig(n){const e=b=>Number.parseInt(b[0],16)&8?"00"+b:b,t=b=>{const C=b.toString(16);return C.length&1?`0${C}`:C},r=e(t(n.s)),i=e(t(n.r)),o=r.length/2,s=i.length/2,u=t(o),h=t(s);return`30${t(s+o+4)}02${h}${i}02${u}${r}`}},ut=BigInt(0),Ve=BigInt(1);BigInt(2);const No=BigInt(3);BigInt(4);function Al(n){const e=El(n),{Fp:t}=e,r=e.toBytes||((D,m,_)=>{const f=m.toAffine();return nn(Uint8Array.from([4]),t.toBytes(f.x),t.toBytes(f.y))}),i=e.fromBytes||(D=>{const m=D.subarray(1),_=t.fromBytes(m.subarray(0,t.BYTES)),f=t.fromBytes(m.subarray(t.BYTES,2*t.BYTES));return{x:_,y:f}});function o(D){const{a:m,b:_}=e,f=t.sqr(D),c=t.mul(f,D);return t.add(t.add(c,t.mul(D,m)),_)}if(!t.eql(t.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function s(D){return typeof D=="bigint"&&ut<D&&D<e.n}function u(D){if(!s(D))throw new Error("Expected valid bigint: 0 < bigint < curve.n")}function h(D){const{allowedPrivateKeyLengths:m,nByteLength:_,wrapPrivateKey:f,n:c}=e;if(m&&typeof D!="bigint"){if(Ot(D)&&(D=Bt(D)),typeof D!="string"||!m.includes(D.length))throw new Error("Invalid key");D=D.padStart(_*2,"0")}let S;try{S=typeof D=="bigint"?D:xt(Qe("private key",D,_))}catch{throw new Error(`private key must be ${_} bytes, hex or bigint, not ${typeof D}`)}return f&&(S=qe(S,c)),u(S),S}const b=new Map;function C(D){if(!(D instanceof O))throw new Error("ProjectivePoint expected")}class O{constructor(m,_,f){if(this.px=m,this.py=_,this.pz=f,m==null||!t.isValid(m))throw new Error("x required");if(_==null||!t.isValid(_))throw new Error("y required");if(f==null||!t.isValid(f))throw new Error("z required")}static fromAffine(m){const{x:_,y:f}=m||{};if(!m||!t.isValid(_)||!t.isValid(f))throw new Error("invalid affine point");if(m instanceof O)throw new Error("projective point not allowed");const c=S=>t.eql(S,t.ZERO);return c(_)&&c(f)?O.ZERO:new O(_,f,t.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(m){const _=t.invertBatch(m.map(f=>f.pz));return m.map((f,c)=>f.toAffine(_[c])).map(O.fromAffine)}static fromHex(m){const _=O.fromAffine(i(Qe("pointHex",m)));return _.assertValidity(),_}static fromPrivateKey(m){return O.BASE.multiply(h(m))}_setWindowSize(m){this._WINDOW_SIZE=m,b.delete(this)}assertValidity(){if(this.is0()){if(e.allowInfinityPoint&&!t.is0(this.py))return;throw new Error("bad point: ZERO")}const{x:m,y:_}=this.toAffine();if(!t.isValid(m)||!t.isValid(_))throw new Error("bad point: x or y not FE");const f=t.sqr(_),c=o(m);if(!t.eql(f,c))throw new Error("bad point: equation left != right");if(!this.isTorsionFree())throw new Error("bad point: not in prime-order subgroup")}hasEvenY(){const{y:m}=this.toAffine();if(t.isOdd)return!t.isOdd(m);throw new Error("Field doesn't support isOdd")}equals(m){C(m);const{px:_,py:f,pz:c}=this,{px:S,py:B,pz:N}=m,F=t.eql(t.mul(_,N),t.mul(S,c)),G=t.eql(t.mul(f,N),t.mul(B,c));return F&&G}negate(){return new O(this.px,t.neg(this.py),this.pz)}double(){const{a:m,b:_}=e,f=t.mul(_,No),{px:c,py:S,pz:B}=this;let N=t.ZERO,F=t.ZERO,G=t.ZERO,a=t.mul(c,c),w=t.mul(S,S),l=t.mul(B,B),d=t.mul(c,S);return d=t.add(d,d),G=t.mul(c,B),G=t.add(G,G),N=t.mul(m,G),F=t.mul(f,l),F=t.add(N,F),N=t.sub(w,F),F=t.add(w,F),F=t.mul(N,F),N=t.mul(d,N),G=t.mul(f,G),l=t.mul(m,l),d=t.sub(a,l),d=t.mul(m,d),d=t.add(d,G),G=t.add(a,a),a=t.add(G,a),a=t.add(a,l),a=t.mul(a,d),F=t.add(F,a),l=t.mul(S,B),l=t.add(l,l),a=t.mul(l,d),N=t.sub(N,a),G=t.mul(l,w),G=t.add(G,G),G=t.add(G,G),new O(N,F,G)}add(m){C(m);const{px:_,py:f,pz:c}=this,{px:S,py:B,pz:N}=m;let F=t.ZERO,G=t.ZERO,a=t.ZERO;const w=e.a,l=t.mul(e.b,No);let d=t.mul(_,S),y=t.mul(f,B),g=t.mul(c,N),L=t.add(_,f),p=t.add(S,B);L=t.mul(L,p),p=t.add(d,y),L=t.sub(L,p),p=t.add(_,c);let R=t.add(S,N);return p=t.mul(p,R),R=t.add(d,g),p=t.sub(p,R),R=t.add(f,c),F=t.add(B,N),R=t.mul(R,F),F=t.add(y,g),R=t.sub(R,F),a=t.mul(w,p),F=t.mul(l,g),a=t.add(F,a),F=t.sub(y,a),a=t.add(y,a),G=t.mul(F,a),y=t.add(d,d),y=t.add(y,d),g=t.mul(w,g),p=t.mul(l,p),y=t.add(y,g),g=t.sub(d,g),g=t.mul(w,g),p=t.add(p,g),d=t.mul(y,p),G=t.add(G,d),d=t.mul(R,p),F=t.mul(L,F),F=t.sub(F,d),d=t.mul(L,y),a=t.mul(R,a),a=t.add(a,d),new O(F,G,a)}subtract(m){return this.add(m.negate())}is0(){return this.equals(O.ZERO)}wNAF(m){return j.wNAFCached(this,b,m,_=>{const f=t.invertBatch(_.map(c=>c.pz));return _.map((c,S)=>c.toAffine(f[S])).map(O.fromAffine)})}multiplyUnsafe(m){const _=O.ZERO;if(m===ut)return _;if(u(m),m===Ve)return this;const{endo:f}=e;if(!f)return j.unsafeLadder(this,m);let{k1neg:c,k1:S,k2neg:B,k2:N}=f.splitScalar(m),F=_,G=_,a=this;for(;S>ut||N>ut;)S&Ve&&(F=F.add(a)),N&Ve&&(G=G.add(a)),a=a.double(),S>>=Ve,N>>=Ve;return c&&(F=F.negate()),B&&(G=G.negate()),G=new O(t.mul(G.px,f.beta),G.py,G.pz),F.add(G)}multiply(m){u(m);let _=m,f,c;const{endo:S}=e;if(S){const{k1neg:B,k1:N,k2neg:F,k2:G}=S.splitScalar(_);let{p:a,f:w}=this.wNAF(N),{p:l,f:d}=this.wNAF(G);a=j.constTimeNegate(B,a),l=j.constTimeNegate(F,l),l=new O(t.mul(l.px,S.beta),l.py,l.pz),f=a.add(l),c=w.add(d)}else{const{p:B,f:N}=this.wNAF(_);f=B,c=N}return O.normalizeZ([f,c])[0]}multiplyAndAddUnsafe(m,_,f){const c=O.BASE,S=(N,F)=>F===ut||F===Ve||!N.equals(c)?N.multiplyUnsafe(F):N.multiply(F),B=S(this,_).add(S(m,f));return B.is0()?void 0:B}toAffine(m){const{px:_,py:f,pz:c}=this,S=this.is0();m==null&&(m=S?t.ONE:t.inv(c));const B=t.mul(_,m),N=t.mul(f,m),F=t.mul(c,m);if(S)return{x:t.ZERO,y:t.ZERO};if(!t.eql(F,t.ONE))throw new Error("invZ was invalid");return{x:B,y:N}}isTorsionFree(){const{h:m,isTorsionFree:_}=e;if(m===Ve)return!0;if(_)return _(O,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:m,clearCofactor:_}=e;return m===Ve?this:_?_(O,this):this.multiplyUnsafe(e.h)}toRawBytes(m=!0){return this.assertValidity(),r(O,this,m)}toHex(m=!0){return Bt(this.toRawBytes(m))}}O.BASE=new O(e.Gx,e.Gy,t.ONE),O.ZERO=new O(t.ZERO,t.ONE,t.ZERO);const A=e.nBitLength,j=ml(O,e.endo?Math.ceil(A/2):A);return{CURVE:e,ProjectivePoint:O,normPrivateKeyToScalar:h,weierstrassEquation:o,isWithinCurveOrder:s}}function Rl(n){const e=ws(n);return an(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}function Il(n){const e=Rl(n),{Fp:t,n:r}=e,i=t.BYTES+1,o=2*t.BYTES+1;function s(p){return ut<p&&p<t.ORDER}function u(p){return qe(p,r)}function h(p){return Xr(p,r)}const{ProjectivePoint:b,normPrivateKeyToScalar:C,weierstrassEquation:O,isWithinCurveOrder:A}=Al({...e,toBytes(p,R,P){const E=R.toAffine(),k=t.toBytes(E.x),v=nn;return P?v(Uint8Array.from([R.hasEvenY()?2:3]),k):v(Uint8Array.from([4]),k,t.toBytes(E.y))},fromBytes(p){const R=p.length,P=p[0],E=p.subarray(1);if(R===i&&(P===2||P===3)){const k=xt(E);if(!s(k))throw new Error("Point is not on curve");const v=O(k);let $;try{$=t.sqrt(v)}catch(ee){const Q=ee instanceof Error?": "+ee.message:"";throw new Error("Point is not on curve"+Q)}const J=($&Ve)===Ve;return(P&1)===1!==J&&($=t.neg($)),{x:k,y:$}}else if(R===o&&P===4){const k=t.fromBytes(E.subarray(0,t.BYTES)),v=t.fromBytes(E.subarray(t.BYTES,2*t.BYTES));return{x:k,y:v}}else throw new Error(`Point of length ${R} was invalid. Expected ${i} compressed bytes or ${o} uncompressed bytes`)}}),j=p=>Bt(kt(p,e.nByteLength));function D(p){const R=r>>Ve;return p>R}function m(p){return D(p)?u(-p):p}const _=(p,R,P)=>xt(p.slice(R,P));class f{constructor(R,P,E){this.r=R,this.s=P,this.recovery=E,this.assertValidity()}static fromCompact(R){const P=e.nByteLength;return R=Qe("compactSignature",R,P*2),new f(_(R,0,P),_(R,P,2*P))}static fromDER(R){const{r:P,s:E}=Tt.toSig(Qe("DER",R));return new f(P,E)}assertValidity(){if(!A(this.r))throw new Error("r must be 0 < r < CURVE.n");if(!A(this.s))throw new Error("s must be 0 < s < CURVE.n")}addRecoveryBit(R){return new f(this.r,this.s,R)}recoverPublicKey(R){const{r:P,s:E,recovery:k}=this,v=G(Qe("msgHash",R));if(k==null||![0,1,2,3].includes(k))throw new Error("recovery id invalid");const $=k===2||k===3?P+e.n:P;if($>=t.ORDER)throw new Error("recovery id 2 or 3 invalid");const J=(k&1)===0?"02":"03",H=b.fromHex(J+j($)),ee=h($),Q=u(-v*ee),le=u(E*ee),ue=b.BASE.multiplyAndAddUnsafe(H,Q,le);if(!ue)throw new Error("point at infinify");return ue.assertValidity(),ue}hasHighS(){return D(this.s)}normalizeS(){return this.hasHighS()?new f(this.r,u(-this.s),this.recovery):this}toDERRawBytes(){return Dt(this.toDERHex())}toDERHex(){return Tt.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Dt(this.toCompactHex())}toCompactHex(){return j(this.r)+j(this.s)}}const c={isValidPrivateKey(p){try{return C(p),!0}catch{return!1}},normPrivateKeyToScalar:C,randomPrivateKey:()=>{const p=ps(e.n);return yl(e.randomBytes(p),e.n)},precompute(p=8,R=b.BASE){return R._setWindowSize(p),R.multiply(BigInt(3)),R}};function S(p,R=!0){return b.fromPrivateKey(p).toRawBytes(R)}function B(p){const R=Ot(p),P=typeof p=="string",E=(R||P)&&p.length;return R?E===i||E===o:P?E===2*i||E===2*o:p instanceof b}function N(p,R,P=!0){if(B(p))throw new Error("first arg must be private key");if(!B(R))throw new Error("second arg must be public key");return b.fromHex(R).multiply(C(p)).toRawBytes(P)}const F=e.bits2int||function(p){const R=xt(p),P=p.length*8-e.nBitLength;return P>0?R>>BigInt(P):R},G=e.bits2int_modN||function(p){return u(F(p))},a=ci(e.nBitLength);function w(p){if(typeof p!="bigint")throw new Error("bigint expected");if(!(ut<=p&&p<a))throw new Error(`bigint expected < 2^${e.nBitLength}`);return kt(p,e.nByteLength)}function l(p,R,P=d){if(["recovered","canonical"].some(Y=>Y in P))throw new Error("sign() legacy options not supported");const{hash:E,randomBytes:k}=e;let{lowS:v,prehash:$,extraEntropy:J}=P;v==null&&(v=!0),p=Qe("msgHash",p),$&&(p=Qe("prehashed msgHash",E(p)));const H=G(p),ee=C(R),Q=[w(ee),w(H)];if(J!=null&&J!==!1){const Y=J===!0?k(t.BYTES):J;Q.push(Qe("extraEntropy",Y))}const le=nn(...Q),ue=H;function K(Y){const oe=F(Y);if(!A(oe))return;const ce=h(oe),fe=b.BASE.multiply(oe).toAffine(),M=u(fe.x);if(M===ut)return;const U=u(ce*u(ue+M*ee));if(U===ut)return;let z=(fe.x===M?0:2)|Number(fe.y&Ve),re=U;return v&&D(U)&&(re=m(U),z^=1),new f(M,re,z)}return{seed:le,k2sig:K}}const d={lowS:e.lowS,prehash:!1},y={lowS:e.lowS,prehash:!1};function g(p,R,P=d){const{seed:E,k2sig:k}=l(p,R,P),v=e;return ds(v.hash.outputLen,v.nByteLength,v.hmac)(E,k)}b.BASE._setWindowSize(8);function L(p,R,P,E=y){const k=p;if(R=Qe("msgHash",R),P=Qe("publicKey",P),"strict"in E)throw new Error("options.strict was renamed to lowS");const{lowS:v,prehash:$}=E;let J,H;try{if(typeof k=="string"||Ot(k))try{J=f.fromDER(k)}catch(fe){if(!(fe instanceof Tt.Err))throw fe;J=f.fromCompact(k)}else if(typeof k=="object"&&typeof k.r=="bigint"&&typeof k.s=="bigint"){const{r:fe,s:M}=k;J=new f(fe,M)}else throw new Error("PARSE");H=b.fromHex(P)}catch(fe){if(fe.message==="PARSE")throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(v&&J.hasHighS())return!1;$&&(R=e.hash(R));const{r:ee,s:Q}=J,le=G(R),ue=h(Q),K=u(le*ue),Y=u(ee*ue),oe=b.BASE.multiplyAndAddUnsafe(H,K,Y)?.toAffine();return oe?u(oe.x)===ee:!1}return{CURVE:e,getPublicKey:S,getSharedSecret:N,sign:g,verify:L,ProjectivePoint:b,Signature:f,utils:c}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function Tl(n){return{hash:n,hmac:(e,...t)=>us(n,e,$a(...t)),randomBytes:za}}function xl(n,e){const t=r=>Il({...n,...Tl(r)});return Object.freeze({...t(e),create:t})}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const bs=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),Co=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),Ol=BigInt(1),Qr=BigInt(2),Lo=(n,e)=>(n+e/Qr)/e;function Nl(n){const e=bs,t=BigInt(3),r=BigInt(6),i=BigInt(11),o=BigInt(22),s=BigInt(23),u=BigInt(44),h=BigInt(88),b=n*n*n%e,C=b*b*n%e,O=He(C,t,e)*C%e,A=He(O,t,e)*C%e,j=He(A,Qr,e)*b%e,D=He(j,i,e)*j%e,m=He(D,o,e)*D%e,_=He(m,u,e)*m%e,f=He(_,h,e)*_%e,c=He(f,u,e)*m%e,S=He(c,t,e)*C%e,B=He(S,s,e)*D%e,N=He(B,r,e)*b%e,F=He(N,Qr,e);if(!ei.eql(ei.sqr(F),n))throw new Error("Cannot find square root");return F}const ei=bl(bs,void 0,void 0,{sqrt:Nl}),di=xl({a:BigInt(0),b:BigInt(7),Fp:ei,n:Co,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:n=>{const e=Co,t=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-Ol*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),o=t,s=BigInt("0x100000000000000000000000000000000"),u=Lo(o*n,e),h=Lo(-r*n,e);let b=qe(n-u*t-h*i,e),C=qe(-u*r-h*o,e);const O=b>s,A=C>s;if(O&&(b=e-b),A&&(C=e-C),b>s||C>s)throw new Error("splitScalar: Endomorphism failed, k="+n);return{k1neg:O,k1:b,k2neg:A,k2:C}}}},Xa);BigInt(0);di.ProjectivePoint;function Cl(n){let e=n;if(typeof e!="string")throw new Error(`[padToEven] value must be type 'string', received ${typeof e}`);return e.length%2&&(e=`0${e}`),e}BigInt(0);const ti={},ni={};for(let n=0;n<16;n++){const e=n,t=n*16,r=n.toString(16).toLowerCase();ni[r]=e,ni[r.toUpperCase()]=e,ti[r]=t,ti[r.toUpperCase()]=t}function Ll(n){const e=n.length,t=new Uint8Array(e/2);for(let r=0;r<e;r+=2)t[r/2]=ti[n[r]]+ni[n[r+1]];return t}Array.from({length:256},(n,e)=>e.toString(16).padStart(2,"0"));for(let n=0;n<=256*256-1;n++)BigInt(n);const Hn=n=>{if(typeof n!="string")throw new Error(`hex argument type ${typeof n} must be of type string`);if(!/^0x[0-9a-fA-F]*$/.test(n))throw new Error(`Input must be a 0x-prefixed hexadecimal string, got ${n}`);const e=n.slice(2);return Ll(e.length%2===0?e:Cl(e))},Pl=n=>{if(!Number.isSafeInteger(n)||n<0)throw new Error(`Received an invalid integer type: ${n}`);return`0x${n.toString(16)}`},ln=n=>{const e=Pl(n);return Hn(e)};BigInt("0xffffffffffffffff");BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff");BigInt("115792089237316195423570985008687907853269984665640564039457584007913129639935");di.CURVE.n;di.CURVE.n/BigInt(2);BigInt("0x10000000000000000000000000000000000000000000000000000000000000000");const Ml="0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470";Hn(Ml);const Bl="0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347";Hn(Bl);const Dl="0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421";Hn(Dl);Uint8Array.from([128]);BigInt(-1);BigInt(0);BigInt(1);BigInt(2);BigInt(3);BigInt(7);BigInt(8);BigInt(27);BigInt(28);BigInt(31);BigInt(32);BigInt(64);BigInt(128);BigInt(255);BigInt(256);BigInt(96);BigInt(100);BigInt(160);BigInt(224);BigInt(7922816251426434e13);BigInt(1461501637330903e33);BigInt(2695994666715064e52);BigInt(1e9);Array.from({length:256},(n,e)=>e.toString(16).padStart(2,"0"));var Po;(function(n){n.String="string",n.Bytes="view",n.Number="number"})(Po||(Po={}));var Mo;(function(n){n.String="string",n.Bytes="view",n.JSON="json"})(Mo||(Mo={}));var Bo;(function(n){n[n.Number=0]="Number",n[n.BigInt=1]="BigInt",n[n.Uint8Array=2]="Uint8Array",n[n.PrefixedHexString=3]="PrefixedHexString"})(Bo||(Bo={}));var Do;(function(n){n[n.Deposit=0]="Deposit",n[n.Withdrawal=1]="Withdrawal",n[n.Consolidation=2]="Consolidation"})(Do||(Do={}));var Nt;(function(n){n[n.Version=0]="Version",n[n.Balance=1]="Balance",n[n.Nonce=2]="Nonce",n[n.CodeHash=3]="CodeHash",n[n.CodeSize=4]="CodeSize"})(Nt||(Nt={}));ln(Nt.Version);ln(Nt.Balance);ln(Nt.Nonce);ln(Nt.CodeHash);ln(Nt.CodeSize);BigInt(256)**BigInt(31);var Hr={exports:{}},On={},Nn={},ko;function kl(){if(ko)return Nn;ko=1,Object.defineProperty(Nn,"__esModule",{value:!0});function n(e){var t=4,r=e.length,i=r%t;if(!i)return e;var o=r,s=t-i,u=r+s,h=Buffer.alloc(u);for(h.write(e);s--;)h.write("=",o++);return h.toString()}return Nn.default=n,Nn}var Uo;function Ul(){if(Uo)return On;Uo=1,Object.defineProperty(On,"__esModule",{value:!0});var n=kl();function e(u,h){return h===void 0&&(h="utf8"),Buffer.isBuffer(u)?i(u.toString("base64")):i(Buffer.from(u,h).toString("base64"))}function t(u,h){return h===void 0&&(h="utf8"),Buffer.from(r(u),"base64").toString(h)}function r(u){return u=u.toString(),n.default(u).replace(/\-/g,"+").replace(/_/g,"/")}function i(u){return u.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function o(u){return Buffer.from(r(u),"base64")}var s=e;return s.encode=e,s.decode=t,s.toBase64=r,s.fromBase64=i,s.toBuffer=o,On.default=s,On}var jo;function jl(){return jo||(jo=1,function(n){n.exports=Ul().default,n.exports.default=n.exports}(Hr)),Hr.exports}jl();const Se={MAINNET:"mainnet",TESTNET:"testnet",CYAN:"cyan",AQUA:"aqua",CELESTE:"celeste"},bt={SAPPHIRE_DEVNET:"sapphire_devnet",SAPPHIRE_MAINNET:"sapphire_mainnet"};Se.MAINNET+"",Se.TESTNET+"",Se.CYAN+"",Se.AQUA+"",Se.CELESTE+"";Se.AQUA+"",bt.SAPPHIRE_MAINNET,Se.CELESTE+"",bt.SAPPHIRE_MAINNET,Se.CYAN+"",bt.SAPPHIRE_MAINNET,Se.MAINNET+"",bt.SAPPHIRE_MAINNET,Se.TESTNET+"",bt.SAPPHIRE_DEVNET;Se.MAINNET+"",Se.TESTNET+"",Se.CYAN+"",Se.AQUA+"",Se.CELESTE+"";bt.SAPPHIRE_MAINNET+"",bt.SAPPHIRE_DEVNET+"",Se.MAINNET+"",Se.TESTNET+"",Se.CYAN+"",Se.AQUA+"",Se.CELESTE+"";Se.MAINNET+"",Se.TESTNET+"",Se.CYAN+"",Se.AQUA+"",Se.CELESTE+"";function qo(n){let e=!1,t=0,r;try{r=window[n],e=!0,t=r.length;const i="__storage_test__";return r.setItem(i,i),r.removeItem(i),!0}catch(i){const o=i;return o&&(o.code===22||o.code===1014||o.name==="QuotaExceededError"||o.name==="NS_ERROR_DOM_QUOTA_REACHED")&&e&&t!==0}}class ql{constructor(){ge(this,"store",new Map)}getItem(e){return this.store.get(e)||null}setItem(e,t){this.store.set(e,t)}removeItem(e){this.store.delete(e)}}class Wl{constructor(e,t){ge(this,"storage",void 0),ge(this,"_storeKey",void 0),this.storage=t,this._storeKey=e;try{t.getItem(e)||this.resetStore()}catch{}}static getInstance(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"local";if(!this.instanceMap.has(e)){let r;t==="local"&&qo("localStorage")?r=window.localStorage:t==="session"&&qo("sessionStorage")?r=window.sessionStorage:r=new ql,this.instanceMap.set(e,new this(e,r))}return this.instanceMap.get(e)}toJSON(){return this.storage.getItem(this._storeKey)}resetStore(){const e=this.getStore();return this.storage.removeItem(this._storeKey),e}getStore(){return JSON.parse(this.storage.getItem(this._storeKey)||"{}")}get(e){return JSON.parse(this.storage.getItem(this._storeKey)||"{}")[e]}set(e,t){const r=JSON.parse(this.storage.getItem(this._storeKey)||"{}");r[e]=t,this.storage.setItem(this._storeKey,JSON.stringify(r))}}ge(Wl,"instanceMap",new Map);Ke(Ke({},bt),Se);class Fl extends Error{}Fl.prototype.name="InvalidTokenError";const Cn={IFRAME_STATUS:"iframe_status",CLOSE_WINDOW:"close_window",USER_LOGGED_IN:"user_logged_in",USER_LOGGED_OUT:"user_logged_out"},ft={LOGOUT:"logout",WALLET_INSTANCE_ID:"wallet_instance_id",USER_INFO:"user_info",SET_PROVIDER:"set_provider",TOPUP:"topup",IFRAME_STATUS:"iframe_status",CLOSED_WINDOW:"closed_window",WINDOW_BLOCKED:"window_blocked",GET_PROVIDER_STATE:"get_provider_state",LOGIN_WITH_PRIVATE_KEY:"login_with_private_key",SHOW_WALLET_CONNECT:"show_wallet_connect",SHOW_CHECKOUT:"show_checkout",SHOW_WALLET_UI:"show_wallet_ui",LOGIN_WITH_SESSION_ID:"login_with_session_id"},$l={GET_PROVIDER_STATE:"wallet_get_provider_state"},zr={ACCOUNTS_CHANGED:"wallet_accounts_changed",CHAIN_CHANGED:"wallet_chain_changed",UNLOCK_STATE_CHANGED:"wallet_unlock_state_changed"};function Hl(n){return function(t,r,i){i(o=>{r.error&&Bn.warn(`Error in RPC response:
`,r),!t.isTorusInternal&&(Bn.info(`RPC (${n.origin}):`,t,"->",r),o())})}}var Gr={},At={},Wo;function hi(){if(Wo)return At;Wo=1,Object.defineProperty(At,"__esModule",{value:!0}),At.EthereumProviderError=At.EthereumRpcError=void 0;const n=ns();class e extends Error{constructor(s,u,h){if(!Number.isInteger(s))throw new Error('"code" must be an integer.');if(!u||typeof u!="string")throw new Error('"message" must be a nonempty string.');super(u),this.code=s,h!==void 0&&(this.data=h)}serialize(){const s={code:this.code,message:this.message};return this.data!==void 0&&(s.data=this.data),this.stack&&(s.stack=this.stack),s}toString(){return n.default(this.serialize(),i,2)}}At.EthereumRpcError=e;class t extends e{constructor(s,u,h){if(!r(s))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(s,u,h)}}At.EthereumProviderError=t;function r(o){return Number.isInteger(o)&&o>=1e3&&o<=4999}function i(o,s){if(s!=="[Circular]")return s}return At}var Vr={},Rt={},Fo;function gi(){return Fo||(Fo=1,Object.defineProperty(Rt,"__esModule",{value:!0}),Rt.errorValues=Rt.errorCodes=void 0,Rt.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},Rt.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}}),Rt}var $o;function ys(){return $o||($o=1,function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.serializeError=n.isValidCode=n.getMessageFromCode=n.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const e=gi(),t=hi(),r=e.errorCodes.rpc.internal,i="Unspecified error message. This is a bug, please report it.",o={code:r,message:s(r)};n.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.";function s(A,j=i){if(Number.isInteger(A)){const D=A.toString();if(O(e.errorValues,D))return e.errorValues[D].message;if(b(A))return n.JSON_RPC_SERVER_ERROR_MESSAGE}return j}n.getMessageFromCode=s;function u(A){if(!Number.isInteger(A))return!1;const j=A.toString();return!!(e.errorValues[j]||b(A))}n.isValidCode=u;function h(A,{fallbackError:j=o,shouldIncludeStack:D=!1}={}){var m,_;if(!j||!Number.isInteger(j.code)||typeof j.message!="string")throw new Error("Must provide fallback error with integer number code and string message.");if(A instanceof t.EthereumRpcError)return A.serialize();const f={};if(A&&typeof A=="object"&&!Array.isArray(A)&&O(A,"code")&&u(A.code)){const S=A;f.code=S.code,S.message&&typeof S.message=="string"?(f.message=S.message,O(S,"data")&&(f.data=S.data)):(f.message=s(f.code),f.data={originalError:C(A)})}else{f.code=j.code;const S=(m=A)===null||m===void 0?void 0:m.message;f.message=S&&typeof S=="string"?S:j.message,f.data={originalError:C(A)}}const c=(_=A)===null||_===void 0?void 0:_.stack;return D&&A&&c&&typeof c=="string"&&(f.stack=c),f}n.serializeError=h;function b(A){return A>=-32099&&A<=-32e3}function C(A){return A&&typeof A=="object"&&!Array.isArray(A)?Object.assign({},A):A}function O(A,j){return Object.prototype.hasOwnProperty.call(A,j)}}(Vr)),Vr}var Yt={},Ho;function zl(){if(Ho)return Yt;Ho=1,Object.defineProperty(Yt,"__esModule",{value:!0}),Yt.ethErrors=void 0;const n=hi(),e=ys(),t=gi();Yt.ethErrors={rpc:{parse:s=>r(t.errorCodes.rpc.parse,s),invalidRequest:s=>r(t.errorCodes.rpc.invalidRequest,s),invalidParams:s=>r(t.errorCodes.rpc.invalidParams,s),methodNotFound:s=>r(t.errorCodes.rpc.methodNotFound,s),internal:s=>r(t.errorCodes.rpc.internal,s),server:s=>{if(!s||typeof s!="object"||Array.isArray(s))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:u}=s;if(!Number.isInteger(u)||u>-32005||u<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return r(u,s)},invalidInput:s=>r(t.errorCodes.rpc.invalidInput,s),resourceNotFound:s=>r(t.errorCodes.rpc.resourceNotFound,s),resourceUnavailable:s=>r(t.errorCodes.rpc.resourceUnavailable,s),transactionRejected:s=>r(t.errorCodes.rpc.transactionRejected,s),methodNotSupported:s=>r(t.errorCodes.rpc.methodNotSupported,s),limitExceeded:s=>r(t.errorCodes.rpc.limitExceeded,s)},provider:{userRejectedRequest:s=>i(t.errorCodes.provider.userRejectedRequest,s),unauthorized:s=>i(t.errorCodes.provider.unauthorized,s),unsupportedMethod:s=>i(t.errorCodes.provider.unsupportedMethod,s),disconnected:s=>i(t.errorCodes.provider.disconnected,s),chainDisconnected:s=>i(t.errorCodes.provider.chainDisconnected,s),custom:s=>{if(!s||typeof s!="object"||Array.isArray(s))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:u,message:h,data:b}=s;if(!h||typeof h!="string")throw new Error('"message" must be a nonempty string');return new n.EthereumProviderError(u,h,b)}}};function r(s,u){const[h,b]=o(u);return new n.EthereumRpcError(s,h||e.getMessageFromCode(s),b)}function i(s,u){const[h,b]=o(u);return new n.EthereumProviderError(s,h||e.getMessageFromCode(s),b)}function o(s){if(s){if(typeof s=="string")return[s];if(typeof s=="object"&&!Array.isArray(s)){const{message:u,data:h}=s;if(u&&typeof u!="string")throw new Error("Must specify string message.");return[u||void 0,h]}}return[]}return Yt}var zo;function Gl(){return zo||(zo=1,function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.getMessageFromCode=n.serializeError=n.EthereumProviderError=n.EthereumRpcError=n.ethErrors=n.errorCodes=void 0;const e=hi();Object.defineProperty(n,"EthereumRpcError",{enumerable:!0,get:function(){return e.EthereumRpcError}}),Object.defineProperty(n,"EthereumProviderError",{enumerable:!0,get:function(){return e.EthereumProviderError}});const t=ys();Object.defineProperty(n,"serializeError",{enumerable:!0,get:function(){return t.serializeError}}),Object.defineProperty(n,"getMessageFromCode",{enumerable:!0,get:function(){return t.getMessageFromCode}});const r=zl();Object.defineProperty(n,"ethErrors",{enumerable:!0,get:function(){return r.ethErrors}});const i=gi();Object.defineProperty(n,"errorCodes",{enumerable:!0,get:function(){return i.errorCodes}})}(Gr)),Gr}var yt=Gl(),Kr,Go;function Vl(){return Go||(Go=1,Kr=function n(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var r,i,o;if(Array.isArray(e)){if(r=e.length,r!=t.length)return!1;for(i=r;i--!==0;)if(!n(e[i],t[i]))return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(o=Object.keys(e),r=o.length,r!==Object.keys(t).length)return!1;for(i=r;i--!==0;)if(!Object.prototype.hasOwnProperty.call(t,o[i]))return!1;for(i=r;i--!==0;){var s=o[i];if(!n(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}),Kr}var Kl=Vl();const Yl=Ut(Kl);var Jl="2.1.0";function _s(n){return n!==null&&typeof n=="object"&&typeof n.pipe=="function"}function Zl(n){return _s(n)&&n.writable!==!1&&typeof n._write=="function"&&typeof n._writableState=="object"}function Xl(n){return _s(n)&&n.readable!==!1&&typeof n._read=="function"&&typeof n._readableState=="object"}function Ql(n){return Zl(n)&&Xl(n)}var nt={errors:{disconnected:()=>"Torus: Lost connection to Torus.",permanentlyDisconnected:()=>"Torus: Disconnected from iframe. Page reload required.",unsupportedSync:n=>`Torus: The Torus Ethereum provider does not support synchronous methods like ${n} without a callback parameter.`,invalidDuplexStream:()=>"Must provide a Node.js-style duplex stream.",invalidOptions:n=>`Invalid options. Received: { maxEventListeners: ${n}}`,invalidRequestArgs:()=>"Expected a single, non-array, object argument.",invalidRequestMethod:()=>"'args.method' must be a non-empty string.",invalidRequestParams:()=>"'args.params' must be an object or array if provided.",invalidLoggerObject:()=>"'args.logger' must be an object if provided.",invalidLoggerMethod:n=>`'args.logger' must include required method '${n}'.`},info:{connected:n=>`Torus: Connected to chain with ID "${n}".`}};const eu={PRODUCTION:"production"},Xt={BOTTOM_LEFT:"bottom-left",TOP_LEFT:"top-left",BOTTOM_RIGHT:"bottom-right",TOP_RIGHT:"top-right"},tu={en:{embed:{continue:"Continue",actionRequired:"Authorization required",pendingAction:"Click continue to proceed with your request in a popup",cookiesRequired:"Cookies Required",enableCookies:"Please enable cookies in your browser preferences to access Torus",clickHere:"More Info"}},de:{embed:{continue:"Fortsetzen",actionRequired:"Autorisierung erforderlich",pendingAction:"Klicken Sie in einem Popup auf Weiter, um mit Ihrer Anfrage fortzufahren",cookiesRequired:"Cookies benötigt",enableCookies:"Bitte aktivieren Sie Cookies in Ihren Browsereinstellungen, um auf Torus zuzugreifen",clickHere:"Mehr Info"}},ja:{embed:{continue:"継続する",actionRequired:"認証が必要です",pendingAction:"続行をクリックして、ポップアップでリクエストを続行します",cookiesRequired:"必要なクッキー",enableCookies:"Torusにアクセスするには、ブラウザの設定でCookieを有効にしてください。",clickHere:"詳しくは"}},ko:{embed:{continue:"계속하다",actionRequired:"승인 필요",pendingAction:"팝업에서 요청을 진행하려면 계속을 클릭하십시오.",cookiesRequired:"쿠키 필요",enableCookies:"브라우저 환경 설정에서 쿠키를 활성화하여 Torus에 액세스하십시오.",clickHere:"더 많은 정보"}},zh:{embed:{continue:"继续",actionRequired:"需要授权",pendingAction:"单击继续以在弹出窗口中继续您的请求",cookiesRequired:"必填Cookie",enableCookies:"请在您的浏览器首选项中启用cookie以访问Torus。",clickHere:"更多信息"}}};var pi={translations:tu,localStorageKeyPrefix:"torus-"},Ee=Bn.getLogger("solana-embed");function nu(){return(n,e,t)=>{(typeof n.method!="string"||!n.method)&&(e.error=yt.ethErrors.rpc.invalidRequest({message:"The request 'method' must be a non-empty string.",data:n})),t(r=>{const{error:i}=e;return i&&Ee.error(`Torus - RPC Error: ${i.message}`,i),r()})}}function ru(n,e,t){let r=`Torus: Lost connection to "${n}".`;e?.stack&&(r+=`
${e.stack}`),Ee.warn(r),t&&t.listenerCount("error")>0&&t.emit("error",r)}const Ln=()=>Math.random().toString(36).slice(2),ms=async n=>{let e,t;switch(n){case"testing":e="https://solana-testing.tor.us",t="debug";break;case"development":e="http://localhost:8080",t="debug";break;default:e="https://solana.tor.us",t="error";break}return{torusUrl:e,logLevel:t}},iu=()=>{let n=window.navigator.language||"en-US";const e=n.split("-");return n=Object.prototype.hasOwnProperty.call(pi.translations,e[0])?e[0]:"en",n},ou={height:660,width:375},su={height:740,width:1315},au={height:700,width:1200},Es={height:600,width:400};function lu(n){let e;try{e=window[n];const t="__storage_test__";return e.setItem(t,t),e.removeItem(t),!0}catch(t){const r=t;return r&&(r.code===22||r.code===1014||r.name==="QuotaExceededError"||r.name==="NS_ERROR_DOM_QUOTA_REACHED")&&e&&e.length!==0}}function Qt(n){let{width:e,height:t}=n;const r=window.screenLeft!==void 0?window.screenLeft:window.screenX,i=window.screenTop!==void 0?window.screenTop:window.screenY,o=window.innerWidth?window.innerWidth:document.documentElement.clientWidth?document.documentElement.clientWidth:window.screen.width,s=window.innerHeight?window.innerHeight:document.documentElement.clientHeight?document.documentElement.clientHeight:window.screen.height,u=1,h=Math.abs((o-e)/2/u+r),b=Math.abs((s-t)/2/u+i);return`titlebar=0,toolbar=0,status=0,location=0,menubar=0,height=${t/u},width=${e/u},top=${b},left=${h}`}class Ss extends Wn{constructor(e,t){let{maxEventListeners:r=100,jsonRpcStreamName:i="provider"}=t;if(super(),ge(this,"isTorus",void 0),ge(this,"_rpcEngine",void 0),ge(this,"jsonRpcConnectionEvents",void 0),ge(this,"_state",void 0),!Ql(e))throw new Error(nt.errors.invalidDuplexStream());this.isTorus=!0,this.setMaxListeners(r),this._handleConnect=this._handleConnect.bind(this),this._handleDisconnect=this._handleDisconnect.bind(this),this._handleStreamDisconnect=this._handleStreamDisconnect.bind(this),this._rpcRequest=this._rpcRequest.bind(this),this._initializeState=this._initializeState.bind(this),this.request=this.request.bind(this),this.sendAsync=this.sendAsync.bind(this);const o=new Pa;fo(e,o,e,this._handleStreamDisconnect.bind(this,"Torus")),o.ignoreStream("phishing");const s=Na();fo(s.stream,o.createStream(i),s.stream,this._handleStreamDisconnect.bind(this,"Torus RpcProvider"));const u=new lt;u.push(Ca()),u.push(nu()),u.push(Hl({origin:location.origin})),u.push(s.middleware),this._rpcEngine=u,this.jsonRpcConnectionEvents=s.events}async request(e){if(!e||typeof e!="object"||Array.isArray(e))throw yt.ethErrors.rpc.invalidRequest({message:nt.errors.invalidRequestArgs(),data:e});const{method:t,params:r}=e;if(typeof t!="string"||t.length===0)throw yt.ethErrors.rpc.invalidRequest({message:nt.errors.invalidRequestMethod(),data:e});if(r!==void 0&&!Array.isArray(r)&&(typeof r!="object"||r===null))throw yt.ethErrors.rpc.invalidRequest({message:nt.errors.invalidRequestParams(),data:e});return new Promise((i,o)=>{this._rpcRequest({method:t,params:r},Jr(i,o))})}send(e,t){this._rpcRequest(e,t)}sendAsync(e){return new Promise((t,r)=>{this._rpcRequest(e,Jr(t,r))})}_handleStreamDisconnect(e,t){ru(e,t,this),this._handleDisconnect(!1,t?t.message:void 0)}}const Mt=n=>{const e=window.document.createElement("template"),t=n.trim();return e.innerHTML=t,e.content.firstChild};function Pn(n){return n.version===void 0}class vs extends Wn{constructor(e){let{url:t,target:r,features:i,timeout:o=3e4}=e;super(),ge(this,"url",void 0),ge(this,"target",void 0),ge(this,"features",void 0),ge(this,"window",void 0),ge(this,"windowTimer",void 0),ge(this,"iClosedWindow",void 0),ge(this,"timeout",void 0),this.url=t,this.target=r||"_blank",this.features=i||Qt(au),this.window=void 0,this.windowTimer=void 0,this.iClosedWindow=!1,this.timeout=o,this._setupTimer()}_setupTimer(){this.windowTimer=Number(setInterval(()=>{this.window&&this.window.closed&&(clearInterval(this.windowTimer),setTimeout(()=>{this.iClosedWindow||this.emit("close"),this.iClosedWindow=!1,this.window=void 0},this.timeout)),this.window===void 0&&clearInterval(this.windowTimer)},500))}open(){return this.window=window.open(this.url.href,this.target,this.features),this.window?.focus&&this.window.focus(),Promise.resolve()}close(){this.iClosedWindow=!0,this.window&&this.window.close()}redirect(e){e?window.location.replace(this.url.href):window.location.href=this.url.href}}class zn extends Ss{constructor(e,t){let{maxEventListeners:r=100,jsonRpcStreamName:i="provider"}=t;super(e,{maxEventListeners:r,jsonRpcStreamName:i}),ge(this,"embedTranslations",void 0),ge(this,"torusUrl",void 0),ge(this,"dappStorageKey",void 0),ge(this,"windowRefs",void 0),ge(this,"tryWindowHandle",void 0),ge(this,"torusAlertContainer",void 0),ge(this,"torusIframe",void 0),this._state=Ke({},zn._defaultState),this.torusUrl="",this.dappStorageKey="";const o=pi.translations[iu()];this.embedTranslations=o.embed,this.windowRefs={},this.on("connect",()=>{this._state.isConnected=!0});const s=u=>{const{method:h,params:b}=u;if(h===Cn.IFRAME_STATUS){const{isFullScreen:C,rid:O}=b;this._displayIframe({isFull:C,rid:O})}else if(h==="create_window"){const{windowId:C,url:O}=b;this._createPopupBlockAlert(C,O)}else if(h===Cn.CLOSE_WINDOW)this._handleCloseWindow(b);else if(h===Cn.USER_LOGGED_IN){const{currentLoginProvider:C}=b;this._state.isLoggedIn=!0,this._state.currentLoginProvider=C}else h===Cn.USER_LOGGED_OUT&&(this._state.isLoggedIn=!1,this._state.currentLoginProvider=null,this._displayIframe())};this.jsonRpcConnectionEvents.on("notification",s)}get isLoggedIn(){return this._state.isLoggedIn}get isIFrameFullScreen(){return this._state.isIFrameFullScreen}isConnected(){return this._state.isConnected}async _initializeState(e){try{const{torusUrl:t,dappStorageKey:r,torusAlertContainer:i,torusIframe:o}=e;this.torusUrl=t,this.dappStorageKey=r,this.torusAlertContainer=i,this.torusIframe=o,this.torusIframe.addEventListener("load",()=>{this._state.isIFrameFullScreen||this._displayIframe()});const{currentLoginProvider:s,isLoggedIn:u}=await this.request({method:ft.GET_PROVIDER_STATE,params:[]});this._handleConnect(s,u)}catch(t){Ee.error("Torus: Failed to get initial state. Please report this bug.",t)}finally{Ee.info("initialized communication state"),this._state.initialized=!0,this.emit("_initialized")}}_handleWindow(e){let{url:t,target:r,features:i}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const o=new URL(t||`${this.torusUrl}/redirect?windowId=${e}`);this.dappStorageKey&&(o.hash?o.hash+=`&dappStorageKey=${this.dappStorageKey}`:o.hash=`#dappStorageKey=${this.dappStorageKey}`);const s=new vs({url:o,target:r,features:i});if(s.open(),!s.window){this._createPopupBlockAlert(e,o.href);return}this.windowRefs[e]=s,this.request({method:"opened_window",params:{windowId:e}}),s.once("close",()=>{delete this.windowRefs[e],this.request({method:ft.CLOSED_WINDOW,params:{windowId:e}})})}_displayIframe(){let{isFull:e=!1,rid:t=""}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const r={};if(e)r.display="block",r.width="100%",r.height="100%",r.top="0px",r.right="0px",r.left="0px",r.bottom="0px";else switch(r.display=this._state.torusWidgetVisibility?"block":"none",r.height="70px",r.width="70px",this._state.buttonPosition){case Xt.TOP_LEFT:r.top="0px",r.left="0px",r.right="auto",r.bottom="auto";break;case Xt.TOP_RIGHT:r.top="0px",r.right="0px",r.left="auto",r.bottom="auto";break;case Xt.BOTTOM_RIGHT:r.bottom="0px",r.right="0px",r.top="auto",r.left="auto";break;case Xt.BOTTOM_LEFT:default:r.bottom="0px",r.left="0px",r.top="auto",r.right="auto";break}Object.assign(this.torusIframe.style,r),this._state.isIFrameFullScreen=e,this.request({method:ft.IFRAME_STATUS,params:{isIFrameFullScreen:e,rid:t}})}hideTorusButton(){this._state.torusWidgetVisibility=!1,this._displayIframe()}showTorusButton(){this._state.torusWidgetVisibility=!0,this._displayIframe()}_rpcRequest(e,t){const r=t,i=e;Array.isArray(i)||i.jsonrpc||(i.jsonrpc="2.0"),this.tryWindowHandle(i,r)}_handleConnect(e,t){this._state.isConnected||(this._state.isConnected=!0,this.emit("connect",{currentLoginProvider:e,isLoggedIn:t}),Ee.debug(nt.info.connected(e)))}_handleDisconnect(e,t){if(this._state.isConnected||!this._state.isPermanentlyDisconnected&&!e){this._state.isConnected=!1;let r;e?(r=new yt.EthereumRpcError(1013,t||nt.errors.disconnected()),Ee.debug(r)):(r=new yt.EthereumRpcError(1011,t||nt.errors.permanentlyDisconnected()),Ee.error(r),this._state.currentLoginProvider=null,this._state.isLoggedIn=!1,this._state.torusWidgetVisibility=!1,this._state.isIFrameFullScreen=!1,this._state.isPermanentlyDisconnected=!0),this.emit("disconnect",r)}}_handleCloseWindow(e){const{windowId:t}=e;this.windowRefs[t]&&(this.windowRefs[t].close(),delete this.windowRefs[t])}async _createPopupBlockAlert(e,t){const r=this.getLogoUrl(),i=Mt(`<div id="torusAlert" class="torus-alert--v2"><div id="torusAlert__logo"><img src="${r}" /></div><div><h1 id="torusAlert__title">${this.embedTranslations.actionRequired}</h1><p id="torusAlert__desc">${this.embedTranslations.pendingAction}</p></div></div>`),o=Mt(`<div><a id="torusAlert__btn">${this.embedTranslations.continue}</a></div>`),s=Mt('<div id="torusAlert__btn-container"></div>');s.appendChild(o),i.appendChild(s);const u=()=>{o.addEventListener("click",()=>{this._handleWindow(e,{url:t,target:"_blank",features:Qt(Es)}),i.remove(),this.torusAlertContainer.children.length===0&&(this.torusAlertContainer.style.display="none")})};(()=>{this.torusAlertContainer.appendChild(i)})(),u(),this.torusAlertContainer.style.display="block"}getLogoUrl(){return`${this.torusUrl}/images/torus_icon-blue.svg`}}ge(zn,"_defaultState",{buttonPosition:"bottom-left",currentLoginProvider:null,isIFrameFullScreen:!1,hasEmittedConnection:!1,torusWidgetVisibility:!1,initialized:!1,isLoggedIn:!1,isPermanentlyDisconnected:!1,isConnected:!1});class Gn extends Ss{constructor(e,t){let{maxEventListeners:r=100,jsonRpcStreamName:i="provider"}=t;super(e,{maxEventListeners:r,jsonRpcStreamName:i}),ge(this,"chainId",void 0),ge(this,"selectedAddress",void 0),ge(this,"tryWindowHandle",void 0),this._state=Ke({},Gn._defaultState),this.selectedAddress=null,this.chainId=null,this._handleAccountsChanged=this._handleAccountsChanged.bind(this),this._handleChainChanged=this._handleChainChanged.bind(this),this._handleUnlockStateChanged=this._handleUnlockStateChanged.bind(this),this.on("connect",()=>{this._state.isConnected=!0});const o=s=>{const{method:u,params:h}=s;u===zr.ACCOUNTS_CHANGED?this._handleAccountsChanged(h):u===zr.UNLOCK_STATE_CHANGED?this._handleUnlockStateChanged(h):u===zr.CHAIN_CHANGED&&this._handleChainChanged(h)};this.jsonRpcConnectionEvents.on("notification",o)}isConnected(){return this._state.isConnected}async _initializeState(){try{const{accounts:e,chainId:t,isUnlocked:r}=await this.request({method:$l.GET_PROVIDER_STATE,params:[]});this.emit("connect",{chainId:t}),this._handleChainChanged({chainId:t}),this._handleUnlockStateChanged({accounts:e,isUnlocked:r}),this._handleAccountsChanged(e)}catch(e){Ee.error("Torus: Failed to get initial state. Please report this bug.",e)}finally{Ee.info("initialized provider state"),this._state.initialized=!0,this.emit("_initialized")}}_rpcRequest(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=t;const o=e;if(!Array.isArray(o)){if(o.jsonrpc||(o.jsonrpc="2.0"),o.method==="solana_accounts"||o.method==="solana_requestAccounts")i=(s,u)=>{this._handleAccountsChanged(u.result||[],o.method==="solana_accounts",r),t(s,u)};else if(o.method==="wallet_getProviderState"){this._rpcEngine.handle(e,i);return}}this.tryWindowHandle(o,i)}_handleConnect(e){this._state.isConnected||(this._state.isConnected=!0,this.emit("connect",{chainId:e}),Ee.debug(nt.info.connected(e)))}_handleDisconnect(e,t){if(this._state.isConnected||!this._state.isPermanentlyDisconnected&&!e){this._state.isConnected=!1;let r;e?(r=new yt.EthereumRpcError(1013,t||nt.errors.disconnected()),Ee.debug(r)):(r=new yt.EthereumRpcError(1011,t||nt.errors.permanentlyDisconnected()),Ee.error(r),this.chainId=null,this._state.accounts=null,this.selectedAddress=null,this._state.isUnlocked=!1,this._state.isPermanentlyDisconnected=!0),this.emit("disconnect",r)}}_handleAccountsChanged(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=e;Array.isArray(i)||(Ee.error("Torus: Received non-array accounts parameter. Please report this bug.",i),i=[]);for(const o of e)if(typeof o!="string"){Ee.error("Torus: Received non-string account. Please report this bug.",e),i=[];break}Yl(this._state.accounts,i)||(t&&Array.isArray(this._state.accounts)&&this._state.accounts.length>0&&!r&&Ee.error('Torus: "solana_accounts" unexpectedly updated accounts. Please report this bug.',i),this._state.accounts=i,this.emit("accountsChanged",i)),this.selectedAddress!==i[0]&&(this.selectedAddress=i[0]||null)}_handleChainChanged(){let{chainId:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!e){Ee.error("Torus: Received invalid network parameters. Please report this bug.",{chainId:e});return}e==="loading"?this._handleDisconnect(!0):(this._handleConnect(e),e!==this.chainId&&(this.chainId=e,this._state.initialized&&this.emit("chainChanged",this.chainId)))}_handleUnlockStateChanged(){let{accounts:e,isUnlocked:t}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(typeof t!="boolean"){Ee.error("Torus: Received invalid isUnlocked parameter. Please report this bug.",{isUnlocked:t});return}t!==this._state.isUnlocked&&(this._state.isUnlocked=t,this._handleAccountsChanged(e||[]))}}ge(Gn,"_defaultState",{accounts:null,isConnected:!1,isUnlocked:!1,initialized:!1,isPermanentlyDisconnected:!1,hasEmittedConnection:!1});function Vo(n){return new Promise((e,t)=>{try{const r=document.createElement("img");r.onload=()=>e(!0),r.onerror=()=>e(!1),r.src=n}catch(r){t(r)}})}const uu=n=>{const{document:e}=n,t=e.querySelector('head > meta[property="og:site_name"]');if(t)return t.content;const r=e.querySelector('head > meta[name="title"]');return r?r.content:e.title&&e.title.length>0?e.title:n.location.hostname};async function fu(n){try{const{document:e}=n;let t=e.querySelector('head > link[rel="shortcut icon"]');return t&&await Vo(t.href)||(t=Array.from(e.querySelectorAll('head > link[rel="icon"]')).find(r=>!!r.href),t&&await Vo(t.href))?t.href:""}catch{return""}}const cu=async()=>({name:uu(window),icon:await fu(window)}),du=["send_transaction","sign_transaction","sign_all_transactions","sign_message","connect"],hu=[ft.SET_PROVIDER],gu=lu("localStorage");(async function(){try{if(typeof document>"u")return;const e=document.createElement("link"),{torusUrl:t}=await ms("production");e.href=`${t}/frame`,e.crossOrigin="anonymous",e.type="text/html",e.rel="prefetch",e.relList&&e.relList.supports&&e.relList.supports("prefetch")&&document.head.appendChild(e)}catch(e){Ee.warn(e)}})();class yu{constructor(){let{modalZIndex:e=99999}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};ge(this,"isInitialized",void 0),ge(this,"torusAlert",void 0),ge(this,"modalZIndex",void 0),ge(this,"alertZIndex",void 0),ge(this,"requestedLoginProvider",void 0),ge(this,"provider",void 0),ge(this,"communicationProvider",void 0),ge(this,"dappStorageKey",void 0),ge(this,"isTopupHidden",!1),ge(this,"torusAlertContainer",void 0),ge(this,"torusUrl",void 0),ge(this,"torusIframe",void 0),ge(this,"styleLink",void 0),this.torusUrl="",this.isInitialized=!1,this.requestedLoginProvider=null,this.modalZIndex=e,this.alertZIndex=e+1e3,this.dappStorageKey=""}get isLoggedIn(){return this.communicationProvider?this.communicationProvider.isLoggedIn:!1}async init(){let{buildEnv:e=eu.PRODUCTION,enableLogging:t=!1,network:r,showTorusButton:i=!1,useLocalStorage:o=!1,buttonPosition:s=Xt.BOTTOM_LEFT,apiKey:u="torus-default",extraParams:h={},whiteLabel:b}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(this.isInitialized)throw new Error("Already initialized");const{torusUrl:C,logLevel:O}=await ms(e);Ee.enableAll(),Ee.info(C,"url loaded"),Ee.info(`Solana Embed Version :${Jl}`),this.torusUrl=C,Ee.setDefaultLevel(O),t?Ee.enableAll():Ee.disableAll();const A=this.handleDappStorageKey(o),j=new URL(C);j.pathname.endsWith("/")?j.pathname+="frame":j.pathname+="/frame";const D=new URLSearchParams;return A&&D.append("dappStorageKey",A),D.append("origin",window.location.origin),j.hash=D.toString(),this.torusIframe=Mt(`<iframe
        id="torusIframe"
        class="torusIframe"
        src="${j.href}"
        style="display: none; position: fixed; top: 0; right: 0; width: 100%;
        height: 100%; border: none; border-radius: 0; z-index: ${this.modalZIndex.toString()}"
      ></iframe>`),this.torusAlertContainer=Mt(`<div id="torusAlertContainer" style="display:none; z-index: ${this.alertZIndex.toString()}"></div>`),this.styleLink=Mt(`<link href="${C}/css/widget.css" rel="stylesheet" type="text/css">`),new Promise((m,_)=>{try{this.torusIframe.addEventListener("load",async()=>{const f=await cu();this.torusIframe.contentWindow.postMessage({buttonPosition:s,apiKey:u,network:r,dappMetadata:f,extraParams:h,whiteLabel:b},j.origin),await this._setupWeb3({torusUrl:C}),i&&this.showTorusButton(),b?.topupHide?this.isTopupHidden=b.topupHide:this.hideTorusButton(),this.isInitialized=!0,window.torus=this,m()}),window.document.head.appendChild(this.styleLink),window.document.body.appendChild(this.torusIframe),window.document.body.appendChild(this.torusAlertContainer)}catch(f){_(f)}})}async login(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.isInitialized)throw new Error("Call init() first");try{this.requestedLoginProvider=e.loginProvider||null,this.requestedLoginProvider||this.communicationProvider._displayIframe({isFull:!0});const t=await new Promise((r,i)=>{this.provider._rpcRequest({method:"solana_requestAccounts",params:[this.requestedLoginProvider,e.login_hint]},Jr(r,i))});if(Array.isArray(t)&&t.length>0)return t;throw new Error("Login failed")}catch(t){throw Ee.error("login failed",t),t}finally{this.communicationProvider.isIFrameFullScreen&&this.communicationProvider._displayIframe()}}async loginWithPrivateKey(e){if(!this.isInitialized)throw new Error("Call init() first");const{privateKey:t,userInfo:r}=e,{success:i}=await this.communicationProvider.request({method:"login_with_private_key",params:{privateKey:t,userInfo:r}});if(!i)throw new Error("Login Failed")}async logout(){if(!this.communicationProvider.isLoggedIn)throw new Error("Not logged in");await this.communicationProvider.request({method:ft.LOGOUT,params:[]}),this.requestedLoginProvider=null}async cleanUp(){this.communicationProvider.isLoggedIn&&await this.logout(),this.clearInit()}clearInit(){function e(t){return t instanceof Element||t instanceof Document}e(this.styleLink)&&window.document.body.contains(this.styleLink)&&(this.styleLink.remove(),this.styleLink=void 0),e(this.torusIframe)&&window.document.body.contains(this.torusIframe)&&(this.torusIframe.remove(),this.torusIframe=void 0),e(this.torusAlertContainer)&&window.document.body.contains(this.torusAlertContainer)&&(this.torusAlert=void 0,this.torusAlertContainer.remove(),this.torusAlertContainer=void 0),this.isInitialized=!1}hideTorusButton(){this.communicationProvider.hideTorusButton()}showTorusButton(){this.communicationProvider.showTorusButton()}async setProvider(e){await this.communicationProvider.request({method:ft.SET_PROVIDER,params:Ke({},e)})}async showWallet(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=await this.communicationProvider.request({method:ft.WALLET_INSTANCE_ID,params:[]}),i=e?`/${e}`:"",o=new URL(`${this.torusUrl}/wallet${i}`);o.searchParams.append("instanceId",r),Object.keys(t).forEach(u=>{o.searchParams.append(u,t[u])}),this.dappStorageKey&&(o.hash=`#dappStorageKey=${this.dappStorageKey}`),new vs({url:o,features:Qt(su)}).open()}async getUserInfo(){return await this.communicationProvider.request({method:ft.USER_INFO,params:[]})}async initiateTopup(e,t){if(!this.isInitialized)throw new Error("Torus is not initialized");const r=Ln();return this.communicationProvider._handleWindow(r),await this.communicationProvider.request({method:ft.TOPUP,params:{provider:e,params:t,windowId:r}})}async getAccounts(){return await this.provider.request({method:"getAccounts",params:[]})}async sendTransaction(e){const t=Pn(e),r=t?e.serialize({requireAllSignatures:!1}).toString("hex"):Buffer.from(e.serialize()).toString("hex");return await this.provider.request({method:"send_transaction",params:{message:r,isLegacyTransaction:t}})}async signAndSendTransaction(e,t){const r=Pn(e),i=r?e.serialize({requireAllSignatures:!1}).toString("hex"):Buffer.from(e.serialize()).toString("hex");return{signature:await this.provider.request({method:"send_transaction",params:{message:i,options:t,isLegacyTransaction:r}})}}async signTransaction(e){const t=Pn(e),r=t?e.serializeMessage().toString("hex"):Buffer.from(e.message.serialize()).toString("hex"),i=await this.provider.request({method:"sign_transaction",params:{message:r,messageOnly:!0,isLegacyTransaction:t}}),o=JSON.parse(i),s={publicKey:new Ii(o.publicKey),signature:Buffer.from(o.signature,"hex")};return e.addSignature(s.publicKey,s.signature),e}async signAllTransactions(e){let t;const r=e.map(s=>(t=Pn(s),t?s.serializeMessage().toString("hex"):Buffer.from(s.message.serialize()).toString("hex"))),o=(await this.provider.request({method:"sign_all_transactions",params:{message:r,messageOnly:!0,isLegacyTransaction:t}})).map(s=>{const u=JSON.parse(s);return{publicKey:new Ii(u.publicKey),signature:Buffer.from(u.signature,"hex")}});return e.forEach((s,u)=>(s.addSignature(o[u].publicKey,o[u].signature),s)),e}async signMessage(e){return await this.provider.request({method:"sign_message",params:{data:e}})}async getGaslessPublicKey(){return await this.provider.request({method:"get_gasless_public_key",params:[]})}handleDappStorageKey(e){const t=`${pi.localStorageKeyPrefix}${window.location.hostname}`;let r="";if(gu&&e){const i=window.localStorage.getItem(t);if(i)r=i;else{const o=`torus-app-${Ln()}`;window.localStorage.setItem(t,o),r=o}}return this.dappStorageKey=r,r}async _setupWeb3(e){Ee.info("setupWeb3 running");const t=new go({name:"embed_torus",target:"iframe_torus",targetWindow:this.torusIframe.contentWindow}),r=new go({name:"embed_communication",target:"iframe_communication",targetWindow:this.torusIframe.contentWindow}),i=new Gn(t,{}),o=new zn(r,{});i.tryWindowHandle=(b,C)=>{const O=b;if(!Array.isArray(O)&&du.includes(O.method)){if(!this.communicationProvider.isLoggedIn)throw new Error("User Not Logged In");const A=Ln();o._handleWindow(A,{target:"_blank",features:Qt(Es)}),O.windowId=A}i._rpcEngine.handle(O,C)},o.tryWindowHandle=(b,C)=>{const O=b;if(!Array.isArray(O)&&hu.includes(O.method)){const A=Ln();o._handleWindow(A,{target:"_blank",features:Qt(ou)}),O.params.windowId=A}o._rpcEngine.handle(O,C)};const s=b=>{const C=i[b],O=this;i[b]=function(j,D){const{method:m,params:_=[]}=j;if(m==="solana_requestAccounts"){if(!D)return O.login({loginProvider:_[0]});O.login({loginProvider:_[0]}).then(f=>D(null,f)).catch(f=>D(f))}return C.apply(this,[j,D])}};s("request"),s("sendAsync"),s("send");const u=new Proxy(i,{deleteProperty:()=>!0}),h=new Proxy(o,{deleteProperty:()=>!0});this.provider=u,this.communicationProvider=h,await Promise.all([i._initializeState(),o._initializeState(Ke(Ke({},e),{},{dappStorageKey:this.dappStorageKey,torusAlertContainer:this.torusAlertContainer,torusIframe:this.torusIframe}))]),Ee.debug("Torus - injected provider")}}export{Xt as BUTTON_POSITION,eu as TORUS_BUILD_ENV,Gn as TorusInPageProvider,yu as default};
//# sourceMappingURL=solanaEmbed.esm-JewB_VXV.js.map
