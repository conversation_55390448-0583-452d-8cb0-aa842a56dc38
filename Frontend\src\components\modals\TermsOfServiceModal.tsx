/**
 * Terms of Service Modal Component for DeepTrade
 *
 * Displays the Terms of Service in a modal dialog with proper scrolling,
 * accessibility features, and responsive design.
 * Always displays in English regardless of language selector setting.
 */

import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, FileText } from 'lucide-react';
import { Button } from '../ui/Button';
import { cn } from '@/lib/utils';

interface TermsOfServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const TermsOfServiceModal: React.FC<TermsOfServiceModalProps> = ({
  isOpen,
  onClose,
  className,
}) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div
        className={cn(
          "relative bg-white dark:bg-gray-800 rounded-lg shadow-2xl",
          "w-full max-w-4xl max-h-[90vh] flex flex-col",
          "border border-gray-200 dark:border-gray-700",
          className
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby="terms-modal-title"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-primary" />
            <h2 id="terms-modal-title" className="text-xl font-semibold text-gray-900 dark:text-white">
              Terms of Service
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="rounded-full"
            aria-label="Close Terms of Service"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="prose prose-gray dark:prose-invert max-w-none">
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              Last updated: January 15, 2025
            </div>

            <h3>1. Acceptance of Terms</h3>
            <p>
              By accessing or using DeepTrade ("the Platform"), you agree to be bound by these Terms of Service. If you do not agree with these Terms, you may not access or use the Platform.
            </p>

            <h3>2. Nature of the Service</h3>
            <p>
              DeepTrade is a software-based automation tool that provides algorithmic trading signal infrastructure and automation interfaces via API. DeepTrade <strong>does not provide financial advice</strong>, <strong>does not act as a broker</strong>, <strong>does not custody funds</strong>, and <strong>does not execute trades on behalf of users</strong>. Users remain in full control of their exchange accounts at all times.
            </p>

            <h3>3. Eligibility</h3>
            <p>
              You must be at least 18 years old and have the legal capacity to enter into these Terms. By using the Platform, you confirm that you are not a citizen or resident of any jurisdiction where use of such tools is prohibited by law.
            </p>

            <h3>4. User Responsibilities</h3>
            <p>You acknowledge that:</p>
            <ul>
              <li>You are solely responsible for connecting and managing your exchange accounts via API.</li>
              <li>You maintain full responsibility for your trading decisions.</li>
              <li>You understand and accept the inherent risks of cryptocurrency trading.</li>
              <li>You will comply with all applicable local, national, and international laws.</li>
            </ul>

            <h3>5. No Financial Advice</h3>
            <p>
              Nothing provided by DeepTrade should be construed as investment advice, financial recommendation, or solicitation to buy or sell assets. You are solely responsible for your investment decisions and should consult with a licensed financial advisor if needed.
            </p>

            <h3>6. Risk Disclosure</h3>
            <p>
              You acknowledge that cryptocurrency trading involves significant risk, including the potential loss of all invested capital. Automated trading increases exposure to such risks. You accept that any use of DeepTrade is at your own sole discretion and risk.
            </p>

            <h3>7. Limitation of Liability</h3>
            <p>
              To the fullest extent permitted by law, DeepTrade, its owners, affiliates, or partners shall <strong>not be liable</strong> for:
            </p>
            <ul>
              <li>Any losses, damages, or liabilities incurred due to market fluctuations or trading strategies.</li>
              <li>Any failures of exchange APIs, third-party services, or automation bugs.</li>
              <li>Any indirect, incidental, consequential, or punitive damages.</li>
            </ul>
            <p>
              <strong>YOU EXPRESSLY WAIVE ANY RIGHT TO BRING LEGAL ACTION AGAINST DEEPTRADE FOR FINANCIAL LOSSES.</strong>
            </p>

            <h3>8. Payments & Profit Sharing</h3>
            <p>
              Any payment made to DeepTrade (e.g., through profit-sharing or membership fee in USDT or other crypto assets) is considered voluntary compensation for access to the software service. DeepTrade does not guarantee performance or returns of any kind.
            </p>

            <h3>9. Account Suspension</h3>
            <p>
              We reserve the right to suspend or terminate your access to the Platform at any time, without prior notice, for violations of these Terms or at our sole discretion.
            </p>

            <h3>10. Changes to Terms</h3>
            <p>
              We may update these Terms at any time. Continued use of the Platform after such changes constitutes your acceptance of the revised Terms.
            </p>

            <h3>11. Contact</h3>
            <p>
              For legal inquiries, please contact: <strong><EMAIL></strong>
            </p>

            <h3>3. User Responsibilities</h3>
            <p>
              Users are responsible for:
            </p>
            <ul>
              <li>Maintaining the confidentiality of their account credentials</li>
              <li>All activities that occur under their account</li>
              <li>Ensuring compliance with applicable laws and regulations</li>
              <li>Understanding the risks associated with cryptocurrency trading</li>
            </ul>

            <h3>4. Risk Disclosure</h3>
            <p>
              <strong>IMPORTANT:</strong> Cryptocurrency trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. You should carefully consider whether trading is suitable for you in light of your circumstances, knowledge, and financial resources.
            </p>

            <h3>5. Prohibited Activities</h3>
            <p>
              Users may not:
            </p>
            <ul>
              <li>Use the Service for any illegal or unauthorized purpose</li>
              <li>Attempt to gain unauthorized access to the Service or its related systems</li>
              <li>Interfere with or disrupt the Service or servers or networks connected to the Service</li>
              <li>Share account credentials with third parties</li>
            </ul>

            <h3>6. Intellectual Property</h3>
            <p>
              The Service and its original content, features, and functionality are and will remain the exclusive property of DeepTrade and its licensors. The Service is protected by copyright, trademark, and other laws.
            </p>

            <h3>7. Limitation of Liability</h3>
            <p>
              In no event shall DeepTrade, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the Service.
            </p>

            <h3>8. Termination</h3>
            <p>
              We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.
            </p>

            <h3>9. Changes to Terms</h3>
            <p>
              We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.
            </p>

            <h3>10. Contact Information</h3>
            <p>
              If you have any questions about these Terms of Service, please contact us at:
              <br />
              Email: <EMAIL>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <Button onClick={onClose} className="min-w-[100px]">
            Close
          </Button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default TermsOfServiceModal;
