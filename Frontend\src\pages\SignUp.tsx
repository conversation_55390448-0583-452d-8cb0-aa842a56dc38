import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { PasswordStrengthIndicator } from '@/components/ui/PasswordStrengthIndicator';
import { useTranslation } from '@/hooks/useTranslation';
import { TermsOfServiceModal } from '@/components/modals/TermsOfServiceModal';
import { API_BASE_URL } from '@/config';
import { PrivacyPolicyModal } from '@/components/modals/PrivacyPolicyModal';

export default function SignUp() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const { } = useAuth();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Password validation function
  const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      const redirect_uri = `${window.location.origin}/auth/callback`;
      console.log('Frontend redirect_uri:', redirect_uri);
      
      // Call the backend to get the Google OAuth URL
      const response = await fetch(`${API_BASE_URL}/api/auth/login/google`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          redirect_uri: redirect_uri
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to initiate Google login');
      }
      
      const data = await response.json();
      
      // Redirect to Google's OAuth page
      window.location.href = data.authorization_url;
      
    } catch (error) {
      console.error('Google login failed:', error);
      toastError({
        title: 'Google login failed',
        description: error instanceof Error ? error.message : 'Failed to initiate Google login',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!firstName.trim() || !lastName.trim() || !email.trim() || !password.trim()) {
      toastError({
        title: 'Error',
        description: 'Please fill in all fields',
      });
      return;
    }

    // Validate password requirements
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      toastError({
        title: 'Password Requirements Not Met',
        description: passwordValidation.errors.join('. '),
      });
      return;
    }

    if (password !== confirmPassword) {
      toastError({
        title: 'Error',
        description: 'Passwords do not match',
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          firstName,
          lastName,
          email,
          password
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      // Registration successful - show verification message
      setRegisteredEmail(email);
      setRegistrationComplete(true);
      toastSuccess({
        title: 'Registration Successful!',
        description: 'Please check your email to verify your account.',
      });

    } catch (error: any) {
      console.error('Registration failed:', error);
      let backendMsg = error.message || 'Failed to create account';
      if (error.message?.includes('already exists')) {
        toastError({
          title: 'Email already registered',
          description: backendMsg,
        });
      } else {
        toastError({
          title: 'Registration failed',
          description: backendMsg,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    setIsResending(true);
    try {
      const response = await fetch('https://************:5000/api/auth/resend-verification', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          email: registeredEmail
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to resend verification email');
      }

      toastSuccess({
        title: 'Verification Email Sent',
        description: 'Please check your email for the verification link.',
      });
    } catch (error: any) {
      console.error('Resend verification failed:', error);
      toastError({
        title: 'Failed to resend email',
        description: error.message || 'Please try again later.',
      });
    } finally {
      setIsResending(false);
    }
  };

  // Show verification message after successful registration
  if (registrationComplete) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 px-4 sm:w-[350px] sm:px-0">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Check Your Email</h1>
          <p className="text-sm text-muted-foreground">
            We've sent a verification link to <strong>{registeredEmail}</strong>
          </p>
        </div>
        <div className="grid gap-6">
          <div className="grid gap-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Next Steps:</h3>
              <ol className="text-sm text-blue-800 space-y-1">
                <li>1. Check your email inbox</li>
                <li>2. Click the verification link</li>
                <li>3. Return here to log in</li>
              </ol>
            </div>
            <Button
              type="button"
              variant="outline"
              disabled={isResending}
              onClick={handleResendVerification}
              className="w-full"
            >
              {isResending ? 'Sending...' : 'Resend Verification Email'}
            </Button>
            <Button
              type="button"
              variant="ghost"
              onClick={() => navigate('/login')}
              className="w-full"
            >
              Back to Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 px-4 sm:w-[350px] sm:px-0">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">{t('auth.register.title')}</h1>
        <p className="text-sm text-muted-foreground">
          {t('auth.register.subtitle')}
        </p>
      </div>
      <div className="grid gap-6">
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="firstName">{t('auth.register.firstName')}</Label>
              <Input
                id="firstName"
                placeholder="John"
                autoCapitalize="words"
                autoComplete="given-name"
                disabled={isLoading}
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="lastName">{t('auth.register.lastName')}</Label>
              <Input
                id="lastName"
                placeholder="Doe"
                autoCapitalize="words"
                autoComplete="family-name"
                disabled={isLoading}
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">{t('auth.register.email')}</Label>
              <Input
                id="email"
                placeholder="<EMAIL>"
                type="email"
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect="off"
                disabled={isLoading}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">{t('auth.register.password')}</Label>
              <Input
                id="password"
                placeholder="Enter your password"
                type="password"
                autoComplete="new-password"
                disabled={isLoading}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              {password && (
                <PasswordStrengthIndicator password={password} className="mt-2" />
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword">{t('auth.register.confirmPassword')}</Label>
              <Input
                id="confirmPassword"
                placeholder="Confirm your password"
                type="password"
                autoComplete="new-password"
                disabled={isLoading}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
            <Button disabled={isLoading} className="w-full">
              {isLoading ? t('common.loading') : t('auth.register.createAccount')}
            </Button>
          </div>
        </form>
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              {t('auth.register.orContinueWith')}
            </span>
          </div>
        </div>
        <Button
          variant="outline"
          type="button"
          disabled={isLoading}
          onClick={handleGoogleLogin}
          className="w-full flex items-center justify-center"
        >
          <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
            <path
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              fill="#4285F4"
            />
            <path
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              fill="#34A853"
            />
            <path
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              fill="#FBBC05"
            />
            <path
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              fill="#EA4335"
            />
          </svg>
          {isLoading ? t('common.loading') : t('auth.register.googleSignUp')}
        </Button>
        <p className="px-8 text-center text-sm text-muted-foreground">
          {t('auth.register.agreeTerms')}{' '}
          <button
            type="button"
            onClick={() => setShowTermsModal(true)}
            className="underline underline-offset-4 hover:text-primary text-sm"
          >
            Terms of Service
          </button>{' '}
          {t('common.and')}{' '}
          <button
            type="button"
            onClick={() => setShowPrivacyModal(true)}
            className="underline underline-offset-4 hover:text-primary text-sm"
          >
            Privacy Policy
          </button>
          .
        </p>
        <p className="text-center text-sm">
          {t('auth.register.hasAccount')}{' '}
          <Link to="/login" className="underline underline-offset-4 hover:text-primary">
            {t('auth.register.signIn')}
          </Link>
        </p>
      </div>

      {/* Terms of Service Modal */}
      <TermsOfServiceModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
}
