"""Add paper trading mode to users

Revision ID: 49d634cd32b1
Revises: 11fe5e2f5e5d
Create Date: 2025-07-23 04:20:44.479429

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '49d634cd32b1'
down_revision = '11fe5e2f5e5d'
branch_labels = None
depends_on = None


def upgrade():
    # Add paper_trading_mode column to users table
    op.add_column('users', sa.Column('paper_trading_mode', sa.<PERSON>(), nullable=False, server_default='0'))

    # Create paper_trading_accounts table
    op.create_table('paper_trading_accounts',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('user_id', sa.String(36), nullable=False),
        sa.Column('virtual_balance', sa.Numeric(20, 8), nullable=False, server_default='10000.********'),
        sa.Column('initial_balance', sa.Numeric(20, 8), nullable=False, server_default='10000.********'),
        sa.Column('total_pnl', sa.Numeric(20, 8), server_default='0.********'),
        sa.Column('total_trades_count', sa.Integer(), server_default='0'),
        sa.Column('winning_trades_count', sa.Integer(), server_default='0'),
        sa.Column('losing_trades_count', sa.Integer(), server_default='0'),
        sa.Column('win_rate', sa.Numeric(5, 2), server_default='0.00'),
        sa.Column('reset_count', sa.Integer(), server_default='0'),
        sa.Column('last_reset_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('user_id')
    )

    # Create paper_trades table
    op.create_table('paper_trades',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('user_id', sa.String(36), nullable=False),
        sa.Column('paper_account_id', sa.String(36), nullable=False),
        sa.Column('session_id', sa.String(36), nullable=True),
        sa.Column('symbol', sa.String(20), nullable=False),
        sa.Column('side', sa.Enum('buy', 'sell', 'long', 'short', name='paper_trade_side'), nullable=False),
        sa.Column('quantity', sa.Numeric(20, 8), nullable=False),
        sa.Column('source', sa.String(20), nullable=False, server_default='app'),
        sa.Column('entry_price', sa.Numeric(20, 8), nullable=False),
        sa.Column('exit_price', sa.Numeric(20, 8), nullable=True),
        sa.Column('stop_loss', sa.Numeric(20, 8), nullable=True),
        sa.Column('take_profit', sa.Numeric(20, 8), nullable=True),
        sa.Column('pnl', sa.Numeric(20, 8), nullable=True),
        sa.Column('simulated_fee', sa.Numeric(20, 8), server_default='0'),
        sa.Column('status', sa.Enum('open', 'closed', 'cancelled', name='paper_trade_status'), nullable=False, server_default='open'),
        sa.Column('exit_reason', sa.String(100), nullable=True),
        sa.Column('archived', sa.Boolean(), server_default='0'),
        sa.Column('entry_time', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('exit_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['paper_account_id'], ['paper_trading_accounts.id'], ondelete='CASCADE')
    )

    # Create paper_trading_sessions table
    op.create_table('paper_trading_sessions',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('user_id', sa.String(36), nullable=False),
        sa.Column('paper_account_id', sa.String(36), nullable=False),
        sa.Column('symbol', sa.String(20), nullable=False),
        sa.Column('status', sa.Enum('active', 'paused', 'ended', name='paper_session_status'), nullable=False, server_default='active'),
        sa.Column('initial_balance', sa.Numeric(20, 8), nullable=False),
        sa.Column('current_balance', sa.Numeric(20, 8), nullable=False),
        sa.Column('leverage', sa.Integer(), server_default='1'),
        sa.Column('investment_percentage', sa.Integer(), server_default='0'),
        sa.Column('total_trades', sa.Integer(), server_default='0'),
        sa.Column('winning_trades', sa.Integer(), server_default='0'),
        sa.Column('total_pnl', sa.Numeric(20, 8), server_default='0'),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('ended_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['paper_account_id'], ['paper_trading_accounts.id'], ondelete='CASCADE')
    )

    # Create paper_balance_snapshots table
    op.create_table('paper_balance_snapshots',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('paper_account_id', sa.String(36), nullable=False),
        sa.Column('balance', sa.Numeric(20, 8), nullable=False),
        sa.Column('pnl_change', sa.Numeric(20, 8), server_default='0'),
        sa.Column('transaction_type', sa.String(20), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['paper_account_id'], ['paper_trading_accounts.id'], ondelete='CASCADE')
    )


def downgrade():
    # Drop tables in reverse order
    op.drop_table('paper_balance_snapshots')
    op.drop_table('paper_trading_sessions')
    op.drop_table('paper_trades')
    op.drop_table('paper_trading_accounts')

    # Drop column
    op.drop_column('users', 'paper_trading_mode')
