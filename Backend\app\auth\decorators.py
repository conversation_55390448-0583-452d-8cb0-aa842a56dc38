from functools import wraps
from flask import jsonify, current_app, request
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, get_jwt
from app.models.user import User
from app.models.subscription import SubscriptionTier
from app.auth.jwt_manager import jwt_manager

def jwt_required(optional=False):
    """JWT authentication decorator with blacklist and revocation checking."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                current_app.logger.info("[JWT Debug] === JWT VALIDATION STARTED ===")
                current_app.logger.info(f"[JWT Debug] Request: {request.method} {request.url}")
                current_app.logger.info(f"[JWT Debug] Authorization header: {request.headers.get('Authorization', 'Missing')}")
                # Verify JWT token
                current_app.logger.info("[JWT Debug] About to verify JWT...")
                verify_jwt_in_request(optional=optional)
                current_app.logger.info("[JWT Debug] Basic JWT verification passed")
                
                if not optional or get_jwt_identity():
                    # Get token claims
                    claims = get_jwt()
                    jti = claims['jti']
                    user_id = get_jwt_identity()
                    issued_at = claims.get('iat', 0)
                    
                    # Check if token is blacklisted
                    if jwt_manager.is_token_blacklisted(jti):
                        return jsonify({'message': 'Token has been revoked', 'error': 'token_revoked'}), 401

                    # Check if user tokens were globally revoked
                    if jwt_manager.is_user_tokens_revoked(user_id, issued_at):
                        return jsonify({'message': 'All user tokens have been revoked', 'error': 'tokens_revoked'}), 401

                    # Check if user exists and is active
                    user = User.query.get(user_id)
                    if not user:
                        return jsonify({'message': 'User not found', 'error': 'user_not_found'}), 401
                    if not user.is_active:
                        return jsonify({'message': 'User not found or inactive', 'error': 'user_inactive'}), 401
                    
                    # Check for 2FA pending state
                    if claims.get('two_fa_pending'):
                        return jsonify({'message': '2FA verification required', 'error': '2fa_required'}), 403
                
                return f(*args, **kwargs)
                
            except Exception as e:
                current_app.logger.error(f"[JWT Debug] === JWT VALIDATION EXCEPTION ===")
                current_app.logger.error(f"[JWT Debug] Exception type: {type(e).__name__}")
                current_app.logger.error(f"[JWT Debug] Exception message: {str(e)}")
                current_app.logger.error(f"[JWT Debug] Token validation failed: {str(e)}")
                return jsonify({'message': 'Invalid token', 'error': 'invalid_token', 'details': str(e)}), 401
        
        return decorated_function
    return decorator


def two_fa_required(f):
    """Decorator to require 2FA verification for sensitive operations."""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if user and user.two_fa_enabled:
            # Check if recent 2FA verification exists in session
            session_data = jwt_manager.get_user_session(user_id)
            
            if not session_data or 'two_fa_verified' not in session_data:
                return jsonify({
                    'message': '2FA verification required for this operation',
                    'error': '2fa_verification_required'
                }), 403
        
        return f(*args, **kwargs)
    
    return decorated_function


def subscription_required(required_tier=None, allow_expired_grace_period=False):
    """Decorator to check user subscription status."""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            user_id = get_jwt_identity()
            user = User.query.get(user_id)
            
            if not user:
                return jsonify({'message': 'User not found', 'error': 'user_not_found'}), 404
            
            # Get current subscription
            subscription = user.get_current_subscription()
            
            if not subscription:
                return jsonify({
                    'message': 'No active subscription found',
                    'error': 'no_subscription'
                }), 403
            
            # Check if subscription is active
            if not subscription.is_active():
                # Check for grace period if allowed
                if allow_expired_grace_period and subscription.is_expired():
                    days_expired = (subscription.end_date - subscription.end_date).days
                    if days_expired <= 7:  # 7-day grace period
                        pass  # Allow access
                    else:
                        return jsonify({
                            'message': 'Subscription has expired',
                            'error': 'subscription_expired',
                            'days_expired': days_expired
                        }), 403
                else:
                    return jsonify({
                        'message': 'Subscription is not active',
                        'error': 'subscription_inactive',
                        'subscription_status': subscription.status.value
                    }), 403
            
            # Check tier requirement using user tier booleans
            if required_tier:
                tier_status = getattr(user, 'tier_status', None)
                if not tier_status or not hasattr(tier_status, 'get_current_tier'):
                    return jsonify({
                        'message': 'User tier status not found',
                        'error': 'tier_status_missing'
                    }), 403
            
                required_tier_num = None
                if isinstance(required_tier, str) and required_tier.startswith('tier_'):
                    try:
                        required_tier_num = int(required_tier.split('_')[1])
                    except Exception:
                        required_tier_num = None
                elif isinstance(required_tier, int):
                    required_tier_num = required_tier
            
                user_tier_num = tier_status.get_current_tier()
                if required_tier_num and user_tier_num != required_tier_num:
                    return jsonify({
                        'message': f'User must have tier_{required_tier_num}',
                        'error': 'insufficient_tier',
                        'current_tier': f'tier_{user_tier_num}',
                        'required_tier': f'tier_{required_tier_num}'
                    }), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def admin_required(f):
    """Decorator to require admin privileges."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            from flask_jwt_extended import verify_jwt_in_request, get_jwt, get_jwt_identity

            # Verify JWT token
            verify_jwt_in_request()
            claims = get_jwt()
            identity = get_jwt_identity()

            # Check for admin claim
            if not claims.get('is_admin', False):
                return jsonify({'message': 'Admin privileges required', 'error': 'admin_required'}), 403

            # Verify admin identity format
            if not identity or not identity.startswith('admin_'):
                return jsonify({'message': 'Invalid admin token', 'error': 'invalid_admin_token'}), 403

            return f(*args, **kwargs)

        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"Admin auth error: {str(e)}")
            return jsonify({'message': 'Authentication failed', 'error': 'auth_failed'}), 401

    return decorated_function


def rate_limit_required(rate_limit):
    """Decorator to apply rate limiting to endpoints."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from flask_limiter import Limiter
            from flask_limiter.util import get_remote_address
            from flask import current_app
            
            # Get limiter instance
            limiter = current_app.extensions.get('limiter')
            
            if limiter:
                # Apply rate limit
                try:
                    limiter.limit(rate_limit)(f)(*args, **kwargs)
                except Exception as e:
                    return jsonify({
                        'message': 'Rate limit exceeded',
                        'error': 'rate_limit_exceeded'
                    }), 429
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def trading_enabled_required(f):
    """Decorator to check if user has trading enabled."""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'message': 'User not found', 'error': 'user_not_found'}), 404
        
        # Check if user has valid API credentials
        from app.models.security_log import APICredential
        active_credentials = APICredential.query.filter_by(
            user_id=user_id,
            is_active=True,
            is_valid=True
        ).first()
        
        if not active_credentials:
            return jsonify({
                'message': 'Valid API credentials required for trading',
                'error': 'no_valid_api_credentials'
            }), 403
        
        # Check subscription allows trading
        subscription = user.get_current_subscription()
        if not subscription or not subscription.is_active():
            return jsonify({
                'message': 'Active subscription required for trading',
                'error': 'subscription_required'
            }), 403
        
        return f(*args, **kwargs)
    
    return decorated_function


def validate_2fa_token(f):
    """Decorator to validate 2FA token in request."""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        from flask import request
        from app.auth.two_factor import TwoFactorAuth
        
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'message': 'User not found', 'error': 'user_not_found'}), 404
        
        if user.two_fa_enabled:
            # Get 2FA code from request
            two_fa_code = None
            if request.is_json and request.json:
                two_fa_code = request.json.get('two_fa_code')
            elif request.form:
                two_fa_code = request.form.get('two_fa_code')
            
            if not two_fa_code:
                return jsonify({
                    'message': '2FA code required',
                    'error': '2fa_code_required'
                }), 400
            
            # Verify 2FA code
            if not TwoFactorAuth.verify_user_2fa_code(user_id, two_fa_code):
                return jsonify({
                    'message': 'Invalid 2FA code',
                    'error': 'invalid_2fa_code'
                }), 401
            
            # Store 2FA verification in session
            jwt_manager.store_user_session(user_id, {'two_fa_verified': True}, expires_in=1800)  # 30 minutes
        
        return f(*args, **kwargs)
    
    return decorated_function


def api_key_required(f):
    """Decorator to require API key authentication (for external integrations)."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import request
        
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            return jsonify({'message': 'API key required', 'error': 'api_key_required'}), 401
        
        # Validate API key (implement your own validation logic)
        if not _validate_api_key(api_key):
            return jsonify({'message': 'Invalid API key', 'error': 'invalid_api_key'}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function


def _validate_api_key(api_key):
    """Validate API key (implement your own logic)."""
    # This is a placeholder - implement your own API key validation
    valid_keys = current_app.config.get('VALID_API_KEYS', [])
    return api_key in valid_keys