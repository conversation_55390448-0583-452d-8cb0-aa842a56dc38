{"C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\config.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\config.ts", "statementMap": {"0": {"start": {"line": 2, "column": 13}, "end": {"line": 2, "column": 84}}, "1": {"start": {"line": 5, "column": 13}, "end": {"line": 15, "column": 11}}, "2": {"start": {"line": 18, "column": 13}, "end": {"line": 40, "column": 11}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 56}}, {"start": {"line": 2, "column": 60}, "end": {"line": 2, "column": 83}}]}, "1": {"loc": {"start": {"line": 6, "column": 10}, "end": {"line": 6, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 10}, "end": {"line": 6, "column": 55}}, {"start": {"line": 6, "column": 59}, "end": {"line": 6, "column": 61}}]}, "2": {"loc": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 67}}, {"start": {"line": 7, "column": 71}, "end": {"line": 7, "column": 73}}]}, "3": {"loc": {"start": {"line": 8, "column": 12}, "end": {"line": 8, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 8, "column": 12}, "end": {"line": 8, "column": 59}}, {"start": {"line": 8, "column": 63}, "end": {"line": 8, "column": 65}}]}, "4": {"loc": {"start": {"line": 14, "column": 16}, "end": {"line": 14, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 14, "column": 39}, "end": {"line": 14, "column": 45}}, {"start": {"line": 14, "column": 48}, "end": {"line": 14, "column": 62}}]}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\setupTests.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\setupTests.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 35}}, "1": {"start": {"line": 4, "column": 25}, "end": {"line": 18, "column": 4}}, "2": {"start": {"line": 5, "column": 38}, "end": {"line": 5, "column": 40}}, "3": {"start": {"line": 6, "column": 2}, "end": {"line": 17, "column": 4}}, "4": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 48}}, "5": {"start": {"line": 9, "column": 6}, "end": {"line": 9, "column": 36}}, "6": {"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 24}}, "7": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 17}}, "8": {"start": {"line": 20, "column": 0}, "end": {"line": 22, "column": 3}}, "9": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 25}}, "10": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 56}}, "11": {"start": {"line": 31, "column": 0}, "end": {"line": 43, "column": 3}}, "12": {"start": {"line": 33, "column": 48}, "end": {"line": 42, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": 29}}, "loc": {"start": {"line": 4, "column": 31}, "end": {"line": 18, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 14}}, "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 48}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 14}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 10, "column": 5}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 17}}, "loc": {"start": {"line": 11, "column": 32}, "end": {"line": 13, "column": 5}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 14, "column": 11}, "end": {"line": 14, "column": 14}}, "loc": {"start": {"line": 14, "column": 16}, "end": {"line": 16, "column": 5}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 33, "column": 38}, "end": {"line": 33, "column": 43}}, "loc": {"start": {"line": 33, "column": 48}, "end": {"line": 42, "column": 4}}}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 40}}, {"start": {"line": 7, "column": 44}, "end": {"line": 7, "column": 48}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\__tests__\\simple.test.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\__tests__\\simple.test.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 33}}, "2": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 40}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 46}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 13, "column": 3}}, "5": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 40}}, "6": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 55}}, "7": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 39}}, "8": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 45}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 34}, "end": {"line": 9, "column": 37}}, "loc": {"start": {"line": 9, "column": 39}, "end": {"line": 13, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\__tests__\\smoke.test.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\__tests__\\smoke.test.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 3, "column": 2}, "end": {"line": 6, "column": 5}}, "2": {"start": {"line": 4, "column": 4}, "end": {"line": 4, "column": 41}}, "3": {"start": {"line": 5, "column": 4}, "end": {"line": 5, "column": 28}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "5": {"start": {"line": 9, "column": 19}, "end": {"line": 9, "column": 48}}, "6": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 32}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 26}}, "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 12, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 3, "column": 33}, "end": {"line": 3, "column": 36}}, "loc": {"start": {"line": 3, "column": 38}, "end": {"line": 6, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 38}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 11, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\__tests__\\trading.service.test.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\__tests__\\trading.service.test.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 26}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 45}}, "2": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": null}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 103, "column": 3}}, "4": {"start": {"line": 16, "column": 2}, "end": {"line": 18, "column": 5}}, "5": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 34}}, "6": {"start": {"line": 20, "column": 2}, "end": {"line": 22, "column": 5}}, "7": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 19}}, "8": {"start": {"line": 24, "column": 2}, "end": {"line": 26, "column": 5}}, "9": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 28}}, "10": {"start": {"line": 28, "column": 2}, "end": {"line": 60, "column": 5}}, "11": {"start": {"line": 29, "column": 4}, "end": {"line": 46, "column": 7}}, "12": {"start": {"line": 31, "column": 24}, "end": {"line": 36, "column": 8}}, "13": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 71}}, "14": {"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 46}}, "15": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 40}}, "16": {"start": {"line": 48, "column": 4}, "end": {"line": 59, "column": 7}}, "17": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 87}}, "18": {"start": {"line": 53, "column": 6}, "end": {"line": 58, "column": 9}}, "19": {"start": {"line": 62, "column": 2}, "end": {"line": 81, "column": 5}}, "20": {"start": {"line": 63, "column": 4}, "end": {"line": 80, "column": 7}}, "21": {"start": {"line": 65, "column": 24}, "end": {"line": 70, "column": 8}}, "22": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 66}}, "23": {"start": {"line": 76, "column": 21}, "end": {"line": 76, "column": 49}}, "24": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 40}}, "25": {"start": {"line": 83, "column": 2}, "end": {"line": 102, "column": 5}}, "26": {"start": {"line": 84, "column": 4}, "end": {"line": 101, "column": 7}}, "27": {"start": {"line": 86, "column": 26}, "end": {"line": 91, "column": 8}}, "28": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 70}}, "29": {"start": {"line": 97, "column": 21}, "end": {"line": 97, "column": 43}}, "30": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 42}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 28}, "end": {"line": 15, "column": 31}}, "loc": {"start": {"line": 15, "column": 33}, "end": {"line": 103, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 16}}, "loc": {"start": {"line": 16, "column": 18}, "end": {"line": 18, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 15}}, "loc": {"start": {"line": 20, "column": 17}, "end": {"line": 22, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 24, "column": 38}, "end": {"line": 24, "column": 41}}, "loc": {"start": {"line": 24, "column": 43}, "end": {"line": 26, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 28, "column": 32}, "end": {"line": 28, "column": 35}}, "loc": {"start": {"line": 28, "column": 37}, "end": {"line": 60, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 29, "column": 44}, "end": {"line": 29, "column": 49}}, "loc": {"start": {"line": 29, "column": 55}, "end": {"line": 46, "column": 5}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 48, "column": 35}, "end": {"line": 48, "column": 40}}, "loc": {"start": {"line": 48, "column": 46}, "end": {"line": 59, "column": 5}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 62, "column": 35}, "end": {"line": 62, "column": 38}}, "loc": {"start": {"line": 62, "column": 40}, "end": {"line": 81, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 63, "column": 42}, "end": {"line": 63, "column": 47}}, "loc": {"start": {"line": 63, "column": 53}, "end": {"line": 80, "column": 5}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 83, "column": 29}, "end": {"line": 83, "column": 32}}, "loc": {"start": {"line": 83, "column": 34}, "end": {"line": 102, "column": 3}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 84, "column": 36}, "end": {"line": 84, "column": 41}}, "loc": {"start": {"line": 84, "column": 47}, "end": {"line": 101, "column": 5}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\api\\auth.service.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\api\\auth.service.ts", "statementMap": {"0": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 7}}, "1": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "2": {"start": {"line": 3, "column": 16}, "end": {"line": 3, "column": 48}}, "3": {"start": {"line": 6, "column": 12}, "end": {"line": 12, "column": 2}}, "4": {"start": {"line": 15, "column": 0}, "end": {"line": 26, "column": 2}}, "5": {"start": {"line": 17, "column": 18}, "end": {"line": 17, "column": 54}}, "6": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, "7": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 55}}, "8": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 18}}, "9": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 33}}, "10": {"start": {"line": 29, "column": 0}, "end": {"line": 81, "column": 2}}, "11": {"start": {"line": 30, "column": 16}, "end": {"line": 30, "column": 24}}, "12": {"start": {"line": 32, "column": 28}, "end": {"line": 32, "column": 40}}, "13": {"start": {"line": 35, "column": 4}, "end": {"line": 77, "column": 5}}, "14": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 36}}, "15": {"start": {"line": 38, "column": 6}, "end": {"line": 76, "column": 7}}, "16": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 66}}, "17": {"start": {"line": 40, "column": 8}, "end": {"line": 45, "column": 9}}, "18": {"start": {"line": 42, "column": 10}, "end": {"line": 42, "column": 50}}, "19": {"start": {"line": 43, "column": 10}, "end": {"line": 43, "column": 42}}, "20": {"start": {"line": 44, "column": 10}, "end": {"line": 44, "column": 39}}, "21": {"start": {"line": 48, "column": 25}, "end": {"line": 52, "column": 10}}, "22": {"start": {"line": 54, "column": 39}, "end": {"line": 54, "column": 52}}, "23": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 59}}, "24": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 73}}, "25": {"start": {"line": 63, "column": 8}, "end": {"line": 66, "column": 9}}, "26": {"start": {"line": 65, "column": 10}, "end": {"line": 65, "column": 51}}, "27": {"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 36}}, "28": {"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 48}}, "29": {"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": 49}}, "30": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 40}}, "31": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 44}}, "32": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 33}}, "33": {"start": {"line": 85, "column": 1}, "end": {"line": 93, "column": 2}}, "34": {"start": {"line": 87, "column": 15}, "end": {"line": 87, "column": 48}}, "35": {"start": {"line": 88, "column": 3}, "end": {"line": 88, "column": 19}}, "36": {"start": {"line": 91, "column": 15}, "end": {"line": 91, "column": 37}}, "37": {"start": {"line": 92, "column": 3}, "end": {"line": 92, "column": 19}}, "38": {"start": {"line": 119, "column": 13}, "end": {"line": 166, "column": 2}}, "39": {"start": {"line": 121, "column": 4}, "end": {"line": 134, "column": 5}}, "40": {"start": {"line": 122, "column": 23}, "end": {"line": 122, "column": 82}}, "41": {"start": {"line": 123, "column": 52}, "end": {"line": 123, "column": 65}}, "42": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 57}}, "43": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "44": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 61}}, "45": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 51}}, "46": {"start": {"line": 132, "column": 6}, "end": {"line": 132, "column": 43}}, "47": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 18}}, "48": {"start": {"line": 138, "column": 4}, "end": {"line": 146, "column": 5}}, "49": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 32}}, "50": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 44}}, "51": {"start": {"line": 143, "column": 6}, "end": {"line": 143, "column": 46}}, "52": {"start": {"line": 144, "column": 6}, "end": {"line": 144, "column": 47}}, "53": {"start": {"line": 145, "column": 6}, "end": {"line": 145, "column": 38}}, "54": {"start": {"line": 150, "column": 4}, "end": {"line": 150, "column": 42}}, "55": {"start": {"line": 154, "column": 4}, "end": {"line": 160, "column": 5}}, "56": {"start": {"line": 155, "column": 23}, "end": {"line": 155, "column": 43}}, "57": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 32}}, "58": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 59}}, "59": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 18}}, "60": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 50}}, "61": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 3}}, "loc": {"start": {"line": 16, "column": 13}, "end": {"line": 22, "column": 3}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 3}}, "loc": {"start": {"line": 23, "column": 12}, "end": {"line": 25, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 3}}, "loc": {"start": {"line": 30, "column": 16}, "end": {"line": 30, "column": 24}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 7}}, "loc": {"start": {"line": 31, "column": 18}, "end": {"line": 80, "column": 3}}}, "4": {"name": "getUserTierInfo", "decl": {"start": {"line": 84, "column": 22}, "end": {"line": 84, "column": 37}}, "loc": {"start": {"line": 84, "column": 51}, "end": {"line": 94, "column": 1}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 7}}, "loc": {"start": {"line": 120, "column": 44}, "end": {"line": 135, "column": 3}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 137, "column": 2}, "end": {"line": 137, "column": 7}}, "loc": {"start": {"line": 137, "column": 14}, "end": {"line": 147, "column": 3}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 149, "column": 2}, "end": {"line": 149, "column": 7}}, "loc": {"start": {"line": 149, "column": 39}, "end": {"line": 151, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 153, "column": 2}, "end": {"line": 153, "column": 7}}, "loc": {"start": {"line": 153, "column": 22}, "end": {"line": 161, "column": 3}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 17}}, "loc": {"start": {"line": 163, "column": 17}, "end": {"line": 165, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 77, "column": 5}}, "type": "if", "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 77, "column": 5}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 38}}, {"start": {"line": 35, "column": 42}, "end": {"line": 35, "column": 65}}]}, "3": {"loc": {"start": {"line": 40, "column": 8}, "end": {"line": 45, "column": 9}}, "type": "if", "locations": [{"start": {"line": 40, "column": 8}, "end": {"line": 45, "column": 9}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 63, "column": 8}, "end": {"line": 66, "column": 9}}, "type": "if", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 66, "column": 9}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 63, "column": 12}, "end": {"line": 63, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 12}, "end": {"line": 63, "column": 16}}, {"start": {"line": 63, "column": 20}, "end": {"line": 63, "column": 57}}]}, "6": {"loc": {"start": {"line": 85, "column": 1}, "end": {"line": 93, "column": 2}}, "type": "if", "locations": [{"start": {"line": 85, "column": 1}, "end": {"line": 93, "column": 2}}, {"start": {"line": 89, "column": 8}, "end": {"line": 93, "column": 2}}]}, "7": {"loc": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "type": "if", "locations": [{"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, {"start": {}, "end": {}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\api\\trading.service.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\api\\trading.service.ts", "statementMap": {"0": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 7}}, "1": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 7}}, "2": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 7}}, "3": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 7}}, "4": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 7}}, "5": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "6": {"start": {"line": 4, "column": 0}, "end": {"line": 21, "column": 3}}, "7": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 94}}, "8": {"start": {"line": 6, "column": 2}, "end": {"line": 13, "column": 3}}, "9": {"start": {"line": 8, "column": 4}, "end": {"line": 10, "column": 5}}, "10": {"start": {"line": 9, "column": 6}, "end": {"line": 9, "column": 53}}, "11": {"start": {"line": 12, "column": 5}, "end": {"line": 12, "column": 65}}, "12": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 97}}, "13": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 103}}, "14": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 16}}, "15": {"start": {"line": 25, "column": 14}, "end": {"line": 25, "column": 61}}, "16": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 18}}, "17": {"start": {"line": 30, "column": 14}, "end": {"line": 30, "column": 56}}, "18": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 29}}, "19": {"start": {"line": 35, "column": 14}, "end": {"line": 35, "column": 58}}, "20": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 18}}, "21": {"start": {"line": 41, "column": 14}, "end": {"line": 41, "column": 61}}, "22": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 18}}, "23": {"start": {"line": 47, "column": 14}, "end": {"line": 47, "column": 66}}, "24": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 31}}, "25": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 4, "column": 31}, "end": {"line": 4, "column": 32}}, "loc": {"start": {"line": 4, "column": 42}, "end": {"line": 21, "column": 1}}}, "1": {"name": "getDashboardStats", "decl": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 39}}, "loc": {"start": {"line": 24, "column": 39}, "end": {"line": 27, "column": 1}}}, "2": {"name": "getTradingStatistics", "decl": {"start": {"line": 29, "column": 22}, "end": {"line": 29, "column": 42}}, "loc": {"start": {"line": 29, "column": 42}, "end": {"line": 32, "column": 1}}}, "3": {"name": "getRealBalance", "decl": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": 36}}, "loc": {"start": {"line": 34, "column": 36}, "end": {"line": 37, "column": 1}}}, "4": {"name": "getActivePosition", "decl": {"start": {"line": 40, "column": 22}, "end": {"line": 40, "column": 39}}, "loc": {"start": {"line": 40, "column": 39}, "end": {"line": 43, "column": 1}}}, "5": {"name": "getTradingHistory", "decl": {"start": {"line": 46, "column": 22}, "end": {"line": 46, "column": 39}}, "loc": {"start": {"line": 46, "column": 48}, "end": {"line": 49, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 94}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 52}}, {"start": {"line": 5, "column": 56}, "end": {"line": 5, "column": 94}}]}, "1": {"loc": {"start": {"line": 6, "column": 2}, "end": {"line": 13, "column": 3}}, "type": "if", "locations": [{"start": {"line": 6, "column": 2}, "end": {"line": 13, "column": 3}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 8, "column": 4}, "end": {"line": 10, "column": 5}}, "type": "if", "locations": [{"start": {"line": 8, "column": 4}, "end": {"line": 10, "column": 5}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 46, "column": 40}, "end": {"line": 46, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 46, "column": 47}, "end": {"line": 46, "column": 48}}]}, "4": {"loc": {"start": {"line": 48, "column": 9}, "end": {"line": 48, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 9}, "end": {"line": 48, "column": 24}}, {"start": {"line": 48, "column": 28}, "end": {"line": 48, "column": 30}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\components\\ui\\use-toast.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\components\\ui\\use-toast.ts", "statementMap": {"0": {"start": {"line": 193, "column": 9}, "end": {"line": 193, "column": 17}}, "1": {"start": {"line": 193, "column": 19}, "end": {"line": 193, "column": 24}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 30}}, "3": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 21}}, "4": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 34}}, "5": {"start": {"line": 20, "column": 20}, "end": {"line": 25, "column": null}}, "6": {"start": {"line": 27, "column": 12}, "end": {"line": 27, "column": 13}}, "7": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "8": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "9": {"start": {"line": 58, "column": 22}, "end": {"line": 58, "column": 70}}, "10": {"start": {"line": 60, "column": 25}, "end": {"line": 74, "column": 1}}, "11": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "12": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 10}}, "13": {"start": {"line": 65, "column": 18}, "end": {"line": 71, "column": 24}}, "14": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": null}}, "15": {"start": {"line": 67, "column": 4}, "end": {"line": 70, "column": null}}, "16": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": null}}, "17": {"start": {"line": 76, "column": 23}, "end": {"line": 129, "column": 1}}, "18": {"start": {"line": 77, "column": 2}, "end": {"line": 128, "column": 3}}, "19": {"start": {"line": 79, "column": 6}, "end": {"line": 82, "column": null}}, "20": {"start": {"line": 85, "column": 6}, "end": {"line": 90, "column": null}}, "21": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 66}}, "22": {"start": {"line": 93, "column": 26}, "end": {"line": 93, "column": 32}}, "23": {"start": {"line": 97, "column": 6}, "end": {"line": 103, "column": 7}}, "24": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": null}}, "25": {"start": {"line": 100, "column": 8}, "end": {"line": 102, "column": null}}, "26": {"start": {"line": 101, "column": 10}, "end": {"line": 101, "column": null}}, "27": {"start": {"line": 105, "column": 6}, "end": {"line": 115, "column": null}}, "28": {"start": {"line": 108, "column": 10}, "end": {"line": 113, "column": 15}}, "29": {"start": {"line": 118, "column": 6}, "end": {"line": 123, "column": 7}}, "30": {"start": {"line": 119, "column": 8}, "end": {"line": 122, "column": null}}, "31": {"start": {"line": 124, "column": 6}, "end": {"line": 127, "column": null}}, "32": {"start": {"line": 126, "column": 43}, "end": {"line": 126, "column": 66}}, "33": {"start": {"line": 76, "column": 13}, "end": {"line": 76, "column": 23}}, "34": {"start": {"line": 131, "column": 49}, "end": {"line": 131, "column": 51}}, "35": {"start": {"line": 133, "column": 25}, "end": {"line": 133, "column": 39}}, "36": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": null}}, "37": {"start": {"line": 137, "column": 2}, "end": {"line": 139, "column": null}}, "38": {"start": {"line": 138, "column": 4}, "end": {"line": 138, "column": null}}, "39": {"start": {"line": 145, "column": 13}, "end": {"line": 145, "column": 20}}, "40": {"start": {"line": 147, "column": 17}, "end": {"line": 151, "column": 6}}, "41": {"start": {"line": 148, "column": 4}, "end": {"line": 151, "column": 6}}, "42": {"start": {"line": 152, "column": 18}, "end": {"line": 152, "column": 72}}, "43": {"start": {"line": 152, "column": 24}, "end": {"line": 152, "column": 72}}, "44": {"start": {"line": 154, "column": 2}, "end": {"line": 164, "column": null}}, "45": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": null}}, "46": {"start": {"line": 161, "column": 19}, "end": {"line": 161, "column": null}}, "47": {"start": {"line": 166, "column": 2}, "end": {"line": 170, "column": null}}, "48": {"start": {"line": 174, "column": 28}, "end": {"line": 174, "column": 62}}, "49": {"start": {"line": 176, "column": 2}, "end": {"line": 184, "column": null}}, "50": {"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": null}}, "51": {"start": {"line": 178, "column": 4}, "end": {"line": 183, "column": null}}, "52": {"start": {"line": 179, "column": 20}, "end": {"line": 179, "column": 47}}, "53": {"start": {"line": 180, "column": 6}, "end": {"line": 182, "column": 7}}, "54": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": null}}, "55": {"start": {"line": 186, "column": 2}, "end": {"line": 190, "column": null}}, "56": {"start": {"line": 189, "column": 35}, "end": {"line": 189, "column": 79}}}, "fnMap": {"0": {"name": "genId", "decl": {"start": {"line": 29, "column": 9}, "end": {"line": 29, "column": 14}}, "loc": {"start": {"line": 29, "column": 14}, "end": {"line": 32, "column": 1}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 60, "column": 25}, "end": {"line": 60, "column": 26}}, "loc": {"start": {"line": 60, "column": 45}, "end": {"line": 74, "column": 1}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 65, "column": 29}, "end": {"line": 65, "column": 32}}, "loc": {"start": {"line": 65, "column": 34}, "end": {"line": 71, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 76, "column": 23}, "end": {"line": 76, "column": 24}}, "loc": {"start": {"line": 76, "column": 63}, "end": {"line": 129, "column": 1}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 87, "column": 33}, "end": {"line": 87, "column": 34}}, "loc": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 66}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 100, "column": 29}, "end": {"line": 100, "column": 30}}, "loc": {"start": {"line": 100, "column": 39}, "end": {"line": 102, "column": 9}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 107, "column": 33}, "end": {"line": 107, "column": 34}}, "loc": {"start": {"line": 108, "column": 10}, "end": {"line": 113, "column": 15}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 126, "column": 36}, "end": {"line": 126, "column": 37}}, "loc": {"start": {"line": 126, "column": 43}, "end": {"line": 126, "column": 66}}}, "8": {"name": "dispatch", "decl": {"start": {"line": 135, "column": 9}, "end": {"line": 135, "column": 17}}, "loc": {"start": {"line": 135, "column": 32}, "end": {"line": 140, "column": 1}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 137, "column": 20}, "end": {"line": 137, "column": 21}}, "loc": {"start": {"line": 137, "column": 33}, "end": {"line": 139, "column": 3}}}, "10": {"name": "toast", "decl": {"start": {"line": 144, "column": 9}, "end": {"line": 144, "column": 14}}, "loc": {"start": {"line": 144, "column": 34}, "end": {"line": 171, "column": 1}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 147, "column": 17}, "end": {"line": 147, "column": 18}}, "loc": {"start": {"line": 148, "column": 4}, "end": {"line": 151, "column": 6}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 152, "column": 18}, "end": {"line": 152, "column": 21}}, "loc": {"start": {"line": 152, "column": 24}, "end": {"line": 152, "column": 72}}}, "13": {"name": "(anonymous_22)", "decl": {"start": {"line": 160, "column": 20}, "end": {"line": 160, "column": 21}}, "loc": {"start": {"line": 160, "column": 29}, "end": {"line": 162, "column": 7}}}, "14": {"name": "useToast", "decl": {"start": {"line": 173, "column": 9}, "end": {"line": 173, "column": 17}}, "loc": {"start": {"line": 173, "column": 17}, "end": {"line": 191, "column": 1}}}, "15": {"name": "(anonymous_24)", "decl": {"start": {"line": 176, "column": 18}, "end": {"line": 176, "column": 21}}, "loc": {"start": {"line": 176, "column": 23}, "end": {"line": 184, "column": 3}}}, "16": {"name": "(anonymous_25)", "decl": {"start": {"line": 178, "column": 11}, "end": {"line": 178, "column": 14}}, "loc": {"start": {"line": 178, "column": 16}, "end": {"line": 183, "column": 5}}}, "17": {"name": "(anonymous_26)", "decl": {"start": {"line": 189, "column": 13}, "end": {"line": 189, "column": 14}}, "loc": {"start": {"line": 189, "column": 35}, "end": {"line": 189, "column": 79}}}}, "branchMap": {"0": {"loc": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "type": "if", "locations": [{"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 128, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": null}}, {"start": {"line": 84, "column": 4}, "end": {"line": 90, "column": null}}, {"start": {"line": 92, "column": 4}, "end": {"line": 116, "column": 5}}, {"start": {"line": 117, "column": 4}, "end": {"line": 127, "column": null}}]}, "2": {"loc": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 88, "column": 37}, "end": {"line": 88, "column": 62}}, {"start": {"line": 88, "column": 65}, "end": {"line": 88, "column": 66}}]}, "3": {"loc": {"start": {"line": 97, "column": 6}, "end": {"line": 103, "column": 7}}, "type": "if", "locations": [{"start": {"line": 97, "column": 6}, "end": {"line": 103, "column": 7}}, {"start": {"line": 99, "column": 13}, "end": {"line": 103, "column": 7}}]}, "4": {"loc": {"start": {"line": 108, "column": 10}, "end": {"line": 113, "column": 15}}, "type": "cond-expr", "locations": [{"start": {"line": 109, "column": 14}, "end": {"line": 112, "column": null}}, {"start": {"line": 113, "column": 14}, "end": {"line": 113, "column": 15}}]}, "5": {"loc": {"start": {"line": 108, "column": 10}, "end": {"line": 108, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 108, "column": 10}, "end": {"line": 108, "column": 26}}, {"start": {"line": 108, "column": 30}, "end": {"line": 108, "column": 51}}]}, "6": {"loc": {"start": {"line": 118, "column": 6}, "end": {"line": 123, "column": 7}}, "type": "if", "locations": [{"start": {"line": 118, "column": 6}, "end": {"line": 123, "column": 7}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": null}}, "type": "if", "locations": [{"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": null}}, {"start": {}, "end": {}}]}, "8": {"loc": {"start": {"line": 180, "column": 6}, "end": {"line": 182, "column": 7}}, "type": "if", "locations": [{"start": {"line": 180, "column": 6}, "end": {"line": 182, "column": 7}}, {"start": {}, "end": {}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0, 0], "1": [0, 0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\lib\\utils.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\lib\\utils.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 16}}, "1": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 16}}, "2": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 16}}, "3": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 16}}, "4": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 16}}, "5": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 16}}, "6": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 16}}, "7": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 16}}, "8": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 16}}, "9": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "10": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "11": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 31}}, "12": {"start": {"line": 9, "column": 2}, "end": {"line": 14, "column": 20}}, "13": {"start": {"line": 18, "column": 2}, "end": {"line": 22, "column": 25}}, "14": {"start": {"line": 26, "column": 2}, "end": {"line": 29, "column": 19}}, "15": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "16": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 26}}, "17": {"start": {"line": 36, "column": 2}, "end": {"line": 42, "column": 18}}, "18": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 31}}, "19": {"start": {"line": 46, "column": 22}, "end": {"line": 46, "column": 31}}, "20": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 50}}, "21": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 39}}, "22": {"start": {"line": 51, "column": 28}, "end": {"line": 51, "column": 39}}, "23": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 42}}, "24": {"start": {"line": 56, "column": 14}, "end": {"line": 56, "column": 67}}, "25": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 32}}, "26": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 36}}, "27": {"start": {"line": 61, "column": 19}, "end": {"line": 61, "column": 36}}, "28": {"start": {"line": 63, "column": 12}, "end": {"line": 63, "column": 16}}, "29": {"start": {"line": 64, "column": 13}, "end": {"line": 64, "column": 40}}, "30": {"start": {"line": 65, "column": 16}, "end": {"line": 65, "column": 73}}, "31": {"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 53}}, "32": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 75}}}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 18}}, "loc": {"start": {"line": 4, "column": 42}, "end": {"line": 6, "column": 1}}}, "1": {"name": "formatCurrency", "decl": {"start": {"line": 8, "column": 16}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 71}, "end": {"line": 15, "column": 1}}}, "2": {"name": "formatPercent", "decl": {"start": {"line": 17, "column": 16}, "end": {"line": 17, "column": 29}}, "loc": {"start": {"line": 17, "column": 43}, "end": {"line": 23, "column": 1}}}, "3": {"name": "formatNumber", "decl": {"start": {"line": 25, "column": 16}, "end": {"line": 25, "column": 28}}, "loc": {"start": {"line": 25, "column": 64}, "end": {"line": 30, "column": 1}}}, "4": {"name": "formatDate", "decl": {"start": {"line": 32, "column": 16}, "end": {"line": 32, "column": 26}}, "loc": {"start": {"line": 32, "column": 46}, "end": {"line": 43, "column": 1}}}, "5": {"name": "calculatePercentageChange", "decl": {"start": {"line": 45, "column": 16}, "end": {"line": 45, "column": 41}}, "loc": {"start": {"line": 45, "column": 76}, "end": {"line": 48, "column": 1}}}, "6": {"name": "truncateString", "decl": {"start": {"line": 50, "column": 16}, "end": {"line": 50, "column": 30}}, "loc": {"start": {"line": 50, "column": 63}, "end": {"line": 53, "column": 1}}}, "7": {"name": "isPositiveNumber", "decl": {"start": {"line": 55, "column": 16}, "end": {"line": 55, "column": 32}}, "loc": {"start": {"line": 55, "column": 55}, "end": {"line": 58, "column": 1}}}, "8": {"name": "formatBytes", "decl": {"start": {"line": 60, "column": 16}, "end": {"line": 60, "column": 27}}, "loc": {"start": {"line": 60, "column": 63}, "end": {"line": 70, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 47}, "end": {"line": 8, "column": 71}}, "type": "default-arg", "locations": [{"start": {"line": 8, "column": 66}, "end": {"line": 8, "column": 71}}]}, "1": {"loc": {"start": {"line": 25, "column": 44}, "end": {"line": 25, "column": 64}}, "type": "default-arg", "locations": [{"start": {"line": 25, "column": 63}, "end": {"line": 25, "column": 64}}]}, "2": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "type": "if", "locations": [{"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 31}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 31}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 50, "column": 44}, "end": {"line": 50, "column": 63}}, "type": "default-arg", "locations": [{"start": {"line": 50, "column": 61}, "end": {"line": 50, "column": 63}}]}, "5": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 39}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 39}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 56, "column": 14}, "end": {"line": 56, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 56, "column": 42}, "end": {"line": 56, "column": 59}}, {"start": {"line": 56, "column": 62}, "end": {"line": 56, "column": 67}}]}, "7": {"loc": {"start": {"line": 57, "column": 9}, "end": {"line": 57, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 9}, "end": {"line": 57, "column": 20}}, {"start": {"line": 57, "column": 24}, "end": {"line": 57, "column": 31}}]}, "8": {"loc": {"start": {"line": 60, "column": 43}, "end": {"line": 60, "column": 63}}, "type": "default-arg", "locations": [{"start": {"line": 60, "column": 62}, "end": {"line": 60, "column": 63}}]}, "9": {"loc": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 36}}, "type": "if", "locations": [{"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 36}}, {"start": {}, "end": {}}]}, "10": {"loc": {"start": {"line": 64, "column": 13}, "end": {"line": 64, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 64, "column": 28}, "end": {"line": 64, "column": 29}}, {"start": {"line": 64, "column": 32}, "end": {"line": 64, "column": 40}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0], "9": [0, 0], "10": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\api.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\api.ts", "statementMap": {"0": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": 60}}, "1": {"start": {"line": 10, "column": 12}, "end": {"line": 73, "column": 2}}, "2": {"start": {"line": 12, "column": 18}, "end": {"line": 12, "column": 54}}, "3": {"start": {"line": 14, "column": 32}, "end": {"line": 23, "column": 6}}, "4": {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, "5": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 33}}, "6": {"start": {"line": 29, "column": 21}, "end": {"line": 29, "column": 70}}, "7": {"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": 5}}, "8": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 64}}, "9": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 20}}, "10": {"start": {"line": 39, "column": 21}, "end": {"line": 39, "column": 49}}, "11": {"start": {"line": 40, "column": 4}, "end": {"line": 42, "column": 6}}, "12": {"start": {"line": 46, "column": 21}, "end": {"line": 49, "column": 6}}, "13": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 6}}, "14": {"start": {"line": 56, "column": 21}, "end": {"line": 59, "column": 6}}, "15": {"start": {"line": 60, "column": 4}, "end": {"line": 62, "column": 6}}, "16": {"start": {"line": 66, "column": 21}, "end": {"line": 68, "column": 6}}, "17": {"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 6}}, "18": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 19}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 7}}, "loc": {"start": {"line": 11, "column": 62}, "end": {"line": 36, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 7}}, "loc": {"start": {"line": 38, "column": 28}, "end": {"line": 43, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 7}}, "loc": {"start": {"line": 45, "column": 41}, "end": {"line": 53, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 7}}, "loc": {"start": {"line": 55, "column": 40}, "end": {"line": 63, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 7}}, "loc": {"start": {"line": 65, "column": 31}, "end": {"line": 72, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": 54}}, {"start": {"line": 2, "column": 58}, "end": {"line": 2, "column": 60}}]}, "1": {"loc": {"start": {"line": 11, "column": 34}, "end": {"line": 11, "column": 62}}, "type": "default-arg", "locations": [{"start": {"line": 11, "column": 60}, "end": {"line": 11, "column": 62}}]}, "2": {"loc": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 28}}, {"start": {"line": 15, "column": 32}, "end": {"line": 15, "column": 37}}]}, "3": {"loc": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 17}}, {"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 59}}]}, "4": {"loc": {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, "type": "if", "locations": [{"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": 5}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": 5}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 48, "column": 12}, "end": {"line": 48, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 48, "column": 19}, "end": {"line": 48, "column": 39}}, {"start": {"line": 48, "column": 42}, "end": {"line": 48, "column": 51}}]}, "7": {"loc": {"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 58, "column": 19}, "end": {"line": 58, "column": 39}}, {"start": {"line": 58, "column": 42}, "end": {"line": 58, "column": 51}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\market.service.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\market.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 41}}, "1": {"start": {"line": 17, "column": 13}, "end": {"line": 69, "column": 2}}, "2": {"start": {"line": 19, "column": 4}, "end": {"line": 67, "column": 5}}, "3": {"start": {"line": 20, "column": 23}, "end": {"line": 27, "column": null}}, "4": {"start": {"line": 30, "column": 6}, "end": {"line": 32, "column": 7}}, "5": {"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": 66}}, "6": {"start": {"line": 34, "column": 19}, "end": {"line": 34, "column": 40}}, "7": {"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, "8": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 36}}, "9": {"start": {"line": 40, "column": 6}, "end": {"line": 51, "column": 8}}, "10": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 55}}, "11": {"start": {"line": 54, "column": 6}, "end": {"line": 66, "column": 8}}, "12": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 29}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 7}}, "loc": {"start": {"line": 18, "column": 53}, "end": {"line": 68, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 6}, "end": {"line": 32, "column": 7}}, "type": "if", "locations": [{"start": {"line": 30, "column": 6}, "end": {"line": 32, "column": 7}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, "type": "if", "locations": [{"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": 27}}, {"start": {"line": 41, "column": 31}, "end": {"line": 41, "column": 40}}]}, "3": {"loc": {"start": {"line": 45, "column": 18}, "end": {"line": 45, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 18}, "end": {"line": 45, "column": 31}}, {"start": {"line": 45, "column": 35}, "end": {"line": 45, "column": 37}}]}, "4": {"loc": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 43}}, {"start": {"line": 46, "column": 47}, "end": {"line": 46, "column": 49}}]}, "5": {"loc": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 35}}, {"start": {"line": 47, "column": 39}, "end": {"line": 47, "column": 41}}]}, "6": {"loc": {"start": {"line": 65, "column": 15}, "end": {"line": 65, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 40}, "end": {"line": 65, "column": 53}}, {"start": {"line": 65, "column": 56}, "end": {"line": 65, "column": 82}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\nft.service.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\nft.service.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "1": {"start": {"line": 33, "column": 13}, "end": {"line": 129, "column": 2}}, "2": {"start": {"line": 36, "column": 21}, "end": {"line": 36, "column": 53}}, "3": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 25}}, "4": {"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 69}}, "5": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 25}}, "6": {"start": {"line": 48, "column": 21}, "end": {"line": 48, "column": 62}}, "7": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 25}}, "8": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 57}}, "9": {"start": {"line": 59, "column": 21}, "end": {"line": 59, "column": 69}}, "10": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 25}}, "11": {"start": {"line": 65, "column": 21}, "end": {"line": 65, "column": 59}}, "12": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 40}}, "13": {"start": {"line": 75, "column": 4}, "end": {"line": 77, "column": 5}}, "14": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 32}}, "15": {"start": {"line": 80, "column": 24}, "end": {"line": 80, "column": 69}}, "16": {"start": {"line": 81, "column": 29}, "end": {"line": 81, "column": 76}}, "17": {"start": {"line": 83, "column": 4}, "end": {"line": 88, "column": 5}}, "18": {"start": {"line": 84, "column": 6}, "end": {"line": 87, "column": 8}}, "19": {"start": {"line": 90, "column": 4}, "end": {"line": 95, "column": 5}}, "20": {"start": {"line": 91, "column": 6}, "end": {"line": 94, "column": 8}}, "21": {"start": {"line": 97, "column": 4}, "end": {"line": 102, "column": 5}}, "22": {"start": {"line": 98, "column": 6}, "end": {"line": 101, "column": 8}}, "23": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 30}}, "24": {"start": {"line": 109, "column": 19}, "end": {"line": 109, "column": 39}}, "25": {"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": 26}}, "26": {"start": {"line": 111, "column": 21}, "end": {"line": 111, "column": 53}}, "27": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 55}}, "28": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 35}}, "29": {"start": {"line": 117, "column": 18}, "end": {"line": 117, "column": 35}}, "30": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": 43}}, "31": {"start": {"line": 118, "column": 20}, "end": {"line": 118, "column": 43}}, "32": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 45}}, "33": {"start": {"line": 119, "column": 20}, "end": {"line": 119, "column": 45}}, "34": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 51}}, "35": {"start": {"line": 120, "column": 19}, "end": {"line": 120, "column": 51}}, "36": {"start": {"line": 122, "column": 19}, "end": {"line": 122, "column": 40}}, "37": {"start": {"line": 123, "column": 26}, "end": {"line": 123, "column": 35}}, "38": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 72}}, "39": {"start": {"line": 125, "column": 45}, "end": {"line": 125, "column": 72}}, "40": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 65}}, "41": {"start": {"line": 126, "column": 29}, "end": {"line": 126, "column": 65}}, "42": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 52}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 7}}, "loc": {"start": {"line": 35, "column": 20}, "end": {"line": 38, "column": 3}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 7}}, "loc": {"start": {"line": 41, "column": 45}, "end": {"line": 44, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 7}}, "loc": {"start": {"line": 47, "column": 21}, "end": {"line": 50, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 7}}, "loc": {"start": {"line": 53, "column": 31}, "end": {"line": 55, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 7}}, "loc": {"start": {"line": 58, "column": 60}, "end": {"line": 61, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 7}}, "loc": {"start": {"line": 64, "column": 25}, "end": {"line": 67, "column": 3}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 22}}, "loc": {"start": {"line": 73, "column": 41}, "end": {"line": 105, "column": 3}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 24}}, "loc": {"start": {"line": 108, "column": 43}, "end": {"line": 113, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 116, "column": 2}, "end": {"line": 116, "column": 21}}, "loc": {"start": {"line": 116, "column": 34}, "end": {"line": 128, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 66, "column": 11}, "end": {"line": 66, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 11}, "end": {"line": 66, "column": 33}}, {"start": {"line": 66, "column": 37}, "end": {"line": 66, "column": 39}}]}, "1": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 77, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 77, "column": 5}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 80, "column": 24}, "end": {"line": 80, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 24}, "end": {"line": 80, "column": 43}}, {"start": {"line": 80, "column": 47}, "end": {"line": 80, "column": 69}}]}, "3": {"loc": {"start": {"line": 83, "column": 4}, "end": {"line": 88, "column": 5}}, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 88, "column": 5}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 20}}, {"start": {"line": 83, "column": 24}, "end": {"line": 83, "column": 41}}]}, "5": {"loc": {"start": {"line": 90, "column": 4}, "end": {"line": 95, "column": 5}}, "type": "if", "locations": [{"start": {"line": 90, "column": 4}, "end": {"line": 95, "column": 5}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 97, "column": 4}, "end": {"line": 102, "column": 5}}, "type": "if", "locations": [{"start": {"line": 97, "column": 4}, "end": {"line": 102, "column": 5}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 35}}, "type": "if", "locations": [{"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 35}}, {"start": {}, "end": {}}]}, "8": {"loc": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": 43}}, "type": "if", "locations": [{"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": 43}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 45}}, "type": "if", "locations": [{"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 45}}, {"start": {}, "end": {}}]}, "10": {"loc": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 51}}, "type": "if", "locations": [{"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 51}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 72}}, "type": "if", "locations": [{"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 72}}, {"start": {}, "end": {}}]}, "12": {"loc": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 20}}, {"start": {"line": 125, "column": 24}, "end": {"line": 125, "column": 43}}]}, "13": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 65}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 65}}, {"start": {}, "end": {}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\realtime.service.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\realtime.service.ts", "statementMap": {"0": {"start": {"line": 23, "column": 10}, "end": {"line": 23, "column": 49}}, "1": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 59}}, "2": {"start": {"line": 25, "column": 10}, "end": {"line": 25, "column": 30}}, "3": {"start": {"line": 33, "column": 4}, "end": {"line": 35, "column": 5}}, "4": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 13}}, "5": {"start": {"line": 37, "column": 4}, "end": {"line": 76, "column": 5}}, "6": {"start": {"line": 38, "column": 20}, "end": {"line": 38, "column": 56}}, "7": {"start": {"line": 39, "column": 6}, "end": {"line": 42, "column": 7}}, "8": {"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 70}}, "9": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 15}}, "10": {"start": {"line": 44, "column": 6}, "end": {"line": 46, "column": 9}}, "11": {"start": {"line": 48, "column": 6}, "end": {"line": 51, "column": 8}}, "12": {"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 56}}, "13": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 32}}, "14": {"start": {"line": 53, "column": 6}, "end": {"line": 60, "column": 8}}, "15": {"start": {"line": 54, "column": 8}, "end": {"line": 59, "column": 9}}, "16": {"start": {"line": 55, "column": 37}, "end": {"line": 55, "column": 59}}, "17": {"start": {"line": 56, "column": 10}, "end": {"line": 56, "column": 37}}, "18": {"start": {"line": 58, "column": 10}, "end": {"line": 58, "column": 64}}, "19": {"start": {"line": 62, "column": 6}, "end": {"line": 72, "column": 8}}, "20": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 60}}, "21": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 33}}, "22": {"start": {"line": 67, "column": 8}, "end": {"line": 71, "column": 17}}, "23": {"start": {"line": 68, "column": 10}, "end": {"line": 70, "column": 11}}, "24": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 29}}, "25": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 72}}, "26": {"start": {"line": 83, "column": 4}, "end": {"line": 86, "column": 5}}, "27": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 31}}, "28": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 30}}, "29": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 29}}, "30": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 22}}, "31": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 43}}, "32": {"start": {"line": 95, "column": 21}, "end": {"line": 95, "column": 35}}, "33": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 34}}, "34": {"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 5}}, "35": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 21}}, "36": {"start": {"line": 110, "column": 4}, "end": {"line": 120, "column": 6}}, "37": {"start": {"line": 111, "column": 20}, "end": {"line": 111, "column": 52}}, "38": {"start": {"line": 112, "column": 6}, "end": {"line": 114, "column": 7}}, "39": {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 40}}, "40": {"start": {"line": 117, "column": 6}, "end": {"line": 119, "column": 7}}, "41": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 26}}, "42": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 28}}, "43": {"start": {"line": 134, "column": 4}, "end": {"line": 152, "column": 5}}, "44": {"start": {"line": 135, "column": 20}, "end": {"line": 135, "column": 56}}, "45": {"start": {"line": 136, "column": 23}, "end": {"line": 141, "column": 8}}, "46": {"start": {"line": 143, "column": 6}, "end": {"line": 148, "column": 7}}, "47": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 37}}, "48": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 50}}, "49": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 20}}, "50": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 55}}, "51": {"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 18}}, "52": {"start": {"line": 159, "column": 4}, "end": {"line": 165, "column": 7}}, "53": {"start": {"line": 160, "column": 6}, "end": {"line": 164, "column": 7}}, "54": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 23}}, "55": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 61}}, "56": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 13}}, "57": {"start": {"line": 170, "column": 13}, "end": {"line": 170, "column": 53}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 17}}, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 18}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 9}}, "loc": {"start": {"line": 32, "column": 9}, "end": {"line": 77, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 48, "column": 32}, "end": {"line": 48, "column": 35}}, "loc": {"start": {"line": 48, "column": 37}, "end": {"line": 51, "column": 7}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 53, "column": 35}, "end": {"line": 53, "column": 36}}, "loc": {"start": {"line": 53, "column": 45}, "end": {"line": 60, "column": 7}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 62, "column": 33}, "end": {"line": 62, "column": 34}}, "loc": {"start": {"line": 62, "column": 43}, "end": {"line": 72, "column": 7}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 22}}, "loc": {"start": {"line": 67, "column": 24}, "end": {"line": 71, "column": 9}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 12}}, "loc": {"start": {"line": 82, "column": 12}, "end": {"line": 88, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 11}}, "loc": {"start": {"line": 93, "column": 11}, "end": {"line": 96, "column": 3}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 95, "column": 15}, "end": {"line": 95, "column": 18}}, "loc": {"start": {"line": 95, "column": 21}, "end": {"line": 95, "column": 35}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 11}}, "loc": {"start": {"line": 101, "column": 50}, "end": {"line": 121, "column": 3}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 110, "column": 11}, "end": {"line": 110, "column": 14}}, "loc": {"start": {"line": 110, "column": 16}, "end": {"line": 120, "column": 5}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 126, "column": 2}, "end": {"line": 126, "column": 21}}, "loc": {"start": {"line": 126, "column": 21}, "end": {"line": 128, "column": 3}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 133, "column": 2}, "end": {"line": 133, "column": 7}}, "loc": {"start": {"line": 133, "column": 19}, "end": {"line": 153, "column": 3}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 158, "column": 10}, "end": {"line": 158, "column": 25}}, "loc": {"start": {"line": 158, "column": 44}, "end": {"line": 166, "column": 3}}}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 159, "column": 27}, "end": {"line": 159, "column": 35}}, "loc": {"start": {"line": 159, "column": 38}, "end": {"line": 165, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 35, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 35, "column": 5}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 39, "column": 6}, "end": {"line": 42, "column": 7}}, "type": "if", "locations": [{"start": {"line": 39, "column": 6}, "end": {"line": 42, "column": 7}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 68, "column": 10}, "end": {"line": 70, "column": 11}}, "type": "if", "locations": [{"start": {"line": 68, "column": 10}, "end": {"line": 70, "column": 11}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 83, "column": 4}, "end": {"line": 86, "column": 5}}, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 86, "column": 5}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 5}}, "type": "if", "locations": [{"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 5}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 112, "column": 6}, "end": {"line": 114, "column": 7}}, "type": "if", "locations": [{"start": {"line": 112, "column": 6}, "end": {"line": 114, "column": 7}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 117, "column": 6}, "end": {"line": 119, "column": 7}}, "type": "if", "locations": [{"start": {"line": 117, "column": 6}, "end": {"line": 119, "column": 7}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 143, "column": 6}, "end": {"line": 148, "column": 7}}, "type": "if", "locations": [{"start": {"line": 143, "column": 6}, "end": {"line": 148, "column": 7}}, {"start": {"line": 145, "column": 13}, "end": {"line": 148, "column": 7}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\__tests__\\api.clean.test.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\__tests__\\api.clean.test.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": 27}}, "2": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 38}}, "3": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 30}}, "4": {"start": {"line": 9, "column": 39}, "end": {"line": 9, "column": 57}}, "5": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 38}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 61}}, "7": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 28}}, "8": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 38}}, "9": {"start": {"line": 28, "column": 0}, "end": {"line": 118, "column": 3}}, "10": {"start": {"line": 30, "column": 29}, "end": {"line": 40, "column": 3}}, "11": {"start": {"line": 31, "column": 4}, "end": {"line": 39, "column": 6}}, "12": {"start": {"line": 42, "column": 2}, "end": {"line": 47, "column": 5}}, "13": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 25}}, "14": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 25}}, "15": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 27}}, "16": {"start": {"line": 49, "column": 2}, "end": {"line": 91, "column": 5}}, "17": {"start": {"line": 50, "column": 4}, "end": {"line": 70, "column": 7}}, "18": {"start": {"line": 52, "column": 23}, "end": {"line": 52, "column": 40}}, "19": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 73}}, "20": {"start": {"line": 56, "column": 21}, "end": {"line": 56, "column": 52}}, "21": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 49}}, "22": {"start": {"line": 60, "column": 6}, "end": {"line": 68, "column": 8}}, "23": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 39}}, "24": {"start": {"line": 72, "column": 4}, "end": {"line": 90, "column": 7}}, "25": {"start": {"line": 74, "column": 24}, "end": {"line": 74, "column": 40}}, "26": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 54}}, "27": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 67}}, "28": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 43}}, "29": {"start": {"line": 82, "column": 6}, "end": {"line": 89, "column": 8}}, "30": {"start": {"line": 93, "column": 2}, "end": {"line": 117, "column": 5}}, "31": {"start": {"line": 94, "column": 4}, "end": {"line": 105, "column": 7}}, "32": {"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": 38}}, "33": {"start": {"line": 97, "column": 6}, "end": {"line": 99, "column": 8}}, "34": {"start": {"line": 102, "column": 6}, "end": {"line": 104, "column": 31}}, "35": {"start": {"line": 107, "column": 4}, "end": {"line": 116, "column": 7}}, "36": {"start": {"line": 109, "column": 27}, "end": {"line": 109, "column": 42}}, "37": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 63}}, "38": {"start": {"line": 113, "column": 6}, "end": {"line": 115, "column": 31}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 21}}, "loc": {"start": {"line": 9, "column": 57}, "end": {"line": 9, "column": 61}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 6}}, "loc": {"start": {"line": 11, "column": 6}, "end": {"line": 13, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 6}}, "loc": {"start": {"line": 15, "column": 8}, "end": {"line": 17, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 6}}, "loc": {"start": {"line": 19, "column": 12}, "end": {"line": 21, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 6}}, "loc": {"start": {"line": 23, "column": 16}, "end": {"line": 25, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 27}}, "loc": {"start": {"line": 28, "column": 29}, "end": {"line": 118, "column": 1}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 30, "column": 29}, "end": {"line": 30, "column": 30}}, "loc": {"start": {"line": 30, "column": 88}, "end": {"line": 40, "column": 3}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 42, "column": 13}, "end": {"line": 42, "column": 16}}, "loc": {"start": {"line": 42, "column": 18}, "end": {"line": 47, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 49, "column": 27}, "end": {"line": 49, "column": 30}}, "loc": {"start": {"line": 49, "column": 32}, "end": {"line": 91, "column": 3}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 50, "column": 65}, "end": {"line": 50, "column": 70}}, "loc": {"start": {"line": 50, "column": 76}, "end": {"line": 70, "column": 5}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 72, "column": 80}, "end": {"line": 72, "column": 85}}, "loc": {"start": {"line": 72, "column": 91}, "end": {"line": 90, "column": 5}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 93, "column": 29}, "end": {"line": 93, "column": 32}}, "loc": {"start": {"line": 93, "column": 34}, "end": {"line": 117, "column": 3}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 94, "column": 53}, "end": {"line": 94, "column": 58}}, "loc": {"start": {"line": 94, "column": 64}, "end": {"line": 105, "column": 5}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 107, "column": 39}, "end": {"line": 107, "column": 44}}, "loc": {"start": {"line": 107, "column": 50}, "end": {"line": 116, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 11}, "end": {"line": 16, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 11}, "end": {"line": 16, "column": 34}}, {"start": {"line": 16, "column": 38}, "end": {"line": 16, "column": 60}}]}, "1": {"loc": {"start": {"line": 24, "column": 11}, "end": {"line": 24, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 11}, "end": {"line": 24, "column": 31}}, {"start": {"line": 24, "column": 35}, "end": {"line": 24, "column": 37}}]}, "2": {"loc": {"start": {"line": 30, "column": 61}, "end": {"line": 30, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 30, "column": 82}, "end": {"line": 30, "column": 84}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\__tests__\\api.test.new.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\__tests__\\api.test.new.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 25}}, "2": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 27}}, "3": {"start": {"line": 8, "column": 18}, "end": {"line": 8, "column": 27}}, "4": {"start": {"line": 9, "column": 1}, "end": {"line": 9, "column": 34}}, "5": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 30}}, "6": {"start": {"line": 13, "column": 39}, "end": {"line": 13, "column": 57}}, "7": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 38}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 61}}, "9": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 28}}, "10": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 38}}, "11": {"start": {"line": 32, "column": 0}, "end": {"line": 156, "column": 3}}, "12": {"start": {"line": 34, "column": 29}, "end": {"line": 44, "column": 3}}, "13": {"start": {"line": 35, "column": 4}, "end": {"line": 43, "column": 6}}, "14": {"start": {"line": 46, "column": 2}, "end": {"line": 56, "column": 5}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 25}}, "16": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 25}}, "17": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 27}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": 6}}, "19": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 48}}, "20": {"start": {"line": 58, "column": 2}, "end": {"line": 60, "column": 5}}, "21": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 27}}, "22": {"start": {"line": 62, "column": 2}, "end": {"line": 104, "column": 5}}, "23": {"start": {"line": 63, "column": 4}, "end": {"line": 83, "column": 7}}, "24": {"start": {"line": 65, "column": 23}, "end": {"line": 65, "column": 39}}, "25": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 73}}, "26": {"start": {"line": 69, "column": 21}, "end": {"line": 69, "column": 52}}, "27": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 49}}, "28": {"start": {"line": 73, "column": 6}, "end": {"line": 81, "column": 8}}, "29": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 39}}, "30": {"start": {"line": 85, "column": 4}, "end": {"line": 103, "column": 7}}, "31": {"start": {"line": 87, "column": 24}, "end": {"line": 87, "column": 40}}, "32": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 54}}, "33": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 67}}, "34": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 43}}, "35": {"start": {"line": 95, "column": 6}, "end": {"line": 102, "column": 8}}, "36": {"start": {"line": 106, "column": 2}, "end": {"line": 130, "column": 5}}, "37": {"start": {"line": 107, "column": 4}, "end": {"line": 118, "column": 7}}, "38": {"start": {"line": 109, "column": 27}, "end": {"line": 109, "column": 38}}, "39": {"start": {"line": 110, "column": 6}, "end": {"line": 112, "column": 8}}, "40": {"start": {"line": 115, "column": 6}, "end": {"line": 117, "column": 31}}, "41": {"start": {"line": 120, "column": 4}, "end": {"line": 129, "column": 7}}, "42": {"start": {"line": 122, "column": 27}, "end": {"line": 122, "column": 42}}, "43": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 63}}, "44": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 31}}, "45": {"start": {"line": 132, "column": 2}, "end": {"line": 155, "column": 5}}, "46": {"start": {"line": 133, "column": 4}, "end": {"line": 154, "column": 7}}, "47": {"start": {"line": 135, "column": 26}, "end": {"line": 135, "column": 42}}, "48": {"start": {"line": 136, "column": 27}, "end": {"line": 136, "column": 52}}, "49": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 88}}, "50": {"start": {"line": 140, "column": 21}, "end": {"line": 140, "column": 57}}, "51": {"start": {"line": 143, "column": 6}, "end": {"line": 152, "column": 8}}, "52": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 43}}, "53": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 45}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 21}}, "loc": {"start": {"line": 13, "column": 57}, "end": {"line": 13, "column": 61}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 6}}, "loc": {"start": {"line": 15, "column": 6}, "end": {"line": 17, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 6}}, "loc": {"start": {"line": 19, "column": 8}, "end": {"line": 21, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 6}}, "loc": {"start": {"line": 23, "column": 12}, "end": {"line": 25, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 6}}, "loc": {"start": {"line": 27, "column": 16}, "end": {"line": 29, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 32, "column": 30}, "end": {"line": 32, "column": 33}}, "loc": {"start": {"line": 32, "column": 35}, "end": {"line": 156, "column": 1}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 34, "column": 29}, "end": {"line": 34, "column": 30}}, "loc": {"start": {"line": 34, "column": 88}, "end": {"line": 44, "column": 3}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 46, "column": 13}, "end": {"line": 46, "column": 16}}, "loc": {"start": {"line": 46, "column": 18}, "end": {"line": 56, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 53, "column": 33}, "end": {"line": 53, "column": 36}}, "loc": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 48}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": 15}}, "loc": {"start": {"line": 58, "column": 17}, "end": {"line": 60, "column": 3}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 62, "column": 27}, "end": {"line": 62, "column": 30}}, "loc": {"start": {"line": 62, "column": 32}, "end": {"line": 104, "column": 3}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 63, "column": 65}, "end": {"line": 63, "column": 70}}, "loc": {"start": {"line": 63, "column": 76}, "end": {"line": 83, "column": 5}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 85, "column": 80}, "end": {"line": 85, "column": 85}}, "loc": {"start": {"line": 85, "column": 91}, "end": {"line": 103, "column": 5}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 106, "column": 29}, "end": {"line": 106, "column": 32}}, "loc": {"start": {"line": 106, "column": 34}, "end": {"line": 130, "column": 3}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 107, "column": 53}, "end": {"line": 107, "column": 58}}, "loc": {"start": {"line": 107, "column": 64}, "end": {"line": 118, "column": 5}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 120, "column": 39}, "end": {"line": 120, "column": 44}}, "loc": {"start": {"line": 120, "column": 50}, "end": {"line": 129, "column": 5}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 132, "column": 28}, "end": {"line": 132, "column": 31}}, "loc": {"start": {"line": 132, "column": 33}, "end": {"line": 155, "column": 3}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 133, "column": 67}, "end": {"line": 133, "column": 72}}, "loc": {"start": {"line": 133, "column": 78}, "end": {"line": 154, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 11}, "end": {"line": 20, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 11}, "end": {"line": 20, "column": 34}}, {"start": {"line": 20, "column": 38}, "end": {"line": 20, "column": 60}}]}, "1": {"loc": {"start": {"line": 28, "column": 11}, "end": {"line": 28, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 11}, "end": {"line": 28, "column": 31}}, {"start": {"line": 28, "column": 35}, "end": {"line": 28, "column": 37}}]}, "2": {"loc": {"start": {"line": 34, "column": 61}, "end": {"line": 34, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 34, "column": 82}, "end": {"line": 34, "column": 84}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\__tests__\\api.test.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\DeepTrade\\DeepTrade2 - Copy\\frontend\\src\\services\\__tests__\\api.test.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 25}}, "2": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": 30}}, "3": {"start": {"line": 8, "column": 39}, "end": {"line": 8, "column": 57}}, "4": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 38}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 63}}, "6": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 29}}, "7": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 38}}, "8": {"start": {"line": 27, "column": 0}, "end": {"line": 205, "column": 3}}, "9": {"start": {"line": 29, "column": 29}, "end": {"line": 39, "column": 3}}, "10": {"start": {"line": 30, "column": 4}, "end": {"line": 38, "column": 6}}, "11": {"start": {"line": 41, "column": 2}, "end": {"line": 46, "column": 5}}, "12": {"start": {"line": 43, "column": 5}, "end": {"line": 43, "column": 44}}, "13": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 25}}, "14": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 27}}, "15": {"start": {"line": 48, "column": 2}, "end": {"line": 50, "column": 5}}, "16": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 25}}, "17": {"start": {"line": 52, "column": 2}, "end": {"line": 115, "column": 5}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 72, "column": 7}}, "19": {"start": {"line": 54, "column": 23}, "end": {"line": 54, "column": 40}}, "20": {"start": {"line": 55, "column": 7}, "end": {"line": 57, "column": 8}}, "21": {"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 41}}, "22": {"start": {"line": 59, "column": 21}, "end": {"line": 59, "column": 52}}, "23": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 52}}, "24": {"start": {"line": 62, "column": 6}, "end": {"line": 70, "column": 8}}, "25": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 39}}, "26": {"start": {"line": 74, "column": 4}, "end": {"line": 93, "column": 7}}, "27": {"start": {"line": 75, "column": 23}, "end": {"line": 75, "column": 40}}, "28": {"start": {"line": 76, "column": 24}, "end": {"line": 76, "column": 40}}, "29": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 54}}, "30": {"start": {"line": 79, "column": 7}, "end": {"line": 81, "column": 8}}, "31": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 41}}, "32": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 43}}, "33": {"start": {"line": 85, "column": 6}, "end": {"line": 92, "column": 8}}, "34": {"start": {"line": 95, "column": 4}, "end": {"line": 114, "column": 7}}, "35": {"start": {"line": 96, "column": 23}, "end": {"line": 96, "column": 40}}, "36": {"start": {"line": 97, "column": 24}, "end": {"line": 97, "column": 48}}, "37": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": 56}}, "38": {"start": {"line": 100, "column": 7}, "end": {"line": 102, "column": 8}}, "39": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 41}}, "40": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 43}}, "41": {"start": {"line": 106, "column": 6}, "end": {"line": 113, "column": 8}}, "42": {"start": {"line": 117, "column": 2}, "end": {"line": 135, "column": 5}}, "43": {"start": {"line": 118, "column": 4}, "end": {"line": 125, "column": 7}}, "44": {"start": {"line": 119, "column": 27}, "end": {"line": 119, "column": 38}}, "45": {"start": {"line": 120, "column": 7}, "end": {"line": 122, "column": 8}}, "46": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 69}}, "47": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 83}}, "48": {"start": {"line": 127, "column": 4}, "end": {"line": 134, "column": 7}}, "49": {"start": {"line": 128, "column": 27}, "end": {"line": 128, "column": 42}}, "50": {"start": {"line": 129, "column": 7}, "end": {"line": 131, "column": 8}}, "51": {"start": {"line": 130, "column": 8}, "end": {"line": 130, "column": 47}}, "52": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 67}}, "53": {"start": {"line": 137, "column": 2}, "end": {"line": 172, "column": 5}}, "54": {"start": {"line": 138, "column": 4}, "end": {"line": 154, "column": 7}}, "55": {"start": {"line": 139, "column": 26}, "end": {"line": 139, "column": 50}}, "56": {"start": {"line": 140, "column": 27}, "end": {"line": 140, "column": 52}}, "57": {"start": {"line": 141, "column": 7}, "end": {"line": 143, "column": 8}}, "58": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 45}}, "59": {"start": {"line": 145, "column": 23}, "end": {"line": 145, "column": 60}}, "60": {"start": {"line": 146, "column": 6}, "end": {"line": 152, "column": 8}}, "61": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 50}}, "62": {"start": {"line": 156, "column": 4}, "end": {"line": 171, "column": 7}}, "63": {"start": {"line": 157, "column": 27}, "end": {"line": 157, "column": 44}}, "64": {"start": {"line": 158, "column": 7}, "end": {"line": 160, "column": 8}}, "65": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 45}}, "66": {"start": {"line": 162, "column": 23}, "end": {"line": 162, "column": 47}}, "67": {"start": {"line": 163, "column": 6}, "end": {"line": 169, "column": 8}}, "68": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 50}}, "69": {"start": {"line": 174, "column": 2}, "end": {"line": 191, "column": 5}}, "70": {"start": {"line": 175, "column": 4}, "end": {"line": 190, "column": 7}}, "71": {"start": {"line": 176, "column": 27}, "end": {"line": 176, "column": 44}}, "72": {"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 8}}, "73": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 45}}, "74": {"start": {"line": 182, "column": 23}, "end": {"line": 182, "column": 50}}, "75": {"start": {"line": 183, "column": 6}, "end": {"line": 188, "column": 8}}, "76": {"start": {"line": 189, "column": 6}, "end": {"line": 189, "column": 50}}, "77": {"start": {"line": 193, "column": 2}, "end": {"line": 204, "column": 5}}, "78": {"start": {"line": 194, "column": 4}, "end": {"line": 203, "column": 7}}, "79": {"start": {"line": 196, "column": 6}, "end": {"line": 198, "column": 8}}, "80": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 62}}, "81": {"start": {"line": 200, "column": 6}, "end": {"line": 202, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 21}}, "loc": {"start": {"line": 8, "column": 57}, "end": {"line": 8, "column": 61}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 6}}, "loc": {"start": {"line": 10, "column": 6}, "end": {"line": 12, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 6}}, "loc": {"start": {"line": 14, "column": 8}, "end": {"line": 16, "column": 3}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 6}}, "loc": {"start": {"line": 18, "column": 12}, "end": {"line": 20, "column": 3}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 6}}, "loc": {"start": {"line": 22, "column": 16}, "end": {"line": 24, "column": 3}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 27, "column": 24}, "end": {"line": 27, "column": 27}}, "loc": {"start": {"line": 27, "column": 29}, "end": {"line": 205, "column": 1}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 29, "column": 29}, "end": {"line": 29, "column": 30}}, "loc": {"start": {"line": 29, "column": 88}, "end": {"line": 39, "column": 3}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 41, "column": 13}, "end": {"line": 41, "column": 16}}, "loc": {"start": {"line": 41, "column": 18}, "end": {"line": 46, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 48, "column": 12}, "end": {"line": 48, "column": 15}}, "loc": {"start": {"line": 48, "column": 17}, "end": {"line": 50, "column": 3}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 52, "column": 27}, "end": {"line": 52, "column": 30}}, "loc": {"start": {"line": 52, "column": 32}, "end": {"line": 115, "column": 3}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 53, "column": 69}, "end": {"line": 53, "column": 74}}, "loc": {"start": {"line": 53, "column": 80}, "end": {"line": 72, "column": 5}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 55, "column": 57}, "end": {"line": 55, "column": 60}}, "loc": {"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 41}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 74, "column": 80}, "end": {"line": 74, "column": 85}}, "loc": {"start": {"line": 74, "column": 91}, "end": {"line": 93, "column": 5}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 79, "column": 57}, "end": {"line": 79, "column": 60}}, "loc": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 41}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 95, "column": 82}, "end": {"line": 95, "column": 87}}, "loc": {"start": {"line": 95, "column": 93}, "end": {"line": 114, "column": 5}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 100, "column": 57}, "end": {"line": 100, "column": 60}}, "loc": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 41}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 117, "column": 29}, "end": {"line": 117, "column": 32}}, "loc": {"start": {"line": 117, "column": 34}, "end": {"line": 135, "column": 3}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 118, "column": 53}, "end": {"line": 118, "column": 58}}, "loc": {"start": {"line": 118, "column": 64}, "end": {"line": 125, "column": 5}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 120, "column": 57}, "end": {"line": 120, "column": 60}}, "loc": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 69}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 127, "column": 39}, "end": {"line": 127, "column": 44}}, "loc": {"start": {"line": 127, "column": 50}, "end": {"line": 134, "column": 5}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 129, "column": 57}, "end": {"line": 129, "column": 60}}, "loc": {"start": {"line": 130, "column": 8}, "end": {"line": 130, "column": 47}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 137, "column": 18}, "end": {"line": 137, "column": 21}}, "loc": {"start": {"line": 137, "column": 23}, "end": {"line": 172, "column": 3}}}, "22": {"name": "(anonymous_23)", "decl": {"start": {"line": 138, "column": 66}, "end": {"line": 138, "column": 71}}, "loc": {"start": {"line": 138, "column": 77}, "end": {"line": 154, "column": 5}}}, "23": {"name": "(anonymous_24)", "decl": {"start": {"line": 141, "column": 57}, "end": {"line": 141, "column": 60}}, "loc": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 45}}}, "24": {"name": "(anonymous_25)", "decl": {"start": {"line": 156, "column": 43}, "end": {"line": 156, "column": 48}}, "loc": {"start": {"line": 156, "column": 54}, "end": {"line": 171, "column": 5}}}, "25": {"name": "(anonymous_26)", "decl": {"start": {"line": 158, "column": 57}, "end": {"line": 158, "column": 60}}, "loc": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 45}}}, "26": {"name": "(anonymous_27)", "decl": {"start": {"line": 174, "column": 21}, "end": {"line": 174, "column": 24}}, "loc": {"start": {"line": 174, "column": 26}, "end": {"line": 191, "column": 3}}}, "27": {"name": "(anonymous_28)", "decl": {"start": {"line": 175, "column": 59}, "end": {"line": 175, "column": 64}}, "loc": {"start": {"line": 175, "column": 70}, "end": {"line": 190, "column": 5}}}, "28": {"name": "(anonymous_29)", "decl": {"start": {"line": 178, "column": 42}, "end": {"line": 178, "column": 45}}, "loc": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 45}}}, "29": {"name": "(anonymous_30)", "decl": {"start": {"line": 193, "column": 29}, "end": {"line": 193, "column": 32}}, "loc": {"start": {"line": 193, "column": 34}, "end": {"line": 204, "column": 3}}}, "30": {"name": "(anonymous_31)", "decl": {"start": {"line": 194, "column": 53}, "end": {"line": 194, "column": 58}}, "loc": {"start": {"line": 194, "column": 64}, "end": {"line": 203, "column": 5}}}, "31": {"name": "(anonymous_32)", "decl": {"start": {"line": 196, "column": 42}, "end": {"line": 196, "column": 45}}, "loc": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 62}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 11}, "end": {"line": 15, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 11}, "end": {"line": 15, "column": 35}}, {"start": {"line": 15, "column": 39}, "end": {"line": 15, "column": 62}}]}, "1": {"loc": {"start": {"line": 23, "column": 11}, "end": {"line": 23, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 23, "column": 11}, "end": {"line": 23, "column": 31}}, {"start": {"line": 23, "column": 35}, "end": {"line": 23, "column": 37}}]}, "2": {"loc": {"start": {"line": 29, "column": 61}, "end": {"line": 29, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 29, "column": 82}, "end": {"line": 29, "column": 84}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0]}}}