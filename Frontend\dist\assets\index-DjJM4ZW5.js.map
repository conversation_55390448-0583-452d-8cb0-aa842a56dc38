{"version": 3, "file": "index-DjJM4ZW5.js", "sources": ["../../node_modules/@solflare-wallet/sdk/lib/esm/adapters/base.js", "../../node_modules/@solflare-wallet/sdk/lib/esm/adapters/WalletProvider.js", "../../node_modules/@solflare-wallet/sdk/lib/esm/adapters/web.js", "../../node_modules/@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/rng.js", "../../node_modules/@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/stringify.js", "../../node_modules/@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/native.js", "../../node_modules/@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/v4.js", "../../node_modules/@solflare-wallet/sdk/lib/esm/adapters/iframe.js", "../../node_modules/@solflare-wallet/sdk/lib/esm/utils.js", "../../node_modules/@solflare-wallet/sdk/lib/esm/version.js", "../../node_modules/@solflare-wallet/sdk/lib/esm/index.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport EventEmitter from 'eventemitter3';\nvar WalletAdapter = /** @class */ (function (_super) {\n    __extends(WalletAdapter, _super);\n    function WalletAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return WalletAdapter;\n}(EventEmitter));\nexport default WalletAdapter;\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport EventEmitter from 'eventemitter3';\nimport { PublicKey } from '@solana/web3.js';\nimport bs58 from 'bs58';\nvar Wallet = /** @class */ (function (_super) {\n    __extends(Wallet, _super);\n    function Wallet(provider, network) {\n        var _this = _super.call(this) || this;\n        _this._handleMessage = function (e) {\n            if ((_this._injectedProvider && e.source === window) ||\n                (e.origin === _this._providerUrl.origin && e.source === _this._popup)) {\n                if (e.data.method === 'connected') {\n                    var newPublicKey = new PublicKey(e.data.params.publicKey);\n                    if (!_this._publicKey || !_this._publicKey.equals(newPublicKey)) {\n                        if (_this._publicKey && !_this._publicKey.equals(newPublicKey)) {\n                            _this._handleDisconnect();\n                        }\n                        _this._publicKey = newPublicKey;\n                        _this._autoApprove = !!e.data.params.autoApprove;\n                        _this.emit('connect', _this._publicKey);\n                    }\n                }\n                else if (e.data.method === 'disconnected') {\n                    _this._handleDisconnect();\n                }\n                else if (e.data.result || e.data.error) {\n                    if (_this._responsePromises.has(e.data.id)) {\n                        var _a = __read(_this._responsePromises.get(e.data.id), 2), resolve = _a[0], reject = _a[1];\n                        if (e.data.result) {\n                            resolve(e.data.result);\n                        }\n                        else {\n                            reject(new Error(e.data.error));\n                        }\n                    }\n                }\n            }\n        };\n        _this._handleConnect = function () {\n            if (!_this._handlerAdded) {\n                _this._handlerAdded = true;\n                window.addEventListener('message', _this._handleMessage);\n                window.addEventListener('beforeunload', _this.disconnect);\n            }\n            if (_this._injectedProvider) {\n                return new Promise(function (resolve) {\n                    _this._sendRequest('connect', {});\n                    resolve();\n                });\n            }\n            else {\n                window.name = 'parent';\n                _this._popup = window.open(_this._providerUrl.toString(), '_blank', 'location,resizable,width=460,height=675');\n                return new Promise(function (resolve) {\n                    _this.once('connect', resolve);\n                });\n            }\n        };\n        _this._handleDisconnect = function () {\n            if (_this._handlerAdded) {\n                _this._handlerAdded = false;\n                window.removeEventListener('message', _this._handleMessage);\n                window.removeEventListener('beforeunload', _this.disconnect);\n            }\n            if (_this._publicKey) {\n                _this._publicKey = null;\n                _this.emit('disconnect');\n            }\n            _this._responsePromises.forEach(function (_a, id) {\n                var _b = __read(_a, 2), resolve = _b[0], reject = _b[1];\n                _this._responsePromises.delete(id);\n                reject('Wallet disconnected');\n            });\n        };\n        _this._sendRequest = function (method, params) { return __awaiter(_this, void 0, void 0, function () {\n            var requestId;\n            var _this = this;\n            return __generator(this, function (_a) {\n                if (method !== 'connect' && !this.connected) {\n                    throw new Error('Wallet not connected');\n                }\n                requestId = this._nextRequestId;\n                ++this._nextRequestId;\n                return [2 /*return*/, new Promise(function (resolve, reject) {\n                        _this._responsePromises.set(requestId, [resolve, reject]);\n                        if (_this._injectedProvider) {\n                            _this._injectedProvider.postMessage({\n                                jsonrpc: '2.0',\n                                id: requestId,\n                                method: method,\n                                params: __assign({ network: _this._network }, params),\n                            });\n                        }\n                        else {\n                            _this._popup.postMessage({\n                                jsonrpc: '2.0',\n                                id: requestId,\n                                method: method,\n                                params: params,\n                            }, _this._providerUrl.origin);\n                            if (!_this.autoApprove) {\n                                _this._popup.focus();\n                            }\n                        }\n                    })];\n            });\n        }); };\n        _this.connect = function () {\n            if (_this._popup) {\n                _this._popup.close();\n            }\n            return _this._handleConnect();\n        };\n        _this.disconnect = function () { return __awaiter(_this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._injectedProvider) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this._sendRequest('disconnect', {})];\n                    case 1:\n                        _a.sent();\n                        _a.label = 2;\n                    case 2:\n                        if (this._popup) {\n                            this._popup.close();\n                        }\n                        this._handleDisconnect();\n                        return [2 /*return*/];\n                }\n            });\n        }); };\n        _this.sign = function (data, display) { return __awaiter(_this, void 0, void 0, function () {\n            var response, signature, publicKey;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!(data instanceof Uint8Array)) {\n                            throw new Error('Data must be an instance of Uint8Array');\n                        }\n                        return [4 /*yield*/, this._sendRequest('sign', {\n                                data: data,\n                                display: display,\n                            })];\n                    case 1:\n                        response = _a.sent();\n                        signature = bs58.decode(response.signature);\n                        publicKey = new PublicKey(response.publicKey);\n                        return [2 /*return*/, {\n                                signature: signature,\n                                publicKey: publicKey,\n                            }];\n                }\n            });\n        }); };\n        if (isInjectedProvider(provider)) {\n            _this._injectedProvider = provider;\n        }\n        else if (isString(provider)) {\n            _this._providerUrl = new URL(provider);\n            _this._providerUrl.hash = new URLSearchParams({\n                origin: window.location.origin,\n                network: network,\n            }).toString();\n        }\n        else {\n            throw new Error('provider parameter must be an injected provider or a URL string.');\n        }\n        _this._network = network;\n        _this._publicKey = null;\n        _this._autoApprove = false;\n        _this._popup = null;\n        _this._handlerAdded = false;\n        _this._nextRequestId = 1;\n        _this._responsePromises = new Map();\n        return _this;\n    }\n    Object.defineProperty(Wallet.prototype, \"publicKey\", {\n        get: function () {\n            return this._publicKey;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Wallet.prototype, \"connected\", {\n        get: function () {\n            return this._publicKey !== null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Wallet.prototype, \"autoApprove\", {\n        get: function () {\n            return this._autoApprove;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Wallet;\n}(EventEmitter));\nexport default Wallet;\nfunction isString(a) {\n    return typeof a === 'string';\n}\nfunction isInjectedProvider(a) {\n    return isObject(a) && isFunction(a.postMessage);\n}\nfunction isObject(a) {\n    return typeof a === 'object' && a !== null;\n}\nfunction isFunction(a) {\n    return typeof a === 'function';\n}\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport WalletAdapter from './base';\nimport Wallet from './WalletProvider';\nimport bs58 from 'bs58';\nvar WebAdapter = /** @class */ (function (_super) {\n    __extends(WebAdapter, _super);\n    // @ts-ignore\n    function WebAdapter(iframe, network, provider) {\n        var _this = _super.call(this) || this;\n        _this._instance = null;\n        // @ts-ignore\n        _this.handleMessage = function (data) {\n            // nothing to do here\n        };\n        _this._sendRequest = function (method, params) { return __awaiter(_this, void 0, void 0, function () {\n            var _a, _b;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        if (!((_a = this._instance) === null || _a === void 0 ? void 0 : _a.sendRequest)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this._instance.sendRequest(method, params)];\n                    case 1: return [2 /*return*/, _c.sent()];\n                    case 2:\n                        if (!((_b = this._instance) === null || _b === void 0 ? void 0 : _b._sendRequest)) return [3 /*break*/, 4];\n                        return [4 /*yield*/, this._instance._sendRequest(method, params)];\n                    case 3: return [2 /*return*/, _c.sent()];\n                    case 4: throw new Error('Unsupported version of `@project-serum/sol-wallet-adapter`');\n                }\n            });\n        }); };\n        _this._handleConnect = function () {\n            _this.emit('connect');\n        };\n        _this._handleDisconnect = function () {\n            window.clearInterval(_this._pollTimer);\n            _this.emit('disconnect');\n        };\n        _this._network = network;\n        _this._provider = provider;\n        return _this;\n    }\n    Object.defineProperty(WebAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._instance.publicKey || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(WebAdapter.prototype, \"connected\", {\n        get: function () {\n            return this._instance.connected || false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WebAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this._instance = new Wallet(this._provider, this._network);\n                        this._instance.on('connect', this._handleConnect);\n                        this._instance.on('disconnect', this._handleDisconnect);\n                        this._pollTimer = window.setInterval(function () {\n                            var _a, _b;\n                            // @ts-ignore\n                            if (((_b = (_a = _this._instance) === null || _a === void 0 ? void 0 : _a._popup) === null || _b === void 0 ? void 0 : _b.closed) !== false) {\n                                _this._handleDisconnect();\n                            }\n                        }, 200);\n                        return [4 /*yield*/, this._instance.connect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        this._instance.removeAllListeners('connect');\n                        this._instance.removeAllListeners('disconnect');\n                        return [4 /*yield*/, this._instance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signTransactionV2', {\n                                transaction: bs58.encode(transaction)\n                            })];\n                    case 1:\n                        signedTransaction = (_a.sent()).transaction;\n                        return [2 /*return*/, bs58.decode(signedTransaction)];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransactions;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signAllTransactionsV2', {\n                                transactions: transactions.map(function (transaction) { return bs58.encode(transaction); })\n                            })];\n                    case 1:\n                        signedTransactions = (_a.sent()).transactions;\n                        return [2 /*return*/, signedTransactions.map(function (transaction) { return bs58.decode(transaction); })];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signAndSendTransaction = function (transaction, options) {\n        return __awaiter(this, void 0, void 0, function () {\n            var response;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signAndSendTransaction', {\n                                transaction: bs58.encode(transaction),\n                                options: options\n                            })];\n                    case 1:\n                        response = (_a.sent());\n                        return [2 /*return*/, response.signature];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'hex'; }\n        return __awaiter(this, void 0, void 0, function () {\n            var signature;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._instance.sign(data, display)];\n                    case 1:\n                        signature = (_a.sent()).signature;\n                        return [2 /*return*/, Uint8Array.from(signature)];\n                }\n            });\n        });\n    };\n    return WebAdapter;\n}(WalletAdapter));\nexport default WebAdapter;\n", "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { PublicKey } from '@solana/web3.js';\nimport WalletAdapter from './base';\nimport { v4 as uuidv4 } from 'uuid';\nimport bs58 from 'bs58';\nvar IframeAdapter = /** @class */ (function (_super) {\n    __extends(IframeAdapter, _super);\n    function IframeAdapter(iframe, publicKey) {\n        var _this = this;\n        var _a;\n        _this = _super.call(this) || this;\n        _this._publicKey = null;\n        _this._messageHandlers = {};\n        _this.handleMessage = function (data) {\n            if (_this._messageHandlers[data.id]) {\n                var _a = _this._messageHandlers[data.id], resolve = _a.resolve, reject = _a.reject;\n                delete _this._messageHandlers[data.id];\n                if (data.error) {\n                    reject(data.error);\n                }\n                else {\n                    resolve(data.result);\n                }\n            }\n        };\n        _this._sendMessage = function (data) {\n            if (!_this.connected) {\n                throw new Error('Wallet not connected');\n            }\n            return new Promise(function (resolve, reject) {\n                var _a, _b;\n                var messageId = uuidv4();\n                _this._messageHandlers[messageId] = { resolve: resolve, reject: reject };\n                (_b = (_a = _this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                    channel: 'solflareWalletAdapterToIframe',\n                    data: __assign({ id: messageId }, data)\n                }, '*');\n            });\n        };\n        _this._iframe = iframe;\n        _this._publicKey = new PublicKey((_a = publicKey === null || publicKey === void 0 ? void 0 : publicKey.toString) === null || _a === void 0 ? void 0 : _a.call(publicKey));\n        return _this;\n    }\n    Object.defineProperty(IframeAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._publicKey || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(IframeAdapter.prototype, \"connected\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IframeAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [2 /*return*/];\n            });\n        });\n    };\n    IframeAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this._sendMessage({\n                            method: 'disconnect'\n                        })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signTransaction = function (transaction) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransaction, e_1;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signTransaction',\n                                params: {\n                                    transaction: bs58.encode(transaction)\n                                }\n                            })];\n                    case 2:\n                        signedTransaction = _b.sent();\n                        return [2 /*return*/, bs58.decode(signedTransaction)];\n                    case 3:\n                        e_1 = _b.sent();\n                        throw new Error(((_a = e_1 === null || e_1 === void 0 ? void 0 : e_1.toString) === null || _a === void 0 ? void 0 : _a.call(e_1)) || 'Failed to sign transaction');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signAllTransactions = function (transactions) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransactions, e_2;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signAllTransactions',\n                                params: {\n                                    transactions: transactions.map(function (transaction) { return bs58.encode(transaction); })\n                                }\n                            })];\n                    case 2:\n                        signedTransactions = _b.sent();\n                        return [2 /*return*/, signedTransactions.map(function (transaction) { return bs58.decode(transaction); })];\n                    case 3:\n                        e_2 = _b.sent();\n                        throw new Error(((_a = e_2 === null || e_2 === void 0 ? void 0 : e_2.toString) === null || _a === void 0 ? void 0 : _a.call(e_2)) || 'Failed to sign transactions');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signAndSendTransaction = function (transaction, options) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var result, e_3;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signAndSendTransaction',\n                                params: {\n                                    transaction: bs58.encode(transaction),\n                                    options: options\n                                }\n                            })];\n                    case 2:\n                        result = _b.sent();\n                        return [2 /*return*/, result];\n                    case 3:\n                        e_3 = _b.sent();\n                        throw new Error(((_a = e_3 === null || e_3 === void 0 ? void 0 : e_3.toString) === null || _a === void 0 ? void 0 : _a.call(e_3)) || 'Failed to sign and send transaction');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signMessage = function (data, display) {\n        var _a;\n        if (display === void 0) { display = 'hex'; }\n        return __awaiter(this, void 0, void 0, function () {\n            var result, e_4;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signMessage',\n                                params: {\n                                    data: data,\n                                    display: display\n                                }\n                            })];\n                    case 2:\n                        result = _b.sent();\n                        return [2 /*return*/, Uint8Array.from(bs58.decode(result))];\n                    case 3:\n                        e_4 = _b.sent();\n                        throw new Error(((_a = e_4 === null || e_4 === void 0 ? void 0 : e_4.toString) === null || _a === void 0 ? void 0 : _a.call(e_4)) || 'Failed to sign message');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    return IframeAdapter;\n}(WalletAdapter));\nexport default IframeAdapter;\n", "export function isLegacyTransactionInstance(transaction) {\n    return transaction.version === undefined;\n}\n", "export var VERSION = \"1.4.2\";\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport { Transaction, VersionedTransaction } from '@solana/web3.js';\nimport EventEmitter from 'eventemitter3';\nimport WebAdapter from './adapters/web';\nimport IframeAdapter from './adapters/iframe';\nimport { isLegacyTransactionInstance } from './utils';\nimport { VERSION } from './version';\nvar Solflare = /** @class */ (function (_super) {\n    __extends(Solflare, _super);\n    function Solflare(config) {\n        var _this = _super.call(this) || this;\n        _this._network = 'mainnet-beta';\n        _this._provider = null;\n        _this._iframeParams = {};\n        _this._adapterInstance = null;\n        _this._element = null;\n        _this._iframe = null;\n        _this._connectHandler = null;\n        _this._flutterHandlerInterval = null;\n        _this._handleEvent = function (event) {\n            var _a, _b, _c, _d;\n            switch (event.type) {\n                case 'connect_native_web': {\n                    _this._collapseIframe();\n                    _this._adapterInstance = new WebAdapter(_this._iframe, _this._network, ((_a = event.data) === null || _a === void 0 ? void 0 : _a.provider) || _this._provider || 'https://solflare.com/provider');\n                    _this._adapterInstance.on('connect', _this._webConnected);\n                    _this._adapterInstance.on('disconnect', _this._webDisconnected);\n                    _this._adapterInstance.connect();\n                    _this._setPreferredAdapter('native_web');\n                    return;\n                }\n                case 'connect': {\n                    _this._collapseIframe();\n                    _this._adapterInstance = new IframeAdapter(_this._iframe, ((_b = event.data) === null || _b === void 0 ? void 0 : _b.publicKey) || '');\n                    _this._adapterInstance.connect();\n                    _this._setPreferredAdapter((_c = event.data) === null || _c === void 0 ? void 0 : _c.adapter);\n                    if (_this._connectHandler) {\n                        _this._connectHandler.resolve();\n                        _this._connectHandler = null;\n                    }\n                    _this.emit('connect', _this.publicKey);\n                    return;\n                }\n                case 'disconnect': {\n                    if (_this._connectHandler) {\n                        _this._connectHandler.reject();\n                        _this._connectHandler = null;\n                    }\n                    _this._disconnected();\n                    _this.emit('disconnect');\n                    return;\n                }\n                case 'accountChanged': {\n                    if ((_d = event.data) === null || _d === void 0 ? void 0 : _d.publicKey) {\n                        _this._adapterInstance = new IframeAdapter(_this._iframe, event.data.publicKey);\n                        _this._adapterInstance.connect();\n                        _this.emit('accountChanged', _this.publicKey);\n                    }\n                    else {\n                        _this.emit('accountChanged', undefined);\n                    }\n                    return;\n                }\n                // legacy event, use resize message type instead\n                case 'collapse': {\n                    _this._collapseIframe();\n                    return;\n                }\n                default: {\n                    return;\n                }\n            }\n        };\n        _this._handleResize = function (data) {\n            if (data.resizeMode === 'full') {\n                if (data.params.mode === 'fullscreen') {\n                    _this._expandIframe();\n                }\n                else if (data.params.mode === 'hide') {\n                    _this._collapseIframe();\n                }\n            }\n            else if (data.resizeMode === 'coordinates') {\n                if (_this._iframe) {\n                    _this._iframe.style.top = isFinite(data.params.top) ? \"\".concat(data.params.top, \"px\") : '';\n                    _this._iframe.style.bottom = isFinite(data.params.bottom) ? \"\".concat(data.params.bottom, \"px\") : '';\n                    _this._iframe.style.left = isFinite(data.params.left) ? \"\".concat(data.params.left, \"px\") : '';\n                    _this._iframe.style.right = isFinite(data.params.right) ? \"\".concat(data.params.right, \"px\") : '';\n                    _this._iframe.style.width = isFinite(data.params.width) ? \"\".concat(data.params.width, \"px\") : data.params.width;\n                    _this._iframe.style.height = isFinite(data.params.height) ? \"\".concat(data.params.height, \"px\") : data.params.height;\n                }\n            }\n        };\n        _this._handleMessage = function (event) {\n            var _a;\n            if (((_a = event.data) === null || _a === void 0 ? void 0 : _a.channel) !== 'solflareIframeToWalletAdapter') {\n                return;\n            }\n            var data = event.data.data || {};\n            if (data.type === 'event') {\n                _this._handleEvent(data.event);\n            }\n            else if (data.type === 'resize') {\n                _this._handleResize(data);\n            }\n            else if (data.type === 'response') {\n                if (_this._adapterInstance) {\n                    _this._adapterInstance.handleMessage(data);\n                }\n            }\n        };\n        _this._removeElement = function () {\n            if (_this._flutterHandlerInterval !== null) {\n                clearInterval(_this._flutterHandlerInterval);\n                _this._flutterHandlerInterval = null;\n            }\n            if (_this._element) {\n                _this._element.remove();\n                _this._element = null;\n            }\n        };\n        _this._removeDanglingElements = function () {\n            var e_1, _a;\n            var elements = document.getElementsByClassName('solflare-wallet-adapter-iframe');\n            try {\n                for (var elements_1 = __values(elements), elements_1_1 = elements_1.next(); !elements_1_1.done; elements_1_1 = elements_1.next()) {\n                    var element = elements_1_1.value;\n                    if (element.parentElement) {\n                        element.remove();\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (elements_1_1 && !elements_1_1.done && (_a = elements_1.return)) _a.call(elements_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        };\n        _this._injectElement = function () {\n            _this._removeElement();\n            _this._removeDanglingElements();\n            var params = __assign(__assign({}, _this._iframeParams), { cluster: _this._network || 'mainnet-beta', origin: window.location.origin || '', title: document.title || '', version: 1, sdkVersion: VERSION || 'unknown' });\n            var preferredAdapter = _this._getPreferredAdapter();\n            if (preferredAdapter) {\n                params.adapter = preferredAdapter;\n            }\n            if (_this._provider) {\n                params.provider = _this._provider;\n            }\n            var queryString = Object.keys(params)\n                .map(function (key) { return \"\".concat(key, \"=\").concat(encodeURIComponent(params[key])); })\n                .join('&');\n            var iframeUrl = \"\".concat(Solflare.IFRAME_URL, \"?\").concat(queryString);\n            _this._element = document.createElement('div');\n            _this._element.className = 'solflare-wallet-adapter-iframe';\n            _this._element.innerHTML = \"\\n      <iframe src='\".concat(iframeUrl, \"' referrerPolicy='strict-origin-when-cross-origin' style='position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; border: none; border-radius: 0; z-index: 99999; color-scheme: auto;' allowtransparency='true'></iframe>\\n    \");\n            document.body.appendChild(_this._element);\n            _this._iframe = _this._element.querySelector('iframe');\n            // @ts-ignore\n            window.fromFlutter = _this._handleMobileMessage;\n            _this._flutterHandlerInterval = setInterval(function () {\n                // @ts-ignore\n                window.fromFlutter = _this._handleMobileMessage;\n            }, 100);\n            window.addEventListener('message', _this._handleMessage, false);\n        };\n        _this._collapseIframe = function () {\n            if (_this._iframe) {\n                _this._iframe.style.top = '';\n                _this._iframe.style.right = '';\n                _this._iframe.style.height = '2px';\n                _this._iframe.style.width = '2px';\n            }\n        };\n        _this._expandIframe = function () {\n            if (_this._iframe) {\n                _this._iframe.style.top = '0px';\n                _this._iframe.style.bottom = '0px';\n                _this._iframe.style.left = '0px';\n                _this._iframe.style.right = '0px';\n                _this._iframe.style.width = '100%';\n                _this._iframe.style.height = '100%';\n            }\n        };\n        _this._getPreferredAdapter = function () {\n            if (localStorage) {\n                return localStorage.getItem('solflarePreferredWalletAdapter') || null;\n            }\n            return null;\n        };\n        _this._setPreferredAdapter = function (adapter) {\n            if (localStorage && adapter) {\n                localStorage.setItem('solflarePreferredWalletAdapter', adapter);\n            }\n        };\n        _this._clearPreferredAdapter = function () {\n            if (localStorage) {\n                localStorage.removeItem('solflarePreferredWalletAdapter');\n            }\n        };\n        _this._webConnected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.resolve();\n                _this._connectHandler = null;\n            }\n            _this.emit('connect', _this.publicKey);\n        };\n        _this._webDisconnected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.reject();\n                _this._connectHandler = null;\n            }\n            _this._disconnected();\n            _this.emit('disconnect');\n        };\n        _this._disconnected = function () {\n            window.removeEventListener('message', _this._handleMessage, false);\n            _this._removeElement();\n            _this._clearPreferredAdapter();\n            _this._adapterInstance = null;\n        };\n        _this._handleMobileMessage = function (data) {\n            var _a, _b;\n            (_b = (_a = _this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                channel: 'solflareMobileToIframe',\n                data: data\n            }, '*');\n        };\n        if (config === null || config === void 0 ? void 0 : config.network) {\n            _this._network = config === null || config === void 0 ? void 0 : config.network;\n        }\n        if (config === null || config === void 0 ? void 0 : config.provider) {\n            _this._provider = config === null || config === void 0 ? void 0 : config.provider;\n        }\n        if (config === null || config === void 0 ? void 0 : config.params) {\n            _this._iframeParams = __assign({}, config === null || config === void 0 ? void 0 : config.params);\n        }\n        return _this;\n    }\n    Object.defineProperty(Solflare.prototype, \"publicKey\", {\n        get: function () {\n            var _a;\n            return ((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.publicKey) || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"isConnected\", {\n        get: function () {\n            var _a;\n            return !!((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.connected);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"connected\", {\n        get: function () {\n            return this.isConnected;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"autoApprove\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Solflare.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (this.connected) {\n                            return [2 /*return*/];\n                        }\n                        this._injectElement();\n                        return [4 /*yield*/, new Promise(function (resolve, reject) {\n                                _this._connectHandler = { resolve: resolve, reject: reject };\n                            })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Solflare.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._adapterInstance) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, this._adapterInstance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        this._disconnected();\n                        this.emit('disconnect');\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransaction, signedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransaction = isLegacyTransactionInstance(transaction) ?\n                            Uint8Array.from(transaction.serialize({ verifySignatures: false, requireAllSignatures: false })) :\n                            transaction.serialize();\n                        return [4 /*yield*/, this._adapterInstance.signTransaction(serializedTransaction)];\n                    case 1:\n                        signedTransaction = _a.sent();\n                        return [2 /*return*/, isLegacyTransactionInstance(transaction) ? Transaction.from(signedTransaction) : VersionedTransaction.deserialize(signedTransaction)];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransactions, signedTransactions;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransactions = transactions.map(function (transaction) {\n                            return isLegacyTransactionInstance(transaction) ?\n                                Uint8Array.from(transaction.serialize({ verifySignatures: false, requireAllSignatures: false })) :\n                                transaction.serialize();\n                        });\n                        return [4 /*yield*/, this._adapterInstance.signAllTransactions(serializedTransactions)];\n                    case 1:\n                        signedTransactions = _a.sent();\n                        if (signedTransactions.length !== transactions.length) {\n                            throw new Error('Failed to sign all transactions');\n                        }\n                        return [2 /*return*/, signedTransactions.map(function (signedTransaction, index) {\n                                return isLegacyTransactionInstance(transactions[index]) ? Transaction.from(signedTransaction) : VersionedTransaction.deserialize(signedTransaction);\n                            })];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signAndSendTransaction = function (transaction, options) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransaction = isLegacyTransactionInstance(transaction) ? transaction.serialize({ verifySignatures: false, requireAllSignatures: false }) : transaction.serialize();\n                        return [4 /*yield*/, this._adapterInstance.signAndSendTransaction(serializedTransaction, options)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._adapterInstance.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.sign = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.detectWallet = function (timeout) {\n        var _a;\n        if (timeout === void 0) { timeout = 10; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_b) {\n                if (window.SolflareApp || ((_a = window.solflare) === null || _a === void 0 ? void 0 : _a.isSolflare)) {\n                    return [2 /*return*/, true];\n                }\n                return [2 /*return*/, new Promise(function (resolve) {\n                        var pollInterval, pollTimeout;\n                        pollInterval = setInterval(function () {\n                            var _a;\n                            if (window.SolflareApp || ((_a = window.solflare) === null || _a === void 0 ? void 0 : _a.isSolflare)) {\n                                clearInterval(pollInterval);\n                                clearTimeout(pollTimeout);\n                                resolve(true);\n                            }\n                        }, 500);\n                        pollTimeout = setTimeout(function () {\n                            clearInterval(pollInterval);\n                            resolve(false);\n                        }, timeout * 1000);\n                    })];\n            });\n        });\n    };\n    Solflare.IFRAME_URL = 'https://connect.solflare.com/';\n    return Solflare;\n}(EventEmitter));\nexport default Solflare;\n"], "names": ["__extends", "extendStatics", "d", "b", "p", "__", "WalletAdapter", "_super", "EventEmitter", "__assign", "t", "s", "i", "n", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "reject", "fulfilled", "step", "e", "rejected", "result", "__generator", "body", "_", "f", "y", "g", "verb", "v", "op", "__read", "o", "m", "r", "ar", "error", "Wallet", "provider", "network", "_this", "newPublicKey", "PublicKey", "_a", "id", "_b", "method", "params", "requestId", "data", "display", "response", "signature", "public<PERSON>ey", "bs58", "isInjectedProvider", "isString", "a", "isObject", "isFunction", "WebAdapter", "iframe", "_c", "transaction", "signedTransaction", "transactions", "signedTransactions", "options", "getRandomValues", "rnds8", "rng", "byteToHex", "unsafeStringify", "arr", "offset", "randomUUID", "native", "v4", "buf", "rnds", "IframeAdapter", "messageId", "uuidv4", "e_1", "e_2", "e_3", "e_4", "isLegacyTransactionInstance", "VERSION", "__values", "Solflare", "config", "event", "_d", "elements", "elements_1", "elements_1_1", "element", "e_1_1", "preferredAdapter", "queryString", "key", "iframeUrl", "adapter", "serializedTransaction", "Transaction", "VersionedTransaction", "serializedTransactions", "index", "timeout", "pollInterval", "pollTimeout"], "mappings": "8DAAA,IAAIA,EAAyC,UAAY,CACrD,IAAIC,EAAgB,SAAUC,EAAGC,EAAG,CAChC,OAAAF,EAAgB,OAAO,gBAClB,CAAE,UAAW,CAAA,aAAgB,OAAS,SAAUC,EAAGC,EAAG,CAAED,EAAE,UAAYC,CAAG,GAC1E,SAAUD,EAAGC,EAAG,CAAE,QAASC,KAAKD,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGC,CAAC,IAAGF,EAAEE,CAAC,EAAID,EAAEC,CAAC,EAAG,EAC7FH,EAAcC,EAAGC,CAAC,CAC7B,EACA,OAAO,SAAUD,EAAGC,EAAG,CACnB,GAAI,OAAOA,GAAM,YAAcA,IAAM,KACjC,MAAM,IAAI,UAAU,uBAAyB,OAAOA,CAAC,EAAI,+BAA+B,EAC5FF,EAAcC,EAAGC,CAAC,EAClB,SAASE,GAAK,CAAE,KAAK,YAAcH,CAAG,CACtCA,EAAE,UAAYC,IAAM,KAAO,OAAO,OAAOA,CAAC,GAAKE,EAAG,UAAYF,EAAE,UAAW,IAAIE,EACnF,CACJ,EAAC,EAEGC,EAA+B,SAAUC,EAAQ,CACjDP,EAAUM,EAAeC,CAAM,EAC/B,SAASD,GAAgB,CACrB,OAAOC,IAAW,MAAQA,EAAO,MAAM,KAAM,SAAS,GAAK,IAC/D,CACA,OAAOD,CACX,EAAEE,CAAY,ECtBVR,EAAyC,UAAY,CACrD,IAAIC,EAAgB,SAAUC,EAAGC,EAAG,CAChC,OAAAF,EAAgB,OAAO,gBAClB,CAAE,UAAW,CAAA,aAAgB,OAAS,SAAUC,EAAGC,EAAG,CAAED,EAAE,UAAYC,CAAG,GAC1E,SAAUD,EAAGC,EAAG,CAAE,QAASC,KAAKD,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGC,CAAC,IAAGF,EAAEE,CAAC,EAAID,EAAEC,CAAC,EAAG,EAC7FH,EAAcC,EAAGC,CAAC,CAC7B,EACA,OAAO,SAAUD,EAAGC,EAAG,CACnB,GAAI,OAAOA,GAAM,YAAcA,IAAM,KACjC,MAAM,IAAI,UAAU,uBAAyB,OAAOA,CAAC,EAAI,+BAA+B,EAC5FF,EAAcC,EAAGC,CAAC,EAClB,SAASE,GAAK,CAAE,KAAK,YAAcH,CAAG,CACtCA,EAAE,UAAYC,IAAM,KAAO,OAAO,OAAOA,CAAC,GAAKE,EAAG,UAAYF,EAAE,UAAW,IAAIE,EACnF,CACJ,EAAC,EACGI,EAAsC,UAAY,CAClDA,OAAAA,EAAW,OAAO,QAAU,SAASC,EAAG,CACpC,QAASC,EAAGC,EAAI,EAAGC,EAAI,UAAU,OAAQD,EAAIC,EAAGD,IAAK,CACjDD,EAAI,UAAUC,CAAC,EACf,QAASR,KAAKO,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGP,CAAC,IAC1DM,EAAEN,CAAC,EAAIO,EAAEP,CAAC,EAClB,CACA,OAAOM,CACX,EACOD,EAAS,MAAM,KAAM,SAAS,CACzC,EACIK,EAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAG,CAAC,CAAG,CAC3G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC1F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC7F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAG,CAC7GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAA,CAAE,GAAG,MAAM,CACxE,CAAC,CACL,EACIY,EAA4C,SAAUb,EAASc,EAAM,CACrE,IAAIC,EAAI,CAAE,MAAO,EAAG,KAAM,UAAW,CAAE,GAAIpB,EAAE,CAAC,EAAI,EAAG,MAAMA,EAAE,CAAC,EAAG,OAAOA,EAAE,CAAC,CAAG,EAAG,KAAM,CAAA,EAAI,IAAK,CAAA,CAAE,EAAIqB,EAAGC,EAAGtB,EAAGuB,EAC/G,OAAOA,EAAI,CAAE,KAAMC,EAAK,CAAC,EAAG,MAASA,EAAK,CAAC,EAAG,OAAUA,EAAK,CAAC,CAAC,EAAI,OAAO,QAAW,aAAeD,EAAE,OAAO,QAAQ,EAAI,UAAW,CAAE,OAAO,IAAM,GAAIA,EACvJ,SAASC,EAAKrB,EAAG,CAAE,OAAO,SAAUsB,EAAG,CAAE,OAAOX,EAAK,CAACX,EAAGsB,CAAC,CAAC,CAAG,CAAG,CACjE,SAASX,EAAKY,EAAI,CACd,GAAIL,EAAG,MAAM,IAAI,UAAU,iCAAiC,EAC5D,KAAOE,IAAMA,EAAI,EAAGG,EAAG,CAAC,IAAMN,EAAI,IAAKA,GAAG,GAAI,CAC1C,GAAIC,EAAI,EAAGC,IAAMtB,EAAI0B,EAAG,CAAC,EAAI,EAAIJ,EAAE,OAAYI,EAAG,CAAC,EAAIJ,EAAE,SAActB,EAAIsB,EAAE,SAActB,EAAE,KAAKsB,CAAC,EAAG,GAAKA,EAAE,OAAS,EAAEtB,EAAIA,EAAE,KAAKsB,EAAGI,EAAG,CAAC,CAAC,GAAG,KAAM,OAAO1B,EAE3J,OADIsB,EAAI,EAAGtB,IAAG0B,EAAK,CAACA,EAAG,CAAC,EAAI,EAAG1B,EAAE,KAAK,GAC9B0B,EAAG,CAAC,EAAC,CACT,IAAK,GAAG,IAAK,GAAG1B,EAAI0B,EAAI,MACxB,IAAK,GAAG,OAAAN,EAAE,QAAgB,CAAE,MAAOM,EAAG,CAAC,EAAG,KAAM,EAAK,EACrD,IAAK,GAAGN,EAAE,QAASE,EAAII,EAAG,CAAC,EAAGA,EAAK,CAAC,CAAC,EAAG,SACxC,IAAK,GAAGA,EAAKN,EAAE,IAAI,MAAOA,EAAE,KAAK,IAAG,EAAI,SACxC,QACI,GAAMpB,EAAIoB,EAAE,KAAM,EAAApB,EAAIA,EAAE,OAAS,GAAKA,EAAEA,EAAE,OAAS,CAAC,KAAO0B,EAAG,CAAC,IAAM,GAAKA,EAAG,CAAC,IAAM,GAAI,CAAEN,EAAI,EAAG,QAAU,CAC3G,GAAIM,EAAG,CAAC,IAAM,IAAM,CAAC1B,GAAM0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK,CAAEoB,EAAE,MAAQM,EAAG,CAAC,EAAG,KAAO,CACrF,GAAIA,EAAG,CAAC,IAAM,GAAKN,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGA,EAAI0B,EAAI,KAAO,CACpE,GAAI1B,GAAKoB,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGoB,EAAE,IAAI,KAAKM,CAAE,EAAG,KAAO,CAC9D1B,EAAE,CAAC,GAAGoB,EAAE,IAAI,IAAG,EACnBA,EAAE,KAAK,IAAG,EAAI,QAClC,CACYM,EAAKP,EAAK,KAAKd,EAASe,CAAC,CAC7B,OAASL,EAAG,CAAEW,EAAK,CAAC,EAAGX,CAAC,EAAGO,EAAI,CAAG,QAAC,CAAWD,EAAIrB,EAAI,CAAG,CACzD,GAAI0B,EAAG,CAAC,EAAI,EAAG,MAAMA,EAAG,CAAC,EAAG,MAAO,CAAE,MAAOA,EAAG,CAAC,EAAIA,EAAG,CAAC,EAAI,OAAQ,KAAM,EAAI,CAClF,CACJ,EACIC,EAAkC,SAAUC,EAAGzB,EAAG,CAClD,IAAI0B,EAAI,OAAO,QAAW,YAAcD,EAAE,OAAO,QAAQ,EACzD,GAAI,CAACC,EAAG,OAAOD,EACf,IAAI1B,EAAI2B,EAAE,KAAKD,CAAC,EAAGE,EAAGC,EAAK,CAAA,EAAIhB,EAC/B,GAAI,CACA,MAAQZ,IAAM,QAAUA,KAAM,IAAM,EAAE2B,EAAI5B,EAAE,KAAI,GAAI,MAAM6B,EAAG,KAAKD,EAAE,KAAK,CAC7E,OACOE,EAAO,CAAEjB,EAAI,CAAE,MAAOiB,CAAK,CAAI,QAC1C,CACQ,GAAI,CACIF,GAAK,CAACA,EAAE,OAASD,EAAI3B,EAAE,SAAY2B,EAAE,KAAK3B,CAAC,CACnD,QACR,CAAkB,GAAIa,EAAG,MAAMA,EAAE,KAAO,CACpC,CACA,OAAOgB,CACX,EAIIE,EAAwB,SAAUpC,EAAQ,CAC1CP,EAAU2C,EAAQpC,CAAM,EACxB,SAASoC,EAAOC,EAAUC,EAAS,CAC/B,IAAIC,EAAQvC,EAAO,KAAK,IAAI,GAAK,KAmJjC,GAlJAuC,EAAM,eAAiB,SAAUrB,EAAG,CAChC,GAAKqB,EAAM,mBAAqBrB,EAAE,SAAW,QACxCA,EAAE,SAAWqB,EAAM,aAAa,QAAUrB,EAAE,SAAWqB,EAAM,QAC9D,GAAIrB,EAAE,KAAK,SAAW,YAAa,CAC/B,IAAIsB,EAAe,IAAIC,EAAUvB,EAAE,KAAK,OAAO,SAAS,GACpD,CAACqB,EAAM,YAAc,CAACA,EAAM,WAAW,OAAOC,CAAY,KACtDD,EAAM,YAAc,CAACA,EAAM,WAAW,OAAOC,CAAY,GACzDD,EAAM,kBAAiB,EAE3BA,EAAM,WAAaC,EACnBD,EAAM,aAAe,CAAC,CAACrB,EAAE,KAAK,OAAO,YACrCqB,EAAM,KAAK,UAAWA,EAAM,UAAU,EAE9C,SACSrB,EAAE,KAAK,SAAW,eACvBqB,EAAM,kBAAiB,WAElBrB,EAAE,KAAK,QAAUA,EAAE,KAAK,QACzBqB,EAAM,kBAAkB,IAAIrB,EAAE,KAAK,EAAE,EAAG,CACxC,IAAIwB,EAAKZ,EAAOS,EAAM,kBAAkB,IAAIrB,EAAE,KAAK,EAAE,EAAG,CAAC,EAAGJ,EAAU4B,EAAG,CAAC,EAAG3B,EAAS2B,EAAG,CAAC,EACtFxB,EAAE,KAAK,OACPJ,EAAQI,EAAE,KAAK,MAAM,EAGrBH,EAAO,IAAI,MAAMG,EAAE,KAAK,KAAK,CAAC,CAEtC,EAGZ,EACAqB,EAAM,eAAiB,UAAY,CAM/B,OALKA,EAAM,gBACPA,EAAM,cAAgB,GACtB,OAAO,iBAAiB,UAAWA,EAAM,cAAc,EACvD,OAAO,iBAAiB,eAAgBA,EAAM,UAAU,GAExDA,EAAM,kBACC,IAAI,QAAQ,SAAUzB,EAAS,CAClCyB,EAAM,aAAa,UAAW,EAAE,EAChCzB,EAAO,CACX,CAAC,GAGD,OAAO,KAAO,SACdyB,EAAM,OAAS,OAAO,KAAKA,EAAM,aAAa,SAAQ,EAAI,SAAU,yCAAyC,EACtG,IAAI,QAAQ,SAAUzB,EAAS,CAClCyB,EAAM,KAAK,UAAWzB,CAAO,CACjC,CAAC,EAET,EACAyB,EAAM,kBAAoB,UAAY,CAC9BA,EAAM,gBACNA,EAAM,cAAgB,GACtB,OAAO,oBAAoB,UAAWA,EAAM,cAAc,EAC1D,OAAO,oBAAoB,eAAgBA,EAAM,UAAU,GAE3DA,EAAM,aACNA,EAAM,WAAa,KACnBA,EAAM,KAAK,YAAY,GAE3BA,EAAM,kBAAkB,QAAQ,SAAUG,EAAIC,EAAI,CAC3C,IAACC,EAAKd,EAAOY,EAAI,CAAC,EAAaE,EAAG,CAAC,MAAG7B,EAAS6B,EAAG,CAAC,EACtDL,EAAM,kBAAkB,OAAOI,CAAE,EACjC5B,EAAO,qBAAqB,CAChC,CAAC,CACL,EACAwB,EAAM,aAAe,SAAUM,EAAQC,EAAQ,CAAE,OAAOvC,EAAUgC,EAAO,OAAQ,OAAQ,UAAY,CACjG,IAAIQ,EACAR,EAAQ,KACZ,OAAOlB,EAAY,KAAM,SAAUqB,EAAI,CACnC,GAAIG,IAAW,WAAa,CAAC,KAAK,UAC9B,MAAM,IAAI,MAAM,sBAAsB,EAE1C,OAAAE,EAAY,KAAK,eACjB,EAAE,KAAK,eACA,CAAC,EAAc,IAAI,QAAQ,SAAUjC,EAASC,EAAQ,CACrDwB,EAAM,kBAAkB,IAAIQ,EAAW,CAACjC,EAASC,CAAM,CAAC,EACpDwB,EAAM,kBACNA,EAAM,kBAAkB,YAAY,CAChC,QAAS,MACT,GAAIQ,EACJ,OAAQF,EACR,OAAQ3C,EAAS,CAAE,QAASqC,EAAM,QAAQ,EAAIO,CAAM,CACpF,CAA6B,GAGDP,EAAM,OAAO,YAAY,CACrB,QAAS,MACT,GAAIQ,EACJ,OAAQF,EACR,OAAQC,CACxC,EAA+BP,EAAM,aAAa,MAAM,EACvBA,EAAM,aACPA,EAAM,OAAO,MAAK,EAG9B,CAAC,CAAC,CACV,CAAC,CACL,CAAC,CAAG,EACJA,EAAM,QAAU,UAAY,CACxB,OAAIA,EAAM,QACNA,EAAM,OAAO,MAAK,EAEfA,EAAM,eAAc,CAC/B,EACAA,EAAM,WAAa,UAAY,CAAE,OAAOhC,EAAUgC,EAAO,OAAQ,OAAQ,UAAY,CACjF,OAAOlB,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,OAAK,KAAK,kBACH,CAAC,EAAa,KAAK,aAAa,aAAc,CAAA,CAAE,CAAC,EADpB,CAAC,EAAa,CAAC,EAEvD,IAAK,GACDA,EAAG,KAAI,EACPA,EAAG,MAAQ,EACf,IAAK,GACD,OAAI,KAAK,QACL,KAAK,OAAO,MAAK,EAErB,KAAK,kBAAiB,EACf,CAAC,CAAC,CACjC,CACY,CAAC,CACL,CAAC,CAAG,EACJH,EAAM,KAAO,SAAUS,EAAMC,EAAS,CAAE,OAAO1C,EAAUgC,EAAO,OAAQ,OAAQ,UAAY,CACxF,IAAIW,EAAUC,EAAWC,EACzB,OAAO/B,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,EAAEM,aAAgB,YAClB,MAAM,IAAI,MAAM,wCAAwC,EAE5D,MAAO,CAAC,EAAa,KAAK,aAAa,OAAQ,CACvC,KAAMA,EACN,QAASC,CACzC,CAA6B,CAAC,EACV,IAAK,GACD,OAAAC,EAAWR,EAAG,KAAI,EAClBS,EAAYE,EAAK,OAAOH,EAAS,SAAS,EAC1CE,EAAY,IAAIX,EAAUS,EAAS,SAAS,EACrC,CAAC,EAAc,CACd,UAAWC,EACX,UAAWC,CAC3C,CAA6B,CAC7B,CACY,CAAC,CACL,CAAC,CAAG,EACAE,EAAmBjB,CAAQ,EAC3BE,EAAM,kBAAoBF,UAErBkB,EAASlB,CAAQ,EACtBE,EAAM,aAAe,IAAI,IAAIF,CAAQ,EACrCE,EAAM,aAAa,KAAO,IAAI,gBAAgB,CAC1C,OAAQ,OAAO,SAAS,OACxB,QAASD,CACzB,CAAa,EAAE,SAAQ,MAGX,OAAM,IAAI,MAAM,kEAAkE,EAEtF,OAAAC,EAAM,SAAWD,EACjBC,EAAM,WAAa,KACnBA,EAAM,aAAe,GACrBA,EAAM,OAAS,KACfA,EAAM,cAAgB,GACtBA,EAAM,eAAiB,EACvBA,EAAM,kBAAoB,IAAI,IACvBA,CACX,CACA,cAAO,eAAeH,EAAO,UAAW,YAAa,CACjD,IAAK,UAAY,CACb,OAAO,KAAK,UAChB,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACD,OAAO,eAAeA,EAAO,UAAW,YAAa,CACjD,IAAK,UAAY,CACb,OAAO,KAAK,aAAe,IAC/B,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACD,OAAO,eAAeA,EAAO,UAAW,cAAe,CACnD,IAAK,UAAY,CACb,OAAO,KAAK,YAChB,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACMA,CACX,EAAEnC,CAAY,EAEd,SAASsD,EAASC,EAAG,CACjB,OAAO,OAAOA,GAAM,QACxB,CACA,SAASF,EAAmBE,EAAG,CAC3B,OAAOC,EAASD,CAAC,GAAKE,EAAWF,EAAE,WAAW,CAClD,CACA,SAASC,EAASD,EAAG,CACjB,OAAO,OAAOA,GAAM,UAAYA,IAAM,IAC1C,CACA,SAASE,EAAWF,EAAG,CACnB,OAAO,OAAOA,GAAM,UACxB,CChSA,IAAI/D,EAAyC,UAAY,CACrD,IAAIC,EAAgB,SAAUC,EAAGC,EAAG,CAChC,OAAAF,EAAgB,OAAO,gBAClB,CAAE,UAAW,CAAA,aAAgB,OAAS,SAAUC,EAAGC,EAAG,CAAED,EAAE,UAAYC,CAAG,GAC1E,SAAUD,EAAGC,EAAG,CAAE,QAASC,KAAKD,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGC,CAAC,IAAGF,EAAEE,CAAC,EAAID,EAAEC,CAAC,EAAG,EAC7FH,EAAcC,EAAGC,CAAC,CAC7B,EACA,OAAO,SAAUD,EAAGC,EAAG,CACnB,GAAI,OAAOA,GAAM,YAAcA,IAAM,KACjC,MAAM,IAAI,UAAU,uBAAyB,OAAOA,CAAC,EAAI,+BAA+B,EAC5FF,EAAcC,EAAGC,CAAC,EAClB,SAASE,GAAK,CAAE,KAAK,YAAcH,CAAG,CACtCA,EAAE,UAAYC,IAAM,KAAO,OAAO,OAAOA,CAAC,GAAKE,EAAG,UAAYF,EAAE,UAAW,IAAIE,EACnF,CACJ,EAAC,EACGS,EAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAG,CAAC,CAAG,CAC3G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC1F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC7F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAG,CAC7GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAA,CAAE,GAAG,MAAM,CACxE,CAAC,CACL,EACIY,EAA4C,SAAUb,EAASc,EAAM,CACrE,IAAIC,EAAI,CAAE,MAAO,EAAG,KAAM,UAAW,CAAE,GAAIpB,EAAE,CAAC,EAAI,EAAG,MAAMA,EAAE,CAAC,EAAG,OAAOA,EAAE,CAAC,CAAG,EAAG,KAAM,CAAA,EAAI,IAAK,CAAA,CAAE,EAAIqB,EAAGC,EAAGtB,EAAGuB,EAC/G,OAAOA,EAAI,CAAE,KAAMC,EAAK,CAAC,EAAG,MAASA,EAAK,CAAC,EAAG,OAAUA,EAAK,CAAC,CAAC,EAAI,OAAO,QAAW,aAAeD,EAAE,OAAO,QAAQ,EAAI,UAAW,CAAE,OAAO,IAAM,GAAIA,EACvJ,SAASC,EAAKrB,EAAG,CAAE,OAAO,SAAUsB,EAAG,CAAE,OAAOX,EAAK,CAACX,EAAGsB,CAAC,CAAC,CAAG,CAAG,CACjE,SAASX,EAAKY,EAAI,CACd,GAAIL,EAAG,MAAM,IAAI,UAAU,iCAAiC,EAC5D,KAAOE,IAAMA,EAAI,EAAGG,EAAG,CAAC,IAAMN,EAAI,IAAKA,GAAG,GAAI,CAC1C,GAAIC,EAAI,EAAGC,IAAMtB,EAAI0B,EAAG,CAAC,EAAI,EAAIJ,EAAE,OAAYI,EAAG,CAAC,EAAIJ,EAAE,SAActB,EAAIsB,EAAE,SAActB,EAAE,KAAKsB,CAAC,EAAG,GAAKA,EAAE,OAAS,EAAEtB,EAAIA,EAAE,KAAKsB,EAAGI,EAAG,CAAC,CAAC,GAAG,KAAM,OAAO1B,EAE3J,OADIsB,EAAI,EAAGtB,IAAG0B,EAAK,CAACA,EAAG,CAAC,EAAI,EAAG1B,EAAE,KAAK,GAC9B0B,EAAG,CAAC,EAAC,CACT,IAAK,GAAG,IAAK,GAAG1B,EAAI0B,EAAI,MACxB,IAAK,GAAG,OAAAN,EAAE,QAAgB,CAAE,MAAOM,EAAG,CAAC,EAAG,KAAM,EAAK,EACrD,IAAK,GAAGN,EAAE,QAASE,EAAII,EAAG,CAAC,EAAGA,EAAK,CAAC,CAAC,EAAG,SACxC,IAAK,GAAGA,EAAKN,EAAE,IAAI,MAAOA,EAAE,KAAK,IAAG,EAAI,SACxC,QACI,GAAMpB,EAAIoB,EAAE,KAAM,EAAApB,EAAIA,EAAE,OAAS,GAAKA,EAAEA,EAAE,OAAS,CAAC,KAAO0B,EAAG,CAAC,IAAM,GAAKA,EAAG,CAAC,IAAM,GAAI,CAAEN,EAAI,EAAG,QAAU,CAC3G,GAAIM,EAAG,CAAC,IAAM,IAAM,CAAC1B,GAAM0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK,CAAEoB,EAAE,MAAQM,EAAG,CAAC,EAAG,KAAO,CACrF,GAAIA,EAAG,CAAC,IAAM,GAAKN,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGA,EAAI0B,EAAI,KAAO,CACpE,GAAI1B,GAAKoB,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGoB,EAAE,IAAI,KAAKM,CAAE,EAAG,KAAO,CAC9D1B,EAAE,CAAC,GAAGoB,EAAE,IAAI,IAAG,EACnBA,EAAE,KAAK,IAAG,EAAI,QAClC,CACYM,EAAKP,EAAK,KAAKd,EAASe,CAAC,CAC7B,OAASL,EAAG,CAAEW,EAAK,CAAC,EAAGX,CAAC,EAAGO,EAAI,CAAG,QAAC,CAAWD,EAAIrB,EAAI,CAAG,CACzD,GAAI0B,EAAG,CAAC,EAAI,EAAG,MAAMA,EAAG,CAAC,EAAG,MAAO,CAAE,MAAOA,EAAG,CAAC,EAAIA,EAAG,CAAC,EAAI,OAAQ,KAAM,EAAI,CAClF,CACJ,EAII8B,EAA4B,SAAU3D,EAAQ,CAC9CP,EAAUkE,EAAY3D,CAAM,EAE5B,SAAS2D,EAAWC,EAAQtB,EAASD,EAAU,CAC3C,IAAIE,EAAQvC,EAAO,KAAK,IAAI,GAAK,KACjC,OAAAuC,EAAM,UAAY,KAElBA,EAAM,cAAgB,SAAUS,EAAM,CAEtC,EACAT,EAAM,aAAe,SAAUM,EAAQC,EAAQ,CAAE,OAAOvC,EAAUgC,EAAO,OAAQ,OAAQ,UAAY,CACjG,IAAIG,EAAIE,EACR,OAAOvB,EAAY,KAAM,SAAUwC,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,MAAO,GAAAnB,EAAK,KAAK,aAAe,MAAQA,IAAO,SAAkBA,EAAG,YAC7D,CAAC,EAAa,KAAK,UAAU,YAAYG,EAAQC,CAAM,CAAC,EAD0B,CAAC,EAAa,CAAC,EAE5G,IAAK,GAAG,MAAO,CAAC,EAAce,EAAG,KAAI,CAAE,EACvC,IAAK,GACD,MAAO,GAAAjB,EAAK,KAAK,aAAe,MAAQA,IAAO,SAAkBA,EAAG,aAC7D,CAAC,EAAa,KAAK,UAAU,aAAaC,EAAQC,CAAM,CAAC,EAD0B,CAAC,EAAa,CAAC,EAE7G,IAAK,GAAG,MAAO,CAAC,EAAce,EAAG,KAAI,CAAE,EACvC,IAAK,GAAG,MAAM,IAAI,MAAM,4DAA4D,CACxG,CACY,CAAC,CACL,CAAC,CAAG,EACJtB,EAAM,eAAiB,UAAY,CAC/BA,EAAM,KAAK,SAAS,CACxB,EACAA,EAAM,kBAAoB,UAAY,CAClC,OAAO,cAAcA,EAAM,UAAU,EACrCA,EAAM,KAAK,YAAY,CAC3B,EACAA,EAAM,SAAWD,EACjBC,EAAM,UAAYF,EACXE,CACX,CACA,cAAO,eAAeoB,EAAW,UAAW,YAAa,CACrD,IAAK,UAAY,CACb,OAAO,KAAK,UAAU,WAAa,IACvC,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACD,OAAO,eAAeA,EAAW,UAAW,YAAa,CACrD,IAAK,UAAY,CACb,OAAO,KAAK,UAAU,WAAa,EACvC,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACDA,EAAW,UAAU,QAAU,UAAY,CACvC,OAAOpD,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIgC,EAAQ,KACZ,OAAOlB,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,YAAK,UAAY,IAAIN,EAAO,KAAK,UAAW,KAAK,QAAQ,EACzD,KAAK,UAAU,GAAG,UAAW,KAAK,cAAc,EAChD,KAAK,UAAU,GAAG,aAAc,KAAK,iBAAiB,EACtD,KAAK,WAAa,OAAO,YAAY,UAAY,CAC7C,IAAIM,EAAIE,IAEFA,GAAMF,EAAKH,EAAM,aAAe,MAAQG,IAAO,OAAS,OAASA,EAAG,UAAY,MAAQE,IAAO,OAAS,OAASA,EAAG,UAAY,IAClIL,EAAM,kBAAiB,CAE/B,EAAG,GAAG,EACC,CAAC,EAAa,KAAK,UAAU,QAAO,CAAE,EACjD,IAAK,GACD,OAAAG,EAAG,KAAI,EACA,CAAC,CAAC,CACjC,CACY,CAAC,CACL,CAAC,CACL,EACAiB,EAAW,UAAU,WAAa,UAAY,CAC1C,OAAOpD,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,OAAOc,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,YAAK,UAAU,mBAAmB,SAAS,EAC3C,KAAK,UAAU,mBAAmB,YAAY,EACvC,CAAC,EAAa,KAAK,UAAU,WAAU,CAAE,EACpD,IAAK,GACD,OAAAA,EAAG,KAAI,EACA,CAAC,CAAC,CACjC,CACY,CAAC,CACL,CAAC,CACL,EACAiB,EAAW,UAAU,gBAAkB,SAAUG,EAAa,CAC1D,OAAOvD,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIwD,EACJ,OAAO1C,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,MAAO,CAAC,EAAa,KAAK,aAAa,oBAAqB,CACpD,YAAaW,EAAK,OAAOS,CAAW,CACpE,CAA6B,CAAC,EACV,IAAK,GACD,OAAAC,EAAqBrB,EAAG,KAAI,EAAI,YACzB,CAAC,EAAcW,EAAK,OAAOU,CAAiB,CAAC,CAC5E,CACY,CAAC,CACL,CAAC,CACL,EACAJ,EAAW,UAAU,oBAAsB,SAAUK,EAAc,CAC/D,OAAOzD,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAI0D,EACJ,OAAO5C,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,MAAO,CAAC,EAAa,KAAK,aAAa,wBAAyB,CACxD,aAAcsB,EAAa,IAAI,SAAUF,EAAa,CAAE,OAAOT,EAAK,OAAOS,CAAW,CAAG,CAAC,CAC1H,CAA6B,CAAC,EACV,IAAK,GACD,OAAAG,EAAsBvB,EAAG,KAAI,EAAI,aAC1B,CAAC,EAAcuB,EAAmB,IAAI,SAAUH,EAAa,CAAE,OAAOT,EAAK,OAAOS,CAAW,CAAG,CAAC,CAAC,CACjI,CACY,CAAC,CACL,CAAC,CACL,EACAH,EAAW,UAAU,uBAAyB,SAAUG,EAAaI,EAAS,CAC1E,OAAO3D,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAI2C,EACJ,OAAO7B,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,MAAO,CAAC,EAAa,KAAK,aAAa,yBAA0B,CACzD,YAAaW,EAAK,OAAOS,CAAW,EACpC,QAASI,CACzC,CAA6B,CAAC,EACV,IAAK,GACD,OAAAhB,EAAYR,EAAG,OACR,CAAC,EAAcQ,EAAS,SAAS,CAChE,CACY,CAAC,CACL,CAAC,CACL,EACAS,EAAW,UAAU,YAAc,SAAUX,EAAMC,EAAS,CACxD,OAAIA,IAAY,SAAUA,EAAU,OAC7B1C,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAI4C,EACJ,OAAO9B,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,MAAO,CAAC,EAAa,KAAK,UAAU,KAAKM,EAAMC,CAAO,CAAC,EAC3D,IAAK,GACD,OAAAE,EAAaT,EAAG,KAAI,EAAI,UACjB,CAAC,EAAc,WAAW,KAAKS,CAAS,CAAC,CACxE,CACY,CAAC,CACL,CAAC,CACL,EACOQ,CACX,EAAE5D,CAAa,EC7Nf,IAAIoE,EACJ,MAAMC,EAAQ,IAAI,WAAW,EAAE,EAChB,SAASC,GAAM,CAE5B,GAAI,CAACF,IAEHA,EAAkB,OAAO,OAAW,KAAe,OAAO,iBAAmB,OAAO,gBAAgB,KAAK,MAAM,EAE3G,CAACA,GACH,MAAM,IAAI,MAAM,0GAA0G,EAI9H,OAAOA,EAAgBC,CAAK,CAC9B,CCXA,MAAME,EAAY,CAAA,EAElB,QAASjE,EAAI,EAAGA,EAAI,IAAK,EAAEA,EACzBiE,EAAU,MAAMjE,EAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,EAG3C,SAASkE,EAAgBC,EAAKC,EAAS,EAAG,CAG/C,OAAOH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,CACnf,CChBA,MAAMC,EAAa,OAAO,OAAW,KAAe,OAAO,YAAc,OAAO,WAAW,KAAK,MAAM,EACtGC,EAAe,CACb,WAAAD,CACF,ECCA,SAASE,EAAGV,EAASW,EAAKJ,EAAQ,CAChC,GAAIE,EAAO,YAAsB,CAACT,EAChC,OAAOS,EAAO,WAAU,EAG1BT,EAAUA,GAAW,CAAA,EACrB,MAAMY,EAAOZ,EAAQ,SAAWA,EAAQ,KAAOG,KAE/C,OAAAS,EAAK,CAAC,EAAIA,EAAK,CAAC,EAAI,GAAO,GAC3BA,EAAK,CAAC,EAAIA,EAAK,CAAC,EAAI,GAAO,IAYpBP,EAAgBO,CAAI,CAC7B,CC1BA,IAAIrF,EAAyC,UAAY,CACrD,IAAIC,EAAgB,SAAUC,EAAGC,EAAG,CAChC,OAAAF,EAAgB,OAAO,gBAClB,CAAE,UAAW,CAAA,aAAgB,OAAS,SAAUC,EAAGC,EAAG,CAAED,EAAE,UAAYC,CAAG,GAC1E,SAAUD,EAAGC,EAAG,CAAE,QAASC,KAAKD,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGC,CAAC,IAAGF,EAAEE,CAAC,EAAID,EAAEC,CAAC,EAAG,EAC7FH,EAAcC,EAAGC,CAAC,CAC7B,EACA,OAAO,SAAUD,EAAGC,EAAG,CACnB,GAAI,OAAOA,GAAM,YAAcA,IAAM,KACjC,MAAM,IAAI,UAAU,uBAAyB,OAAOA,CAAC,EAAI,+BAA+B,EAC5FF,EAAcC,EAAGC,CAAC,EAClB,SAASE,GAAK,CAAE,KAAK,YAAcH,CAAG,CACtCA,EAAE,UAAYC,IAAM,KAAO,OAAO,OAAOA,CAAC,GAAKE,EAAG,UAAYF,EAAE,UAAW,IAAIE,EACnF,CACJ,EAAC,EACGI,EAAsC,UAAY,CAClDA,OAAAA,EAAW,OAAO,QAAU,SAASC,EAAG,CACpC,QAASC,EAAGC,EAAI,EAAGC,EAAI,UAAU,OAAQD,EAAIC,EAAGD,IAAK,CACjDD,EAAI,UAAUC,CAAC,EACf,QAASR,KAAKO,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGP,CAAC,IAC1DM,EAAEN,CAAC,EAAIO,EAAEP,CAAC,EAClB,CACA,OAAOM,CACX,EACOD,EAAS,MAAM,KAAM,SAAS,CACzC,EACIK,EAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAG,CAAC,CAAG,CAC3G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC1F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC7F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAG,CAC7GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAA,CAAE,GAAG,MAAM,CACxE,CAAC,CACL,EACIY,EAA4C,SAAUb,EAASc,EAAM,CACrE,IAAIC,EAAI,CAAE,MAAO,EAAG,KAAM,UAAW,CAAE,GAAIpB,EAAE,CAAC,EAAI,EAAG,MAAMA,EAAE,CAAC,EAAG,OAAOA,EAAE,CAAC,CAAG,EAAG,KAAM,CAAA,EAAI,IAAK,CAAA,CAAE,EAAIqB,EAAGC,EAAGtB,EAAGuB,EAC/G,OAAOA,EAAI,CAAE,KAAMC,EAAK,CAAC,EAAG,MAASA,EAAK,CAAC,EAAG,OAAUA,EAAK,CAAC,CAAC,EAAI,OAAO,QAAW,aAAeD,EAAE,OAAO,QAAQ,EAAI,UAAW,CAAE,OAAO,IAAM,GAAIA,EACvJ,SAASC,EAAKrB,EAAG,CAAE,OAAO,SAAUsB,EAAG,CAAE,OAAOX,EAAK,CAACX,EAAGsB,CAAC,CAAC,CAAG,CAAG,CACjE,SAASX,EAAKY,EAAI,CACd,GAAIL,EAAG,MAAM,IAAI,UAAU,iCAAiC,EAC5D,KAAOE,IAAMA,EAAI,EAAGG,EAAG,CAAC,IAAMN,EAAI,IAAKA,GAAG,GAAI,CAC1C,GAAIC,EAAI,EAAGC,IAAMtB,EAAI0B,EAAG,CAAC,EAAI,EAAIJ,EAAE,OAAYI,EAAG,CAAC,EAAIJ,EAAE,SAActB,EAAIsB,EAAE,SAActB,EAAE,KAAKsB,CAAC,EAAG,GAAKA,EAAE,OAAS,EAAEtB,EAAIA,EAAE,KAAKsB,EAAGI,EAAG,CAAC,CAAC,GAAG,KAAM,OAAO1B,EAE3J,OADIsB,EAAI,EAAGtB,IAAG0B,EAAK,CAACA,EAAG,CAAC,EAAI,EAAG1B,EAAE,KAAK,GAC9B0B,EAAG,CAAC,EAAC,CACT,IAAK,GAAG,IAAK,GAAG1B,EAAI0B,EAAI,MACxB,IAAK,GAAG,OAAAN,EAAE,QAAgB,CAAE,MAAOM,EAAG,CAAC,EAAG,KAAM,EAAK,EACrD,IAAK,GAAGN,EAAE,QAASE,EAAII,EAAG,CAAC,EAAGA,EAAK,CAAC,CAAC,EAAG,SACxC,IAAK,GAAGA,EAAKN,EAAE,IAAI,MAAOA,EAAE,KAAK,IAAG,EAAI,SACxC,QACI,GAAMpB,EAAIoB,EAAE,KAAM,EAAApB,EAAIA,EAAE,OAAS,GAAKA,EAAEA,EAAE,OAAS,CAAC,KAAO0B,EAAG,CAAC,IAAM,GAAKA,EAAG,CAAC,IAAM,GAAI,CAAEN,EAAI,EAAG,QAAU,CAC3G,GAAIM,EAAG,CAAC,IAAM,IAAM,CAAC1B,GAAM0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK,CAAEoB,EAAE,MAAQM,EAAG,CAAC,EAAG,KAAO,CACrF,GAAIA,EAAG,CAAC,IAAM,GAAKN,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGA,EAAI0B,EAAI,KAAO,CACpE,GAAI1B,GAAKoB,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGoB,EAAE,IAAI,KAAKM,CAAE,EAAG,KAAO,CAC9D1B,EAAE,CAAC,GAAGoB,EAAE,IAAI,IAAG,EACnBA,EAAE,KAAK,IAAG,EAAI,QAClC,CACYM,EAAKP,EAAK,KAAKd,EAASe,CAAC,CAC7B,OAASL,EAAG,CAAEW,EAAK,CAAC,EAAGX,CAAC,EAAGO,EAAI,CAAG,QAAC,CAAWD,EAAIrB,EAAI,CAAG,CACzD,GAAI0B,EAAG,CAAC,EAAI,EAAG,MAAMA,EAAG,CAAC,EAAG,MAAO,CAAE,MAAOA,EAAG,CAAC,EAAIA,EAAG,CAAC,EAAI,OAAQ,KAAM,EAAI,CAClF,CACJ,EAKIkD,EAA+B,SAAU/E,EAAQ,CACjDP,EAAUsF,EAAe/E,CAAM,EAC/B,SAAS+E,EAAcnB,EAAQR,EAAW,CACtC,IAAIb,EAAQ,KACRG,EACJ,OAAAH,EAAQvC,EAAO,KAAK,IAAI,GAAK,KAC7BuC,EAAM,WAAa,KACnBA,EAAM,iBAAmB,CAAA,EACzBA,EAAM,cAAgB,SAAUS,EAAM,CAClC,GAAIT,EAAM,iBAAiBS,EAAK,EAAE,EAAG,CACjC,IAAIN,EAAKH,EAAM,iBAAiBS,EAAK,EAAE,EAAGlC,EAAU4B,EAAG,QAAS3B,EAAS2B,EAAG,OAC5E,OAAOH,EAAM,iBAAiBS,EAAK,EAAE,EACjCA,EAAK,MACLjC,EAAOiC,EAAK,KAAK,EAGjBlC,EAAQkC,EAAK,MAAM,CAE3B,CACJ,EACAT,EAAM,aAAe,SAAUS,EAAM,CACjC,GAAI,CAACT,EAAM,UACP,MAAM,IAAI,MAAM,sBAAsB,EAE1C,OAAO,IAAI,QAAQ,SAAUzB,EAASC,EAAQ,CAC1C,IAAI2B,EAAIE,EACJoC,EAAYC,EAAM,EACtB1C,EAAM,iBAAiByC,CAAS,EAAI,CAAE,QAASlE,EAAS,OAAQC,CAAM,GACrE6B,GAAMF,EAAKH,EAAM,WAAa,MAAQG,IAAO,OAAS,OAASA,EAAG,iBAAmB,MAAQE,IAAO,QAAkBA,EAAG,YAAY,CAClI,QAAS,gCACT,KAAM1C,EAAS,CAAE,GAAI8E,CAAS,EAAIhC,CAAI,CAC1D,EAAmB,GAAG,CACV,CAAC,CACL,EACAT,EAAM,QAAUqB,EAChBrB,EAAM,WAAa,IAAIE,GAAWC,EAA2DU,GAAU,YAAc,MAAQV,IAAO,OAAS,OAASA,EAAG,KAAKU,CAAS,CAAC,EACjKb,CACX,CACA,cAAO,eAAewC,EAAc,UAAW,YAAa,CACxD,IAAK,UAAY,CACb,OAAO,KAAK,YAAc,IAC9B,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACD,OAAO,eAAeA,EAAc,UAAW,YAAa,CACxD,IAAK,UAAY,CACb,MAAO,EACX,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACDA,EAAc,UAAU,QAAU,UAAY,CAC1C,OAAOxE,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,OAAOc,EAAY,KAAM,SAAUqB,EAAI,CACnC,MAAO,CAAC,CAAC,CACb,CAAC,CACL,CAAC,CACL,EACAqC,EAAc,UAAU,WAAa,UAAY,CAC7C,OAAOxE,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,OAAOc,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GAAG,MAAO,CAAC,EAAa,KAAK,aAAa,CACvC,OAAQ,YACpC,CAAyB,CAAC,EACN,IAAK,GACD,OAAAA,EAAG,KAAI,EACA,CAAC,CAAC,CACjC,CACY,CAAC,CACL,CAAC,CACL,EACAqC,EAAc,UAAU,gBAAkB,SAAUjB,EAAa,CAC7D,IAAIpB,EACJ,OAAOnC,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIwD,EAAmBmB,EACvB,OAAO7D,EAAY,KAAM,SAAUuB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1CA,EAAG,MAAQ,EACf,IAAK,GACD,OAAAA,EAAG,KAAK,KAAK,CAAC,EAAG,EAAC,CAAI,CAAC,CAAC,EACjB,CAAC,EAAa,KAAK,aAAa,CAC/B,OAAQ,kBACR,OAAQ,CACJ,YAAaS,EAAK,OAAOS,CAAW,CACxE,CACA,CAA6B,CAAC,EACV,IAAK,GACD,OAAAC,EAAoBnB,EAAG,KAAI,EACpB,CAAC,EAAcS,EAAK,OAAOU,CAAiB,CAAC,EACxD,IAAK,GACD,MAAAmB,EAAMtC,EAAG,KAAI,EACP,IAAI,QAAQF,EAA+CwC,GAAI,YAAc,MAAQxC,IAAO,OAAS,OAASA,EAAG,KAAKwC,CAAG,IAAM,4BAA4B,EACrK,IAAK,GAAG,MAAO,CAAC,CAAC,CACrC,CACY,CAAC,CACL,CAAC,CACL,EACAH,EAAc,UAAU,oBAAsB,SAAUf,EAAc,CAClE,IAAItB,EACJ,OAAOnC,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAI0D,EAAoBkB,EACxB,OAAO9D,EAAY,KAAM,SAAUuB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1CA,EAAG,MAAQ,EACf,IAAK,GACD,OAAAA,EAAG,KAAK,KAAK,CAAC,EAAG,EAAC,CAAI,CAAC,CAAC,EACjB,CAAC,EAAa,KAAK,aAAa,CAC/B,OAAQ,sBACR,OAAQ,CACJ,aAAcoB,EAAa,IAAI,SAAUF,EAAa,CAAE,OAAOT,EAAK,OAAOS,CAAW,CAAG,CAAC,CAC9H,CACA,CAA6B,CAAC,EACV,IAAK,GACD,OAAAG,EAAqBrB,EAAG,KAAI,EACrB,CAAC,EAAcqB,EAAmB,IAAI,SAAUH,EAAa,CAAE,OAAOT,EAAK,OAAOS,CAAW,CAAG,CAAC,CAAC,EAC7G,IAAK,GACD,MAAAqB,EAAMvC,EAAG,KAAI,EACP,IAAI,QAAQF,EAA+CyC,GAAI,YAAc,MAAQzC,IAAO,OAAS,OAASA,EAAG,KAAKyC,CAAG,IAAM,6BAA6B,EACtK,IAAK,GAAG,MAAO,CAAC,CAAC,CACrC,CACY,CAAC,CACL,CAAC,CACL,EACAJ,EAAc,UAAU,uBAAyB,SAAUjB,EAAaI,EAAS,CAC7E,IAAIxB,EACJ,OAAOnC,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIa,EAAQgE,EACZ,OAAO/D,EAAY,KAAM,SAAUuB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1CA,EAAG,MAAQ,EACf,IAAK,GACD,OAAAA,EAAG,KAAK,KAAK,CAAC,EAAG,EAAC,CAAI,CAAC,CAAC,EACjB,CAAC,EAAa,KAAK,aAAa,CAC/B,OAAQ,yBACR,OAAQ,CACJ,YAAaS,EAAK,OAAOS,CAAW,EACpC,QAASI,CAC7C,CACA,CAA6B,CAAC,EACV,IAAK,GACD,OAAA9C,EAASwB,EAAG,KAAI,EACT,CAAC,EAAcxB,CAAM,EAChC,IAAK,GACD,MAAAgE,EAAMxC,EAAG,KAAI,EACP,IAAI,QAAQF,EAA+C0C,GAAI,YAAc,MAAQ1C,IAAO,OAAS,OAASA,EAAG,KAAK0C,CAAG,IAAM,qCAAqC,EAC9K,IAAK,GAAG,MAAO,CAAC,CAAC,CACrC,CACY,CAAC,CACL,CAAC,CACL,EACAL,EAAc,UAAU,YAAc,SAAU/B,EAAMC,EAAS,CAC3D,IAAIP,EACJ,OAAIO,IAAY,SAAUA,EAAU,OAC7B1C,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIa,EAAQiE,EACZ,OAAOhE,EAAY,KAAM,SAAUuB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1CA,EAAG,MAAQ,EACf,IAAK,GACD,OAAAA,EAAG,KAAK,KAAK,CAAC,EAAG,EAAC,CAAI,CAAC,CAAC,EACjB,CAAC,EAAa,KAAK,aAAa,CAC/B,OAAQ,cACR,OAAQ,CACJ,KAAMI,EACN,QAASC,CAC7C,CACA,CAA6B,CAAC,EACV,IAAK,GACD,OAAA7B,EAASwB,EAAG,KAAI,EACT,CAAC,EAAc,WAAW,KAAKS,EAAK,OAAOjC,CAAM,CAAC,CAAC,EAC9D,IAAK,GACD,MAAAiE,EAAMzC,EAAG,KAAI,EACP,IAAI,QAAQF,EAA+C2C,GAAI,YAAc,MAAQ3C,IAAO,OAAS,OAASA,EAAG,KAAK2C,CAAG,IAAM,wBAAwB,EACjK,IAAK,GAAG,MAAO,CAAC,CAAC,CACrC,CACY,CAAC,CACL,CAAC,CACL,EACON,CACX,EAAEhF,CAAa,ECvQR,SAASuF,EAA4BxB,EAAa,CACrD,OAAOA,EAAY,UAAY,MACnC,CCFO,IAAIyB,EAAU,QCAjB9F,EAAyC,UAAY,CACrD,IAAIC,EAAgB,SAAUC,EAAGC,EAAG,CAChC,OAAAF,EAAgB,OAAO,gBAClB,CAAE,UAAW,CAAA,aAAgB,OAAS,SAAUC,EAAGC,EAAG,CAAED,EAAE,UAAYC,CAAG,GAC1E,SAAUD,EAAGC,EAAG,CAAE,QAASC,KAAKD,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGC,CAAC,IAAGF,EAAEE,CAAC,EAAID,EAAEC,CAAC,EAAG,EAC7FH,EAAcC,EAAGC,CAAC,CAC7B,EACA,OAAO,SAAUD,EAAGC,EAAG,CACnB,GAAI,OAAOA,GAAM,YAAcA,IAAM,KACjC,MAAM,IAAI,UAAU,uBAAyB,OAAOA,CAAC,EAAI,+BAA+B,EAC5FF,EAAcC,EAAGC,CAAC,EAClB,SAASE,GAAK,CAAE,KAAK,YAAcH,CAAG,CACtCA,EAAE,UAAYC,IAAM,KAAO,OAAO,OAAOA,CAAC,GAAKE,EAAG,UAAYF,EAAE,UAAW,IAAIE,EACnF,CACJ,EAAC,EACGI,EAAsC,UAAY,CAClD,OAAAA,EAAW,OAAO,QAAU,SAASC,EAAG,CACpC,QAASC,EAAGC,EAAI,EAAGC,EAAI,UAAU,OAAQD,EAAIC,EAAGD,IAAK,CACjDD,EAAI,UAAUC,CAAC,EACf,QAASR,KAAKO,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGP,CAAC,IAC1DM,EAAEN,CAAC,EAAIO,EAAEP,CAAC,EAClB,CACA,OAAOM,CACX,EACOD,EAAS,MAAM,KAAM,SAAS,CACzC,EACIK,EAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAG,CAAC,CAAG,CAC3G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC1F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC7F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAG,CAC7GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAA,CAAE,GAAG,MAAM,CACxE,CAAC,CACL,EACIY,EAA4C,SAAUb,EAASc,EAAM,CACrE,IAAIC,EAAI,CAAE,MAAO,EAAG,KAAM,UAAW,CAAE,GAAIpB,EAAE,CAAC,EAAI,EAAG,MAAMA,EAAE,CAAC,EAAG,OAAOA,EAAE,CAAC,CAAG,EAAG,KAAM,CAAA,EAAI,IAAK,CAAA,CAAE,EAAIqB,EAAGC,EAAGtB,EAAGuB,EAC/G,OAAOA,EAAI,CAAE,KAAMC,EAAK,CAAC,EAAG,MAASA,EAAK,CAAC,EAAG,OAAUA,EAAK,CAAC,CAAC,EAAI,OAAO,QAAW,aAAeD,EAAE,OAAO,QAAQ,EAAI,UAAW,CAAE,OAAO,IAAM,GAAIA,EACvJ,SAASC,EAAKrB,EAAG,CAAE,OAAO,SAAUsB,EAAG,CAAE,OAAOX,EAAK,CAACX,EAAGsB,CAAC,CAAC,CAAG,CAAG,CACjE,SAASX,EAAKY,EAAI,CACd,GAAIL,EAAG,MAAM,IAAI,UAAU,iCAAiC,EAC5D,KAAOE,IAAMA,EAAI,EAAGG,EAAG,CAAC,IAAMN,EAAI,IAAKA,GAAG,GAAI,CAC1C,GAAIC,EAAI,EAAGC,IAAMtB,EAAI0B,EAAG,CAAC,EAAI,EAAIJ,EAAE,OAAYI,EAAG,CAAC,EAAIJ,EAAE,SAActB,EAAIsB,EAAE,SAActB,EAAE,KAAKsB,CAAC,EAAG,GAAKA,EAAE,OAAS,EAAEtB,EAAIA,EAAE,KAAKsB,EAAGI,EAAG,CAAC,CAAC,GAAG,KAAM,OAAO1B,EAE3J,OADIsB,EAAI,EAAGtB,IAAG0B,EAAK,CAACA,EAAG,CAAC,EAAI,EAAG1B,EAAE,KAAK,GAC9B0B,EAAG,CAAC,EAAC,CACT,IAAK,GAAG,IAAK,GAAG1B,EAAI0B,EAAI,MACxB,IAAK,GAAG,OAAAN,EAAE,QAAgB,CAAE,MAAOM,EAAG,CAAC,EAAG,KAAM,EAAK,EACrD,IAAK,GAAGN,EAAE,QAASE,EAAII,EAAG,CAAC,EAAGA,EAAK,CAAC,CAAC,EAAG,SACxC,IAAK,GAAGA,EAAKN,EAAE,IAAI,MAAOA,EAAE,KAAK,IAAG,EAAI,SACxC,QACI,GAAMpB,EAAIoB,EAAE,KAAM,EAAApB,EAAIA,EAAE,OAAS,GAAKA,EAAEA,EAAE,OAAS,CAAC,KAAO0B,EAAG,CAAC,IAAM,GAAKA,EAAG,CAAC,IAAM,GAAI,CAAEN,EAAI,EAAG,QAAU,CAC3G,GAAIM,EAAG,CAAC,IAAM,IAAM,CAAC1B,GAAM0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK0B,EAAG,CAAC,EAAI1B,EAAE,CAAC,GAAK,CAAEoB,EAAE,MAAQM,EAAG,CAAC,EAAG,KAAO,CACrF,GAAIA,EAAG,CAAC,IAAM,GAAKN,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGA,EAAI0B,EAAI,KAAO,CACpE,GAAI1B,GAAKoB,EAAE,MAAQpB,EAAE,CAAC,EAAG,CAAEoB,EAAE,MAAQpB,EAAE,CAAC,EAAGoB,EAAE,IAAI,KAAKM,CAAE,EAAG,KAAO,CAC9D1B,EAAE,CAAC,GAAGoB,EAAE,IAAI,IAAG,EACnBA,EAAE,KAAK,IAAG,EAAI,QAClC,CACYM,EAAKP,EAAK,KAAKd,EAASe,CAAC,CAC7B,OAASL,EAAG,CAAEW,EAAK,CAAC,EAAGX,CAAC,EAAGO,EAAI,CAAG,QAAC,CAAWD,EAAIrB,EAAI,CAAG,CACzD,GAAI0B,EAAG,CAAC,EAAI,EAAG,MAAMA,EAAG,CAAC,EAAG,MAAO,CAAE,MAAOA,EAAG,CAAC,EAAIA,EAAG,CAAC,EAAI,OAAQ,KAAM,EAAI,CAClF,CACJ,EACI2D,EAAsC,SAASzD,EAAG,CAClD,IAAI3B,EAAI,OAAO,QAAW,YAAc,OAAO,SAAU4B,EAAI5B,GAAK2B,EAAE3B,CAAC,EAAGC,EAAI,EAC5E,GAAI2B,EAAG,OAAOA,EAAE,KAAKD,CAAC,EACtB,GAAIA,GAAK,OAAOA,EAAE,QAAW,SAAU,MAAO,CAC1C,KAAM,UAAY,CACd,OAAIA,GAAK1B,GAAK0B,EAAE,SAAQA,EAAI,QACrB,CAAE,MAAOA,GAAKA,EAAE1B,GAAG,EAAG,KAAM,CAAC0B,CAAC,CACzC,CACR,EACI,MAAM,IAAI,UAAU3B,EAAI,0BAA4B,iCAAiC,CACzF,EAOIqF,GAA0B,SAAUzF,EAAQ,CAC5CP,EAAUgG,EAAUzF,CAAM,EAC1B,SAASyF,EAASC,EAAQ,CACtB,IAAInD,EAAQvC,EAAO,KAAK,IAAI,GAAK,KACjC,OAAAuC,EAAM,SAAW,eACjBA,EAAM,UAAY,KAClBA,EAAM,cAAgB,CAAA,EACtBA,EAAM,iBAAmB,KACzBA,EAAM,SAAW,KACjBA,EAAM,QAAU,KAChBA,EAAM,gBAAkB,KACxBA,EAAM,wBAA0B,KAChCA,EAAM,aAAe,SAAUoD,EAAO,CAClC,IAAIjD,EAAIE,EAAIiB,EAAI+B,EAChB,OAAQD,EAAM,KAAI,CACd,IAAK,qBAAsB,CACvBpD,EAAM,gBAAe,EACrBA,EAAM,iBAAmB,IAAIoB,EAAWpB,EAAM,QAASA,EAAM,WAAYG,EAAKiD,EAAM,QAAU,MAAQjD,IAAO,OAAS,OAASA,EAAG,WAAaH,EAAM,WAAa,+BAA+B,EACjMA,EAAM,iBAAiB,GAAG,UAAWA,EAAM,aAAa,EACxDA,EAAM,iBAAiB,GAAG,aAAcA,EAAM,gBAAgB,EAC9DA,EAAM,iBAAiB,QAAO,EAC9BA,EAAM,qBAAqB,YAAY,EACvC,MACJ,CACA,IAAK,UAAW,CACZA,EAAM,gBAAe,EACrBA,EAAM,iBAAmB,IAAIwC,EAAcxC,EAAM,UAAWK,EAAK+C,EAAM,QAAU,MAAQ/C,IAAO,OAAS,OAASA,EAAG,YAAc,EAAE,EACrIL,EAAM,iBAAiB,QAAO,EAC9BA,EAAM,sBAAsBsB,EAAK8B,EAAM,QAAU,MAAQ9B,IAAO,OAAS,OAASA,EAAG,OAAO,EACxFtB,EAAM,kBACNA,EAAM,gBAAgB,QAAO,EAC7BA,EAAM,gBAAkB,MAE5BA,EAAM,KAAK,UAAWA,EAAM,SAAS,EACrC,MACJ,CACA,IAAK,aAAc,CACXA,EAAM,kBACNA,EAAM,gBAAgB,OAAM,EAC5BA,EAAM,gBAAkB,MAE5BA,EAAM,cAAa,EACnBA,EAAM,KAAK,YAAY,EACvB,MACJ,CACA,IAAK,iBAAkB,CACd,GAAAqD,EAAKD,EAAM,QAAU,MAAQC,IAAO,SAAkBA,EAAG,WAC1DrD,EAAM,iBAAmB,IAAIwC,EAAcxC,EAAM,QAASoD,EAAM,KAAK,SAAS,EAC9EpD,EAAM,iBAAiB,QAAO,EAC9BA,EAAM,KAAK,iBAAkBA,EAAM,SAAS,GAG5CA,EAAM,KAAK,iBAAkB,MAAS,EAE1C,MACJ,CAEA,IAAK,WAAY,CACbA,EAAM,gBAAe,EACrB,MACJ,CACA,QACI,MAEpB,CACQ,EACAA,EAAM,cAAgB,SAAUS,EAAM,CAC9BA,EAAK,aAAe,OAChBA,EAAK,OAAO,OAAS,aACrBT,EAAM,cAAa,EAEdS,EAAK,OAAO,OAAS,QAC1BT,EAAM,gBAAe,EAGpBS,EAAK,aAAe,eACrBT,EAAM,UACNA,EAAM,QAAQ,MAAM,IAAM,SAASS,EAAK,OAAO,GAAG,EAAI,GAAG,OAAOA,EAAK,OAAO,IAAK,IAAI,EAAI,GACzFT,EAAM,QAAQ,MAAM,OAAS,SAASS,EAAK,OAAO,MAAM,EAAI,GAAG,OAAOA,EAAK,OAAO,OAAQ,IAAI,EAAI,GAClGT,EAAM,QAAQ,MAAM,KAAO,SAASS,EAAK,OAAO,IAAI,EAAI,GAAG,OAAOA,EAAK,OAAO,KAAM,IAAI,EAAI,GAC5FT,EAAM,QAAQ,MAAM,MAAQ,SAASS,EAAK,OAAO,KAAK,EAAI,GAAG,OAAOA,EAAK,OAAO,MAAO,IAAI,EAAI,GAC/FT,EAAM,QAAQ,MAAM,MAAQ,SAASS,EAAK,OAAO,KAAK,EAAI,GAAG,OAAOA,EAAK,OAAO,MAAO,IAAI,EAAIA,EAAK,OAAO,MAC3GT,EAAM,QAAQ,MAAM,OAAS,SAASS,EAAK,OAAO,MAAM,EAAI,GAAG,OAAOA,EAAK,OAAO,OAAQ,IAAI,EAAIA,EAAK,OAAO,OAG1H,EACAT,EAAM,eAAiB,SAAUoD,EAAO,CACpC,IAAIjD,EACJ,KAAMA,EAAKiD,EAAM,QAAU,MAAQjD,IAAO,OAAS,OAASA,EAAG,WAAa,gCAG5E,KAAIM,EAAO2C,EAAM,KAAK,MAAQ,CAAA,EAC1B3C,EAAK,OAAS,QACdT,EAAM,aAAaS,EAAK,KAAK,EAExBA,EAAK,OAAS,SACnBT,EAAM,cAAcS,CAAI,EAEnBA,EAAK,OAAS,YACfT,EAAM,kBACNA,EAAM,iBAAiB,cAAcS,CAAI,EAGrD,EACAT,EAAM,eAAiB,UAAY,CAC3BA,EAAM,0BAA4B,OAClC,cAAcA,EAAM,uBAAuB,EAC3CA,EAAM,wBAA0B,MAEhCA,EAAM,WACNA,EAAM,SAAS,OAAM,EACrBA,EAAM,SAAW,KAEzB,EACAA,EAAM,wBAA0B,UAAY,CACxC,IAAI2C,EAAKxC,EACLmD,EAAW,SAAS,uBAAuB,gCAAgC,EAC/E,GAAI,CACA,QAASC,EAAaN,EAASK,CAAQ,EAAGE,EAAeD,EAAW,KAAI,EAAI,CAACC,EAAa,KAAMA,EAAeD,EAAW,KAAI,EAAI,CAC9H,IAAIE,EAAUD,EAAa,MACvBC,EAAQ,eACRA,EAAQ,OAAM,CAEtB,CACJ,OACOC,EAAO,CAAEf,EAAM,CAAE,MAAOe,CAAK,CAAI,QACpD,CACgB,GAAI,CACIF,GAAgB,CAACA,EAAa,OAASrD,EAAKoD,EAAW,SAASpD,EAAG,KAAKoD,CAAU,CAC1F,QAChB,CAA0B,GAAIZ,EAAK,MAAMA,EAAI,KAAO,CACxC,CACJ,EACA3C,EAAM,eAAiB,UAAY,CAC/BA,EAAM,eAAc,EACpBA,EAAM,wBAAuB,EAC7B,IAAIO,EAAS5C,EAASA,EAAS,CAAA,EAAIqC,EAAM,aAAa,EAAG,CAAE,QAASA,EAAM,UAAY,eAAgB,OAAQ,OAAO,SAAS,QAAU,GAAI,MAAO,SAAS,OAAS,GAAI,QAAS,EAAG,WAAYgD,CAAoB,CAAE,EACnNW,EAAmB3D,EAAM,qBAAoB,EAC7C2D,IACApD,EAAO,QAAUoD,GAEjB3D,EAAM,YACNO,EAAO,SAAWP,EAAM,WAE5B,IAAI4D,EAAc,OAAO,KAAKrD,CAAM,EAC/B,IAAI,SAAUsD,EAAK,CAAE,MAAO,GAAG,OAAOA,EAAK,GAAG,EAAE,OAAO,mBAAmBtD,EAAOsD,CAAG,CAAC,CAAC,CAAG,CAAC,EAC1F,KAAK,GAAG,EACTC,EAAY,GAAG,OAAOZ,EAAS,WAAY,GAAG,EAAE,OAAOU,CAAW,EACtE5D,EAAM,SAAW,SAAS,cAAc,KAAK,EAC7CA,EAAM,SAAS,UAAY,iCAC3BA,EAAM,SAAS,UAAY;AAAA,qBAAwB,OAAO8D,EAAW;AAAA,KAA2P,EAChU,SAAS,KAAK,YAAY9D,EAAM,QAAQ,EACxCA,EAAM,QAAUA,EAAM,SAAS,cAAc,QAAQ,EAErD,OAAO,YAAcA,EAAM,qBAC3BA,EAAM,wBAA0B,YAAY,UAAY,CAEpD,OAAO,YAAcA,EAAM,oBAC/B,EAAG,GAAG,EACN,OAAO,iBAAiB,UAAWA,EAAM,eAAgB,EAAK,CAClE,EACAA,EAAM,gBAAkB,UAAY,CAC5BA,EAAM,UACNA,EAAM,QAAQ,MAAM,IAAM,GAC1BA,EAAM,QAAQ,MAAM,MAAQ,GAC5BA,EAAM,QAAQ,MAAM,OAAS,MAC7BA,EAAM,QAAQ,MAAM,MAAQ,MAEpC,EACAA,EAAM,cAAgB,UAAY,CAC1BA,EAAM,UACNA,EAAM,QAAQ,MAAM,IAAM,MAC1BA,EAAM,QAAQ,MAAM,OAAS,MAC7BA,EAAM,QAAQ,MAAM,KAAO,MAC3BA,EAAM,QAAQ,MAAM,MAAQ,MAC5BA,EAAM,QAAQ,MAAM,MAAQ,OAC5BA,EAAM,QAAQ,MAAM,OAAS,OAErC,EACAA,EAAM,qBAAuB,UAAY,CACrC,OAAI,cACO,aAAa,QAAQ,gCAAgC,GAAK,IAGzE,EACAA,EAAM,qBAAuB,SAAU+D,EAAS,CACxC,cAAgBA,GAChB,aAAa,QAAQ,iCAAkCA,CAAO,CAEtE,EACA/D,EAAM,uBAAyB,UAAY,CACnC,cACA,aAAa,WAAW,gCAAgC,CAEhE,EACAA,EAAM,cAAgB,UAAY,CAC1BA,EAAM,kBACNA,EAAM,gBAAgB,QAAO,EAC7BA,EAAM,gBAAkB,MAE5BA,EAAM,KAAK,UAAWA,EAAM,SAAS,CACzC,EACAA,EAAM,iBAAmB,UAAY,CAC7BA,EAAM,kBACNA,EAAM,gBAAgB,OAAM,EAC5BA,EAAM,gBAAkB,MAE5BA,EAAM,cAAa,EACnBA,EAAM,KAAK,YAAY,CAC3B,EACAA,EAAM,cAAgB,UAAY,CAC9B,OAAO,oBAAoB,UAAWA,EAAM,eAAgB,EAAK,EACjEA,EAAM,eAAc,EACpBA,EAAM,uBAAsB,EAC5BA,EAAM,iBAAmB,IAC7B,EACAA,EAAM,qBAAuB,SAAUS,EAAM,CACzC,IAAIN,EAAIE,GACPA,GAAMF,EAAKH,EAAM,WAAa,MAAQG,IAAO,OAAS,OAASA,EAAG,iBAAmB,MAAQE,IAAO,QAAkBA,EAAG,YAAY,CAClI,QAAS,yBACT,KAAMI,CACtB,EAAe,GAAG,CACV,EACoD0C,GAAO,UACvDnD,EAAM,SAA2DmD,GAAO,SAExBA,GAAO,WACvDnD,EAAM,UAA4DmD,GAAO,UAEzBA,GAAO,SACvDnD,EAAM,cAAgBrC,EAAS,CAAA,EAAoDwF,GAAO,MAAM,GAE7FnD,CACX,CACA,cAAO,eAAekD,EAAS,UAAW,YAAa,CACnD,IAAK,UAAY,CACb,IAAI/C,EACJ,QAASA,EAAK,KAAK,oBAAsB,MAAQA,IAAO,OAAS,OAASA,EAAG,YAAc,IAC/F,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACD,OAAO,eAAe+C,EAAS,UAAW,cAAe,CACrD,IAAK,UAAY,CACb,IAAI/C,EACJ,MAAO,CAAC,EAAG,GAAAA,EAAK,KAAK,oBAAsB,MAAQA,IAAO,SAAkBA,EAAG,UACnF,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACD,OAAO,eAAe+C,EAAS,UAAW,YAAa,CACnD,IAAK,UAAY,CACb,OAAO,KAAK,WAChB,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACD,OAAO,eAAeA,EAAS,UAAW,cAAe,CACrD,IAAK,UAAY,CACb,MAAO,EACX,EACA,WAAY,GACZ,aAAc,EACtB,CAAK,EACDA,EAAS,UAAU,QAAU,UAAY,CACrC,OAAOlF,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIgC,EAAQ,KACZ,OAAOlB,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,OAAI,KAAK,UACE,CAAC,CAAC,GAEb,KAAK,eAAc,EACZ,CAAC,EAAa,IAAI,QAAQ,SAAU5B,EAASC,EAAQ,CACpDwB,EAAM,gBAAkB,CAAE,QAASzB,EAAS,OAAQC,CAAM,CAC9D,CAAC,CAAC,GACV,IAAK,GACD,OAAA2B,EAAG,KAAI,EACA,CAAC,CAAC,CACjC,CACY,CAAC,CACL,CAAC,CACL,EACA+C,EAAS,UAAU,WAAa,UAAY,CACxC,OAAOlF,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,OAAOc,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,OAAK,KAAK,iBAGH,CAAC,EAAa,KAAK,iBAAiB,WAAU,CAAE,EAF5C,CAAC,CAAC,EAGjB,IAAK,GACD,OAAAA,EAAG,KAAI,EACP,KAAK,cAAa,EAClB,KAAK,KAAK,YAAY,EACf,CAAC,CAAC,CACjC,CACY,CAAC,CACL,CAAC,CACL,EACA+C,EAAS,UAAU,gBAAkB,SAAU3B,EAAa,CACxD,OAAOvD,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIgG,EAAuBxC,EAC3B,OAAO1C,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,OAAA6D,EAAwBjB,EAA4BxB,CAAW,EAC3D,WAAW,KAAKA,EAAY,UAAU,CAAE,iBAAkB,GAAO,qBAAsB,EAAK,CAAE,CAAC,EAC/FA,EAAY,UAAS,EAClB,CAAC,EAAa,KAAK,iBAAiB,gBAAgByC,CAAqB,CAAC,EACrF,IAAK,GACD,OAAAxC,EAAoBrB,EAAG,KAAI,EACpB,CAAC,EAAc4C,EAA4BxB,CAAW,EAAI0C,EAAY,KAAKzC,CAAiB,EAAI0C,EAAqB,YAAY1C,CAAiB,CAAC,CAClL,CACY,CAAC,CACL,CAAC,CACL,EACA0B,EAAS,UAAU,oBAAsB,SAAUzB,EAAc,CAC7D,OAAOzD,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAImG,EAAwBzC,EAC5B,OAAO5C,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,OAAAgE,EAAyB1C,EAAa,IAAI,SAAUF,EAAa,CAC7D,OAAOwB,EAA4BxB,CAAW,EAC1C,WAAW,KAAKA,EAAY,UAAU,CAAE,iBAAkB,GAAO,qBAAsB,EAAK,CAAE,CAAC,EAC/FA,EAAY,UAAS,CAC7B,CAAC,EACM,CAAC,EAAa,KAAK,iBAAiB,oBAAoB4C,CAAsB,CAAC,EAC1F,IAAK,GAED,GADAzC,EAAqBvB,EAAG,KAAI,EACxBuB,EAAmB,SAAWD,EAAa,OAC3C,MAAM,IAAI,MAAM,iCAAiC,EAErD,MAAO,CAAC,EAAcC,EAAmB,IAAI,SAAUF,EAAmB4C,EAAO,CACzE,OAAOrB,EAA4BtB,EAAa2C,CAAK,CAAC,EAAIH,EAAY,KAAKzC,CAAiB,EAAI0C,EAAqB,YAAY1C,CAAiB,CACtJ,CAAC,CAAC,CAC9B,CACY,CAAC,CACL,CAAC,CACL,EACA0B,EAAS,UAAU,uBAAyB,SAAU3B,EAAaI,EAAS,CACxE,OAAO3D,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,IAAIgG,EACJ,OAAOlF,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,OAAA6D,EAAwBjB,EAA4BxB,CAAW,EAAIA,EAAY,UAAU,CAAE,iBAAkB,GAAO,qBAAsB,EAAK,CAAE,EAAIA,EAAY,UAAS,EACnK,CAAC,EAAa,KAAK,iBAAiB,uBAAuByC,EAAuBrC,CAAO,CAAC,EACrG,IAAK,GAAG,MAAO,CAAC,EAAcxB,EAAG,KAAI,CAAE,CAC3D,CACY,CAAC,CACL,CAAC,CACL,EACA+C,EAAS,UAAU,YAAc,SAAUzC,EAAMC,EAAS,CACtD,OAAIA,IAAY,SAAUA,EAAU,QAC7B1C,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,OAAOc,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GACD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,sBAAsB,EAE1C,MAAO,CAAC,EAAa,KAAK,iBAAiB,YAAYM,EAAMC,CAAO,CAAC,EACzE,IAAK,GAAG,MAAO,CAAC,EAAcP,EAAG,KAAI,CAAE,CAC3D,CACY,CAAC,CACL,CAAC,CACL,EACA+C,EAAS,UAAU,KAAO,SAAUzC,EAAMC,EAAS,CAC/C,OAAIA,IAAY,SAAUA,EAAU,QAC7B1C,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,OAAOc,EAAY,KAAM,SAAUqB,EAAI,CACnC,OAAQA,EAAG,MAAK,CACZ,IAAK,GAAG,MAAO,CAAC,EAAa,KAAK,YAAYM,EAAMC,CAAO,CAAC,EAC5D,IAAK,GAAG,MAAO,CAAC,EAAcP,EAAG,KAAI,CAAE,CAC3D,CACY,CAAC,CACL,CAAC,CACL,EACA+C,EAAS,UAAU,aAAe,SAAUmB,EAAS,CACjD,IAAIlE,EACJ,OAAIkE,IAAY,SAAUA,EAAU,IAC7BrG,EAAU,KAAM,OAAQ,OAAQ,UAAY,CAC/C,OAAOc,EAAY,KAAM,SAAUuB,EAAI,CACnC,OAAI,OAAO,aAAiB,GAAAF,EAAK,OAAO,YAAc,MAAQA,IAAO,SAAkBA,EAAG,WAC/E,CAAC,EAAc,EAAI,EAEvB,CAAC,EAAc,IAAI,QAAQ,SAAU5B,EAAS,CAC7C,IAAI+F,EAAcC,EAClBD,EAAe,YAAY,UAAY,CACnC,IAAInE,GACA,OAAO,aAAiB,GAAAA,EAAK,OAAO,YAAc,MAAQA,IAAO,SAAkBA,EAAG,cACtF,cAAcmE,CAAY,EAC1B,aAAaC,CAAW,EACxBhG,EAAQ,EAAI,EAEpB,EAAG,GAAG,EACNgG,EAAc,WAAW,UAAY,CACjC,cAAcD,CAAY,EAC1B/F,EAAQ,EAAK,CACjB,EAAG8F,EAAU,GAAI,CACrB,CAAC,CAAC,CACV,CAAC,CACL,CAAC,CACL,EACAnB,EAAS,WAAa,gCACfA,CACX,EAAExF,CAAY", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}