import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { Shield, ArrowLeft, RefreshCw } from 'lucide-react';
import { TermsOfServiceModal } from '@/components/modals/TermsOfServiceModal';
import { PrivacyPolicyModal } from '@/components/modals/PrivacyPolicyModal';
import { API_BASE_URL } from '@/config';

export default function Verify2FA() {
  const [code, setCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { handleOAuthCallback } = useAuth();

  // Get the temp token and user data from navigation state
  const tempToken = location.state?.tempToken;
  const user = location.state?.user;

  useEffect(() => {
    // If no temp token, redirect to login
    if (!tempToken) {
      toastError({
        title: 'Session Expired',
        description: 'Please log in again.',
      });
      navigate('/login');
    }
  }, [tempToken, navigate]);

  const handleVerify2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code.trim()) {
      toastError({
        title: 'Error',
        description: 'Please enter the verification code',
      });
      return;
    }

    if (code.length !== 6) {
      toastError({
        title: 'Error',
        description: 'Verification code must be 6 digits',
      });
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/2fa/login`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tempToken}`,
        },
        body: JSON.stringify({
          code: code,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Verification failed');
      }

      // Update auth context with OAuth callback handler
      await handleOAuthCallback(data);

      toastSuccess({
        title: 'Login Successful',
        description: 'Welcome back!',
      });

      // Redirect to dashboard
      navigate('/');

    } catch (error: any) {
      console.error('2FA verification failed:', error);
      toastError({
        title: 'Verification Failed',
        description: error.message || 'Invalid verification code',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendCode = async () => {
    if (!tempToken) return;

    setIsResending(true);
    try {
      const response = await fetch('https://************:5000/api/auth/2fa/resend', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tempToken}`,
        },
        body: JSON.stringify({}),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to resend code');
      }

      toastSuccess({
        title: 'Code Sent',
        description: 'A new verification code has been sent to your email.',
      });

    } catch (error: any) {
      console.error('Failed to resend 2FA code:', error);
      toastError({
        title: 'Failed to Resend',
        description: error.message || 'Failed to resend verification code',
      });
    } finally {
      setIsResending(false);
    }
  };

  if (!tempToken) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
      <div className="flex flex-col space-y-2 text-center">
        <div className="flex justify-center mb-4">
          <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
            <Shield className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        <h1 className="text-2xl font-semibold tracking-tight">Two-Factor Authentication</h1>
        <p className="text-sm text-muted-foreground">
          A 6-digit verification code has been sent to your email address
        </p>
        {user?.email && (
          <p className="text-sm font-medium text-blue-600">
            {user.email}
          </p>
        )}
      </div>

      <div className="grid gap-6">
        <form onSubmit={handleVerify2FA}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="code">Verification Code</Label>
              <Input
                id="code"
                type="text"
                placeholder="Enter 6-digit code"
                value={code}
                onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                disabled={isVerifying}
                maxLength={6}
                className="text-center text-lg tracking-widest"
                autoComplete="one-time-code"
              />
            </div>
            
            <Button 
              type="submit" 
              disabled={isVerifying || code.length !== 6}
              className="w-full"
            >
              {isVerifying ? 'Verifying...' : 'Verify Code'}
            </Button>
          </div>
        </form>

        <div className="space-y-4">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Need help?
              </span>
            </div>
          </div>

          <Button
            variant="outline"
            onClick={handleResendCode}
            disabled={isResending}
            className="w-full"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isResending ? 'animate-spin' : ''}`} />
            {isResending ? 'Sending...' : 'Resend Code'}
          </Button>

          <Button
            variant="ghost"
            onClick={() => navigate('/login')}
            className="w-full"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Login
          </Button>

          <Button
            variant="outline"
            onClick={() => navigate('/2fa-reset-request')}
            className="w-full border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20"
          >
            Lost 2FA Device? Request Reset
          </Button>
        </div>

        <div className="text-center space-y-2">
          <p className="text-xs text-muted-foreground">
            Didn't receive the code? Check your spam folder or{' '}
            <button
              onClick={handleResendCode}
              disabled={isResending}
              className="text-primary hover:underline"
            >
              resend it
            </button>
          </p>
          <p className="text-xs text-muted-foreground">
            By continuing, you agree to our{' '}
            <button
              type="button"
              onClick={() => setShowTermsModal(true)}
              className="text-primary hover:underline"
            >
              Terms of Service
            </button>{' '}
            and{' '}
            <button
              type="button"
              onClick={() => setShowPrivacyModal(true)}
              className="text-primary hover:underline"
            >
              Privacy Policy
            </button>
            .
          </p>
        </div>
      </div>

      {/* Terms of Service Modal */}
      <TermsOfServiceModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
}
