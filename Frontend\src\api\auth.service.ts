import axios from 'axios';
import { ClockSyncManager } from '../utils/clockSync';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://52.196.239.9:5000';
const API_URL = `${API_BASE_URL}/api/auth`;

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token and handle clock sync
api.interceptors.request.use(
  async (config) => {
    // Handle clock synchronization for authentication requests
    const clockSync = ClockSyncManager.getInstance();

    // Check if we need to sync clock before making auth requests
    if (clockSync.needsSync() && !config.url?.includes('/server-time')) {
      try {
        console.log('[AUTH_SERVICE] Performing clock sync before request');
        await clockSync.syncClock();
      } catch (error) {
        console.warn('[AUTH_SERVICE] Clock sync failed, proceeding with request:', error);
      }
    }

    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token refresh and clock sync errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle clock synchronization errors
    if (error.response?.status === 401 &&
        error.response?.data?.message?.includes('Token used too early')) {

      console.log('[AUTH_SERVICE] Clock sync error detected, attempting to fix...');

      try {
        const clockSync = ClockSyncManager.getInstance();
        const syncResult = await clockSync.forceSyncNow();

        if (syncResult.synchronized && !originalRequest._clockSyncRetry) {
          console.log('[AUTH_SERVICE] Clock synchronized, retrying request');
          originalRequest._clockSyncRetry = true;
          return api.request(originalRequest);
        } else {
          console.error('[AUTH_SERVICE] Clock sync failed or already retried');
        }
      } catch (syncError) {
        console.error('[AUTH_SERVICE] Clock sync retry failed:', syncError);
      }
    }

    // If error is 401 and we haven't tried to refresh yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          // No refresh token, log user out
          localStorage.removeItem('access_token');
          window.location.href = '/login';
          return Promise.reject(error);
        }
        
        // Try to refresh the token
        const response = await axios.post(`${API_URL}/refresh`, {}, {
          headers: {
            'Authorization': `Bearer ${refreshToken}`
          }
        });
        
        const { access_token, user } = response.data;
        
        // Update token and user data
        localStorage.setItem('access_token', access_token);
        
        // Update the original request header
        originalRequest.headers.Authorization = `Bearer ${access_token}`;
        
        // If we have user data, update it in the app state
        if (user && window.location.pathname !== '/login') {
          // You might want to update your auth context or store here
          console.log('User data refreshed', user);
        }
        
        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, log the user out
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Get or update user tier info
export async function getUserTierInfo(tier?: number) {
 if (tier !== undefined) {
   // Update tier
   const res = await api.post("/tier", { tier });
   return res.data;
 } else {
   // Get current tier info
   const res = await api.get("/tier");
   return res.data;
 }
}
export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData extends LoginData {
  firstName: string;
  lastName: string;
}

export interface User {
  id: string;
  email: string;
  username?: string;
  full_name: string;
  is_2fa_enabled?: boolean;
  two_fa_enabled?: boolean;
  email_verified: boolean;
  registration_type: 'google' | 'traditional';
  avatar?: string;
  profile_picture?: string;
  tier?: string;
  subscription_status?: string;
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
}

export interface AuthResponse {
  user: User;
  access_token?: string;
  refresh_token?: string;
  requires_2fa?: boolean;
  temp_token?: string;
}

export const authService = {
  async login({ email, password }: LoginData): Promise<AuthResponse> {
    try {
      console.log('[AUTH_SERVICE] Making login request to backend');
      const response = await api.post<any>('/login', { email, password });
      const data = response.data;
      console.log('[AUTH_SERVICE] Backend response:', data);

      // Check if 2FA is required
      if (data.requires_2fa) {
        console.log('[AUTH_SERVICE] 2FA required, returning 2FA response');
        return {
          requires_2fa: true,
          temp_token: data.temp_token,
          user: data.user
        };
      }

      // Normal login response
      console.log('[AUTH_SERVICE] Normal login, storing tokens');
      const { user, access_token, refresh_token } = data;

      localStorage.setItem('access_token', access_token);
      if (refresh_token) {
        localStorage.setItem('refresh_token', refresh_token);
      }

      return { user, access_token, refresh_token };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  async logout(): Promise<void> {
    try {
      await api.post('/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      window.location.href = '/login';
    }
  },

  async register(userData: RegisterData): Promise<void> {
    await api.post('/register', userData);
  },

  async verifyEmail(token: string): Promise<{ user: User }> {
    const response = await api.post('/verify-email', { token });
    return response.data;
  },

  async resendVerification(email: string): Promise<void> {
    await api.post('/resend-verification', { email });
  },

  async getCurrentUser(): Promise<User> {
    try {
      console.log('[AUTH_SERVICE] Fetching current user...');
      const response = await api.get('/me');
      console.log('[AUTH_SERVICE] Current user response:', response.data);
      return response.data.user;
    } catch (error: unknown) {
      console.error('[AUTH_SERVICE] Failed to fetch current user', error);
      const axiosError = error as any;
      console.error('[AUTH_SERVICE] Error details:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data
      });
      throw error;
    }
  },

  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  },

  // Auto-trading endpoints
  async toggleAutoTrading(enabled: boolean): Promise<{ auto_trading_enabled: boolean; message: string }> {
    const response = await api.post('/trading/auto-trade/toggle', { enabled });
    return response.data;
  },




};

export default authService;
