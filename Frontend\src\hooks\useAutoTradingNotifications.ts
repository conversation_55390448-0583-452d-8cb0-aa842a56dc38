import { useEffect, useCallback } from 'react';
import { toastError } from '@/components/ui/use-toast';
import { getAutoTradingNotifications } from '../services/api';
import { useAuth } from '@/contexts/AuthContext';

interface AutoTradingNotification {
  id: string;
  reason: string;
  timestamp: string;
  risk_level: string;
}

export const useAutoTradingNotifications = () => {
  const { isAuthenticated } = useAuth();

  const checkNotifications = useCallback(async () => {
    // Only check notifications if user is authenticated
    if (!isAuthenticated) {
      return;
    }

    try {
      const response = await getAutoTradingNotifications();
      const data = await response.json();

      // Show toast for recent notifications (within last 5 minutes)
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

      data.notifications.forEach((notification: AutoTradingNotification) => {
        const notificationTime = new Date(notification.timestamp);

        // Only show recent notifications
        if (notificationTime > fiveMinutesAgo) {
          toastError({
            title: 'Auto-Trading Disabled',
            description: notification.reason,
          });
        }
      });

    } catch (error) {
      console.error('Failed to check auto-trading notifications:', error);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    // Only set up notifications checking if user is authenticated
    if (!isAuthenticated) {
      return;
    }

    // Check notifications on mount
    checkNotifications();

    // Set up periodic checking every 30 seconds
    const interval = setInterval(checkNotifications, 30000);

    return () => clearInterval(interval);
  }, [checkNotifications, isAuthenticated]);

  return { checkNotifications };
};
