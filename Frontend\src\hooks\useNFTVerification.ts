import { useState, useEffect, useCallback, useRef } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';

interface NFTStatus {
  owns_nft: boolean;
  is_expired: boolean;
}

interface UseNFTVerificationOptions {
  enabled?: boolean; // Whether to run verification
  onNFTLost?: () => void; // Callback when NFT ownership is lost
  onError?: (error: Error) => void; // Callback for errors
}

export const useNFTVerification = (options: UseNFTVerificationOptions = {}) => {
  const { publicKey, connected } = useWallet();
  const { connection } = useConnection();
  const { enabled = true, onNFTLost, onError } = options;

  const [nftStatus, setNftStatus] = useState<NFTStatus>({
    owns_nft: false,
    is_expired: true
  });
  const [isCheckingNFT, setIsCheckingNFT] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use refs to store callbacks to avoid dependency issues
  const onNFTLostRef = useRef(onNFTLost);
  const onErrorRef = useRef(onError);

  // Update refs when callbacks change
  useEffect(() => {
    onNFTLostRef.current = onNFTLost;
    onErrorRef.current = onError;
  }, [onNFTLost, onError]);

  // NFT collection address from tier.tsx
  const nftCollectionAddress = "7qXTaNobP9Zobr4SCYKRdGfHJk44hctGCoTfwN5L5NkL";

  const verifyNFTOwnership = useCallback(async () => {
    if (!publicKey || !enabled) {
      // Reset NFT status when wallet is disconnected or verification is disabled
      setNftStatus({ owns_nft: false, is_expired: true });
      setError(null);
      return;
    }

    try {
      setIsCheckingNFT(true);
      setError(null);

      // Store previous NFT status to detect changes - use functional update to get current state
      let previousStatus = false;
      setNftStatus(prev => {
        previousStatus = prev.owns_nft;
        return { owns_nft: false, is_expired: true };
      });


      const userPublicKey = new PublicKey(publicKey.toString());
      const collectionAddress = nftCollectionAddress; // base58 collection address

      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(userPublicKey, {
        programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'),
      });

      // Check each token account for NFTs from the specified collection
      for (const tokenAccount of tokenAccounts.value) {
        const tokenAmount = tokenAccount.account.data.parsed.info.tokenAmount;
        
        // Skip if no tokens in this account
        if (tokenAmount.uiAmount === 0) continue;

        const mintAddress = tokenAccount.account.data.parsed.info.mint;
        
        // Get metadata account address (PDA)
        const metadataAddress = PublicKey.findProgramAddressSync(
          [
            Buffer.from('metadata'),
            new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s').toBuffer(),
            new PublicKey(mintAddress).toBuffer(),
          ],
          new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')
        )[0];

        const metadataAccount = await connection.getAccountInfo(metadataAddress);
        if (!metadataAccount) continue;

        // Parse the collection field from the metadata buffer
        try {
          // Metaplex metadata: collection field is 1 byte (option), then 32 bytes (address), then 1 byte (verified)
          // We'll search for the collection address as a 32-byte buffer
          const data = metadataAccount.data;
          const collectionKeyBuffer = new PublicKey(collectionAddress).toBuffer();
          let found = false;
          for (let offset = 0; offset <= data.length - 32; offset++) {
            let match = true;
            for (let j = 0; j < 32; j++) {
              if (data[offset + j] !== collectionKeyBuffer[j]) {
                match = false;
                break;
              }
            }
            if (match) {
              found = true;
              break;
            }
          }
          if (found) {
            setNftStatus({ owns_nft: true, is_expired: false });
            return;
          }
        } catch (e) {
          // fallback: do nothing, continue
        }
      }

      // If we reach here, no NFT was found
      const newStatus = { owns_nft: false, is_expired: true };
      setNftStatus(newStatus);
      
      // Call onNFTLost callback if NFT was previously owned but now lost
      if (previousStatus && !newStatus.owns_nft && onNFTLostRef.current) {
        onNFTLostRef.current();
      }
      
    } catch (error) {
      console.error('Error checking NFT ownership:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to verify NFT ownership';
      setError(errorMessage);
      setNftStatus({ owns_nft: false, is_expired: true });
      
      if (onErrorRef.current) {
        onErrorRef.current(error instanceof Error ? error : new Error(errorMessage));
      }
    } finally {
      setIsCheckingNFT(false);
    }
  }, [publicKey, connection, enabled]);

  // Run verification when wallet connects/disconnects or when enabled changes
  useEffect(() => {
    if (connected && publicKey && enabled) {
      verifyNFTOwnership();
    } else {
      // Reset NFT status when wallet is disconnected or verification is disabled
      setNftStatus({ owns_nft: false, is_expired: true });
      setIsCheckingNFT(false);
      setError(null);
    }
  }, [connected, publicKey, enabled]); // Remove verifyNFTOwnership from dependencies

  // Manual refresh function
  const refreshNFTStatus = useCallback(() => {
    if (connected && publicKey && enabled) {
      verifyNFTOwnership();
    }
  }, [connected, publicKey, enabled]); // Remove verifyNFTOwnership from dependencies

  return {
    nftStatus,
    isCheckingNFT,
    error,
    refreshNFTStatus,
    isWalletConnected: connected && !!publicKey
  };
};
