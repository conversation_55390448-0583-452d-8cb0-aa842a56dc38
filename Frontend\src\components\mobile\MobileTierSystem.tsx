/**
 * Mobile Tier System Component for DeepTrade
 *
 * Provides mobile-optimized tier cards, payment flows, and membership
 * status displays with touch-friendly interactions.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGift,
  faStar,
  faCrown,
  faWallet
} from '@fortawesome/free-solid-svg-icons';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../hooks/useTranslation';
import { MobileContainer, MobileHeader, MobileStack } from './ui/MobileLayout';
import { MobileCard, MobileCardHeader, MobileCardContent, MobileCardFooter } from './ui/MobileCard';
import { MobileModal, MobileModalContent, MobileModalFooter } from './ui/MobileModal';
import { Button } from '@/components/ui/Button';
import { toastError, toastSuccess } from '@/components/ui/use-toast';

interface MobileTierSystemProps {
  // Props are optional as we'll fetch real data
}

interface TierData {
  id: number;
  name: string;
  description: string;
  price: number;
  features: string[];
  icon: any;
  color: string;
  gradient: string;
}

const MobileTierSystem: React.FC<MobileTierSystemProps> = () => {
  const { isMobile } = useMobile();
  const { user } = useAuth();
  const { t } = useTranslation();
  
  // State management
  const [, setTierStatus] = useState<any>(null);
  const [selectedTier, setSelectedTier] = useState<number | null>(null);
  const [currentTier, setCurrentTier] = useState<number>(1);
  const [loading, setLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [, setPendingTier] = useState<number | null>(null);

  if (!isMobile) return null;

  // Tier definitions
  const tiers: TierData[] = [
    {
      id: 1,
      name: 'Free Tier',
      description: 'Basic trading features for beginners',
      price: 0,
      features: [
        'Basic trading signals',
        'Daily market analysis',
        'Community access',
      ],
      icon: faGift,
      color: 'gray',
      gradient: 'from-gray-400 to-gray-600',
    },
    {
      id: 2,
      name: 'Pro Tier',
      description: 'Advanced features for serious traders',
      price: 199,
      features: [
        'Advanced trading signals',
        'Auto-trading capabilities',
        'Priority support',
        'Risk management tools',
      ],
      icon: faStar,
      color: 'blue',
      gradient: 'from-blue-500 to-blue-700',
    },
    {
      id: 3,
      name: 'Premium Tier',
      description: 'Exclusive features for NFT holders',
      price: 0,
      features: [
        'Premium trading signals',
        'Exclusive strategies',
        'Personalized support',
        'Early access to features',
      ],
      icon: faCrown,
      color: 'purple',
      gradient: 'from-purple-500 to-purple-700',
    },
  ];

  // Data fetching
  const fetchTierStatus = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/trading/tier/status', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setTierStatus(data);
        
        // Determine current tier
        if (data.tier_3) {
          setCurrentTier(3);
          setSelectedTier(3);
        } else if (data.tier_2) {
          setCurrentTier(2);
          setSelectedTier(2);
        } else {
          setCurrentTier(1);
          setSelectedTier(1);
        }
      }
    } catch (error) {
      console.error('Error fetching tier status:', error);
      toastError({
        title: 'Error',
        description: 'Failed to load tier information',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchTierStatus();
    }
  }, [user]);

  // Tier activation handler
  const handleTierActivation = async (tierId: number) => {
    if (tierId === currentTier) return;
    
    if (tierId === 2) {
      setPendingTier(tierId);
      setShowPaymentModal(true);
    } else if (tierId === 3) {
      // NFT verification logic would go here
      toastError({
        title: 'NFT Required',
        description: 'Tier 3 requires NFT ownership verification',
      });
    } else {
      // Tier 1 activation
      try {
        const token = localStorage.getItem('access_token');
        const response = await fetch('/api/trading/tier/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ tier_1: true, tier_2: false, tier_3: false })
        });

        if (response.ok) {
          const data = await response.json();
          setTierStatus(data);
          setCurrentTier(1);
          setSelectedTier(1);
          toastSuccess({
            title: 'Success!',
            description: 'Your tier has been updated to Free Tier.',
          });
        }
      } catch (error) {
        toastError({
          title: 'Error',
          description: 'Failed to update tier',
        });
      }
    }
  };

  // Payment handler (mock for now)
  const handlePayment = async () => {
    try {
      // Mock payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/trading/tier/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ tier_1: false, tier_2: true, tier_3: false })
      });

      if (response.ok) {
        const data = await response.json();
        setTierStatus(data);
        setCurrentTier(2);
        setSelectedTier(2);
        setShowPaymentModal(false);
        setPendingTier(null);
        
        toastSuccess({
          title: 'Payment Successful!',
          description: 'Welcome to Tier 2! You now have access to advanced features.',
        });
      }
    } catch (error) {
      toastError({
        title: 'Payment Failed',
        description: 'Please try again or contact support',
      });
    }
  };

  const getTierCardColor = (tier: TierData, isSelected: boolean, isCurrent: boolean) => {
    if (isCurrent) {
      return `border-2 border-${tier.color}-500 bg-gradient-to-br from-${tier.color}-50 to-${tier.color}-100 dark:from-${tier.color}-900/20 dark:to-${tier.color}-800/20`;
    }
    if (isSelected) {
      return `border-2 border-${tier.color}-300 bg-${tier.color}-50 dark:bg-${tier.color}-900/10`;
    }
    return 'border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MobileHeader title="Tier Management" />
        <MobileContainer padding="md">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        </MobileContainer>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Compact Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-20">
        <div className="px-4 py-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Tier Management
            </span>
            <button
              onClick={fetchTierStatus}
              className="p-1 rounded text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Tier Cards */}
      <MobileContainer padding="md">
        <MobileStack spacing="lg">
          {tiers.map((tier) => {
            const isCurrent = currentTier === tier.id;
            const isSelected = selectedTier === tier.id;
            
            return (
              <MobileCard
                key={tier.id}
                className={getTierCardColor(tier, isSelected, isCurrent)}
                interactive={!isCurrent}
                onClick={() => !isCurrent && setSelectedTier(tier.id)}
              >
                <MobileCardHeader
                  icon={<FontAwesomeIcon icon={tier.icon} className="w-6 h-6" />}
                  action={
                    isCurrent ? (
                      <div className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-3 py-1 rounded-full text-xs font-medium">
                        {t('tiers.current')}
                      </div>
                    ) : tier.id === 3 ? (
                      <div className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 px-3 py-1 rounded-full text-xs font-medium">
                        {t('tiers.nftRequired')}
                      </div>
                    ) : tier.price > 0 ? (
                      <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-3 py-1 rounded-full text-xs font-medium">
                        ${tier.price}/30 days
                      </div>
                    ) : (
                      <div className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-xs font-medium">
                        {t('tiers.free')}
                      </div>
                    )
                  }
                >
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                    {tier.name}
                  </h3>
                </MobileCardHeader>

                <MobileCardContent>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {tier.description}
                  </p>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                      {t('tiers.features')}:
                    </h4>
                    <ul className="space-y-2">
                      {tier.features.map((feature, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-green-500 mt-0.5">✓</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {feature}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </MobileCardContent>

                {!isCurrent && (
                  <MobileCardFooter>
                    <Button
                      size="mobile-touch"
                      className={`w-full bg-gradient-to-r ${tier.gradient} hover:opacity-90 text-white rounded-xl shadow-sm`}
                      onClick={() => handleTierActivation(tier.id)}
                      disabled={tier.id === 3} // NFT tier disabled for now
                    >
                      {tier.id === 3 ? t('tiers.comingSoon') : t('tiers.activate')}
                    </Button>
                  </MobileCardFooter>
                )}
              </MobileCard>
            );
          })}
        </MobileStack>
      </MobileContainer>

      {/* Payment Modal */}
      <MobileModal
        isOpen={showPaymentModal}
        onClose={() => {
          setShowPaymentModal(false);
          setPendingTier(null);
        }}
        title={t('tiers.payment.title')}
        size="lg"
        position="bottom"
      >
        <MobileModalContent>
          <div className="text-center py-6">
            <div className="flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faWallet} className="w-12 h-12 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              {t('tiers.payment.upgradeToTier2')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {t('tiers.payment.description')}
            </p>
            
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 mb-6">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                $199 USDT
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {t('tiers.payment.for30Days')}
              </div>
            </div>
          </div>
        </MobileModalContent>
        
        <MobileModalFooter layout="stacked">
          <Button
            size="mobile-touch"
            className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-sm"
            onClick={handlePayment}
          >
            {t('tiers.payment.payNow')}
          </Button>
          <Button
            size="mobile-touch"
            variant="outline"
            className="w-full rounded-xl border-2"
            onClick={() => {
              setShowPaymentModal(false);
              setPendingTier(null);
            }}
          >
            {t('common.cancel')}
          </Button>
        </MobileModalFooter>
      </MobileModal>
    </div>
  );
};

export default MobileTierSystem;
