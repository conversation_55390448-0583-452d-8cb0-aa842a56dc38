/**
 * Mobile Navigation Test Component
 * 
 * This component helps debug mobile navigation issues by showing:
 * 1. Current screen dimensions and orientation
 * 2. Mobile detection status
 * 3. Navigation visibility status
 * 4. Wallet dropdown positioning
 */

import React, { useState, useEffect } from 'react';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';
import MobileTopBar from '../mobile/MobileTopBar';
import NavbarWalletButton from '../solana/NavbarWalletButton';

interface DebugInfo {
  screenWidth: number;
  screenHeight: number;
  orientation: 'portrait' | 'landscape';
  isMobile: boolean;
  isPortrait: boolean;
  isLandscape: boolean;
  userAgent: string;
}

const MobileNavigationTest: React.FC = () => {
  const { isMobile, isPortrait, isLandscape } = useMobile();
  const { user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    screenWidth: 0,
    screenHeight: 0,
    orientation: 'portrait',
    isMobile: false,
    isPortrait: true,
    isLandscape: false,
    userAgent: ''
  });

  const [showWalletTest, setShowWalletTest] = useState(false);

  useEffect(() => {
    const updateDebugInfo = () => {
      setDebugInfo({
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight,
        orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait',
        isMobile,
        isPortrait,
        isLandscape,
        userAgent: navigator.userAgent
      });
    };

    updateDebugInfo();
    window.addEventListener('resize', updateDebugInfo);
    window.addEventListener('orientationchange', updateDebugInfo);

    return () => {
      window.removeEventListener('resize', updateDebugInfo);
      window.removeEventListener('orientationchange', updateDebugInfo);
    };
  }, [isMobile, isPortrait, isLandscape]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Test Mobile Top Bar */}
      <MobileTopBar 
        balance="$1,234.56"
        autoTradingEnabled={true}
      />

      <div className="p-4 space-y-6">
        {/* Debug Information Panel */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
          <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Mobile Navigation Debug Info
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Screen Info</h3>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>Width: {debugInfo.screenWidth}px</li>
                <li>Height: {debugInfo.screenHeight}px</li>
                <li>Orientation: {debugInfo.orientation}</li>
                <li>Aspect Ratio: {(debugInfo.screenWidth / debugInfo.screenHeight).toFixed(2)}</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Mobile Detection</h3>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>isMobile: <span className={isMobile ? 'text-green-600' : 'text-red-600'}>{isMobile.toString()}</span></li>
                <li>isPortrait: <span className={isPortrait ? 'text-green-600' : 'text-red-600'}>{isPortrait.toString()}</span></li>
                <li>isLandscape: <span className={isLandscape ? 'text-green-600' : 'text-red-600'}>{isLandscape.toString()}</span></li>
              </ul>
            </div>
          </div>

          <div className="mt-4">
            <h3 className="font-medium text-gray-700 dark:text-gray-300 mb-2">User Agent</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 break-all">
              {debugInfo.userAgent}
            </p>
          </div>
        </div>

        {/* Navigation Visibility Test */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
          <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Navigation Visibility Test
          </h2>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
              <span>Mobile Top Bar Visible</span>
              <span className="text-green-600">✓ Should be visible above</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
              <span>Hamburger Menu Button</span>
              <span className={isMobile ? 'text-green-600' : 'text-red-600'}>
                {isMobile ? '✓ Should be visible in top bar' : '✗ Not mobile mode'}
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
              <span>User Status</span>
              <span className={user ? 'text-green-600' : 'text-yellow-600'}>
                {user ? '✓ Logged in' : '⚠ Not logged in'}
              </span>
            </div>
          </div>
        </div>

        {/* Wallet Dropdown Test */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
          <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Wallet Dropdown Test
          </h2>
          
          <div className="space-y-4">
            <button
              onClick={() => setShowWalletTest(!showWalletTest)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
            >
              {showWalletTest ? 'Hide' : 'Show'} Wallet Test
            </button>
            
            {showWalletTest && (
              <div className="relative">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Test wallet button (should show dropdown when clicked):
                </p>
                <div className="relative inline-block">
                  <NavbarWalletButton />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  If dropdown doesn't appear, there's a z-index or positioning issue.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* CSS Classes Test */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
          <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            CSS Classes Test
          </h2>
          
          <div className="space-y-2 text-sm">
            <div className="lg:hidden bg-green-100 dark:bg-green-900 p-2 rounded">
              lg:hidden - Should show on screens &lt; 1024px
            </div>
            <div className="md:hidden bg-blue-100 dark:bg-blue-900 p-2 rounded">
              md:hidden - Should show on screens &lt; 768px
            </div>
            <div className="sm:hidden bg-yellow-100 dark:bg-yellow-900 p-2 rounded">
              sm:hidden - Should show on screens &lt; 640px
            </div>
            <div className="hidden lg:block bg-red-100 dark:bg-red-900 p-2 rounded">
              hidden lg:block - Should only show on screens ≥ 1024px
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileNavigationTest;
