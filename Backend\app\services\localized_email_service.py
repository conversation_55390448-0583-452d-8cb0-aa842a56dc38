"""
Localized email service for sending multi-language email notifications.
"""
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from flask import current_app, render_template_string
import logging
from jinja2 import Environment, FileSystemLoader, select_autoescape

logger = logging.getLogger(__name__)

class LocalizedEmailService:
    """Service for sending localized email notifications."""
    
    # Supported languages with their display names
    SUPPORTED_LANGUAGES = {
        'en': 'English',
        'es': 'Español', 
        'fr': 'Français',
        'de': 'Deutsch',
        'pt': 'Português',
        'ja': '日本語',
        'ko': '한국어',
        'zh': '中文'
    }
    
    # Email subject translations
    SUBJECTS = {
        'payday_warning': {
            'en': '⚠️ DeepTrade Payment Reminder - {days} Day{plural} Left',
            'es': '⚠️ Recordatorio de Pago DeepTrade - {days} Día{plural} Restante{plural}',
            'fr': '⚠️ Rappel de Paiement DeepTrade - {days} Jour{plural} Restant{plural}',
            'de': '⚠️ DeepTrade Zahlungserinnerung - {days} Tag{plural} Verbleibend',
            'pt': '⚠️ Lembrete de Pagamento DeepTrade - {days} Dia{plural} Restante{plural}',
            'ja': '⚠️ DeepTrade 支払いリマインダー - 残り{days}日',
            'ko': '⚠️ DeepTrade 결제 알림 - {days}일 남음',
            'zh': '⚠️ DeepTrade 付款提醒 - 剩余{days}天'
        }
    }
    
    def __init__(self):
        # Use Brevo SMTP configuration
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp-relay.brevo.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
        self.smtp_username = os.getenv('SMTP_USERNAME', '')
        self.smtp_password = os.getenv('SMTP_PASSWORD', '')
        # Use FROM_EMAIL2 for alerts
        self.from_email = os.getenv('FROM_EMAIL2', os.getenv('FROM_EMAIL', '<EMAIL>'))
        self.from_name = os.getenv('FROM_NAME', 'DeepTrade Alerts')
        
        # Setup Jinja2 environment for email templates
        template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'templates', 'emails')
        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(['html', 'xml'])
        )
    
    def get_user_language(self, user):
        """Get user's preferred language, fallback to English."""
        if hasattr(user, 'language_preference') and user.language_preference:
            lang = user.language_preference.lower()
            if lang in self.SUPPORTED_LANGUAGES:
                return lang
        return 'en'  # Default to English
    
    def get_plural_suffix(self, language, count):
        """Get plural suffix for different languages."""
        if count <= 1:
            return ''
        
        plural_rules = {
            'en': 's',
            'es': 's', 
            'fr': 's',
            'de': 'e',
            'pt': 's',
            'ja': '',  # Japanese doesn't use plurals
            'ko': '',  # Korean doesn't use plurals
            'zh': ''   # Chinese doesn't use plurals
        }
        
        return plural_rules.get(language, 's')
    
    def format_subject(self, template_key, language, **kwargs):
        """Format email subject in the specified language."""
        if template_key not in self.SUBJECTS:
            return f"DeepTrade Notification"
        
        if language not in self.SUBJECTS[template_key]:
            language = 'en'  # Fallback to English
        
        subject_template = self.SUBJECTS[template_key][language]
        
        # Handle pluralization
        if 'days' in kwargs:
            days = kwargs['days']
            kwargs['plural'] = self.get_plural_suffix(language, days)
        
        try:
            return subject_template.format(**kwargs)
        except KeyError as e:
            logger.warning(f"Missing template variable {e} for subject {template_key} in {language}")
            return self.SUBJECTS[template_key]['en'].format(**kwargs)
    
    def send_email(self, to_email, subject, html_content, text_content=None):
        """Send an email."""
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = to_email
            
            # Add text version if provided
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)
            
            # Add HTML version
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Localized email sent successfully to {to_email}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send localized email to {to_email}: {str(e)}")
            return False
    
    def render_template(self, template_name, language, **context):
        """Render email template in specified language."""
        template_path = f"{language}/{template_name}"
        try:
            # Try to load language-specific template
            template = self.jinja_env.get_template(template_path)
            # Remove 'language' from context if it exists to avoid conflicts
            template_context = {k: v for k, v in context.items() if k != 'language'}
            return template.render(**template_context)
        except Exception as e:
            logger.warning(f"Failed to load template {template_path}: {e}")

            # Fallback to English template
            try:
                template_path = f"en/{template_name}"
                template = self.jinja_env.get_template(template_path)
                # Remove 'language' from context if it exists to avoid conflicts
                template_context = {k: v for k, v in context.items() if k != 'language'}
                return template.render(**template_context)
            except Exception as e2:
                logger.error(f"Failed to load fallback English template {template_path}: {e2}")
                return None
    
    def send_payday_warning_email(self, user, tier_status, days_before):
        """Send localized payday warning email."""
        try:
            # Get user's preferred language
            language = self.get_user_language(user)
            
            # Format subject
            subject = self.format_subject('payday_warning', language, days=days_before)
            
            # Format deadline
            deadline_str = tier_status.next_payday_deadline.strftime('%A, %B %d, %Y at %H:%M UTC')
            
            # Prepare template context
            context = {
                'user_name': user.username or user.email.split('@')[0],
                'days_before': days_before,
                'days_plural': self.get_plural_suffix(language, days_before),
                'amount_due': f"{tier_status.profit_share_owed:.6f}",
                'deadline': deadline_str,
                'current_year': datetime.now().year
            }

            # Render HTML template
            html_content = self.render_template('payday_warning.html', language, **context)
            
            if not html_content:
                logger.error(f"Failed to render email template for user {user.email}")
                return False
            
            # Send email
            return self.send_email(user.email, subject, html_content)
            
        except Exception as e:
            logger.error(f"Failed to send payday warning email to {user.email}: {str(e)}")
            return False
    
    def send_account_disabled_email(self, user, tier_status):
        """Send localized account disabled email."""
        try:
            # Get user's preferred language
            language = self.get_user_language(user)
            
            # Format subject (you can add this to SUBJECTS dict)
            subject_templates = {
                'en': '🚫 DeepTrade Account Disabled - Payment Required',
                'es': '🚫 Cuenta DeepTrade Deshabilitada - Pago Requerido',
                'fr': '🚫 Compte DeepTrade Désactivé - Paiement Requis',
                'de': '🚫 DeepTrade Konto Deaktiviert - Zahlung Erforderlich',
                'pt': '🚫 Conta DeepTrade Desabilitada - Pagamento Necessário',
                'ja': '🚫 DeepTrade アカウント無効 - 支払いが必要',
                'ko': '🚫 DeepTrade 계정 비활성화 - 결제 필요',
                'zh': '🚫 DeepTrade 账户已禁用 - 需要付款'
            }
            
            subject = subject_templates.get(language, subject_templates['en'])
            
            # Prepare template context
            context = {
                'user_name': user.username or user.email.split('@')[0],
                'amount_due': f"{tier_status.profit_share_owed:.6f}",
                'current_year': datetime.now().year,
                'language': language
            }
            
            # For now, use a simple HTML template (you can create account_disabled.html templates later)
            html_content = f"""
            <h1>Account Disabled</h1>
            <p>Hello {context['user_name']},</p>
            <p>Your DeepTrade account has been disabled due to unpaid profit share fees.</p>
            <p>Amount due: ${context['amount_due']} USDT</p>
            <p>Please log in and make payment to reactivate your account.</p>
            """
            
            return self.send_email(user.email, subject, html_content)
            
        except Exception as e:
            logger.error(f"Failed to send account disabled email to {user.email}: {str(e)}")
            return False

# Create global instance
localized_email_service = LocalizedEmailService()
