"use client"

import * as React from "react"
import * as ToastPrimitives from "@radix-ui/react-toast"
import { cva, type VariantProps } from "class-variance-authority"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const ToastProvider = ToastPrimitives.Provider

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
      className
    )}
    {...props}
  />
))
ToastViewport.displayName = ToastPrimitives.Viewport.displayName

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-lg border p-6 pr-8 shadow-xl backdrop-blur-sm transition-all duration-300 ease-in-out data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
  {
    variants: {
      variant: {
        default: "border-gray-200 bg-white/90 text-gray-900 shadow-lg dark:border-gray-700 dark:bg-gray-800/90 dark:text-gray-100",
        destructive:
          "destructive group border-red-200 bg-red-50/95 text-red-900 shadow-lg shadow-red-500/20 dark:border-red-800 dark:bg-red-950/95 dark:text-red-100 dark:shadow-red-900/30",
        success:
          "success group border-green-200 bg-green-50/95 text-green-900 shadow-lg shadow-green-500/20 dark:border-green-800 dark:bg-green-950/95 dark:text-green-100 dark:shadow-green-900/30",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return (
    <ToastPrimitives.Root
      ref={ref}
      className={cn(toastVariants({ variant }), className)}
      {...props}
    />
  )
})
Toast.displayName = ToastPrimitives.Root.displayName

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border border-gray-300 bg-white/80 px-3 text-sm font-medium text-gray-700 transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-red-300 group-[.destructive]:bg-red-50/80 group-[.destructive]:text-red-700 group-[.destructive]:hover:bg-red-100 group-[.destructive]:hover:border-red-400 group-[.destructive]:focus:ring-red-400 group-[.success]:border-green-300 group-[.success]:bg-green-50/80 group-[.success]:text-green-700 group-[.success]:hover:bg-green-100 group-[.success]:hover:border-green-400 group-[.success]:focus:ring-green-400 dark:border-gray-600 dark:bg-gray-700/80 dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:border-gray-500 dark:focus:ring-gray-500 dark:group-[.destructive]:border-red-700 dark:group-[.destructive]:bg-red-900/80 dark:group-[.destructive]:text-red-200 dark:group-[.destructive]:hover:bg-red-800 dark:group-[.destructive]:hover:border-red-600 dark:group-[.destructive]:focus:ring-red-500 dark:group-[.success]:border-green-700 dark:group-[.success]:bg-green-900/80 dark:group-[.success]:text-green-200 dark:group-[.success]:hover:bg-green-800 dark:group-[.success]:hover:border-green-600 dark:group-[.success]:focus:ring-green-500",
      className
    )}
    {...props}
  />
))
ToastAction.displayName = ToastPrimitives.Action.displayName

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute right-2 top-2 rounded-md p-1 text-gray-500 opacity-0 transition-all duration-200 hover:text-gray-700 hover:bg-gray-100/50 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 group-hover:opacity-100 group-[.destructive]:text-red-500 group-[.destructive]:hover:text-red-700 group-[.destructive]:hover:bg-red-100/50 group-[.destructive]:focus:ring-red-400 group-[.success]:text-green-500 group-[.success]:hover:text-green-700 group-[.success]:hover:bg-green-100/50 group-[.success]:focus:ring-green-400 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700/50 dark:focus:ring-gray-500 dark:group-[.destructive]:text-red-400 dark:group-[.destructive]:hover:text-red-200 dark:group-[.destructive]:hover:bg-red-900/50 dark:group-[.destructive]:focus:ring-red-500 dark:group-[.success]:text-green-400 dark:group-[.success]:hover:text-green-200 dark:group-[.success]:hover:bg-green-900/50 dark:group-[.success]:focus:ring-green-500",
      className
    )}
    toast-close=""
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
))
ToastClose.displayName = ToastPrimitives.Close.displayName

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn("text-sm font-semibold leading-none tracking-tight", className)}
    {...props}
  />
))
ToastTitle.displayName = ToastPrimitives.Title.displayName

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn("text-sm opacity-90 leading-relaxed", className)}
    {...props}
  />
))
ToastDescription.displayName = ToastPrimitives.Description.displayName

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>

type ToastActionElement = React.ReactElement<typeof ToastAction>

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
}
