from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.trading_container import UserTradingContainer

trading_container_bp = Blueprint('trading_container', __name__)

# In-memory container registry (replace with persistent store if needed)
user_containers = {}

def get_container(user_id):
    if user_id not in user_containers:
        user_containers[user_id] = UserTradingContainer(user_id)
    return user_containers[user_id]

@trading_container_bp.route('/container/status', methods=['GET'])
@jwt_required()
def get_status():
    user_id = get_jwt_identity()
    container = get_container(user_id)
    return jsonify({
        "active": container.active,
        "risk_params": container.risk_params,
        "current_trade": container.current_trade.to_dict() if container.current_trade else None
    })

@trading_container_bp.route('/container/toggle', methods=['POST'])
@jwt_required()
def toggle_container():
    user_id = get_jwt_identity()
    data = request.get_json()
    active = data.get("active", False)
    container = get_container(user_id)
    container.set_active(active)
    return jsonify({"active": container.active})

@trading_container_bp.route('/container/risk', methods=['POST'])
@jwt_required()
def set_risk_params():
    user_id = get_jwt_identity()
    data = request.get_json()
    allocation_pct = data.get("allocation_pct")
    margin_mode = data.get("margin_mode")
    leverage = data.get("leverage")
    container = get_container(user_id)
    container.update_risk_params(allocation_pct, margin_mode, leverage)
    return jsonify({"risk_params": container.risk_params})