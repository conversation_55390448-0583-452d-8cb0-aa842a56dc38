"""
Profit Share Service
Handles profit share calculations and tracking for tier-based users.
Only calculates profit share when user balance exceeds initial balance.
"""

from decimal import Decimal
from datetime import datetime, date, timedelta
from app import db
from app.models.user_balance_tracker import UserBalanceTracker
from app.models.user_tier_status import UserTierStatus
from app.models.trade import Trade, TradeStatus
from app.models.referral import Referral, ReferralEarning
from app.models.solana_payment import SolanaPayment, SolanaPaymentType
from flask import current_app


class ProfitShareService:
    """Service for managing profit share calculations and payments."""
    
    @staticmethod
    def get_or_create_balance_tracker(user_id, initial_balance=None):
        """Get existing balance tracker or create new one."""
        tracker = UserBalanceTracker.query.filter_by(user_id=user_id).first()
        
        if not tracker and initial_balance is not None:
            tracker = UserBalanceTracker(user_id=user_id, initial_balance=initial_balance)
            db.session.add(tracker)
            db.session.commit()
        
        return tracker
    
    @staticmethod
    def update_balance_from_trade(user_id, trade_pnl, trade_id=None):
        """
        Update user balance from trade result and calculate profit share if applicable.
        
        Args:
            user_id: User ID
            trade_pnl: Profit/Loss from trade
            trade_id: Trade ID for reference
        
        Returns:
            dict: Updated balance info and profit share calculated
        """
        # Get user's balance tracker
        tracker = UserBalanceTracker.query.filter_by(user_id=user_id).first()
        if not tracker:
            current_app.logger.warning(f"No balance tracker found for user {user_id}")
            return None
        
        # Calculate new balance
        old_balance = tracker.current_balance
        new_balance = old_balance + Decimal(str(trade_pnl))
        
        # Update balance tracker
        tracker.update_balance(new_balance, transaction_type='trade')
        
        profit_share_calculated = Decimal('0')
        
        # Calculate profit share if user is in profit
        if tracker.is_in_profit and tracker.true_profit > 0:
            # Get user's tier status
            tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
            if tier_status:
                tier_rate = tier_status.get_profit_share_rate()
                profit_share_calculated = tracker.calculate_profit_share(tier_rate)
                
                if profit_share_calculated > 0:
                    # Add to tier status owed amount
                    tier_status.add_profit_share(float(profit_share_calculated))
                    
                    # Process referral earnings if applicable
                    ProfitShareService._process_referral_earnings(user_id, profit_share_calculated, trade_id)
        
        db.session.commit()
        
        return {
            'old_balance': float(old_balance),
            'new_balance': float(new_balance),
            'true_profit': float(tracker.true_profit),
            'is_in_profit': tracker.is_in_profit,
            'profit_share_calculated': float(profit_share_calculated),
            'total_profit_share_owed': float(tracker.get_profit_share_owed())
        }
    
    @staticmethod
    def _process_referral_earnings(user_id, profit_share_amount, trade_id=None):
        """Process referral earnings when profit share is calculated."""
        # Find if this user was referred by someone
        referral = Referral.query.filter_by(referee_id=user_id, status='active').first()
        
        if referral and referral.is_verified:
            # Calculate referral earning based on referrer's tier
            referral_rate = referral.get_referrer_tier_rate()
            referral_earning = profit_share_amount * Decimal(str(referral_rate))
            
            if referral_earning > 0:
                # Add earning to referral
                earning = referral.add_earning(
                    amount=float(referral_earning),
                    source_trade_id=trade_id,
                    description=f"Referral earning from profit share ({referral_rate*100:.1f}%)"
                )
                
                current_app.logger.info(
                    f"Referral earning calculated: {referral_earning} for referrer {referral.referrer_id} "
                    f"from referee {user_id} profit share {profit_share_amount}"
                )
    
    @staticmethod
    def handle_deposit(user_id, deposit_amount):
        """Handle user deposit - updates balance tracker but doesn't affect profit calculation."""
        tracker = UserBalanceTracker.query.filter_by(user_id=user_id).first()
        if not tracker:
            current_app.logger.warning(f"No balance tracker found for user {user_id}")
            return None
        
        new_balance = tracker.current_balance + Decimal(str(deposit_amount))
        tracker.update_balance(new_balance, transaction_type='deposit', amount=deposit_amount)
        
        db.session.commit()
        
        return {
            'new_balance': float(new_balance),
            'total_deposits': float(tracker.total_deposits),
            'true_profit': float(tracker.true_profit),
            'is_in_profit': tracker.is_in_profit
        }
    
    @staticmethod
    def handle_withdrawal(user_id, withdrawal_amount):
        """Handle user withdrawal - updates balance tracker."""
        tracker = UserBalanceTracker.query.filter_by(user_id=user_id).first()
        if not tracker:
            current_app.logger.warning(f"No balance tracker found for user {user_id}")
            return None
        
        if tracker.current_balance < Decimal(str(withdrawal_amount)):
            raise ValueError("Insufficient balance for withdrawal")
        
        new_balance = tracker.current_balance - Decimal(str(withdrawal_amount))
        tracker.update_balance(new_balance, transaction_type='withdrawal', amount=withdrawal_amount)
        
        db.session.commit()
        
        return {
            'new_balance': float(new_balance),
            'total_withdrawals': float(tracker.total_withdrawals),
            'true_profit': float(tracker.true_profit),
            'is_in_profit': tracker.is_in_profit
        }
    
    @staticmethod
    def get_profit_share_summary(user_id):
        """Get comprehensive profit share summary for user."""
        tracker = UserBalanceTracker.query.filter_by(user_id=user_id).first()
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()

        # If no tracker exists, try to create one with current exchange balance
        if not tracker:
            try:
                from app.models.user import User
                from app.models.security_log import APICredential
                from app.services.exchange_service import get_exchange_service

                user = User.query.get(user_id)
                if user:
                    # Try to get current balance from exchange
                    credentials = APICredential.query.filter_by(
                        user_id=user_id,
                        is_active=True,
                        is_valid=True
                    ).first()

                    if credentials:
                        creds = credentials.get_credentials()
                        if creds:
                            exchange_service = get_exchange_service(
                                credentials.exchange.value,
                                creds['api_key'],
                                creds['secret_key'],
                                creds.get('passphrase')
                            )
                            current_balance = exchange_service.get_balance()

                            # Create tracker with real balance
                            tracker = ProfitShareService.get_or_create_balance_tracker(user_id, float(current_balance))
                            current_app.logger.info(f"[PROFIT_TRACKING] Auto-created balance tracker for user {user_id} with balance {current_balance} USDT")

            except Exception as e:
                current_app.logger.error(f"Error auto-creating balance tracker for user {user_id}: {str(e)}")

        if not tracker or not tier_status:
            return None
        
        return {
            'balance_info': tracker.to_dict(),
            'tier_info': {
                'current_tier': tier_status.get_current_tier(),
                'profit_share_rate': tier_status.get_profit_share_rate(),
                'profit_share_owed': float(tier_status.profit_share_owed),
                'payment_status': tier_status.payment_status
            },
            'profit_analysis': {
                'initial_balance': float(tracker.initial_balance),
                'current_balance': float(tracker.current_balance),
                'net_deposits': float(tracker.total_deposits - tracker.total_withdrawals),
                'trading_profit': float(tracker.trading_profit),
                'true_profit': float(tracker.true_profit),
                'is_profitable': tracker.is_in_profit,
                'profit_percentage': tracker.get_profit_percentage()
            }
        }
    
    @staticmethod
    def calculate_profit_share_for_period(user_id, start_date, end_date):
        """Calculate profit share for a specific period."""
        # Get trades in the period that originated from the app
        trades = Trade.query.filter(
            Trade.user_id == user_id,
            Trade.source == 'app',
            Trade.status == TradeStatus.CLOSED,
            Trade.exit_time >= start_date,
            Trade.exit_time <= end_date
        ).all()
        
        total_profit = Decimal('0')
        total_loss = Decimal('0')
        trade_count = len(trades)
        
        for trade in trades:
            if trade.pnl:
                pnl = Decimal(str(trade.pnl))
                if pnl > 0:
                    total_profit += pnl
                else:
                    total_loss += abs(pnl)
        
        net_profit = total_profit - total_loss
        
        # Get tier rate
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        tier_rate = tier_status.get_profit_share_rate() if tier_status else 0
        
        # Calculate profit share only on net profit
        profit_share = net_profit * Decimal(str(tier_rate)) if net_profit > 0 else Decimal('0')
        
        return {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'trading_summary': {
                'total_trades': trade_count,
                'total_profit': float(total_profit),
                'total_loss': float(total_loss),
                'net_profit': float(net_profit)
            },
            'profit_share': {
                'tier_rate': tier_rate,
                'amount': float(profit_share),
                'applicable': net_profit > 0
            }
        }
    
    @staticmethod
    def process_profit_share_payment(user_id, payment_amount, transaction_signature=None):
        """Process profit share payment and update records."""
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        tracker = UserBalanceTracker.query.filter_by(user_id=user_id).first()
        
        if not tier_status or not tracker:
            raise ValueError("User tier status or balance tracker not found")
        
        payment_amount = Decimal(str(payment_amount))
        
        # Create Solana payment record
        solana_payment = SolanaPayment(
            user_id=user_id,
            payment_type=SolanaPaymentType.PROFIT_SHARE,
            amount=payment_amount,
            to_address=current_app.config.get('SOLANA_TREASURY_WALLET', 'HBWRBVndpKZjAS6VFSSAdb93PB1pZfLZSn5QkDYGvn8k')
        )
        
        if transaction_signature:
            solana_payment.transaction_signature = transaction_signature
        
        db.session.add(solana_payment)
        
        # Update tier status
        tier_status.clear_debt(float(payment_amount))
        
        # Update balance tracker
        tracker.record_profit_share_payment(float(payment_amount))
        
        db.session.commit()
        
        return {
            'payment_id': solana_payment.id,
            'amount_paid': float(payment_amount),
            'remaining_owed': float(tier_status.profit_share_owed),
            'payment_status': tier_status.payment_status
        }
    
    @staticmethod
    def get_users_with_outstanding_profit_share():
        """Get users who have outstanding profit share payments."""
        unpaid_statuses = UserTierStatus.query.filter(
            UserTierStatus.profit_share_owed > 0,
            UserTierStatus.payment_status == 'unpaid'
        ).all()

        partial_statuses = UserTierStatus.query.filter(
            UserTierStatus.profit_share_owed > 0,
            UserTierStatus.payment_status == 'partial'
        ).all()

        tier_statuses = unpaid_statuses + partial_statuses
        
        users_data = []
        for tier_status in tier_statuses:
            tracker = UserBalanceTracker.query.filter_by(user_id=tier_status.user_id).first()
            
            users_data.append({
                'user_id': tier_status.user_id,
                'tier': tier_status.get_current_tier(),
                'profit_share_owed': float(tier_status.profit_share_owed),
                'payment_status': tier_status.payment_status,
                'is_in_profit': tracker.is_in_profit if tracker else False,
                'true_profit': float(tracker.true_profit) if tracker else 0,
                'last_payment_date': tier_status.last_payment_date.isoformat() if tier_status.last_payment_date else None
            })
        
        return users_data
