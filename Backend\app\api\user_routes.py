"""
User Routes for DeepTrade Platform
Handles user management, profile, and subscription operations.
"""
 
from flask import Blueprint, request, jsonify, current_app
from app.auth.decorators import jwt_required, subscription_required, rate_limit_required
from app.auth.security import SecurityManager
from app.models.user import User
from app.models.subscription import Subscription
from app import db
from flask_jwt_extended import get_jwt_identity

user_bp = Blueprint('user', __name__, url_prefix='/api/users')

@user_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get user profile information."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Log API access
        SecurityManager.log_api_access(
            user_id=user_id,
            endpoint='GET /api/users/profile',
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'user': user.to_dict(include_sensitive=False)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting user profile: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """Update user profile information."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update allowed fields
        allowed_fields = ['display_name', 'timezone', 'notification_preferences']
        updated_fields = []
        
        for field in allowed_fields:
            if field in data:
                if hasattr(user, field):
                    setattr(user, field, data[field])
                    updated_fields.append(field)
        
        if updated_fields:
            db.session.commit()
            
            # Log profile update
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='profile_update',
                ip_address=request.remote_addr,
                details={'updated_fields': updated_fields},
                risk_level='low'
            )
        
        return jsonify({
            'message': 'Profile updated successfully',
            'updated_fields': updated_fields,
            'user': user.to_dict(include_sensitive=False)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating user profile: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/subscription', methods=['GET'])
@jwt_required()
def get_subscription():
    """Get user subscription information."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        subscription = user.get_active_subscription()
        
        # Log API access
        SecurityManager.log_api_access(
            user_id=user_id,
            endpoint='GET /api/users/subscription',
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'subscription': subscription.to_dict() if subscription else None,
            'subscription_tier': user.tier_status.get_current_tier() if hasattr(user, 'tier_status') and user.tier_status else None,
            'can_upgrade': not subscription or (hasattr(subscription, 'tier') and subscription.tier != 2)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting subscription: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/upgrade', methods=['POST'])
@jwt_required()
def upgrade_subscription():
    """Initiate subscription upgrade."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        target_tier = data.get('tier', 'tier_2')
        
        if target_tier not in ['tier_1', 'tier_2']:
            return jsonify({'error': 'Invalid subscription tier'}), 400
        
        current_subscription = user.get_active_subscription()
        if current_subscription and hasattr(current_subscription, 'tier') and current_subscription.tier == (2 if target_tier == 'tier_2' else 1):
            return jsonify({'error': 'User already has this subscription tier'}), 400
        
        # Log upgrade attempt
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='subscription_upgrade_attempt',
            ip_address=request.remote_addr,
            details={'target_tier': target_tier},
            risk_level='medium'
        )
        
        # Return payment information (would integrate with payment processor)
        return jsonify({
            'message': 'Upgrade initiated',
            'target_tier': target_tier,
            'price': '29.90' if target_tier == 'tier_2' else '0.00',
            'currency': 'USDC',
            'next_step': 'payment_required' if target_tier == 'tier_2' else 'complete'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error upgrading subscription: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/settings', methods=['GET'])
@jwt_required()
def get_settings():
    """Get user settings and preferences."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'settings': {
                'two_fa_enabled': user.two_fa_enabled,
                'email_notifications': user.notification_preferences.get('email', True) if user.notification_preferences else True,
                'trading_notifications': user.notification_preferences.get('trading', True) if user.notification_preferences else True,
                'timezone': user.timezone or 'UTC',
                'display_name': user.display_name or user.email.split('@')[0],
                'language_preference': user.language_preference or 'en'
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting user settings: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/settings', methods=['PUT'])
@jwt_required()
def update_settings():
    """Update user settings and preferences."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update notification preferences
        if 'email_notifications' in data or 'trading_notifications' in data:
            prefs = user.notification_preferences or {}
            if 'email_notifications' in data:
                prefs['email'] = bool(data['email_notifications'])
            if 'trading_notifications' in data:
                prefs['trading'] = bool(data['trading_notifications'])
            user.notification_preferences = prefs
        
        # Update other settings
        if 'timezone' in data:
            user.timezone = data['timezone']
        if 'display_name' in data:
            user.display_name = data['display_name']
        if 'language_preference' in data:
            # Validate language preference
            supported_languages = ['en', 'es', 'fr', 'de', 'pt', 'ja', 'ko', 'zh']
            lang = data['language_preference'].lower()
            if lang in supported_languages:
                user.language_preference = lang
            else:
                return jsonify({'error': f'Unsupported language: {lang}'}), 400
        
        db.session.commit()
        
        # Log settings update
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='settings_update',
            ip_address=request.remote_addr,
            details={'updated_settings': list(data.keys())},
            risk_level='low'
        )
        
        return jsonify({'message': 'Settings updated successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating user settings: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/activity', methods=['GET'])
@jwt_required()
def get_activity():
    """Get user activity log."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        limit = min(int(request.args.get('limit', 50)), 100)
        offset = int(request.args.get('offset', 0))
        
        # Get security logs for this user
        from app.models.security_log import SecurityLog
        logs = SecurityLog.query.filter_by(user_id=user_id)\
                                .order_by(SecurityLog.created_at.desc())\
                                .limit(limit).offset(offset).all()
        
        return jsonify({
            'activities': [log.to_dict() for log in logs],
            'total': SecurityLog.query.filter_by(user_id=user_id).count(),
            'limit': limit,
            'offset': offset
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting user activity: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@user_bp.route('/security/access-logs', methods=['GET'])
@jwt_required()
def get_user_access_logs():
    """Get user's login history and access logs."""
    try:
        user_id = get_jwt_identity()

        # Get query parameters
        limit = min(int(request.args.get('limit', 20)), 50)
        offset = int(request.args.get('offset', 0))

        # Get user's IP access logs
        try:
            from app.models.ip_tracking import IPAccessLog
            access_logs = IPAccessLog.query.filter_by(user_id=user_id)\
                                          .order_by(IPAccessLog.login_timestamp.desc())\
                                          .limit(limit).offset(offset).all()

            logs_data = []
            for log in access_logs:
                # Construct geolocation data from separate fields
                geolocation_data = {
                    'city': log.city,
                    'country': log.geographic_location.split(', ')[-1] if log.geographic_location and ', ' in log.geographic_location else 'Unknown',
                    'region': log.geographic_location.split(', ')[0] if log.geographic_location and ', ' in log.geographic_location else None,
                    'country_code': log.country_code,
                    'isp': log.isp,
                    'is_proxy': log.is_proxy,
                    'is_vpn': log.is_vpn
                } if log.geographic_location or log.city or log.country_code else None

                logs_data.append({
                    'id': log.id,
                    'ip_address': log.ip_address,
                    'login_timestamp': log.login_timestamp.isoformat() if log.login_timestamp else None,
                    'login_successful': log.login_successful,
                    'user_agent': log.user_agent,
                    'geolocation_data': geolocation_data,
                    'failure_reason': log.failure_reason if not log.login_successful else None
                })

            total_logs = IPAccessLog.query.filter_by(user_id=user_id).count()
        except ImportError:
            # IP tracking model not available
            logs_data = []
            total_logs = 0

        return jsonify({
            'access_logs': logs_data,
            'total': total_logs,
            'limit': limit,
            'offset': offset
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting user access logs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@user_bp.route('/security/stats', methods=['GET'])
@jwt_required()
def get_user_security_stats():
    """Get user's security statistics."""
    try:
        user_id = get_jwt_identity()

        # Get user info
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get login statistics
        try:
            from app.models.ip_tracking import IPAccessLog
            from datetime import datetime, timedelta

            # Last 30 days
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)

            total_logins = IPAccessLog.query.filter(
                IPAccessLog.user_id == user_id,
                IPAccessLog.login_timestamp >= thirty_days_ago
            ).count()

            successful_logins = IPAccessLog.query.filter(
                IPAccessLog.user_id == user_id,
                IPAccessLog.login_successful == True,
                IPAccessLog.login_timestamp >= thirty_days_ago
            ).count()

            failed_logins = IPAccessLog.query.filter(
                IPAccessLog.user_id == user_id,
                IPAccessLog.login_successful == False,
                IPAccessLog.login_timestamp >= thirty_days_ago
            ).count()

            # Unique IPs
            unique_ips = db.session.query(IPAccessLog.ip_address).filter(
                IPAccessLog.user_id == user_id,
                IPAccessLog.login_timestamp >= thirty_days_ago
            ).distinct().count()

            # Last login info
            last_login = IPAccessLog.query.filter(
                IPAccessLog.user_id == user_id,
                IPAccessLog.login_successful == True
            ).order_by(IPAccessLog.login_timestamp.desc()).first()
        except ImportError:
            # IP tracking model not available
            total_logins = 0
            successful_logins = 0
            failed_logins = 0
            unique_ips = 0
            last_login = None

        return jsonify({
            'user_id': user_id,
            'email': user.email,
            'account_created': user.created_at.isoformat() if user.created_at else None,
            'two_fa_enabled': user.two_fa_enabled,
            'is_active': user.is_active,
            'statistics': {
                'period_days': 30,
                'total_logins': total_logins,
                'successful_logins': successful_logins,
                'failed_logins': failed_logins,
                'unique_ips': unique_ips,
                'success_rate': round((successful_logins / total_logins * 100) if total_logins > 0 else 0, 2)
            },
            'last_login': {
                'timestamp': last_login.login_timestamp.isoformat() if last_login and last_login.login_timestamp else None,
                'ip_address': last_login.ip_address if last_login else None,
                'user_agent': last_login.user_agent if last_login else None,
                'geolocation': {
                    'city': last_login.city,
                    'country': last_login.geographic_location.split(', ')[-1] if last_login.geographic_location and ', ' in last_login.geographic_location else 'Unknown',
                    'region': last_login.geographic_location.split(', ')[0] if last_login.geographic_location and ', ' in last_login.geographic_location else None,
                    'country_code': last_login.country_code,
                    'isp': last_login.isp,
                    'is_proxy': last_login.is_proxy,
                    'is_vpn': last_login.is_vpn
                } if last_login and (last_login.geographic_location or last_login.city or last_login.country_code) else None
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting user security stats: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@user_bp.route('/security/current-session', methods=['GET'])
@jwt_required()
def get_current_session():
    """Get current session information."""
    try:
        user_id = get_jwt_identity()

        # Get current session info from request
        current_ip = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')

        # Try to get geolocation for current IP (if available)
        from app.services.ip_tracking_service import IPTrackingService
        try:
            geo_data = IPTrackingService.get_geolocation_data(current_ip)
        except:
            geo_data = None

        # Get recent login for this IP
        try:
            from app.models.ip_tracking import IPAccessLog
            recent_login = IPAccessLog.query.filter(
                IPAccessLog.user_id == user_id,
                IPAccessLog.ip_address == current_ip,
                IPAccessLog.login_successful == True
            ).order_by(IPAccessLog.login_timestamp.desc()).first()
        except ImportError:
            # IP tracking model not available
            recent_login = None

        return jsonify({
            'current_session': {
                'ip_address': current_ip,
                'user_agent': user_agent,
                'geolocation': geo_data,
                'session_start': recent_login.login_timestamp.isoformat() if recent_login and recent_login.login_timestamp else None,
                'is_current_device': True
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting current session: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/email', methods=['PUT'])
@jwt_required()
@rate_limit_required("3 per hour")
def update_email():
    """Update user email address with verification."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        new_email = data.get('new_email', '').strip().lower()
        current_password = data.get('current_password', '')

        # Validate inputs
        if not new_email or not current_password:
            return jsonify({'error': 'New email and current password are required'}), 400

        # Validate email format
        import re
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, new_email):
            return jsonify({'error': 'Invalid email format'}), 400

        # Check if email is the same as current
        if new_email == user.email:
            return jsonify({'error': 'New email must be different from current email'}), 400

        # Verify current password
        if not user.check_password(current_password):
            return jsonify({'error': 'Current password is incorrect'}), 401

        # Check if new email is already taken
        existing_user = User.query.filter_by(email=new_email).first()
        if existing_user:
            return jsonify({'error': 'Email address is already in use'}), 409

        # Generate verification token
        token = user.generate_email_verification_token(new_email)

        # Send verification email
        from app.services.email_service import EmailService
        email_service = EmailService()

        try:
            email_service.send_email_change_verification(user.email, new_email, token)
        except Exception as email_error:
            current_app.logger.error(f"Failed to send email change verification: {str(email_error)}")
            return jsonify({'error': 'Failed to send verification email'}), 500

        # Save changes
        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='email_change_requested',
            ip_address=request.remote_addr,
            details={'old_email': user.email, 'new_email': new_email},
            risk_level='medium'
        )

        return jsonify({
            'message': 'Verification email sent to new address',
            'pending_email': new_email
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error updating email: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/email/verify', methods=['POST'])
def verify_email_change():
    """Verify email change with token."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        token = data.get('token', '').strip()
        if not token:
            return jsonify({'error': 'Verification token is required'}), 400

        # Find user by email verification token
        user = User.query.filter_by(email_verification_token=token).first()
        if not user:
            return jsonify({'error': 'Invalid verification token'}), 400

        # Verify email change
        success, message = user.verify_email_change(token)
        if not success:
            return jsonify({'error': message}), 400

        # Save changes
        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user.id,
            event_type='email_changed',
            ip_address=request.remote_addr,
            details={'new_email': user.email},
            risk_level='medium'
        )

        return jsonify({
            'message': message,
            'new_email': user.email
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error verifying email change: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/password', methods=['PUT'])
@jwt_required()
@rate_limit_required("5 per hour")
def update_password():
    """Update user password."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        current_password = data.get('current_password', '')
        new_password = data.get('new_password', '')
        confirm_password = data.get('confirm_password', '')

        # Validate inputs
        if not current_password or not new_password or not confirm_password:
            return jsonify({'error': 'All password fields are required'}), 400

        # Check if new passwords match
        if new_password != confirm_password:
            return jsonify({'error': 'New passwords do not match'}), 400

        # Verify current password
        if not user.check_password(current_password):
            return jsonify({'error': 'Current password is incorrect'}), 401

        # Validate new password strength
        if len(new_password) < 8:
            return jsonify({'error': 'Password must be at least 8 characters long'}), 400

        # Check if new password is different from current
        if user.check_password(new_password):
            return jsonify({'error': 'New password must be different from current password'}), 400

        # Additional password strength checks
        import re
        if not re.search(r'[A-Z]', new_password):
            return jsonify({'error': 'Password must contain at least one uppercase letter'}), 400
        if not re.search(r'[a-z]', new_password):
            return jsonify({'error': 'Password must contain at least one lowercase letter'}), 400
        if not re.search(r'\d', new_password):
            return jsonify({'error': 'Password must contain at least one number'}), 400

        # Update password
        user.set_password(new_password)
        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='password_changed',
            ip_address=request.remote_addr,
            risk_level='medium'
        )

        return jsonify({'message': 'Password updated successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Error updating password: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/2fa/enable', methods=['POST'])
@jwt_required()
@rate_limit_required("15 per minute")
def enable_2fa():
    """Enable 2FA for user account."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        if user.two_fa_enabled:
            return jsonify({'error': '2FA is already enabled'}), 400

        # Send 2FA code via email
        from app.auth.two_factor import TwoFactorAuth
        success = TwoFactorAuth.send_email_2fa_code(user_id)

        if not success:
            return jsonify({'error': 'Failed to send 2FA code'}), 500

        return jsonify({
            'message': '2FA code sent to your email address',
            'email': user.email
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error enabling 2FA: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/2fa/verify', methods=['POST'])
@jwt_required()
@rate_limit_required("25 per minute")
def verify_2fa():
    """Verify 2FA code and enable 2FA."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        code = data.get('code', '').strip()
        if not code:
            return jsonify({'error': 'Verification code is required'}), 400

        # Verify the code
        from app.auth.two_factor import TwoFactorAuth
        if not TwoFactorAuth.verify_email_2fa_code(user_id, code):
            return jsonify({'error': 'Invalid or expired verification code'}), 400

        # Enable 2FA
        user.two_fa_enabled = True
        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='2fa_enabled',
            ip_address=request.remote_addr,
            risk_level='low'
        )

        return jsonify({'message': '2FA enabled successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Error verifying 2FA: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@user_bp.route('/2fa/disable', methods=['POST'])
@jwt_required()
def disable_2fa():
    """Disable 2FA for user account."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        if not user.two_fa_enabled:
            return jsonify({'error': '2FA is not enabled'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        current_password = data.get('current_password', '')
        if not current_password:
            return jsonify({'error': 'Current password is required'}), 400

        # Check if user has a password (not OAuth user)
        if not user.password_hash:
            return jsonify({'error': 'Password authentication not available for OAuth users. Please use email verification.'}), 400

        # Verify current password
        if not user.check_password(current_password):
            return jsonify({'error': 'Current password is incorrect'}), 401

        # Disable 2FA
        user.disable_2fa()
        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='2fa_disabled',
            ip_address=request.remote_addr,
            risk_level='medium'
        )

        return jsonify({'message': '2FA disabled successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Error disabling 2FA: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@user_bp.route('/2fa/disable/request', methods=['POST'])
@jwt_required()
@rate_limit_required("10 per minute")
def request_2fa_disable():
    """Send email verification code to disable 2FA."""
    try:
        user_id = get_jwt_identity()

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        if not user.two_fa_enabled:
            return jsonify({'error': '2FA is not enabled'}), 400

        # Send email verification code for 2FA disable
        from app.auth.two_factor import TwoFactorAuth
        success = TwoFactorAuth.send_email_2fa_code(user_id)

        if not success:
            return jsonify({'error': 'Failed to send verification code'}), 500

        # Log security event
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='2fa_disable_requested',
            ip_address=request.remote_addr,
            risk_level='low'
        )

        return jsonify({'message': 'Verification code sent to your email'}), 200

    except Exception as e:
        current_app.logger.error(f"2FA disable request failed: {str(e)}")
        return jsonify({'error': 'Failed to send verification code'}), 500


@user_bp.route('/2fa/disable/verify', methods=['POST'])
@jwt_required()
@rate_limit_required("10 per minute")
def verify_2fa_disable():
    """Verify email code and disable 2FA."""
    try:
        user_id = get_jwt_identity()

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        code = data.get('code', '')
        if not code:
            return jsonify({'error': 'Verification code is required'}), 400

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        if not user.two_fa_enabled:
            return jsonify({'error': '2FA is not enabled'}), 400

        # Verify the email code
        from app.auth.two_factor import TwoFactorAuth
        if not TwoFactorAuth.verify_email_2fa_code(user_id, code):
            return jsonify({'error': 'Invalid or expired verification code'}), 401

        # Disable 2FA
        user.disable_2fa()
        db.session.commit()

        # Log security event
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='2fa_disabled',
            ip_address=request.remote_addr,
            risk_level='medium'
        )

        return jsonify({'message': '2FA disabled successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"2FA disable verification failed: {str(e)}")
        return jsonify({'error': 'Failed to disable 2FA'}), 500

@user_bp.route('/delete', methods=['DELETE'])
@jwt_required()
@rate_limit_required("1 per day")
def delete_account():
    """Delete user account (soft delete) with password confirmation."""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        if not user.is_active:
            return jsonify({'error': 'Account is already deleted'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        current_password = data.get('current_password', '')
        confirmation_text = data.get('confirmation_text', '')

        # Validate inputs
        if not current_password:
            return jsonify({'error': 'Current password is required'}), 400

        if confirmation_text != 'DELETE':
            return jsonify({'error': 'Please type DELETE to confirm account deletion'}), 400

        # Verify current password
        if not user.check_password(current_password):
            return jsonify({'error': 'Current password is incorrect'}), 401

        # Log account deletion attempt
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='account_deletion_confirmed',
            ip_address=request.remote_addr,
            details={'email': user.email},
            risk_level='high'
        )

        # Send account deletion confirmation email
        from app.services.email_service import EmailService
        email_service = EmailService()

        try:
            email_service.send_account_deletion_confirmation(user.email, user.full_name)
        except Exception as email_error:
            current_app.logger.error(f"Failed to send account deletion confirmation email: {str(email_error)}")
            # Continue with deletion even if email fails

        # Perform soft delete
        user.deactivate_account()
        db.session.commit()

        return jsonify({'message': 'Account deleted successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Error deleting account: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500