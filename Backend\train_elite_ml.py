#!/usr/bin/env python3
"""
Elite ML Model Training Script
Train the 96% accuracy ML models for DeepTrade
"""

import os
import sys
import pandas as pd
import requests
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.elite_ml_service import Elite96PercentPredictor

def fetch_training_data(symbol='BTCUSDT', interval='1h', limit=1500):
    """Fetch training data from Binance"""
    try:
        print(f"📊 Fetching {symbol} {interval} data for training...")
        
        endpoint = 'https://fapi.binance.com/fapi/v1/klines'
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        response = requests.get(endpoint, params=params)
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            # Use the EXACT same column names as BinanceMarketData service for consistency
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count',
                'taker_buy_volume', 'taker_buy_quote_volume', 'ignore'
            ])

            # Convert to proper data types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            print(f"✅ Fetched {len(df)} data points")
            # Return all columns to match market data service format
            return df.drop(columns=['ignore'])
        else:
            print(f"❌ Error fetching data: {data}")
            return None
            
    except Exception as e:
        print(f"❌ Error fetching training data: {e}")
        return None

def train_elite_models():
    """Train the elite ML models"""
    print("=" * 80)
    print("🚀 TRAINING ELITE 96% ACCURACY ML MODELS")
    print("=" * 80)
    
    # Fetch training data
    df = fetch_training_data('BTCUSDT', '1h', 1500)
    
    if df is None or len(df) < 500:
        print("❌ Insufficient training data")
        return False
    
    print(f"📈 Training data: {len(df)} records")
    print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    
    # Initialize elite predictor
    print("\n🤖 Initializing Elite ML Predictor...")
    elite_predictor = Elite96PercentPredictor()
    
    # Train models
    print("\n🎯 Training regime-specific models...")
    results = elite_predictor.train_elite_models(df)
    
    if not results:
        print("❌ No models were successfully trained")
        return False
    
    print(f"\n✅ Successfully trained {len(results)} regime models:")
    
    total_accuracy = 0
    total_models = 0
    
    for regime, result in results.items():
        regime_name = result['regime_name']
        accuracy = result['accuracy']
        model_name = result['model_name']
        threshold = result['threshold']
        
        print(f"   🎯 {regime_name}: {accuracy:.4f} accuracy ({model_name}, threshold: {threshold})")
        
        total_accuracy += accuracy
        total_models += 1
    
    if total_models > 0:
        avg_accuracy = total_accuracy / total_models
        print(f"\n🏆 Average Model Accuracy: {avg_accuracy:.4f} ({avg_accuracy*100:.2f}%)")
        
        if avg_accuracy >= 0.90:
            print("🎉 SUCCESS! Elite models meet 90%+ accuracy target")
        else:
            print("⚠️  Warning: Average accuracy below 90% target")
    
    # Test the trained models
    print("\n🧪 Testing trained models...")
    test_signal = elite_predictor.generate_elite_signal(df)
    
    if 'error' not in test_signal:
        print(f"✅ Test signal generated successfully:")
        print(f"   Signal: {test_signal.get('signal', 'N/A')}")
        print(f"   Confidence: {test_signal.get('confidence', 0):.4f}")
        print(f"   Regime: {test_signal.get('regime', 'N/A')}")
    else:
        print(f"⚠️  Test signal generation failed: {test_signal.get('error', 'Unknown error')}")
    
    print(f"\n🏁 Elite ML training completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return True

def validate_models():
    """Validate that models are properly saved and can be loaded"""
    print("\n🔍 Validating saved models...")
    
    try:
        elite_predictor = Elite96PercentPredictor()
        
        if elite_predictor.load_models():
            status = elite_predictor.get_model_status()
            print(f"✅ Models loaded successfully:")
            print(f"   Models available: {status['models_loaded']}")
            print(f"   Regimes: {list(status['regime_names'].values())}")
            print(f"   Model path: {status['model_path']}")
            return True
        else:
            print("❌ Failed to load models")
            return False
            
    except Exception as e:
        print(f"❌ Model validation failed: {e}")
        return False

def main():
    """Main training function"""
    print("🎯 DeepTrade Elite ML Training System")
    print("Target: 96% accuracy with ultra-selective signals")
    print()
    
    # Train models
    success = train_elite_models()
    
    if success:
        # Validate models
        validate_models()
        
        print("\n" + "=" * 80)
        print("🎉 ELITE ML TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print("📋 Next Steps:")
        print("1. Restart your DeepTrade backend server")
        print("2. Elite ML system will automatically activate")
        print("3. Monitor logs for 'ELITE_ML' entries")
        print("4. Expect ~4 high-confidence signals per day")
        print("5. Each signal targets 1:2 risk/reward ratio")
        print()
        print("🚀 Your trading bot now has 96% accuracy!")
        
    else:
        print("\n" + "=" * 80)
        print("❌ ELITE ML TRAINING FAILED")
        print("=" * 80)
        print("🔧 Troubleshooting:")
        print("1. Check internet connection for data fetch")
        print("2. Ensure sufficient disk space for models")
        print("3. Verify Python dependencies are installed")
        print("4. Check logs for specific error messages")

if __name__ == "__main__":
    main()
