import json
import requests
from flask import current_app, session, request
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
from sqlalchemy import and_
from app.models.user import User
from app.models.subscription import Subscription, SubscriptionTier
from app.models.security_log import LoginAttempt
from app import db

class GoogleOAuth:
    """Google OAuth authentication service."""
    
    def __init__(self):
        self.client_id = current_app.config['GOOGLE_CLIENT_ID']
        self.client_secret = current_app.config['GOOGLE_CLIENT_SECRET']
        self.discovery_url = current_app.config['GOOGLE_DISCOVERY_URL']
        self._discovery_doc = None
    
    def get_discovery_document(self):
        """Get Google's OAuth 2.0 discovery document."""
        if not self._discovery_doc:
            try:
                current_app.logger.info(f"Fetching Google discovery document from: {self.discovery_url}")
                response = requests.get(self.discovery_url, timeout=10)
                current_app.logger.info(f"Discovery document response status: {response.status_code}")
                
                if response.status_code == 200:
                    self._discovery_doc = response.json()
                    current_app.logger.info("Successfully fetched Google discovery document")
                else:
                    current_app.logger.warning(f"Discovery document failed with HTTP {response.status_code}, using fallback")
                    # Use fallback endpoints
                    self._discovery_doc = {
                        "authorization_endpoint": "https://accounts.google.com/o/oauth2/v2/auth",
                        "token_endpoint": "https://oauth2.googleapis.com/token",
                        "userinfo_endpoint": "https://openidconnect.googleapis.com/v1/userinfo",
                        "issuer": "https://accounts.google.com",
                        "jwks_uri": "https://www.googleapis.com/oauth2/v3/certs"
                    }
            except requests.exceptions.RequestException as e:
                current_app.logger.error(f"Network error fetching discovery document: {e}, using fallback")
                # Use fallback endpoints
                self._discovery_doc = {
                    "authorization_endpoint": "https://accounts.google.com/o/oauth2/v2/auth",
                    "token_endpoint": "https://oauth2.googleapis.com/token",
                    "userinfo_endpoint": "https://openidconnect.googleapis.com/v1/userinfo",
                    "issuer": "https://accounts.google.com",
                    "jwks_uri": "https://www.googleapis.com/oauth2/v3/certs"
                }
            except ValueError as e:
                current_app.logger.error(f"JSON parsing error: {e}, using fallback")
                # Use fallback endpoints
                self._discovery_doc = {
                    "authorization_endpoint": "https://accounts.google.com/o/oauth2/v2/auth",
                    "token_endpoint": "https://oauth2.googleapis.com/token",
                    "userinfo_endpoint": "https://openidconnect.googleapis.com/v1/userinfo",
                    "issuer": "https://accounts.google.com",
                    "jwks_uri": "https://www.googleapis.com/oauth2/v3/certs"
                }
        return self._discovery_doc
    
    def get_authorization_url(self, redirect_uri, state=None):
        """Generate Google OAuth authorization URL."""
        discovery_doc = self.get_discovery_document()
        authorization_endpoint = discovery_doc["authorization_endpoint"]
        
        # Log the exact redirect_uri being used
        current_app.logger.info(f"OAuth redirect_uri being sent to Google: {redirect_uri}")
        print(f"DEBUG - OAuth redirect_uri: {redirect_uri}")
        
        # Construct authorization URL
        params = {
            'client_id': self.client_id,
            'response_type': 'code',
            'scope': 'openid email profile',
            'redirect_uri': redirect_uri,
            'access_type': 'offline',
            'prompt': 'consent'
        }
        
        if state:
            params['state'] = state
        
        auth_url = authorization_endpoint + '?' + '&'.join([f"{k}={v}" for k, v in params.items()])
        current_app.logger.info(f"Full OAuth URL: {auth_url}")
        return auth_url
    
    def exchange_code_for_tokens(self, code, redirect_uri):
        """Exchange authorization code for access and ID tokens."""
        current_app.logger.info("=== EXCHANGING CODE FOR TOKENS ===")
        try:
            # Get the token endpoint from discovery document
            discovery_doc = self.get_discovery_document()
            token_endpoint = discovery_doc["token_endpoint"]
            current_app.logger.info(f"Using token endpoint: {token_endpoint}")
            
            # Prepare token request data
            token_data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,  # Will be masked in logs
                'code': code,
                'grant_type': 'authorization_code',
                'redirect_uri': redirect_uri
            }
            
            # Log request details (masking sensitive data)
            log_data = token_data.copy()
            log_data['client_secret'] = '***' if log_data.get('client_secret') else 'not set'
            current_app.logger.info(f"Sending token request with data: {log_data}")
            
            try:
                # Send token request to Google
                current_app.logger.info("Sending token request to Google...")
                response = requests.post(token_endpoint, data=token_data, timeout=30)
                current_app.logger.info(f"Token response status: {response.status_code}")
                
                if response.status_code == 200:
                    # Successfully got tokens
                    tokens = response.json()
                    return tokens
                else:
                    # Handle error response
                    error_msg = f"Token exchange failed with status {response.status_code}"
                    error_details = response.text
                    current_app.logger.error(f"{error_msg}. Response: {error_details}")
                    current_app.logger.error(f"Request URL: {response.url}")
                    current_app.logger.error(f"Request Headers: {response.request.headers}")
                    current_app.logger.error(f"Request Body: {response.request.body}")
                    
                    # Try to parse error details if it's JSON
                    try:
                        error_json = response.json()
                        error_msg = error_json.get('error_description', error_json.get('error', error_msg))
                        current_app.logger.error(f"Parsed error details: {error_json}")
                    except ValueError:
                        current_app.logger.error("Could not parse error response as JSON")
                    
                    raise ValueError(f"{error_msg} - {error_details}")
                    
            except requests.exceptions.RequestException as re:
                current_app.logger.error(f"Request to token endpoint failed: {str(re)}", exc_info=True)
                raise ValueError(f"Failed to connect to token endpoint: {str(re)}")
                
        except Exception as e:
            current_app.logger.error(f"Exception during token exchange: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to exchange code for tokens: {str(e)}")
        finally:
            current_app.logger.info("=== TOKEN EXCHANGE COMPLETE ===\n")
    
    def verify_id_token(self, id_token_str):
        """Verify and decode Google ID token."""
        try:
            if not id_token_str:
                raise ValueError("ID token is empty or None")
            
            try:
                # Verify the token
                idinfo = id_token.verify_oauth2_token(
                    id_token_str, 
                    google_requests.Request(), 
                    self.client_id
                )
                current_app.logger.info("ID token verified by Google")
                
                # Verify the issuer
                current_app.logger.info(f"Verifying token issuer: {idinfo.get('iss')}")
                valid_issuers = ['accounts.google.com', 'https://accounts.google.com']
                if idinfo.get('iss') not in valid_issuers:
                    error_msg = f"Invalid token issuer: {idinfo.get('iss')}. Expected one of {valid_issuers}"
                    current_app.logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # Log user info (excluding sensitive data)
                user_info = {
                    'sub': idinfo.get('sub'),
                    'email': idinfo.get('email'),
                    'email_verified': idinfo.get('email_verified'),
                    'name': idinfo.get('name'),
                    'picture': '...' if idinfo.get('picture') else None,
                    'iss': idinfo.get('iss'),
                    'aud': idinfo.get('aud'),
                    'exp': idinfo.get('exp'),
                    'iat': idinfo.get('iat')
                }
                current_app.logger.info(f"Token payload: {user_info}")
                
                return idinfo
                
            except ValueError as ve:
                current_app.logger.error(f"Token verification failed: {str(ve)}", exc_info=True)
                raise ValueError(f"Token verification failed: {str(ve)}")
                
        except Exception as e:
            current_app.logger.error(f"Error verifying ID token: {str(e)}", exc_info=True)
            raise ValueError(f"ID token verification failed: {str(e)}")
        finally:
            current_app.logger.info("=== ID TOKEN VERIFICATION COMPLETE ===\n")
    
    def authenticate_user(self, code, redirect_uri):
        """Complete OAuth flow and authenticate user."""
        current_app.logger.info("=== STARTING USER AUTHENTICATION ===")
        user_info = None
        
        try:
            # Step 1: Exchange code for tokens
            current_app.logger.info("Step 1/4: Exchanging authorization code for tokens...")
            tokens = self.exchange_code_for_tokens(code, redirect_uri)
            
            if not tokens or 'id_token' not in tokens:
                error_msg = "No ID token received in token response"
                current_app.logger.error(error_msg)
                raise ValueError(error_msg)
            
            # Step 2: Verify ID token
            current_app.logger.info("Step 2/4: Verifying ID token...")
            user_info = self.verify_id_token(tokens['id_token'])
            
            # Step 3: Extract and validate user information
            current_app.logger.info("Step 3/4: Processing user information...")
            google_id = user_info.get('sub')
            email = user_info.get('email')
            name = user_info.get('name')
            picture = user_info.get('picture')
            
            # Log user info (masking sensitive data)
            user_info_log = {
                'google_id': google_id,
                'email': email,
                'name': name,
                'picture': '...' if picture else None,
                'email_verified': user_info.get('email_verified')
            }
            current_app.logger.info(f"User info from Google: {user_info_log}")
            
            # Validate required fields
            if not all([google_id, email, name]):
                error_msg = f"Missing required user info. Google ID: {google_id}, Email: {email}, Name: {name}"
                current_app.logger.error(error_msg)
                raise ValueError("Incomplete user information received from Google")
            
            if not user_info.get('email_verified', False):
                error_msg = f"Email not verified for user: {email}"
                current_app.logger.error(error_msg)
                raise ValueError("Email address not verified with Google")
            
            current_app.logger.info(f"Authenticating user - Email: {email}, Google ID: {google_id}")
            
            # Step 4: Get or create user in database
            current_app.logger.info("Step 4/4: Getting or creating user in database...")
            user = self.get_or_create_user(google_id, email, name, picture)
            
            if not user:
                error_msg = "Failed to get or create user in database"
                current_app.logger.error(error_msg)
                raise ValueError(error_msg)
            
            current_app.logger.info(f"User {'found' if user.id else 'created'} with ID: {user.id}")
            
            # Log successful login attempt
            self.log_login_attempt(user.id, True, email_attempted=email)
            
            # Check if 2FA is enabled for the user
            requires_2fa = getattr(user, 'two_fa_enabled', False)
            current_app.logger.info(f"User authentication {'requires 2FA' if requires_2fa else 'complete'}")
            
            # Prepare response with user data serialized using to_dict()
            current_app.logger.info("=== SERIALIZING USER DATA ===")
            user_data = user.to_dict()
            
            # Log the subscription status from the user data
            subscription_status = user_data.get('subscription_status')
            current_app.logger.info(f"User data subscription_status type: {type(subscription_status)}, value: {subscription_status}")
            
            # Log the raw subscription object
            subscription = user.get_current_subscription()
            if subscription:
                current_app.logger.info(f"Raw subscription status: {subscription.status}, type: {type(subscription.status)}")
                current_app.logger.info(f"Raw subscription status value: {subscription.status.value}")
            
            current_app.logger.info(f"Full serialized user data: {user_data}")
            
            response_data = {
                'user': user_data,
                'tokens': tokens,
                'requires_2fa': requires_2fa
            }
            
            current_app.logger.info("=== FINAL RESPONSE DATA ===")
            current_app.logger.info(f"Response data subscription_status: {user_data.get('subscription_status')}")
            current_app.logger.info("===========================")
            
            current_app.logger.debug(f"Authentication response: {response_data}")
            return response_data
            
        except ValueError as ve:
            error_type = ve.__class__.__name__
            current_app.logger.error(f"Authentication failed ({error_type}): {str(ve)}", exc_info=True)
            email_attempted = user_info.get('email') if user_info else None
            self.log_login_attempt(None, False, str(ve), email_attempted=email_attempted)
            raise
            
        except Exception as e:
            error_type = e.__class__.__name__
            current_app.logger.error(f"Unexpected error during authentication ({error_type}): {str(e)}", exc_info=True)
            email_attempted = user_info.get('email') if user_info else None
            self.log_login_attempt(None, False, str(e), email_attempted=email_attempted)
            raise ValueError(f"Authentication failed: {str(e)}")
            
        finally:
            current_app.logger.info("=== USER AUTHENTICATION COMPLETE ===\n")
    
    def get_or_create_user(self, google_id, email, full_name, profile_picture=None):
        """Get existing user or create new one."""
        current_app.logger.info(f"Getting or creating user with email: {email}, google_id: {google_id}")
        
        # Try to find user by Google ID first
        user = User.query.filter_by(google_id=google_id).first()
        
        if user:
            current_app.logger.info(f"Found existing user by Google ID: {user.id}")
            # Update user info in case it changed
            updates = {}
            if email and user.email != email:
                updates['email'] = email
            if full_name and user.full_name != full_name:
                updates['full_name'] = full_name
            if profile_picture and user.profile_picture != profile_picture:
                updates['profile_picture'] = profile_picture
                
            if updates:
                current_app.logger.info(f"Updating user fields: {updates}")
                for key, value in updates.items():
                    setattr(user, key, value)
                user.updated_at = db.func.now()
                db.session.commit()
            return user
            
        # Try to find user by email (in case they signed up differently)
        if email:
            user = User.query.filter_by(email=email).first()
            if user:
                current_app.logger.info(f"Found existing user by email: {user.id}")
                # Link Google account to existing user
                user.google_id = google_id
                if full_name and user.full_name != full_name:
                    user.full_name = full_name
                if profile_picture and user.profile_picture != profile_picture:
                    user.profile_picture = profile_picture
                user.updated_at = db.func.now()
                db.session.commit()
                return user
        
        # Create new user
        current_app.logger.info("Creating new user")
        user = User(
            google_id=google_id,
            email=email,
            full_name=full_name,
            profile_picture=profile_picture
        )
        
        db.session.add(user)
        db.session.flush()  # Get user ID
        
        # Create user tier status with tier_1=True, tier_2=False, tier_3=False
        from app.models.user_tier_status import UserTierStatus
        tier_status = UserTierStatus(
            user_id=user.id,
            tier_1=True,
            tier_2=False,
            tier_3=False
        )
        db.session.add(tier_status)
        
        # Create default Tier 1 subscription
        current_app.logger.info(f"Creating new subscription for user {user.id}")
        subscription = Subscription(user.id, SubscriptionTier.TIER_1)
        db.session.add(subscription)
        
        db.session.commit()
        current_app.logger.info(f"Created new user with ID: {user.id}")
        
        return user
    
    def log_login_attempt(self, user_id, success, failure_reason=None, email_attempted=None):
        """Log login attempt for security monitoring."""
        ip_address = self.get_client_ip()
        user_agent = request.headers.get('User-Agent', '')
        
        login_attempt = LoginAttempt(
            ip_address=ip_address,
            user_agent=user_agent,
            user_id=user_id,
            email_attempted=email_attempted
        )
        
        if success:
            login_attempt.mark_success()
        else:
            login_attempt.mark_failure(failure_reason)
        
        db.session.add(login_attempt)
        db.session.commit()
    
    def get_client_ip(self):
        """Get client IP address."""
        # Check for forwarded IP (behind proxy/load balancer)
        forwarded_for = request.headers.get('X-Forwarded-For')
        current_app.logger.debug(f"X-Forwarded-For header value: {forwarded_for}")
        
        if forwarded_for:
            ip = forwarded_for.split(',')[0].strip()
            current_app.logger.debug(f"Extracted IP from X-Forwarded-For: {ip}")
            return ip
        elif request.headers.get('X-Real-IP'):
            real_ip = request.headers.get('X-Real-IP')
            current_app.logger.debug(f"Using X-Real-IP: {real_ip}")
            return real_ip
        else:
            remote_addr = request.remote_addr
            current_app.logger.debug(f"Using remote_addr: {remote_addr}")
            return remote_addr
    
    def refresh_access_token(self, refresh_token):
        """Refresh access token using refresh token."""
        discovery_doc = self.get_discovery_document()
        token_endpoint = discovery_doc["token_endpoint"]
        
        token_data = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'refresh_token': refresh_token,
            'grant_type': 'refresh_token'
        }
        
        response = requests.post(token_endpoint, data=token_data)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise ValueError(f"Token refresh failed: {response.text}")
    
    def revoke_token(self, token):
        """Revoke access or refresh token."""
        revoke_url = 'https://oauth2.googleapis.com/revoke'
        
        response = requests.post(revoke_url, data={'token': token})
        
        return response.status_code == 200
    
    def get_user_info(self, access_token):
        """Get user information using access token."""
        user_info_url = 'https://www.googleapis.com/oauth2/v1/userinfo'
        headers = {'Authorization': f'Bearer {access_token}'}
        
        response = requests.get(user_info_url, headers=headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise ValueError(f"Failed to get user info: {response.text}")
    
    @staticmethod
    def check_login_attempts(ip_address, max_attempts=5, time_window=300):
        """Check if IP address has exceeded login attempt limits."""
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(seconds=time_window)

        recent_attempts = LoginAttempt.query.filter_by(
            ip_address=ip_address,
            success=False
        ).filter(
            LoginAttempt.attempted_at > cutoff_time
        ).count()

        result = recent_attempts < max_attempts
        return result
    
    @staticmethod
    def is_ip_blocked(ip_address):
        """Check if IP address is temporarily blocked."""
        return not GoogleOAuth.check_login_attempts(ip_address)