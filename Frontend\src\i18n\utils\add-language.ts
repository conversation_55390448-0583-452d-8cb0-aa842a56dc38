/**
 * Add New Language Utility
 * 
 * Helper script to add a new language to the DeepTrade i18n system.
 * This script generates the necessary files and provides instructions for integration.
 */

import { generateTranslationTemplate } from './translation-validator';

interface LanguageConfig {
  code: string;
  name: string;
  flag: string;
  nativeName: string;
}

/**
 * Generates the TypeScript content for a new language translation file
 */
function generateLanguageFile(languageConfig: LanguageConfig): string {
  const template = generateTranslationTemplate();
  
  return `/**
 * ${languageConfig.name} (${languageConfig.nativeName}) translations for DeepTrade
 * Language code: ${languageConfig.code}
 * 
 * This file contains all translatable strings for the DeepTrade application.
 * Please ensure all strings are properly translated and culturally appropriate.
 */

export default ${JSON.stringify(template, null, 2).replace(/"\[TRANSLATE\]"/g, '"[TRANSLATE]"')};
`;
}

/**
 * Generates instructions for integrating the new language
 */
function generateIntegrationInstructions(languageConfig: LanguageConfig): string {
  return `
# Integration Instructions for ${languageConfig.name} (${languageConfig.code})

## Step 1: Create Translation File
Create the file: \`frontend/src/i18n/locales/${languageConfig.code}/common.ts\`
The template has been generated and should be saved to this location.

## Step 2: Update i18n Configuration
In \`frontend/src/i18n/index.ts\`, make the following changes:

### Add Import
\`\`\`typescript
import ${languageConfig.code}Common from './locales/${languageConfig.code}/common';
\`\`\`

### Add to Languages Array
\`\`\`typescript
export const languages = [
  // ... existing languages
  { code: '${languageConfig.code}', name: '${languageConfig.name}', flag: '${languageConfig.flag}', nativeName: '${languageConfig.nativeName}' },
];
\`\`\`

### Add to Resources
\`\`\`typescript
const resources = {
  // ... existing resources
  ${languageConfig.code}: { common: ${languageConfig.code}Common },
};
\`\`\`

## Step 3: Translate Content
Replace all \`[TRANSLATE]\` placeholders in the generated file with proper translations.

### Translation Guidelines:
- Maintain the same structure as the English version
- Keep translations concise but clear
- Consider cultural context and local conventions
- Test with longer translations to ensure UI compatibility
- Use appropriate formal/informal tone based on target audience

## Step 4: Add Flag Icon Support
Ensure the flag icon for '${languageConfig.flag}' is supported in the FlagIcon component.

## Step 5: Test Integration
1. Start the development server
2. Use the language selector to switch to ${languageConfig.name}
3. Navigate through all pages to verify translations
4. Test responsive design with the new language
5. Check console for any missing translation warnings

## Step 6: Validation
Use the translation validation utilities:
\`\`\`javascript
// In browser console (development mode)
translationUtils.validate()
translationUtils.findMissing('${languageConfig.code}')
translationUtils.findUntranslated('${languageConfig.code}')
\`\`\`

## Common Translation Patterns

### Buttons and Actions
- "Save" → Appropriate action verb in ${languageConfig.name}
- "Cancel" → Appropriate cancellation term
- "Delete" → Strong action verb with appropriate warning tone

### Navigation
- Keep navigation terms short and clear
- Consider menu space limitations
- Use standard terminology for common actions

### Form Labels
- Use clear, descriptive labels
- Consider field validation messages
- Maintain consistency across forms

### Error Messages
- Use clear, helpful language
- Avoid technical jargon
- Provide actionable guidance when possible

### Currency and Numbers
- The system will automatically format numbers and currency based on locale
- Ensure currency symbols and number formats are appropriate

## Notes
- The language will be automatically available in the language selector once integrated
- Language selection will persist in localStorage
- All components using the useTranslation hook will automatically support the new language
- Consider RTL (right-to-left) support if applicable for the language

## Quality Assurance
Before considering the integration complete:
- [ ] All translation keys have been translated
- [ ] UI layouts work properly with translated text
- [ ] Mobile responsiveness is maintained
- [ ] No console warnings for missing translations
- [ ] Cultural appropriateness has been verified
- [ ] Professional translation review (recommended)

For questions or assistance, refer to the i18n documentation or contact the development team.
`;
}

/**
 * Main function to add a new language
 */
export function addNewLanguage(languageConfig: LanguageConfig): {
  translationFile: string;
  instructions: string;
  filename: string;
} {
  // Validate language config
  if (!languageConfig.code || !languageConfig.name || !languageConfig.flag || !languageConfig.nativeName) {
    throw new Error('All language configuration fields are required: code, name, flag, nativeName');
  }
  
  // Validate language code format (should be 2 characters)
  if (!/^[a-z]{2}$/.test(languageConfig.code)) {
    throw new Error('Language code should be 2 lowercase letters (e.g., "it", "ru", "ar")');
  }
  
  const translationFile = generateLanguageFile(languageConfig);
  const instructions = generateIntegrationInstructions(languageConfig);
  const filename = `${languageConfig.code}/common.ts`;
  
  return {
    translationFile,
    instructions,
    filename,
  };
}

/**
 * Predefined language configurations for common languages
 */
export const commonLanguages: Record<string, LanguageConfig> = {
  italian: {
    code: 'it',
    name: 'Italian',
    flag: 'IT',
    nativeName: 'Italiano',
  },
  russian: {
    code: 'ru',
    name: 'Russian',
    flag: 'RU',
    nativeName: 'Русский',
  },
  arabic: {
    code: 'ar',
    name: 'Arabic',
    flag: 'SA',
    nativeName: 'العربية',
  },
  hindi: {
    code: 'hi',
    name: 'Hindi',
    flag: 'IN',
    nativeName: 'हिन्दी',
  },
  dutch: {
    code: 'nl',
    name: 'Dutch',
    flag: 'NL',
    nativeName: 'Nederlands',
  },
  swedish: {
    code: 'sv',
    name: 'Swedish',
    flag: 'SE',
    nativeName: 'Svenska',
  },
  norwegian: {
    code: 'no',
    name: 'Norwegian',
    flag: 'NO',
    nativeName: 'Norsk',
  },
  finnish: {
    code: 'fi',
    name: 'Finnish',
    flag: 'FI',
    nativeName: 'Suomi',
  },
};

/**
 * Development helper - expose to window for easy access
 */
if (process.env.NODE_ENV === 'development') {
  (window as any).addLanguage = {
    add: addNewLanguage,
    common: commonLanguages,
    
    // Helper function to quickly add a common language
    addCommon: (languageKey: keyof typeof commonLanguages) => {
      const config = commonLanguages[languageKey];
      if (!config) {
        console.error(`Language "${languageKey}" not found in common languages`);
        return;
      }
      
      const result = addNewLanguage(config);
      console.group(`🌍 Adding ${config.name} (${config.code})`);
      console.log('Translation file content:');
      console.log(result.translationFile);
      console.log('\nIntegration instructions:');
      console.log(result.instructions);
      console.groupEnd();
      
      return result;
    },
    
    // Helper to list available common languages
    listCommon: () => {
      console.group('🌍 Available Common Languages');
      Object.entries(commonLanguages).forEach(([key, config]) => {
        console.log(`${key}: ${config.name} (${config.nativeName}) - ${config.code}`);
      });
      console.groupEnd();
    }
  };
  
  console.log('🌍 Language utilities loaded. Use addLanguage.listCommon() to see available languages.');
}

export default {
  addNewLanguage,
  commonLanguages,
};
