"""
Payment Routes for DeepTrade Platform
Handles payment processing, subscription payments, and payment history.
"""

from flask import Blueprint, request, jsonify, current_app
from app.auth.decorators import jwt_required
from app.auth.security import SecurityManager
from app.models.user import User
from app.models.payment import Payment, PaymentMethod, PaymentStatus
from app.models.subscription import Subscription, SubscriptionTier
from app import db
from datetime import datetime

payment_bp = Blueprint('payment', __name__, url_prefix='/api/payments')

@payment_bp.route('/create', methods=['POST'])
@jwt_required()
def create_payment():
    """Create a new payment for subscription."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['amount', 'payment_method']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate payment method
        try:
            payment_method = PaymentMethod(data['payment_method'])
        except ValueError:
            return jsonify({'error': 'Invalid payment method'}), 400
        
        # Create payment
        payment = Payment(
            user_id=user_id,
            amount=float(data['amount']),
            currency=data.get('currency', 'USDC'),
            payment_method=payment_method,
            subscription_id=data.get('subscription_id')
        )
        
        db.session.add(payment)
        db.session.commit()
        
        # Log payment creation
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='payment_created',
            ip_address=request.remote_addr,
            details={
                'payment_id': payment.id,
                'amount': float(data['amount']),
                'payment_method': data['payment_method']
            },
            risk_level='medium'
        )
        
        return jsonify({
            'message': 'Payment created successfully',
            'payment': payment.to_dict(),
            'next_steps': {
                'web3_usdc': 'Send USDC to the provided address',
                'stripe_crypto': 'Complete payment via Stripe',
                'manual': 'Contact support for manual processing'
            }.get(payment_method.value, 'Follow payment instructions')
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating payment: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@payment_bp.route('/status/<payment_id>', methods=['GET'])
@jwt_required()
def get_payment_status(payment_id):
    """Get payment status."""
    try:
        user_id = request.user_id
        
        payment = Payment.query.filter_by(
            id=payment_id,
            user_id=user_id
        ).first()
        
        if not payment:
            return jsonify({'error': 'Payment not found'}), 404
        
        return jsonify({
            'payment': payment.to_dict(),
            'status': payment.status.value,
            'confirmations': payment.confirmation_count if payment.is_blockchain_payment() else None,
            'required_confirmations': payment.required_confirmations if payment.is_blockchain_payment() else None
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting payment status: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@payment_bp.route('/history', methods=['GET'])
@jwt_required()
def get_payment_history():
    """Get user's payment history."""
    try:
        from flask_jwt_extended import get_jwt_identity
        user_id = get_jwt_identity()
        current_app.logger.info(f"DEBUG: user_id from get_jwt_identity() = {user_id}")
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        status = request.args.get('status')
        
        # Build query
        query = Payment.query.filter_by(user_id=user_id)
        if status:
            try:
                payment_status = PaymentStatus(status)
                query = query.filter_by(status=payment_status)
            except ValueError:
                return jsonify({'error': 'Invalid payment status'}), 400
        
        payments = query.order_by(Payment.created_at.desc())\
                        .limit(limit).offset(offset).all()
        
        # Log API access
        SecurityManager.log_api_access(
            user_id=user_id,
            endpoint='GET /api/payments/history',
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'payments': [payment.to_dict() for payment in payments],
            'total': Payment.query.filter_by(user_id=user_id).count(),
            'limit': limit,
            'offset': offset
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting payment history: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@payment_bp.route('/methods', methods=['GET'])
@jwt_required()
def get_payment_methods():
    """Get available payment methods."""
    try:
        methods = [
            {
                'id': 'web3_usdc',
                'name': 'Web3 USDC',
                'description': 'Pay with USDC directly to our wallet',
                'fee': '0%',
                'processing_time': '12 confirmations (~3 minutes)'
            },
            {
                'id': 'stripe_crypto',
                'name': 'Stripe Crypto',
                'description': 'Pay with various cryptocurrencies via Stripe',
                'fee': '2.9%',
                'processing_time': 'Instant'
            },
            {
                'id': 'manual',
                'name': 'Manual Payment',
                'description': 'Contact support for alternative payment methods',
                'fee': 'Varies',
                'processing_time': '1-2 business days'
            }
        ]
        
        return jsonify({'payment_methods': methods}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting payment methods: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@payment_bp.route('/webhook', methods=['POST'])
def payment_webhook():
    """Handle payment webhooks from external providers."""
    try:
        # Get webhook data
        data = request.get_json()
        headers = dict(request.headers)
        
        # Log webhook received
        current_app.logger.info(f"Payment webhook received: {data}")
        
        # Handle different webhook types
        webhook_type = headers.get('X-Webhook-Type', 'unknown')
        
        if webhook_type == 'stripe':
            return handle_stripe_webhook(data, headers)
        elif webhook_type == 'blockchain':
            return handle_blockchain_webhook(data, headers)
        else:
            current_app.logger.warning(f"Unknown webhook type: {webhook_type}")
            return jsonify({'error': 'Unknown webhook type'}), 400
        
    except Exception as e:
        current_app.logger.error(f"Error handling payment webhook: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

def handle_stripe_webhook(data, headers):
    """Handle Stripe payment webhooks."""
    try:
        # Verify webhook signature (implement actual verification)
        # signature = headers.get('Stripe-Signature')
        # if not verify_stripe_signature(data, signature):
        #     return jsonify({'error': 'Invalid signature'}), 400
        
        event_type = data.get('type')
        payment_intent_id = data.get('data', {}).get('object', {}).get('id')
        
        if not payment_intent_id:
            return jsonify({'error': 'Invalid webhook data'}), 400
        
        # Find payment by external ID
        payment = Payment.query.filter_by(
            stripe_payment_intent_id=payment_intent_id
        ).first()
        
        if not payment:
            current_app.logger.warning(f"Payment not found for Stripe intent: {payment_intent_id}")
            return jsonify({'message': 'Payment not found'}), 200
        
        # Handle different event types
        if event_type == 'payment_intent.succeeded':
            payment.confirm_payment()
            db.session.commit()
            
            # Update subscription if this was a subscription payment
            if payment.subscription_id:
                subscription = Subscription.query.get(payment.subscription_id)
                if subscription:
                    subscription.activate()
                    db.session.commit()
        
        elif event_type == 'payment_intent.payment_failed':
            payment.fail_payment('Stripe payment failed')
            db.session.commit()
        
        return jsonify({'message': 'Webhook processed successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling Stripe webhook: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

def handle_blockchain_webhook(data, headers):
    """Handle blockchain payment webhooks."""
    try:
        transaction_hash = data.get('transaction_hash')
        from_address = data.get('from_address')
        to_address = data.get('to_address')
        amount = data.get('amount')
        confirmations = data.get('confirmations', 0)
        
        if not transaction_hash:
            return jsonify({'error': 'Invalid webhook data'}), 400
        
        # Find payment by transaction hash
        payment = Payment.query.filter_by(
            transaction_hash=transaction_hash
        ).first()
        
        if not payment:
            current_app.logger.warning(f"Payment not found for transaction: {transaction_hash}")
            return jsonify({'message': 'Payment not found'}), 200
        
        # Update payment with blockchain details
        payment.set_blockchain_details(
            transaction_hash=transaction_hash,
            from_address=from_address,
            to_address=to_address
        )
        
        # Add confirmations
        for _ in range(confirmations - payment.confirmation_count):
            payment.add_confirmation()
        
        db.session.commit()
        
        return jsonify({'message': 'Webhook processed successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling blockchain webhook: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@payment_bp.route('/cancel/<payment_id>', methods=['POST'])
@jwt_required()
def cancel_payment(payment_id):
    """Cancel a pending payment."""
    try:
        user_id = request.user_id
        
        payment = Payment.query.filter_by(
            id=payment_id,
            user_id=user_id
        ).first()
        
        if not payment:
            return jsonify({'error': 'Payment not found'}), 404
        
        if not payment.is_pending():
            return jsonify({'error': 'Payment cannot be cancelled'}), 400
        
        # Cancel payment
        payment.cancel_payment()
        db.session.commit()
        
        # Log payment cancellation
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='payment_cancelled',
            ip_address=request.remote_addr,
            details={'payment_id': payment_id},
            risk_level='low'
        )
        
        return jsonify({'message': 'Payment cancelled successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error cancelling payment: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@payment_bp.route('/refund/<payment_id>', methods=['POST'])
@jwt_required()
def request_refund(payment_id):
    """Request a refund for a confirmed payment."""
    try:
        user_id = request.user_id
        
        payment = Payment.query.filter_by(
            id=payment_id,
            user_id=user_id
        ).first()
        
        if not payment:
            return jsonify({'error': 'Payment not found'}), 404
        
        if not payment.is_confirmed():
            return jsonify({'error': 'Payment must be confirmed to request refund'}), 400
        
        data = request.get_json() or {}
        reason = data.get('reason', 'User requested refund')
        
        # Log refund request
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='refund_requested',
            ip_address=request.remote_addr,
            details={
                'payment_id': payment_id,
                'reason': reason
            },
            risk_level='medium'
        )
        
        return jsonify({
            'message': 'Refund request submitted successfully',
            'refund_request_id': f"REF_{payment_id}_{int(datetime.utcnow().timestamp())}",
            'status': 'pending_review',
            'processing_time': '3-5 business days'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error requesting refund: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500