/**
 * Language Selector Component for DeepTrade
 * 
 * Provides a dropdown menu for language selection with flag emojis.
 * Supports 8 languages with persistent storage and automatic detection.
 */

import React, { useState, useRef, useEffect } from 'react';
// import { useTranslation } from 'react-i18next'; // Not currently used
import { languages, getCurrentLanguage, changeLanguage, getLanguageInfo } from '../../i18n';
import FlagIcon from './FlagIcon';
import { useAuth } from '../../contexts/AuthContext';
import { TermsOfServiceModal } from '../modals/TermsOfServiceModal';
import { PrivacyPolicyModal } from '../modals/PrivacyPolicyModal';

interface LanguageSelectorProps {
  variant?: 'navbar' | 'settings' | 'mobile' | 'modal';
  showLabel?: boolean;
  className?: string;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'navbar',
  showLabel = false,
  className = ''
}) => {
  // const { t } = useTranslation(); // Commented out as not currently used
  const [isOpen, setIsOpen] = useState(false);
  const [currentLang, setCurrentLang] = useState(getCurrentLanguage());
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, isAuthenticated } = useAuth();

  const currentLanguageInfo = getLanguageInfo(currentLang);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Update current language when i18n language changes
  useEffect(() => {
    const handleLanguageChange = () => {
      setCurrentLang(getCurrentLanguage());
    };

    // Listen for language changes
    window.addEventListener('languageChanged', handleLanguageChange);
    return () => window.removeEventListener('languageChanged', handleLanguageChange);
  }, []);

  // Save language preference to backend
  const saveLanguagePreference = async (languageCode: string) => {
    if (!isAuthenticated || !user) {
      return; // Skip backend save for unauthenticated users
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/user/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          language_preference: languageCode
        })
      });

      if (!response.ok) {
        console.warn('Failed to save language preference to backend:', response.statusText);
      } else {
        console.log(`Language preference saved to backend: ${languageCode}`);
      }
    } catch (error) {
      console.warn('Error saving language preference to backend:', error);
    }
  };

  const handleLanguageChange = async (languageCode: string) => {
    try {
      await changeLanguage(languageCode);
      setCurrentLang(languageCode);
      setIsOpen(false);

      // Dispatch custom event for other components
      window.dispatchEvent(new CustomEvent('languageChanged', { detail: languageCode }));

      // Store in localStorage for persistence
      localStorage.setItem('deeptrade-language', languageCode);

      // Save to backend if user is authenticated
      await saveLanguagePreference(languageCode);

      console.log(`Language changed to: ${languageCode}`);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'navbar':
        return {
          button: 'flex items-center space-x-1 px-2 py-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors',
          dropdown: 'absolute right-0 mt-2 min-w-max bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50',
          item: 'flex items-center space-x-2 px-2 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer'
        };
      case 'settings':
        return {
          button: 'flex items-center justify-between w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:border-gray-400 dark:hover:border-gray-500 transition-colors',
          dropdown: 'absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50',
          item: 'flex items-center space-x-3 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer'
        };
      case 'mobile':
        return {
          button: 'flex items-center space-x-2 w-full px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors',
          dropdown: 'mt-2 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700',
          item: 'flex items-center space-x-3 px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors cursor-pointer'
        };
      case 'modal':
        return {
          button: 'flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors',
          dropdown: 'absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50',
          item: 'flex items-center space-x-3 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer'
        };
      default:
        return {
          button: 'flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors',
          dropdown: 'absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50',
          item: 'flex items-center space-x-3 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer'
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Language Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={styles.button}
        aria-label="Select language"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <FlagIcon
          countryCode={currentLanguageInfo?.flag || 'US'}
          size={variant === 'navbar' ? 'sm' : 'md'}
        />
        {showLabel && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {currentLanguageInfo?.nativeName}
          </span>
        )}
        {variant === 'settings' && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {currentLanguageInfo?.nativeName}
            </span>
            <svg
              className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        )}
        {variant !== 'settings' && (
          <svg
            className={`${variant === 'navbar' ? 'w-3 h-3' : 'w-4 h-4'} text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </button>

      {/* Language Dropdown */}
      {isOpen && (
        <div className={styles.dropdown} role="listbox">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={`${styles.item} ${
                currentLang === language.code
                  ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                  : 'text-gray-700 dark:text-gray-300'
              }`}
              role="option"
              aria-selected={currentLang === language.code}
            >
              <FlagIcon
                countryCode={language.flag}
                size={variant === 'navbar' ? 'sm' : 'md'}
              />
              <div className="flex-1 text-left">
                <div className="font-medium">{language.nativeName}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {language.name}
                </div>
              </div>
              {currentLang === language.code && (
                <svg
                  className="w-4 h-4 text-blue-600 dark:text-blue-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </button>
          ))}

          {/* Divider */}
          <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>

          {/* Terms and Privacy Links */}
          <div className="px-3 py-2 space-y-1">
            <button
              onClick={() => {
                setShowTermsModal(true);
                setIsOpen(false);
              }}
              className="w-full text-left text-xs text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
            >
              Terms of Service
            </button>
            <button
              onClick={() => {
                setShowPrivacyModal(true);
                setIsOpen(false);
              }}
              className="w-full text-left text-xs text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
            >
              Privacy Policy
            </button>
          </div>
        </div>
      )}

      {/* Terms of Service Modal */}
      <TermsOfServiceModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
};

export default LanguageSelector;
