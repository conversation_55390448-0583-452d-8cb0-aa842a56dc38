-- Migration script to add paper trading functionality
-- Add paper_trading_mode column to users table
ALTER TABLE users ADD COLUMN paper_trading_mode BOOLEAN NOT NULL DEFAULT FALSE;

-- Create paper_trading_accounts table
CREATE TABLE paper_trading_accounts (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL UNIQUE,
    virtual_balance DECIMAL(20,8) NOT NULL DEFAULT 10000.********,
    initial_balance DECIMAL(20,8) NOT NULL DEFAULT 10000.********,
    total_pnl DECIMAL(20,8) DEFAULT 0.********,
    total_trades_count INTEGER DEFAULT 0,
    winning_trades_count INTEGER DEFAULT 0,
    losing_trades_count INTEGER DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0.00,
    reset_count INTEGER DEFAULT 0,
    last_reset_at DATETIME NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create paper_trades table
CREATE TABLE paper_trades (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    paper_account_id VARCHAR(36) NOT NULL,
    session_id VARCHAR(36) NULL,
    symbol VARCHAR(20) NOT NULL,
    side ENUM('buy', 'sell', 'long', 'short') NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    source VARCHAR(20) NOT NULL DEFAULT 'app',
    entry_price DECIMAL(20,8) NOT NULL,
    exit_price DECIMAL(20,8) NULL,
    stop_loss DECIMAL(20,8) NULL,
    take_profit DECIMAL(20,8) NULL,
    pnl DECIMAL(20,8) NULL,
    simulated_fee DECIMAL(20,8) DEFAULT 0,
    status ENUM('open', 'closed', 'cancelled') NOT NULL DEFAULT 'open',
    exit_reason VARCHAR(100) NULL,
    archived BOOLEAN DEFAULT FALSE,
    entry_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    exit_time DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (paper_account_id) REFERENCES paper_trading_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES paper_trading_sessions(id) ON DELETE SET NULL
);

-- Create paper_trading_sessions table
CREATE TABLE paper_trading_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    paper_account_id VARCHAR(36) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    status ENUM('active', 'paused', 'ended') NOT NULL DEFAULT 'active',
    initial_balance DECIMAL(20,8) NOT NULL,
    current_balance DECIMAL(20,8) NOT NULL,
    leverage INTEGER DEFAULT 1,
    investment_percentage INTEGER DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    total_pnl DECIMAL(20,8) DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ended_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (paper_account_id) REFERENCES paper_trading_accounts(id) ON DELETE CASCADE
);

-- Create paper_balance_snapshots table
CREATE TABLE paper_balance_snapshots (
    id VARCHAR(36) PRIMARY KEY,
    paper_account_id VARCHAR(36) NOT NULL,
    balance DECIMAL(20,8) NOT NULL,
    pnl_change DECIMAL(20,8) DEFAULT 0,
    transaction_type VARCHAR(20) NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (paper_account_id) REFERENCES paper_trading_accounts(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_paper_trades_user_id ON paper_trades(user_id);
CREATE INDEX idx_paper_trades_account_id ON paper_trades(paper_account_id);
CREATE INDEX idx_paper_trades_status ON paper_trades(status);
CREATE INDEX idx_paper_trades_symbol ON paper_trades(symbol);
CREATE INDEX idx_paper_trades_entry_time ON paper_trades(entry_time);

CREATE INDEX idx_paper_sessions_user_id ON paper_trading_sessions(user_id);
CREATE INDEX idx_paper_sessions_account_id ON paper_trading_sessions(paper_account_id);
CREATE INDEX idx_paper_sessions_status ON paper_trading_sessions(status);

CREATE INDEX idx_paper_snapshots_account_id ON paper_balance_snapshots(paper_account_id);
CREATE INDEX idx_paper_snapshots_created_at ON paper_balance_snapshots(created_at);
CREATE INDEX idx_paper_snapshots_transaction_type ON paper_balance_snapshots(transaction_type);

-- Add foreign key constraint for session_id in paper_trades (after paper_trading_sessions table is created)
ALTER TABLE paper_trades ADD CONSTRAINT fk_paper_trades_session_id 
    FOREIGN KEY (session_id) REFERENCES paper_trading_sessions(id) ON DELETE SET NULL;
