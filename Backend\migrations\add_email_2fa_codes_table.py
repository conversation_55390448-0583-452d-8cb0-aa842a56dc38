#!/usr/bin/env python3
"""
Database migration script to add user_2fa_email_codes table
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db

def migrate_database():
    """Apply database schema migrations for email-based 2FA codes."""
    app = create_app()
    
    with app.app_context():
        try:
            # Get database engine
            engine = db.engine
            
            with engine.connect() as conn:
                # Check if user_2fa_email_codes table exists
                result = conn.execute(text("""
                    SELECT TABLE_NAME 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'user_2fa_email_codes'
                """))
                
                table_exists = result.fetchone() is not None
                
                if not table_exists:
                    print("Creating user_2fa_email_codes table...")
                    conn.execute(text("""
                        CREATE TABLE user_2fa_email_codes (
                            id VARCHAR(36) PRIMARY KEY,
                            user_id VARCHAR(36) NOT NULL,
                            code_hash VARCHAR(255) NOT NULL,
                            is_used BOOLEAN DEFAULT FALSE NOT NULL,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                            expires_at DATETIME NOT NULL,
                            used_at DATETIME NULL,
                            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                            INDEX idx_user_2fa_email_codes_user_id (user_id),
                            INDEX idx_user_2fa_email_codes_expires_at (expires_at)
                        )
                    """))
                    print("✓ user_2fa_email_codes table created")
                else:
                    print("✓ user_2fa_email_codes table already exists")
                
                # Commit the transaction
                conn.commit()
                print("\n✅ Database migration completed successfully!")
                
        except SQLAlchemyError as e:
            print(f"❌ Database migration failed: {str(e)}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error during migration: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting database migration for email-based 2FA codes...")
    success = migrate_database()
    if success:
        print("🎉 Migration completed successfully!")
        sys.exit(0)
    else:
        print("💥 Migration failed!")
        sys.exit(1)
