{"version": 3, "file": "price.service-Chbc3caH.js", "sources": ["../../src/services/price.service.ts"], "sourcesContent": ["/**\n * Centralized Price Service for DeepTrade Frontend\n * Handles real-time price updates via Server-Sent Events (SSE)\n */\n\nexport interface PriceData {\n  symbol: string;\n  price: number;\n  timestamp: number;\n  last_updated: string;\n  change_24h?: number;\n  change_percent_24h?: number;\n  high_24h?: number;\n  low_24h?: number;\n  volume_24h?: number;\n}\n\nexport interface PriceUpdate {\n  type: 'connected' | 'price_update' | 'heartbeat' | 'error';\n  symbol?: string;\n  data?: PriceData;\n  current_prices?: { [symbol: string]: PriceData };\n  timestamp: string;\n  message?: string;\n}\n\nexport type PriceUpdateCallback = (symbol: string, priceData: PriceData, previousPrice?: number) => void;\n\nexport class PriceService {\n  private eventSource: EventSource | null = null;\n  private subscribers: Map<string, PriceUpdateCallback[]> = new Map();\n  private currentPrices: Map<string, PriceData> = new Map();\n  private isConnected = false;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000; // Start with 1 second\n\n  constructor() {}\n\n  /**\n   * Start the price streaming connection\n   */\n  connect(): void {\n    if (this.isConnected) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('access_token');\n      if (!token) {\n        console.error('No auth token found for price streaming');\n        return;\n      }\n\n      // Close existing connection if any\n      this.disconnect();\n\n      this.eventSource = new EventSource(`/api/realtime/price-stream?token=${token}`);\n\n      this.eventSource.onopen = () => {\n        console.log('Price streaming connection established');\n        this.isConnected = true;\n        this.reconnectAttempts = 0;\n        this.reconnectDelay = 1000;\n      };\n\n      this.eventSource.onmessage = (event) => {\n        try {\n          const update: PriceUpdate = JSON.parse(event.data);\n          this.handlePriceUpdate(update);\n        } catch (error) {\n          console.error('Error parsing price update:', error);\n        }\n      };\n\n      this.eventSource.onerror = (error) => {\n        console.error('Price streaming error:', error);\n        this.isConnected = false;\n        \n        // Attempt to reconnect with exponential backoff\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n          this.reconnectAttempts++;\n          const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n          \n          console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n          \n          setTimeout(() => {\n            this.connect();\n          }, delay);\n        } else {\n          console.error('Max reconnection attempts reached. Price streaming disabled.');\n        }\n      };\n\n    } catch (error) {\n      console.error('Error establishing price streaming connection:', error);\n    }\n  }\n\n  /**\n   * Disconnect from price streaming\n   */\n  disconnect(): void {\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n    this.isConnected = false;\n  }\n\n  /**\n   * Handle incoming price updates\n   */\n  private handlePriceUpdate(update: PriceUpdate): void {\n    switch (update.type) {\n      case 'connected':\n        console.log('Price streaming connected');\n        // Load initial prices\n        if (update.current_prices) {\n          Object.entries(update.current_prices).forEach(([symbol, priceData]) => {\n            this.currentPrices.set(symbol, priceData);\n          });\n        }\n        break;\n\n      case 'price_update':\n        if (update.symbol && update.data) {\n          const previousPrice = this.currentPrices.get(update.symbol)?.price;\n          this.currentPrices.set(update.symbol, update.data);\n          this.notifySubscribers(update.symbol, update.data, previousPrice);\n        }\n        break;\n\n      case 'heartbeat':\n        // Keep connection alive\n        break;\n\n      case 'error':\n        console.error('Price streaming error:', update.message);\n        break;\n\n      default:\n        console.warn('Unknown price update type:', update.type);\n    }\n  }\n\n  /**\n   * Notify subscribers of price updates\n   */\n  private notifySubscribers(symbol: string, priceData: PriceData, previousPrice?: number): void {\n    const callbacks = this.subscribers.get(symbol) || [];\n    callbacks.forEach(callback => {\n      try {\n        callback(symbol, priceData, previousPrice);\n      } catch (error) {\n        console.error('Error in price update callback:', error);\n      }\n    });\n  }\n\n  /**\n   * Subscribe to price updates for a specific symbol\n   */\n  subscribe(symbol: string, callback: PriceUpdateCallback): () => void {\n    const upperSymbol = symbol.toUpperCase();\n    \n    if (!this.subscribers.has(upperSymbol)) {\n      this.subscribers.set(upperSymbol, []);\n    }\n    \n    this.subscribers.get(upperSymbol)!.push(callback);\n    \n    // Start connection if not already connected\n    if (!this.isConnected) {\n      this.connect();\n    }\n\n    // Send current price if available\n    const currentPrice = this.currentPrices.get(upperSymbol);\n    if (currentPrice) {\n      setTimeout(() => callback(upperSymbol, currentPrice), 0);\n    }\n\n    // Return unsubscribe function\n    return () => {\n      const callbacks = this.subscribers.get(upperSymbol);\n      if (callbacks) {\n        const index = callbacks.indexOf(callback);\n        if (index > -1) {\n          callbacks.splice(index, 1);\n        }\n        \n        // Remove symbol if no more subscribers\n        if (callbacks.length === 0) {\n          this.subscribers.delete(upperSymbol);\n        }\n      }\n\n      // Disconnect if no more subscribers\n      if (this.subscribers.size === 0) {\n        this.disconnect();\n      }\n    };\n  }\n\n  /**\n   * Get current price for a symbol\n   */\n  getCurrentPrice(symbol: string): PriceData | null {\n    return this.currentPrices.get(symbol.toUpperCase()) || null;\n  }\n\n  /**\n   * Get all current prices\n   */\n  getAllPrices(): { [symbol: string]: PriceData } {\n    const prices: { [symbol: string]: PriceData } = {};\n    this.currentPrices.forEach((priceData, symbol) => {\n      prices[symbol] = priceData;\n    });\n    return prices;\n  }\n\n  /**\n   * Get connection status\n   */\n  getConnectionStatus(): boolean {\n    return this.isConnected;\n  }\n\n  /**\n   * Force reconnection\n   */\n  reconnect(): void {\n    this.disconnect();\n    this.reconnectAttempts = 0;\n    this.connect();\n  }\n}\n\n// Global price service instance\nexport const priceService = new PriceService();\n"], "names": ["PriceService", "token", "event", "update", "error", "delay", "symbol", "priceData", "previousPrice", "callback", "upperSymbol", "currentPrice", "callbacks", "index", "prices", "priceService"], "mappings": "AA4BO,MAAMA,CAAa,CAChB,YAAkC,KAClC,gBAAsD,IACtD,kBAA4C,IAC5C,YAAc,GACd,kBAAoB,EACpB,qBAAuB,EACvB,eAAiB,IAEzB,aAAc,CAAC,CAKf,SAAgB,CACd,GAAI,MAAK,YAIT,GAAI,CACF,MAAMC,EAAQ,aAAa,QAAQ,cAAc,EACjD,GAAI,CAACA,EAAO,CACV,QAAQ,MAAM,yCAAyC,EACvD,MACF,CAGA,KAAK,WAAA,EAEL,KAAK,YAAc,IAAI,YAAY,oCAAoCA,CAAK,EAAE,EAE9E,KAAK,YAAY,OAAS,IAAM,CAC9B,QAAQ,IAAI,wCAAwC,EACpD,KAAK,YAAc,GACnB,KAAK,kBAAoB,EACzB,KAAK,eAAiB,GACxB,EAEA,KAAK,YAAY,UAAaC,GAAU,CACtC,GAAI,CACF,MAAMC,EAAsB,KAAK,MAAMD,EAAM,IAAI,EACjD,KAAK,kBAAkBC,CAAM,CAC/B,OAASC,EAAO,CACd,QAAQ,MAAM,8BAA+BA,CAAK,CACpD,CACF,EAEA,KAAK,YAAY,QAAWA,GAAU,CAKpC,GAJA,QAAQ,MAAM,yBAA0BA,CAAK,EAC7C,KAAK,YAAc,GAGf,KAAK,kBAAoB,KAAK,qBAAsB,CACtD,KAAK,oBACL,MAAMC,EAAQ,KAAK,eAAiB,KAAK,IAAI,EAAG,KAAK,kBAAoB,CAAC,EAE1E,QAAQ,IAAI,8BAA8BA,CAAK,eAAe,KAAK,iBAAiB,IAAI,KAAK,oBAAoB,GAAG,EAEpH,WAAW,IAAM,CACf,KAAK,QAAA,CACP,EAAGA,CAAK,CACV,MACE,QAAQ,MAAM,8DAA8D,CAEhF,CAEF,OAASD,EAAO,CACd,QAAQ,MAAM,iDAAkDA,CAAK,CACvE,CACF,CAKA,YAAmB,CACb,KAAK,cACP,KAAK,YAAY,MAAA,EACjB,KAAK,YAAc,MAErB,KAAK,YAAc,EACrB,CAKQ,kBAAkBD,EAA2B,CACnD,OAAQA,EAAO,KAAA,CACb,IAAK,YACH,QAAQ,IAAI,2BAA2B,EAEnCA,EAAO,gBACT,OAAO,QAAQA,EAAO,cAAc,EAAE,QAAQ,CAAC,CAACG,EAAQC,CAAS,IAAM,CACrE,KAAK,cAAc,IAAID,EAAQC,CAAS,CAC1C,CAAC,EAEH,MAEF,IAAK,eACH,GAAIJ,EAAO,QAAUA,EAAO,KAAM,CAChC,MAAMK,EAAgB,KAAK,cAAc,IAAIL,EAAO,MAAM,GAAG,MAC7D,KAAK,cAAc,IAAIA,EAAO,OAAQA,EAAO,IAAI,EACjD,KAAK,kBAAkBA,EAAO,OAAQA,EAAO,KAAMK,CAAa,CAClE,CACA,MAEF,IAAK,YAEH,MAEF,IAAK,QACH,QAAQ,MAAM,yBAA0BL,EAAO,OAAO,EACtD,MAEF,QACE,QAAQ,KAAK,6BAA8BA,EAAO,IAAI,CAAA,CAE5D,CAKQ,kBAAkBG,EAAgBC,EAAsBC,EAA8B,EAC1E,KAAK,YAAY,IAAIF,CAAM,GAAK,CAAA,GACxC,QAAQG,GAAY,CAC5B,GAAI,CACFA,EAASH,EAAQC,EAAWC,CAAa,CAC3C,OAASJ,EAAO,CACd,QAAQ,MAAM,kCAAmCA,CAAK,CACxD,CACF,CAAC,CACH,CAKA,UAAUE,EAAgBG,EAA2C,CACnE,MAAMC,EAAcJ,EAAO,YAAA,EAEtB,KAAK,YAAY,IAAII,CAAW,GACnC,KAAK,YAAY,IAAIA,EAAa,CAAA,CAAE,EAGtC,KAAK,YAAY,IAAIA,CAAW,EAAG,KAAKD,CAAQ,EAG3C,KAAK,aACR,KAAK,QAAA,EAIP,MAAME,EAAe,KAAK,cAAc,IAAID,CAAW,EACvD,OAAIC,GACF,WAAW,IAAMF,EAASC,EAAaC,CAAY,EAAG,CAAC,EAIlD,IAAM,CACX,MAAMC,EAAY,KAAK,YAAY,IAAIF,CAAW,EAClD,GAAIE,EAAW,CACb,MAAMC,EAAQD,EAAU,QAAQH,CAAQ,EACpCI,EAAQ,IACVD,EAAU,OAAOC,EAAO,CAAC,EAIvBD,EAAU,SAAW,GACvB,KAAK,YAAY,OAAOF,CAAW,CAEvC,CAGI,KAAK,YAAY,OAAS,GAC5B,KAAK,WAAA,CAET,CACF,CAKA,gBAAgBJ,EAAkC,CAChD,OAAO,KAAK,cAAc,IAAIA,EAAO,YAAA,CAAa,GAAK,IACzD,CAKA,cAAgD,CAC9C,MAAMQ,EAA0C,CAAA,EAChD,YAAK,cAAc,QAAQ,CAACP,EAAWD,IAAW,CAChDQ,EAAOR,CAAM,EAAIC,CACnB,CAAC,EACMO,CACT,CAKA,qBAA+B,CAC7B,OAAO,KAAK,WACd,CAKA,WAAkB,CAChB,KAAK,WAAA,EACL,KAAK,kBAAoB,EACzB,KAAK,QAAA,CACP,CACF,CAGO,MAAMC,EAAe,IAAIf"}