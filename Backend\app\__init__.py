import os
import logging
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import redis
from config import config
# Reset logging to default and exclude INFO level from console output
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)
logging.basicConfig(level=logging.WARNING)

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()
limiter = Limiter(key_func=get_remote_address)

def get_redis_client():
    """Get Redis client from Flask app extensions."""
    from flask import current_app
    return current_app.extensions.get('redis')

# Global redis client reference for easy access
redis_client = None

def create_app(config_name='default'):
    """Application factory pattern."""
    app = Flask(__name__, static_folder='static', static_url_path='/static')
    
    # Load configuration
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    # Restrict CORS to frontend origins and enable credentials
    # Get frontend URL from config and support multiple origins for deployment flexibility
    frontend_url = app.config.get('FRONTEND_URL', 'http://localhost:5173')
    allowed_origins = [
        frontend_url,  # Primary frontend URL from environment
        "https://deep-trade-frontend.vercel.app",  # Vercel staging URL
        "https://deeptrade.capitolchilax.com",  # Custom domain
        "http://localhost:5173",  # Local development
        "http://localhost:5174",  # Alternative local port
        "http://127.0.0.1:5173"   # Alternative localhost
    ]

    CORS(
        app,
        origins=allowed_origins,
        supports_credentials=True,
        methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["Content-Type", "Authorization"],
        expose_headers=["Content-Type", "Authorization"],
        max_age=600  # Cache preflight request for 10 minutes
    )
    # Initialize rate limiter
    # limiter.init_app(app)
    
    # Setup Redis connection using Flask extension pattern
    global redis_client
    try:
        redis_client = redis.from_url(app.config['REDIS_URL'])
        if redis_client:
            redis_client.ping()  # Test the connection
            app.extensions['redis'] = redis_client
            app.logger.info("Successfully connected to Redis")
        else:
            raise Exception("Failed to create Redis client")
    except Exception as e:
        app.logger.warning(f"Redis connection failed: {e}")
        app.extensions['redis'] = None
        redis_client = None
    
    # Initialize JWT manager with Redis client
    from app.auth.jwt_manager import jwt_manager
    jwt_manager.redis_client = redis_client
    if redis_client:
        jwt_manager.using_redis = True
    
    # Enhanced logging configuration
    if not os.path.exists('logs'):
        os.makedirs('logs', exist_ok=True)
    
    # Clear any existing handlers
    app.logger.handlers.clear()
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # File handler - logs everything
    file_handler = logging.FileHandler('logs/deeptrade.log', encoding='utf-8')
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.WARNING)
    
    # Console handler - more concise
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.WARNING)
    
    # Add handlers to the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.WARNING)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Set app logger level
    app.logger.setLevel(logging.WARNING)
    
    # Log essential startup information
    app.logger.info(f'DeepTrade startup - Environment: {config_name}')

    # Disable Werkzeug HTTP request logging for cleaner console output
    logging.getLogger('werkzeug').setLevel(logging.ERROR)
    
    # Register all API blueprints
    from .api import blueprints
    for blueprint, url_prefix in blueprints:
        app.register_blueprint(blueprint, url_prefix=url_prefix)
    
    # Register main routes
    from app.main import main_bp
    app.register_blueprint(main_bp)

    # Create default admin user on app startup
    with app.app_context():
        try:
            from app.models.admin import AdminUser
            AdminUser.create_default_admin()
        except Exception as e:
            app.logger.error(f"Error creating default admin: {str(e)}")

    # Initialize and start the centralized price service
    try:
        from app.services.price_service import price_service
        price_service.start()
        app.logger.info("Centralized price service started successfully")
    except Exception as e:
        app.logger.error(f"Error starting price service: {str(e)}")

    # Initialize and start the ML forecast service
    try:
        from app.services.forecast_service import start_forecast_service
        start_forecast_service()
        app.logger.info("ML forecast service started successfully")
    except Exception as e:
        app.logger.error(f"Error starting ML forecast service: {str(e)}")

    # Initialize ML prediction services on startup
    try:
        from app.services.startup_services import initialize_ml_services
        initialize_ml_services()
        app.logger.info("ML prediction services initialized successfully")
    except Exception as e:
        app.logger.error(f"Error initializing ML prediction services: {str(e)}")

    # Initialize trading bot services on startup
    try:
        from app.services.startup_services import initialize_trading_services
        initialize_trading_services()
        app.logger.info("Trading bot services initialized successfully")
    except Exception as e:
        app.logger.error(f"Error initializing trading bot services: {str(e)}")

    # Add static file route for logo (for email templates)
    @app.route('/logo')
    def serve_logo():
        """Serve the DeepTrade logo for email templates."""
        import os
        from flask import send_file
        logo_path = os.path.join(app.root_path, 'static', 'images', 'deeptrade.png')
        if os.path.exists(logo_path):
            return send_file(logo_path, mimetype='image/png')
        else:
            return {'error': f'Logo not found at {logo_path}'}, 404
    
    # JWT configuration
    @jwt.token_in_blocklist_loader
    def check_if_token_revoked(jwt_header, jwt_payload):
        from app.auth.jwt_manager import jwt_manager
        try:
            # Ensure JWT manager is initialized
            if not getattr(jwt_manager, '_initialized', False):
                jwt_manager._ensure_initialized()

            result = jwt_manager.is_token_revoked(jwt_payload)
            return result
        except Exception:
            # Don't revoke token for initialization errors - return False to allow the token
            return False  # Allow token to proceed, let our custom decorators handle validation
    
    # JWT error handlers
    from flask import current_app

    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        current_app.logger.error(f"[JWT ERROR] Token expired: header={jwt_header}, payload={jwt_payload}")
        return {'message': 'Token has expired', 'error': 'token_expired'}, 401
        
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        current_app.logger.error(f"[JWT ERROR] Invalid token: {error}")
        return {'message': 'Invalid token', 'error': 'invalid_token'}, 401
        
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        current_app.logger.error(f"[JWT ERROR] Unauthorized: {error}")
        return {'message': 'Authorization token required', 'error': 'authorization_required'}, 401
        
    @jwt.revoked_token_loader
    def revoked_token_callback(jwt_header, jwt_payload):
        return {'message': 'Token has been revoked', 'error': 'token_revoked'}, 401
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return {'message': 'Resource not found', 'error': 'not_found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return {'message': 'Internal server error', 'error': 'internal_error'}, 500
    
    @app.errorhandler(429)
    def ratelimit_handler(e):
        return {'message': 'Rate limit exceeded', 'error': 'rate_limit'}, 429
    
    return app

# Import models to ensure they are registered with SQLAlchemy
from app.models import user, subscription, payment, trade, fee_calculation, security_log
from app.models.user_tier_status import UserTierStatus
from app.models.password_reset import PasswordResetToken, TwoFAResetRequest