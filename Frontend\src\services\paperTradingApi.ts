/**
 * Paper Trading API Service
 * 
 * This service handles all API calls related to paper trading functionality
 * including mode switching, account management, and trade operations.
 */

const API_BASE_URL = '/api/paper-trading';

interface PaperTradingMode {
  success: boolean;
  mode: 'paper' | 'live';
  paper_trading_enabled: boolean;
}

interface PaperAccount {
  success: boolean;
  account: {
    id: string;
    user_id: string;
    virtual_balance: number;
    initial_balance: number;
    total_pnl: number;
    total_trades_count: number;
    winning_trades_count: number;
    losing_trades_count: number;
    win_rate: number;
    reset_count: number;
    last_reset_at: string | null;
    created_at: string;
    updated_at: string;
  };
  performance: {
    total_return_percentage: number;
    recent_trades_count: number;
    account_age_days: number;
  };
}

interface PaperTradingHistory {
  success: boolean;
  trades: Array<{
    id: string;
    symbol: string;
    side: string;
    quantity: number;
    entry_price: number;
    exit_price: number | null;
    pnl: number | null;
    status: string;
    entry_time: string;
    exit_time: string | null;
  }>;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
  };
}

interface PaperTradingAnalytics {
  success: boolean;
  period: {
    start_date: string;
    end_date: string;
    days: number;
  };
  summary: {
    total_trades: number;
    closed_trades: number;
    winning_trades: number;
    losing_trades: number;
    win_rate: number;
    total_pnl: number;
    average_win: number;
    average_loss: number;
    profit_factor: number;
  };
  balance_history: Array<{
    date: string;
    balance: number;
    pnl_change: number;
  }>;
  current_balance: number;
}

class PaperTradingApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('access_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }
    return response.json();
  }

  /**
   * Get current trading mode (paper or live)
   */
  async getTradingMode(): Promise<PaperTradingMode> {
    const response = await fetch(`${API_BASE_URL}/mode`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<PaperTradingMode>(response);
  }

  /**
   * Switch between paper and live trading modes
   */
  async switchTradingMode(paperMode: boolean): Promise<{ success: boolean; mode: string; message: string }> {
    const response = await fetch(`${API_BASE_URL}/mode`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ paper_mode: paperMode })
    });
    return this.handleResponse(response);
  }

  /**
   * Get paper trading account summary
   */
  async getPaperAccount(): Promise<PaperAccount> {
    const response = await fetch(`${API_BASE_URL}/account`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<PaperAccount>(response);
  }

  /**
   * Reset paper trading account
   */
  async resetPaperAccount(newBalance?: number): Promise<{ success: boolean; message: string; new_balance: number; reset_count: number }> {
    const response = await fetch(`${API_BASE_URL}/account/reset`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(newBalance ? { new_balance: newBalance } : {})
    });
    return this.handleResponse(response);
  }

  /**
   * Get paper trading history with pagination
   */
  async getPaperTradingHistory(limit: number = 50, offset: number = 0): Promise<PaperTradingHistory> {
    const response = await fetch(`${API_BASE_URL}/history?limit=${limit}&offset=${offset}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<PaperTradingHistory>(response);
  }

  /**
   * Get paper trading analytics for specified period
   */
  async getPaperTradingAnalytics(days: number = 30): Promise<PaperTradingAnalytics> {
    const response = await fetch(`${API_BASE_URL}/analytics?days=${days}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<PaperTradingAnalytics>(response);
  }

  /**
   * Create a new paper trading session
   */
  async createPaperTradingSession(symbol: string = 'BTCUSDT', leverage: number = 1, investmentPercentage: number = 0): Promise<{ success: boolean; session: any }> {
    const response = await fetch(`${API_BASE_URL}/session`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        symbol,
        leverage,
        investment_percentage: investmentPercentage
      })
    });
    return this.handleResponse(response);
  }

  /**
   * Get current paper trading positions
   */
  async getPaperPositions(): Promise<{ success: boolean; positions: any[] }> {
    const response = await fetch(`${API_BASE_URL}/positions`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse(response);
  }

  /**
   * Close a specific paper trading position
   */
  async closePaperPosition(positionId: string, reason: string = 'manual'): Promise<{ success: boolean; trade_id: string; exit_price: string; pnl: string; exit_reason: string }> {
    const response = await fetch(`${API_BASE_URL}/positions/${positionId}/close`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ reason })
    });
    return this.handleResponse(response);
  }

  /**
   * Get current paper trading balance
   */
  async getPaperBalance(): Promise<{ success: boolean; balance: number; currency: string }> {
    const response = await fetch(`${API_BASE_URL}/balance`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse(response);
  }
}

// Export singleton instance
export const paperTradingApi = new PaperTradingApiService();
export default paperTradingApi;
