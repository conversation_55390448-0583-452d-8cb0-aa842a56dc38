#!/usr/bin/env python3
"""
Database migration script to add password_hash column and fix login_attempts failure_reason length
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models.user import User
from app.models.security_log import LoginAttempt

def migrate_database():
    """Apply database schema migrations."""
    app = create_app()
    
    with app.app_context():
        try:
            # Get database engine
            engine = db.engine
            
            # Check if password_hash column exists
            with engine.connect() as conn:
                # Check for password_hash column
                result = conn.execute(text("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'password_hash'
                """))
                
                password_hash_exists = result.fetchone() is not None
                
                if not password_hash_exists:
                    print("Adding password_hash column to users table...")
                    conn.execute(text("""
                        ALTER TABLE users 
                        ADD COLUMN password_hash VARCHAR(255) NULL AFTER profile_picture
                    """))
                    print("✓ password_hash column added")
                
                # Check if google_id column is nullable
                result = conn.execute(text("""
                    SELECT IS_NULLABLE 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'google_id'
                """))
                
                google_id_nullable = result.fetchone()
                if google_id_nullable and google_id_nullable[0] == 'NO':
                    print("Making google_id column nullable...")
                    conn.execute(text("""
                        ALTER TABLE users 
                        MODIFY COLUMN google_id VARCHAR(255) NULL
                    """))
                    print("✓ google_id column made nullable")
                
                # Check failure_reason column type in login_attempts
                result = conn.execute(text("""
                    SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'login_attempts' 
                    AND COLUMN_NAME = 'failure_reason'
                """))
                
                failure_reason_info = result.fetchone()
                if failure_reason_info and failure_reason_info[0] == 'varchar' and failure_reason_info[1] == 255:
                    print("Expanding failure_reason column in login_attempts...")
                    conn.execute(text("""
                        ALTER TABLE login_attempts 
                        MODIFY COLUMN failure_reason TEXT
                    """))
                    print("✓ failure_reason column expanded to TEXT")
                
                # Commit changes
                conn.commit()
                
            print("Database migration completed successfully!")
            
        except SQLAlchemyError as e:
            print(f"Database migration failed: {e}")
            db.session.rollback()
            return False
        except Exception as e:
            print(f"Unexpected error during migration: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("Starting database migration...")
    success = migrate_database()
    if success:
        print("Migration completed! You can now restart your application.")
    else:
        print("Migration failed. Please check the errors above.")
        sys.exit(1)