<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Settings - DeepTrade</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1F2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- Navigation Header -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-900 dark:text-white">
                        DeepTrade Admin
                    </a>
                    <span class="ml-4 text-gray-500 dark:text-gray-400">/</span>
                    <span class="ml-2 text-gray-700 dark:text-gray-300">Settings</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg id="sun-icon" class="w-5 h-5 hidden dark:block" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                        </svg>
                        <svg id="moon-icon" class="w-5 h-5 block dark:hidden" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                        </svg>
                    </button>
                    <span class="text-sm text-gray-600 dark:text-gray-400" id="admin-username">{{ admin_username }}</span>
                    <button onclick="logout()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <!-- Page Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Admin Settings</h1>
                <p class="mt-2 text-gray-600 dark:text-gray-400">Manage admin accounts, passwords, and system settings</p>
            </div>

            <!-- Settings Tabs -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <nav class="-mb-px flex space-x-8 px-6">
                        <button id="password-tab" class="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400">
                            Change Password
                        </button>
                        <button id="email-tab" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Change Email
                        </button>
                        <button id="admin-management-tab" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Admin Management
                        </button>
                        <button id="create-admin-tab" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Create Admin
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Password Change Tab -->
                    <div id="password-content" class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Change Password</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">A verification code will be sent to your email address.</p>
                            
                            <form id="password-change-form" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">New Password</label>
                                    <input type="password" id="new-password" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                           placeholder="Enter new password">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Confirm New Password</label>
                                    <input type="password" id="confirm-password" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                           placeholder="Confirm new password">
                                </div>
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium">
                                    Send Verification Code
                                </button>
                            </form>

                            <!-- Verification Code Section (Hidden initially) -->
                            <div id="verification-section" class="hidden mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Email Verification</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Enter the 6-digit code sent to your email address.</p>
                                <div class="flex space-x-2">
                                    <input type="text" id="verification-code" maxlength="6" 
                                           class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                           placeholder="Enter 6-digit code">
                                    <button onclick="verifyPasswordChange()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium">
                                        Verify & Change
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Change Tab -->
                    <div id="email-content" class="space-y-6 hidden">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Change Email Address</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Update your admin email address. A verification code will be sent to both old and new email addresses.</p>
                            
                            <form id="email-change-form" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Email</label>
                                    <input type="email" id="current-email" readonly
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white"
                                           value="{{ admin_username }}">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">New Email Address</label>
                                    <input type="email" id="new-email" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                           placeholder="Enter new email address">
                                </div>
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium">
                                    Send Verification Codes
                                </button>
                            </form>

                            <!-- Email Verification Section (Hidden initially) -->
                            <div id="email-verification-section" class="hidden mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Email Verification</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Enter the verification codes sent to both email addresses.</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Old Email Code</label>
                                        <input type="text" id="old-email-code" maxlength="6" 
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                               placeholder="6-digit code">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Email Code</label>
                                        <input type="text" id="new-email-code" maxlength="6" 
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                               placeholder="6-digit code">
                                    </div>
                                </div>
                                <button onclick="verifyEmailChange()" class="mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium">
                                    Verify & Update Email
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Management Tab -->
                    <div id="admin-management-content" class="space-y-6 hidden">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Admin Accounts</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Manage all administrator accounts</p>
                            </div>
                            <button id="refresh-admins-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                Refresh
                            </button>
                        </div>
                        
                        <!-- Admin Statistics -->
                        <div id="admin-stats" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Total Admins:</span>
                                    <span id="total-admins" class="font-medium text-gray-900 dark:text-white ml-1">-</span>
                                </div>
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Super Admins:</span>
                                    <span id="super-admins" class="font-medium text-gray-900 dark:text-white ml-1">-</span>
                                </div>
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Active:</span>
                                    <span id="active-admins" class="font-medium text-gray-900 dark:text-white ml-1">-</span>
                                </div>
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Limited:</span>
                                    <span id="limited-admins" class="font-medium text-gray-900 dark:text-white ml-1">-</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Admin List -->
                        <div id="admin-list" class="space-y-4">
                            <!-- Admin items will be loaded here -->
                        </div>
                    </div>

                    <!-- Create Admin Tab -->
                    <div id="create-admin-content" class="space-y-6 hidden">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Create New Admin</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Add a new administrator to the system.</p>
                            
                            <form id="create-admin-form" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address</label>
                                    <input type="email" id="admin-email" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                           placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password</label>
                                    <input type="password" id="admin-password" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                           placeholder="Enter password">
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="is-super-admin" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="is-super-admin" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                        Super Admin (Full access)
                                    </label>
                                </div>
                                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium">
                                    Create Admin
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('=== Admin settings page JavaScript loading ===');
        // JavaScript is working test removed

        // Get admin token from localStorage or session (check both possible keys)
        let adminToken = localStorage.getItem('admin_token') || localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
        console.log('Admin token found:', adminToken ? 'Yes' : 'No');

        // Test if basic DOM elements exist
        console.log('Testing DOM elements...');
        setTimeout(() => {
            console.log('Password tab exists:', !!document.getElementById('password-tab'));
            console.log('Email tab exists:', !!document.getElementById('email-tab'));
            console.log('Admin management tab exists:', !!document.getElementById('admin-management-tab'));
            console.log('Create admin tab exists:', !!document.getElementById('create-admin-tab'));

            // Simple test removed - using main initialization now
        }, 100);

        // If no token found, try to get it from the main admin dashboard
        if (!adminToken) {
            // Check if we're coming from the main admin dashboard
            const referrer = document.referrer;
            if (referrer && referrer.includes('/admin') && !referrer.includes('/login')) {
                // Try to get token from parent window or opener
                try {
                    if (window.opener && window.opener.adminToken) {
                        adminToken = window.opener.adminToken;
                        localStorage.setItem('admin_token', adminToken);
                    } else if (window.parent && window.parent.adminToken) {
                        adminToken = window.parent.adminToken;
                        localStorage.setItem('admin_token', adminToken);
                    }
                } catch (e) {
                    console.log('Could not access parent window token');
                }
            }

            // If still no token, redirect to main admin page instead of login
            if (!adminToken) {
                console.log('No admin token found, redirecting to main admin page');
                // Check if we're already on the main page to avoid redirect loop
                if (window.location.pathname !== '/') {
                    window.location.href = '/';
                }
                return;
            }
        }

        // Verify token is valid by making a test API call
        async function verifyToken() {
            try {
                const response = await fetch('/api/admin/settings/admins', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    console.log('Token verification failed, redirecting to main admin page');
                    localStorage.removeItem('admin_token');
                    localStorage.removeItem('adminToken');
                    sessionStorage.removeItem('adminToken');
                    window.location.href = '/';
                    return false;
                }
                return true;
            } catch (error) {
                console.error('Token verification error:', error);
                window.location.href = '/';
                return false;
            }
        }

        // Verify token on page load
        verifyToken();

        // Load admin username from token
        function loadAdminUsername() {
            try {
                if (adminToken) {
                    const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
                    const username = tokenPayload.username || tokenPayload.sub || 'Admin';
                    document.getElementById('admin-username').textContent = username;
                    console.log('Admin username loaded:', username);
                }
            } catch (error) {
                console.error('Error loading admin username:', error);
                document.getElementById('admin-username').textContent = 'Admin';
            }
        }

        // Load username
        loadAdminUsername();

        // Theme toggle functionality
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to light mode
        const currentTheme = localStorage.getItem('theme') || 'light';
        html.classList.toggle('dark', currentTheme === 'dark');

        // Theme toggle event listener will be added in initializeAllEventListeners

        // Tab switching functionality
        function switchTab(tab) {
            console.log('switchTab called with:', tab);
            const tabClass = 'py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300';
            const activeTabClass = 'py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400';

            // Get all tab elements
            const passwordTab = document.getElementById('password-tab');
            const emailTab = document.getElementById('email-tab');
            const adminManagementTab = document.getElementById('admin-management-tab');
            const createAdminTab = document.getElementById('create-admin-tab');

            const passwordContent = document.getElementById('password-content');
            const emailContent = document.getElementById('email-content');
            const adminManagementContent = document.getElementById('admin-management-content');
            const createAdminContent = document.getElementById('create-admin-content');

            // Check if elements exist
            if (!passwordTab || !emailTab || !adminManagementTab || !createAdminTab) {
                console.error('Tab elements not found');
                return;
            }

            if (!passwordContent || !emailContent || !adminManagementContent || !createAdminContent) {
                console.error('Content elements not found');
                return;
            }

            // Reset all tabs
            passwordTab.className = tabClass;
            emailTab.className = tabClass;
            adminManagementTab.className = tabClass;
            createAdminTab.className = tabClass;

            // Hide all content
            passwordContent.classList.add('hidden');
            emailContent.classList.add('hidden');
            adminManagementContent.classList.add('hidden');
            createAdminContent.classList.add('hidden');

            // Show selected tab
            if (tab === 'password') {
                passwordTab.className = activeTabClass;
                passwordContent.classList.remove('hidden');
                console.log('Password tab activated');
            } else if (tab === 'email') {
                emailTab.className = activeTabClass;
                emailContent.classList.remove('hidden');
                console.log('Email tab activated');
            } else if (tab === 'admin-management') {
                adminManagementTab.className = activeTabClass;
                adminManagementContent.classList.remove('hidden');
                loadAdminList();
                console.log('Admin management tab activated');
            } else if (tab === 'create-admin') {
                createAdminTab.className = activeTabClass;
                createAdminContent.classList.remove('hidden');
                console.log('Create admin tab activated');
            }
        }

        // Old complex initializeTabs function removed - using simple approach now

        // Logout function
        function logout() {
            localStorage.removeItem('admin_token');
            localStorage.removeItem('adminToken');
            sessionStorage.removeItem('adminToken');
            window.location.href = '/';
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full ${
                type === 'success' ? 'bg-green-600' :
                type === 'error' ? 'bg-red-600' :
                'bg-blue-600'
            }`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // Slide in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Slide out and remove
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }



        // Verification function
        async function verifyPasswordChange() {
            const verificationCode = document.getElementById('verification-code').value;

            if (!verificationCode || verificationCode.length !== 6) {
                showToast('Please enter a valid 6-digit code', 'error');
                return;
            }

            try {
                const response = await fetch('/api/admin/verify-password-change', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ verification_code: verificationCode })
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Password changed successfully!', 'success');
                    // Reset form
                    document.getElementById('password-change-form').reset();
                    document.getElementById('verification-section').classList.add('hidden');
                    document.getElementById('verification-code').value = '';
                } else {
                    showToast(data.message || 'Verification failed', 'error');
                }
            } catch (error) {
                console.error('Error verifying password change:', error);
                showToast('Network error. Please try again.', 'error');
            }
        }



        // Email verification function
        async function verifyEmailChange() {
            const oldEmailCode = document.getElementById('old-email-code').value;
            const newEmailCode = document.getElementById('new-email-code').value;

            if (!oldEmailCode || oldEmailCode.length !== 6 || !newEmailCode || newEmailCode.length !== 6) {
                showToast('Please enter valid 6-digit codes for both emails', 'error');
                return;
            }

            try {
                const response = await fetch('/api/admin/verify-email-change', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        old_email_code: oldEmailCode,
                        new_email_code: newEmailCode
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showToast('Email address updated successfully!', 'success');
                    // Update current email display
                    document.getElementById('current-email').value = data.new_email;
                    document.getElementById('admin-username').textContent = data.new_email;
                    // Reset form
                    document.getElementById('email-change-form').reset();
                    document.getElementById('email-verification-section').classList.add('hidden');
                } else {
                    showToast(data.message || 'Email verification failed', 'error');
                }
            } catch (error) {
                console.error('Error verifying email change:', error);
                showToast('Network error. Please try again.', 'error');
            }
        }



        // Admin Management Functions
        async function loadAdminList() {
            try {
                const response = await fetch('/api/admin/settings/admins', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAdminList(data.admins);
                    updateAdminStats(data.statistics);
                } else {
                    showToast('Failed to load admin list', 'error');
                }
            } catch (error) {
                console.error('Error loading admin list:', error);
                showToast('Network error loading admin list', 'error');
            }
        }

        function displayAdminList(admins) {
            const adminList = document.getElementById('admin-list');
            adminList.innerHTML = '';

            admins.forEach(admin => {
                const adminItem = document.createElement('div');
                adminItem.className = 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4';

                const statusBadge = admin.is_active
                    ? '<span class="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 text-xs px-2 py-1 rounded-full">Active</span>'
                    : '<span class="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 text-xs px-2 py-1 rounded-full">Inactive</span>';

                const roleBadge = admin.is_super_admin
                    ? '<span class="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 text-xs px-2 py-1 rounded-full">Super Admin</span>'
                    : '<span class="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-full">Limited Admin</span>';

                adminItem.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="font-medium text-gray-900 dark:text-white">${admin.username}</span>
                                ${roleBadge}
                                ${statusBadge}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                <div>Created: ${admin.created_at ? new Date(admin.created_at).toLocaleDateString('en-US', {
                                    timeZone: 'America/Sao_Paulo', // GMT-3
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit'
                                }) : 'N/A'}</div>
                                <div>Last Login: ${admin.last_login_timestamp ? new Date(admin.last_login_timestamp).toLocaleDateString('en-US', {
                                    timeZone: 'America/Sao_Paulo', // GMT-3
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit'
                                }) : 'Never'}</div>
                                <div>Total Logins: ${admin.total_logins || 0}</div>
                                <div>Recent Actions: ${admin.recent_actions_count || 0} (last 7 days)</div>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <button onclick="resetAdminPassword(${admin.id}, '${admin.username}')"
                                    class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium">
                                Reset Password
                            </button>
                            ${admin.id !== getCurrentAdminId() ? `
                                <button onclick="toggleAdminStatus(${admin.id}, ${admin.is_active})"
                                        class="${admin.is_active ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'} text-white px-3 py-1 rounded text-xs font-medium">
                                    ${admin.is_active ? 'Disable' : 'Enable'}
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;

                adminList.appendChild(adminItem);
            });
        }

        function updateAdminStats(stats) {
            document.getElementById('total-admins').textContent = stats.total_admins;
            document.getElementById('super-admins').textContent = stats.super_admins;
            document.getElementById('active-admins').textContent = stats.active_admins;
            document.getElementById('limited-admins').textContent = stats.limited_admins;
        }

        function getCurrentAdminId() {
            try {
                const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
                return tokenPayload.admin_id;
            } catch (error) {
                return null;
            }
        }

        async function resetAdminPassword(adminId, username) {
            const newPassword = prompt(`Enter new password for ${username}:`);
            if (!newPassword) return;

            if (newPassword.length < 8) {
                showToast('Password must be at least 8 characters long', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/admin/admins/${adminId}/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ new_password: newPassword })
                });

                if (response.ok) {
                    showToast(`Password reset successfully for ${username}`, 'success');
                } else {
                    const error = await response.json();
                    showToast(error.message || 'Failed to reset password', 'error');
                }
            } catch (error) {
                console.error('Error resetting password:', error);
                showToast('Network error resetting password', 'error');
            }
        }

        async function toggleAdminStatus(adminId, currentStatus) {
            const action = currentStatus ? 'disable' : 'enable';
            if (!confirm(`Are you sure you want to ${action} this admin?`)) return;

            try {
                const response = await fetch(`/api/admin/admins/${adminId}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ is_active: !currentStatus })
                });

                if (response.ok) {
                    showToast(`Admin ${action}d successfully`, 'success');
                    loadAdminList(); // Refresh the list
                } else {
                    const error = await response.json();
                    showToast(error.message || `Failed to ${action} admin`, 'error');
                }
            } catch (error) {
                console.error(`Error ${action}ing admin:`, error);
                showToast(`Network error ${action}ing admin`, 'error');
            }
        }

        // Simple direct tab initialization
        function initializeTabsSimple() {
            console.log('=== SIMPLE TAB INITIALIZATION ===');

            // Get tab elements
            const passwordTab = document.getElementById('password-tab');
            const emailTab = document.getElementById('email-tab');
            const adminManagementTab = document.getElementById('admin-management-tab');
            const createAdminTab = document.getElementById('create-admin-tab');

            console.log('Tab elements found:', {
                passwordTab: !!passwordTab,
                emailTab: !!emailTab,
                adminManagementTab: !!adminManagementTab,
                createAdminTab: !!createAdminTab
            });

            // Add simple click handlers
            if (passwordTab) {
                passwordTab.onclick = function() {
                    console.log('Password tab clicked - simple handler');
                    switchTab('password');
                };
            }

            if (emailTab) {
                emailTab.onclick = function() {
                    console.log('Email tab clicked - simple handler');
                    switchTab('email');
                };
            }

            if (adminManagementTab) {
                adminManagementTab.onclick = function() {
                    console.log('Admin management tab clicked - simple handler');
                    switchTab('admin-management');
                };
            }

            if (createAdminTab) {
                createAdminTab.onclick = function() {
                    console.log('Create admin tab clicked - simple handler');
                    switchTab('create-admin');
                };
            }

            console.log('Simple tab handlers added');

            // Set default tab
            switchTab('password');
        }

        // Initialize all event listeners
        function initializeAllEventListeners() {
            console.log('Initializing all event listeners...');

            // Initialize tabs with simple approach
            initializeTabsSimple();

            // Password change form
            const passwordForm = document.getElementById('password-change-form');
            if (passwordForm) {
                passwordForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const newPassword = document.getElementById('new-password').value;
                    const confirmPassword = document.getElementById('confirm-password').value;

                    if (newPassword !== confirmPassword) {
                        showToast('Passwords do not match', 'error');
                        return;
                    }

                    try {
                        const response = await fetch('/api/admin/request-password-change', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${adminToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ new_password: newPassword })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            showToast('Verification code sent to your email', 'success');
                            document.getElementById('verification-section').classList.remove('hidden');
                            document.getElementById('verification-code').focus();
                        } else {
                            showToast(data.message || 'Failed to send verification code', 'error');
                        }
                    } catch (error) {
                        console.error('Error requesting password change:', error);
                        showToast('Network error. Please try again.', 'error');
                    }
                });
                console.log('Password form listener added');
            }

            // Email change form
            const emailForm = document.getElementById('email-change-form');
            if (emailForm) {
                emailForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const newEmail = document.getElementById('new-email').value;

                    if (!newEmail || !newEmail.includes('@')) {
                        showToast('Please enter a valid email address', 'error');
                        return;
                    }

                    try {
                        const response = await fetch('/api/admin/request-email-change', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${adminToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ new_email: newEmail })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            showToast('Verification codes sent to both email addresses', 'success');
                            document.getElementById('email-verification-section').classList.remove('hidden');
                            document.getElementById('old-email-code').focus();
                        } else {
                            showToast(data.message || 'Failed to send verification codes', 'error');
                        }
                    } catch (error) {
                        console.error('Error requesting email change:', error);
                        showToast('Network error. Please try again.', 'error');
                    }
                });
                console.log('Email form listener added');
            }

            // Create admin form
            const createAdminForm = document.getElementById('create-admin-form');
            if (createAdminForm) {
                createAdminForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const email = document.getElementById('admin-email').value;
                    const password = document.getElementById('admin-password').value;
                    const isSuperAdmin = document.getElementById('is-super-admin').checked;

                    try {
                        const response = await fetch('/api/admin/create-admin', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${adminToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                email: email,
                                password: password,
                                admin_level: isSuperAdmin ? 'super_admin' : 'limited_admin'
                            })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            showToast('Admin created successfully!', 'success');
                            document.getElementById('create-admin-form').reset();
                            // Refresh admin list if on admin management tab
                            if (!document.getElementById('admin-management-content').classList.contains('hidden')) {
                                loadAdminList();
                            }
                        } else {
                            showToast(data.message || 'Failed to create admin', 'error');
                        }
                    } catch (error) {
                        console.error('Error creating admin:', error);
                        showToast('Network error. Please try again.', 'error');
                    }
                });
                console.log('Create admin form listener added');
            }

            // Theme toggle button
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    const html = document.documentElement;
                    html.classList.toggle('dark');
                    const newTheme = html.classList.contains('dark') ? 'dark' : 'light';
                    localStorage.setItem('theme', newTheme);
                    console.log('Theme switched to:', newTheme);
                });
                console.log('Theme toggle listener added');
            }

            // Refresh admins button
            const refreshBtn = document.getElementById('refresh-admins-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', loadAdminList);
                console.log('Refresh button listener added');
            }

            console.log('All event listeners initialized');
        }

        // Initialize everything when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeAllEventListeners);
        } else {
            initializeAllEventListeners();
        }
    </script>

    <!-- Separate minimal tab script for testing -->
    <script>
        console.log('=== MINIMAL TAB SCRIPT LOADING ===');

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM ready - setting up minimal tabs');

            // Simple tab switching function
            function switchTabMinimal(tabName) {
                console.log('Switching to tab:', tabName);

                // Hide all content sections
                const contents = ['password-content', 'email-content', 'admin-management-content', 'create-admin-content'];
                contents.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.classList.add('hidden');
                    }
                });

                // Show selected content
                const selectedContent = document.getElementById(tabName + '-content');
                if (selectedContent) {
                    selectedContent.classList.remove('hidden');
                    console.log('Showed content for:', tabName);
                }

                // Update tab styles
                const tabs = ['password-tab', 'email-tab', 'admin-management-tab', 'create-admin-tab'];
                const inactiveClass = 'py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300';
                const activeClass = 'py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400';

                tabs.forEach(id => {
                    const tab = document.getElementById(id);
                    if (tab) {
                        tab.className = inactiveClass;
                    }
                });

                const selectedTab = document.getElementById(tabName + '-tab');
                if (selectedTab) {
                    selectedTab.className = activeClass;
                    console.log('Updated tab style for:', tabName);
                }
            }

            // Add click handlers
            const passwordTab = document.getElementById('password-tab');
            const emailTab = document.getElementById('email-tab');
            const adminManagementTab = document.getElementById('admin-management-tab');
            const createAdminTab = document.getElementById('create-admin-tab');

            if (passwordTab) passwordTab.onclick = () => switchTabMinimal('password');
            if (emailTab) emailTab.onclick = () => switchTabMinimal('email');
            if (adminManagementTab) adminManagementTab.onclick = () => switchTabMinimal('admin-management');
            if (createAdminTab) createAdminTab.onclick = () => switchTabMinimal('create-admin');

            // Set default tab
            switchTabMinimal('password');

            console.log('Minimal tab script initialized successfully');
        });
    </script>

</body>
</html>
