-- Migration: Add risk settings fields to users table
-- Date: 2025-07-16
-- Description: Add user-controlled risk management settings fields

-- Add new columns to users table
ALTER TABLE users ADD COLUMN investment_percentage DECIMAL(5,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE users ADD COLUMN leverage_multiplier DECIMAL(3,1) DEFAULT 1.0 NOT NULL;
ALTER TABLE users ADD COLUMN risk_settings_configured BOOLEAN DEFAULT FALSE NOT NULL;
ALTER TABLE users ADD COLUMN selected_exchange VARCHAR(50) DEFAULT 'binance' NOT NULL;
ALTER TABLE users ADD COLUMN account_type VARCHAR(20) DEFAULT 'SPOT' NOT NULL;

-- Add indexes for performance
CREATE INDEX idx_users_risk_configured ON users(risk_settings_configured);
CREATE INDEX idx_users_selected_exchange ON users(selected_exchange);

-- Add comments for documentation
COMMENT ON COLUMN users.investment_percentage IS 'Percentage of balance to use for trading (0-10%)';
COMMENT ON COLUMN users.leverage_multiplier IS 'Leverage multiplier (1x-10x based on tier and exchange limits)';
COMMENT ON COLUMN users.risk_settings_configured IS 'Whether user has configured their risk settings';
COMMENT ON COLUMN users.selected_exchange IS 'Selected exchange for trading (binance, binance_us, kraken, bitso)';
COMMENT ON COLUMN users.account_type IS 'Account type for trading (SPOT, FUTURES, SPOT_MARGIN)';
