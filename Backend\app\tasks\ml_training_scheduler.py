#!/usr/bin/env python3
"""
ML Training Scheduler for DeepTrade
Handles hourly retraining of all ML models:
- Elite ML Entry Signal models
- SL ML Predictor models  
- TP ML Predictor models
- Chart Prediction models
"""

import os
import sys
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def retrain_elite_ml_models():
    """Retrain Elite ML models with latest market data"""
    try:
        logger.info("🤖 Starting Elite ML model retraining...")
        
        from app import create_app
        app = create_app()
        
        with app.app_context():
            from app.services.elite_ml_service import Elite96PercentPredictor
            from app.services.market_data import BinanceMarketData
            
            # Initialize services
            market_service = BinanceMarketData()
            elite_predictor = Elite96PercentPredictor()
            
            # Fetch latest training data (last 1500 hours = ~2 months)
            try:
                endpoint = 'https://fapi.binance.com/fapi/v1/klines'
                params = {
                    'symbol': 'BTCUSDT',
                    'interval': '1h',
                    'limit': 1500
                }
                
                import requests
                response = requests.get(endpoint, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                df = pd.DataFrame(data, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_asset_volume', 'number_of_trades',
                    'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                ])
                
                # Convert to proper types
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    df[col] = pd.to_numeric(df[col])
                
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

                logger.info(f"📊 Fetched {len(df)} records for Elite ML training")

                # Train models
                results = elite_predictor.train_elite_models(df)
                
                if results:
                    trained_regimes = len(results)
                    logger.info(f"✅ Elite ML retraining completed: {trained_regimes} regime models")
                    return True
                else:
                    logger.warning("⚠️ Elite ML retraining failed - no models trained")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Elite ML retraining error: {e}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Elite ML retraining failed: {e}")
        return False

def retrain_sl_tp_ml_models():
    """Retrain SL/TP ML models with latest market data"""
    try:
        logger.info("🎯 Starting SL/TP ML model retraining...")
        
        from app import create_app
        app = create_app()
        
        with app.app_context():
            from app.services.sl_tp_ml_predictors import StopLossMLPredictor, TakeProfitMLPredictor
            
            # Initialize predictors
            sl_predictor = StopLossMLPredictor()
            tp_predictor = TakeProfitMLPredictor()
            
            # Generate fresh training data
            training_data = generate_sl_tp_training_data()
            
            if training_data is None or len(training_data) < 100:
                logger.warning("⚠️ Insufficient training data for SL/TP models")
                return False
            
            logger.info(f"📊 Generated {len(training_data)} training samples")
            
            # Train SL model
            sl_success = train_sl_model(sl_predictor, training_data)
            
            # Train TP model  
            tp_success = train_tp_model(tp_predictor, training_data)
            
            if sl_success and tp_success:
                logger.info("✅ SL/TP ML retraining completed successfully")
                return True
            else:
                logger.warning(f"⚠️ SL/TP ML retraining partial: SL={sl_success}, TP={tp_success}")
                return False
                
    except Exception as e:
        logger.error(f"❌ SL/TP ML retraining failed: {e}")
        return False

def generate_sl_tp_training_data():
    """Generate training data for SL/TP models"""
    try:
        # Fetch market data
        endpoint = 'https://fapi.binance.com/fapi/v1/klines'
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1h',
            'limit': 1000
        }
        
        import requests
        response = requests.get(endpoint, params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        # Convert to proper types
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # Generate training scenarios
        training_samples = []
        
        for i in range(50, len(df) - 50):  # Leave buffer for lookback/forward
            current_data = df.iloc[i-50:i+1].copy()
            entry_price = float(df.iloc[i]['close'])
            
            # Generate BUY scenarios
            for signal in ['BUY', 'SELL']:
                # Look ahead to find optimal SL/TP
                future_data = df.iloc[i+1:i+25]  # Next 24 hours
                
                if signal == 'BUY':
                    # Find lowest point for SL and highest for TP
                    optimal_sl = float(future_data['low'].min())
                    optimal_tp = float(future_data['high'].max())
                else:
                    # Find highest point for SL and lowest for TP
                    optimal_sl = float(future_data['high'].max())
                    optimal_tp = float(future_data['low'].min())
                
                training_samples.append({
                    'market_data': current_data,
                    'entry_price': entry_price,
                    'signal': signal,
                    'optimal_sl': optimal_sl,
                    'optimal_tp': optimal_tp
                })
        
        logger.info(f"Generated {len(training_samples)} training samples")
        return training_samples
        
    except Exception as e:
        logger.error(f"Error generating training data: {e}")
        return None

def train_sl_model(sl_predictor, training_data):
    """Train the SL model"""
    try:
        X = []
        y = []
        
        for sample in training_data:
            features = sl_predictor.extract_sl_features(
                sample['market_data'], 
                sample['entry_price'], 
                sample['signal']
            )
            X.append(list(features.values()))
            y.append(sample['optimal_sl'])
        
        X = np.array(X)
        y = np.array(y)
        
        # Scale features
        X_scaled = sl_predictor.scaler.fit_transform(X)
        
        # Train models
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
        
        accuracies = []
        for model_name, model in sl_predictor.models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracy = np.mean(np.abs(y_pred - y_test) / y_test < 0.2)  # Within 20%
            accuracies.append(accuracy)
        
        sl_predictor.accuracy = np.mean(accuracies)
        sl_predictor.is_trained = True
        
        logger.info(f"SL model trained with {sl_predictor.accuracy*100:.1f}% accuracy")
        return True
        
    except Exception as e:
        logger.error(f"SL model training error: {e}")
        return False

def train_tp_model(tp_predictor, training_data):
    """Train the TP model"""
    try:
        X = []
        y = []
        
        for sample in training_data:
            features = tp_predictor.extract_tp_features(
                sample['market_data'], 
                sample['entry_price'], 
                sample['signal'],
                sample['optimal_sl']
            )
            X.append(list(features.values()))
            y.append(sample['optimal_tp'])
        
        X = np.array(X)
        y = np.array(y)
        
        # Scale features
        X_scaled = tp_predictor.scaler.fit_transform(X)
        
        # Train models
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
        
        accuracies = []
        for model_name, model in tp_predictor.models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracy = np.mean(np.abs(y_pred - y_test) / y_test < 0.2)  # Within 20%
            accuracies.append(accuracy)
        
        tp_predictor.accuracy = np.mean(accuracies)
        tp_predictor.is_trained = True
        
        logger.info(f"TP model trained with {tp_predictor.accuracy*100:.1f}% accuracy")
        return True
        
    except Exception as e:
        logger.error(f"TP model training error: {e}")
        return False

def retrain_hybrid_deep_ml_models():
    """Retrain hybrid deep learning models"""
    logger.info("🤖 Starting hybrid deep learning model retraining...")

    try:
        # Check if hybrid deep learning is enabled
        hybrid_enabled = os.getenv('HYBRID_DEEP_ML_ENABLED', 'true').lower() == 'true'

        if not hybrid_enabled:
            logger.info("⚠️ Hybrid deep learning disabled, skipping training")
            return True

        try:
            from app.services.hybrid_deep_ml import HybridDeepMLEnhancer
        except ImportError:
            logger.warning("⚠️ Hybrid deep learning system not available")
            return True  # Don't fail the entire cycle

        # Initialize hybrid enhancer
        hybrid_enhancer = HybridDeepMLEnhancer()

        # Fetch training data
        training_data = fetch_training_data('BTCUSDT', '1h', 1500)

        if training_data is None or len(training_data) < 500:
            logger.error("❌ Insufficient training data for hybrid deep learning")
            return False

        # Train models
        training_result = hybrid_enhancer.train_deep_models(training_data)

        if training_result.get('success', False):
            logger.info(f"✅ Hybrid deep learning models retrained successfully!")
            logger.info(f"📊 LSTM: {training_result['lstm_accuracy']:.3f}, "
                       f"CNN: {training_result['cnn_accuracy']:.3f}, "
                       f"Ensemble: {training_result['ensemble_accuracy']:.3f}")
            return True
        else:
            logger.error(f"❌ Hybrid deep learning training failed: {training_result.get('error', 'Unknown')}")
            return False

    except Exception as e:
        logger.error(f"❌ Error retraining hybrid deep learning models: {e}")
        return False

def run_hourly_ml_training():
    """Run hourly ML training cycle (runs after forecast generation at :00 + 60s)"""
    logger.info("🚀 Starting hourly ML training cycle (after forecast generation)...")

    results = {
        'timestamp': datetime.utcnow().isoformat(),
        'elite_ml_success': False,
        'sl_tp_ml_success': False,
        'total_success': False
    }

    # Retrain Elite ML models
    results['elite_ml_success'] = retrain_elite_ml_models()

    # Retrain SL/TP ML models
    results['sl_tp_ml_success'] = retrain_sl_tp_ml_models()

    # Overall success (both systems must succeed)
    results['total_success'] = results['elite_ml_success'] and results['sl_tp_ml_success']

    if results['total_success']:
        logger.info("🎉 Hourly ML training completed successfully!")
    else:
        logger.warning("⚠️ Hourly ML training completed with issues")

    logger.info(f"Results: Elite ML={results['elite_ml_success']}, "
               f"SL/TP ML={results['sl_tp_ml_success']}")

    return results

def run_daily_deep_learning_training():
    """Run daily deep learning training cycle (computationally intensive)"""
    logger.info("🤖 Starting daily deep learning training cycle...")

    results = {
        'timestamp': datetime.utcnow().isoformat(),
        'hybrid_deep_ml_success': False,
        'total_success': False
    }

    # Retrain Hybrid Deep Learning models
    results['hybrid_deep_ml_success'] = retrain_hybrid_deep_ml_models()
    results['total_success'] = results['hybrid_deep_ml_success']

    if results['total_success']:
        logger.info("🎉 Daily deep learning training completed successfully!")
    else:
        logger.warning("⚠️ Daily deep learning training failed")

    logger.info(f"Results: Hybrid Deep ML={results['hybrid_deep_ml_success']}")

    return results

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='DeepTrade ML Training Scheduler')
    parser.add_argument('--task', choices=['elite', 'sl-tp', 'hourly', 'daily-deep', 'all'], default='hourly',
                       help='Training task to run')

    args = parser.parse_args()

    if args.task == 'elite':
        success = retrain_elite_ml_models()
    elif args.task == 'sl-tp':
        success = retrain_sl_tp_ml_models()
    elif args.task == 'hourly':
        results = run_hourly_ml_training()
        success = results['total_success']
    elif args.task == 'daily-deep':
        results = run_daily_deep_learning_training()
        success = results['total_success']
    else:  # all (for backward compatibility)
        # Run both hourly and daily training
        hourly_results = run_hourly_ml_training()
        daily_results = run_daily_deep_learning_training()
        success = hourly_results['total_success'] and daily_results['total_success']

    if success:
        logger.info("✅ Training task completed successfully")
        sys.exit(0)
    else:
        logger.error("❌ Training task failed")
        sys.exit(1)
