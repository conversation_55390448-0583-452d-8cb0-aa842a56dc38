"""
Membership Validation Service
Handles Tier 2 membership validation and automatic downgrading.
"""

from datetime import datetime, timedelta
from sqlalchemy import desc
from app import db
from app.models.user_tier_status import UserTierStatus
from app.models.solana_payment import SolanaPayment, SolanaPaymentType, SolanaPaymentStatus
from app.models.user import User
import logging

logger = logging.getLogger(__name__)


class MembershipService:
    """Service for managing Tier 2 membership validation and lifecycle."""
    
    @staticmethod
    def validate_user_membership(user_id):
        """
        Validate a user's Tier 2 membership status.
        Returns dict with membership status and details.
        """
        try:
            tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
            if not tier_status:
                return {
                    'valid': False,
                    'reason': 'No tier status found',
                    'tier': 1,
                    'membership_active': False
                }
            
            current_tier = tier_status.get_current_tier()
            
            # If not Tier 2, return basic info
            if current_tier != 2:
                return {
                    'valid': True,
                    'reason': f'User is Tier {current_tier}',
                    'tier': current_tier,
                    'membership_active': current_tier == 2
                }
            
            # For Tier 2, check membership validity
            is_active = tier_status.is_tier_2_membership_active()
            expiry_date = tier_status.get_membership_expiry_date()
            days_remaining = tier_status.get_days_remaining_in_membership()
            
            if not is_active:
                # Membership expired, should downgrade
                logger.warning(f"User {user_id} Tier 2 membership expired, should be downgraded")
                return {
                    'valid': False,
                    'reason': 'Tier 2 membership expired',
                    'tier': current_tier,
                    'membership_active': False,
                    'expiry_date': expiry_date.isoformat() if expiry_date else None,
                    'days_remaining': 0,
                    'should_downgrade': True
                }
            
            return {
                'valid': True,
                'reason': 'Tier 2 membership active',
                'tier': current_tier,
                'membership_active': True,
                'expiry_date': expiry_date.isoformat() if expiry_date else None,
                'days_remaining': days_remaining,
                'should_downgrade': False
            }
            
        except Exception as e:
            logger.error(f"Error validating membership for user {user_id}: {str(e)}")
            return {
                'valid': False,
                'reason': f'Validation error: {str(e)}',
                'tier': 1,
                'membership_active': False
            }
    
    @staticmethod
    def auto_downgrade_expired_memberships():
        """
        Automatically downgrade users whose Tier 2 memberships have expired.
        This should be run as a scheduled task.
        """
        try:
            # Find all Tier 2 users
            tier_2_users = UserTierStatus.query.filter_by(tier_2=True).all()
            downgraded_count = 0
            
            for tier_status in tier_2_users:
                if tier_status.should_downgrade_from_tier_2():
                    logger.info(f"Auto-downgrading user {tier_status.user_id} from Tier 2 due to expired membership")
                    
                    # Downgrade to Tier 1
                    tier_status.tier_1 = True
                    tier_status.tier_2 = False
                    tier_status.tier_3 = False
                    tier_status.updated_at = datetime.utcnow()
                    
                    downgraded_count += 1
            
            if downgraded_count > 0:
                db.session.commit()
                logger.info(f"Auto-downgraded {downgraded_count} users from Tier 2")
            
            return {
                'success': True,
                'downgraded_count': downgraded_count,
                'message': f'Processed {len(tier_2_users)} Tier 2 users, downgraded {downgraded_count}'
            }
            
        except Exception as e:
            logger.error(f"Error in auto-downgrade process: {str(e)}")
            db.session.rollback()
            return {
                'success': False,
                'error': str(e),
                'downgraded_count': 0
            }
    
    @staticmethod
    def get_membership_summary(user_id):
        """Get comprehensive membership summary for a user."""
        try:
            tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
            if not tier_status:
                return {'error': 'Tier status not found'}
            
            current_tier = tier_status.get_current_tier()
            summary = {
                'user_id': user_id,
                'current_tier': current_tier,
                'tier_1': tier_status.tier_1,
                'tier_2': tier_status.tier_2,
                'tier_3': tier_status.tier_3,
                'profit_share_rate': tier_status.get_profit_share_rate(),
                'profit_share_owed': float(tier_status.profit_share_owed),
                'payment_status': tier_status.payment_status
            }
            
            # Add Tier 2 specific information
            if current_tier == 2:
                # Get latest membership payment
                latest_payment = SolanaPayment.query.filter_by(
                    user_id=user_id,
                    payment_type=SolanaPaymentType.MEMBERSHIP_FEE,
                    status=SolanaPaymentStatus.CONFIRMED
                ).order_by(desc(SolanaPayment.processed_at)).first()
                
                summary.update({
                    'membership_active': tier_status.is_tier_2_membership_active(),
                    'membership_expiry_date': tier_status.get_membership_expiry_date().isoformat() if tier_status.get_membership_expiry_date() else None,
                    'days_remaining': tier_status.get_days_remaining_in_membership(),
                    'should_downgrade': tier_status.should_downgrade_from_tier_2(),
                    'latest_payment': {
                        'id': latest_payment.id,
                        'amount': float(latest_payment.amount),
                        'processed_at': latest_payment.processed_at.isoformat(),
                        'transaction_signature': latest_payment.transaction_signature
                    } if latest_payment else None
                })
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting membership summary for user {user_id}: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def process_successful_payment(user_id, payment_id):
        """
        Process a successful Tier 2 membership payment.
        Updates tier status and ensures user remains in Tier 2.
        """
        try:
            # Get the payment
            payment = SolanaPayment.query.get(payment_id)
            if not payment or payment.payment_type != SolanaPaymentType.MEMBERSHIP_FEE:
                return {'success': False, 'error': 'Invalid payment'}
            
            # Get or create tier status
            tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
            if not tier_status:
                tier_status = UserTierStatus(user_id=user_id)
                db.session.add(tier_status)
            
            # Ensure user is set to Tier 2
            tier_status.tier_1 = False
            tier_status.tier_2 = True
            tier_status.tier_3 = False
            tier_status.payment_status = 'paid'
            tier_status.last_payment_date = payment.processed_at or datetime.utcnow()
            tier_status.next_payment_date = tier_status.last_payment_date + timedelta(days=30)
            tier_status.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            logger.info(f"Processed successful Tier 2 payment for user {user_id}, payment {payment_id}")
            
            return {
                'success': True,
                'message': 'Payment processed successfully',
                'tier_status': tier_status.to_dict()
            }
            
        except Exception as e:
            logger.error(f"Error processing payment for user {user_id}: {str(e)}")
            db.session.rollback()
            return {'success': False, 'error': str(e)}
