/**
 * Responsive Layout Component for DeepTrade
 * 
 * Automatically switches between desktop and mobile layouts based on screen size.
 * Provides consistent navigation and layout patterns across all devices.
 */

import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useMobile } from '../../hooks/useResponsiveDesign';
import MobileSidebar from '../mobile/MobileSidebar';
import MobileTopBar from '../mobile/MobileTopBar';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '@solana/wallet-adapter-react';
import { useTranslation } from '../../hooks/useTranslation';

interface ResponsiveLayoutProps {
  children?: React.ReactNode;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({ children }) => {
  const { isMobile } = useMobile();
  const { user } = useAuth();
  const { publicKey } = useWallet();
  const { t } = useTranslation();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  const formatWalletAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const MobileHeader = () => (
    <MobileTopBar
      onMenuClick={() => setIsMobileMenuOpen(true)}
    />
  );

  const DesktopSidebar = () => (
    <aside className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:bg-white lg:dark:bg-gray-800 lg:border-r lg:border-gray-200 lg:dark:border-gray-700">
      {/* Sidebar content for desktop */}
      <div className="flex flex-col flex-1 min-h-0">
        {/* Logo */}
        <div className="flex items-center h-16 px-6 border-b border-gray-200 dark:border-gray-700">
          <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            DeepTrade
          </span>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {/* Desktop navigation items would go here */}
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Desktop navigation coming soon...
          </div>
        </nav>

        {/* User info */}
        {user && (
          <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold">
                  {user.full_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {user.full_name || user.email}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {user.email}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );

  const MobileLayout = () => (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MobileHeader />
      <MobileSidebar
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
      <main className="pb-safe">
        {children || <Outlet />}
      </main>
    </div>
  );

  const DesktopLayout = () => (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DesktopSidebar />
      <div className="lg:pl-64">
        {/* Desktop header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between px-6 py-4">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                {t('navigation.dashboard')}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {publicKey && (
                <div className="flex items-center space-x-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-3 py-1.5 rounded-full text-sm">
                  <span>🔗</span>
                  <span>{formatWalletAddress(publicKey.toString())}</span>
                </div>
              )}
              {user && (
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {user.full_name || user.email}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {user.email}
                    </p>
                  </div>
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold">
                      {user.full_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="p-6">
          {children || <Outlet />}
        </main>
      </div>
    </div>
  );

  // Render appropriate layout based on screen size
  if (isMobile) {
    return <MobileLayout />;
  }

  return <DesktopLayout />;
};

export default ResponsiveLayout;
