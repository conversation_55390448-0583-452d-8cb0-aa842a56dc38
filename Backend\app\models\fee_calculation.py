import uuid
from datetime import datetime, timedelta
from enum import Enum
from app import db

class FeeCalculationStatus(Enum):
    PENDING = 'pending'
    CALCULATED = 'calculated'
    PROCESSED = 'processed'
    FAILED = 'failed'

class FeeCalculation(db.Model):
    """Model to track weekly fee calculations and distributions."""
    __tablename__ = 'fee_calculations'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    
    # Calculation period
    calculation_period_start = db.Column(db.Date, nullable=False)
    calculation_period_end = db.Column(db.Date, nullable=False)
    
    # Financial data
    total_profit = db.Column(db.Numeric(20, 8), default=0)
    total_loss = db.Column(db.Numeric(20, 8), default=0)
    net_profit = db.Column(db.Numeric(20, 8), default=0)
    
    # Fee calculation
    fee_rate = db.Column(db.Numeric(5, 4), nullable=False)  # e.g., 0.3000 for 30%
    fee_amount = db.Column(db.Numeric(20, 8), default=0)
    
    # Status and processing
    status = db.Column(db.Enum(FeeCalculationStatus), nullable=False, default=FeeCalculationStatus.PENDING)
    
    # Transaction details
    transaction_hash = db.Column(db.String(255))  # Blockchain transaction hash for fee transfer
    payment_reference = db.Column(db.String(255))  # Reference for payment processing
    
    # Processing timestamps
    processed_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Additional metadata
    trades_count = db.Column(db.Integer, default=0)
    winning_trades_count = db.Column(db.Integer, default=0)
    notes = db.Column(db.Text)
    
    def __init__(self, user_id, period_start, period_end, fee_rate):
        self.user_id = user_id
        self.calculation_period_start = period_start
        self.calculation_period_end = period_end
        self.fee_rate = fee_rate
    
    @classmethod
    def create_weekly_calculation(cls, user_id, fee_rate, week_start=None):
        """Create a weekly fee calculation."""
        if week_start is None:
            # Default to last Monday
            today = datetime.utcnow().date()
            days_since_monday = today.weekday()
            week_start = today - timedelta(days=days_since_monday + 7)
        
        week_end = week_start + timedelta(days=6)
        
        return cls(
            user_id=user_id,
            period_start=week_start,
            period_end=week_end,
            fee_rate=fee_rate
        )
    
    def calculate_fees_from_trades(self):
        """Calculate fees based on user's trades in the period."""
        from app.models.trade import Trade, TradeStatus
        
        # Get all closed trades in the calculation period
        trades = Trade.query.filter(
            Trade.user_id == self.user_id,
            Trade.status == TradeStatus.CLOSED,
            Trade.exit_time >= datetime.combine(self.calculation_period_start, datetime.min.time()),
            Trade.exit_time <= datetime.combine(self.calculation_period_end, datetime.max.time())
        ).all()
        
        total_profit = 0
        total_loss = 0
        trades_count = len(trades)
        winning_trades_count = 0
        
        for trade in trades:
            pnl = float(trade.pnl) if trade.pnl else 0
            
            if pnl > 0:
                total_profit += pnl
                winning_trades_count += 1
            elif pnl < 0:
                total_loss += abs(pnl)
        
        # Update calculation data
        self.total_profit = total_profit
        self.total_loss = total_loss
        self.net_profit = total_profit - total_loss
        self.trades_count = trades_count
        self.winning_trades_count = winning_trades_count
        
        # Calculate fee only on net profit
        if self.net_profit > 0:
            self.fee_amount = self.net_profit * float(self.fee_rate)
        else:
            self.fee_amount = 0
        
        self.status = FeeCalculationStatus.CALCULATED
        
        return {
            'total_profit': float(self.total_profit),
            'total_loss': float(self.total_loss),
            'net_profit': float(self.net_profit),
            'fee_amount': float(self.fee_amount),
            'trades_count': self.trades_count,
            'winning_trades_count': self.winning_trades_count
        }
    
    def mark_processed(self, transaction_hash=None, payment_reference=None):
        """Mark fee calculation as processed."""
        self.status = FeeCalculationStatus.PROCESSED
        self.processed_at = datetime.utcnow()
        
        if transaction_hash:
            self.transaction_hash = transaction_hash
        if payment_reference:
            self.payment_reference = payment_reference
    
    def mark_failed(self, error_note=None):
        """Mark fee calculation as failed."""
        self.status = FeeCalculationStatus.FAILED
        if error_note:
            self.notes = error_note
    
    def get_fee_percentage(self):
        """Get fee rate as percentage."""
        return float(self.fee_rate) * 100
    
    def get_win_rate(self):
        """Get win rate for the period."""
        if self.trades_count == 0:
            return 0
        return (self.winning_trades_count / self.trades_count) * 100
    
    def get_profit_factor(self):
        """Calculate profit factor for the period."""
        if self.total_loss == 0:
            return float('inf') if self.total_profit > 0 else 0
        return float(self.total_profit / self.total_loss)
    
    def is_profitable_period(self):
        """Check if this was a profitable period."""
        return float(self.net_profit) > 0
    
    def should_charge_fee(self):
        """Check if fee should be charged."""
        return self.is_profitable_period() and float(self.fee_amount) > 0
    
    def get_period_summary(self):
        """Get a summary of the calculation period."""
        return {
            'period_start': self.calculation_period_start.isoformat(),
            'period_end': self.calculation_period_end.isoformat(),
            'period_days': (self.calculation_period_end - self.calculation_period_start).days + 1,
            'is_profitable': self.is_profitable_period(),
            'should_charge_fee': self.should_charge_fee()
        }
    
    @classmethod
    def get_user_weekly_summary(cls, user_id, weeks=4):
        """Get weekly summary for a user over the last N weeks."""
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(weeks=weeks)
        
        calculations = cls.query.filter(
            cls.user_id == user_id,
            cls.calculation_period_start >= start_date,
            cls.status.in_([FeeCalculationStatus.CALCULATED, FeeCalculationStatus.PROCESSED])
        ).order_by(cls.calculation_period_start.desc()).all()
        
        summary = {
            'total_periods': len(calculations),
            'profitable_periods': sum(1 for calc in calculations if calc.is_profitable_period()),
            'total_net_profit': sum(float(calc.net_profit) for calc in calculations),
            'total_fees_charged': sum(float(calc.fee_amount) for calc in calculations),
            'total_trades': sum(calc.trades_count for calc in calculations),
            'calculations': [calc.to_dict() for calc in calculations]
        }
        
        if summary['total_periods'] > 0:
            summary['avg_weekly_profit'] = summary['total_net_profit'] / summary['total_periods']
            summary['profitability_rate'] = (summary['profitable_periods'] / summary['total_periods']) * 100
        else:
            summary['avg_weekly_profit'] = 0
            summary['profitability_rate'] = 0
        
        return summary
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'calculation_period_start': self.calculation_period_start.isoformat(),
            'calculation_period_end': self.calculation_period_end.isoformat(),
            'total_profit': float(self.total_profit),
            'total_loss': float(self.total_loss),
            'net_profit': float(self.net_profit),
            'fee_rate': float(self.fee_rate),
            'fee_percentage': self.get_fee_percentage(),
            'fee_amount': float(self.fee_amount),
            'status': self.status.value,
            'transaction_hash': self.transaction_hash,
            'payment_reference': self.payment_reference,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'created_at': self.created_at.isoformat(),
            'trades_count': self.trades_count,
            'winning_trades_count': self.winning_trades_count,
            'win_rate': self.get_win_rate(),
            'profit_factor': self.get_profit_factor(),
            'is_profitable_period': self.is_profitable_period(),
            'should_charge_fee': self.should_charge_fee(),
            'period_summary': self.get_period_summary(),
            'notes': self.notes
        }
    
    def __repr__(self):
        return f'<FeeCalculation {self.id} - User: {self.user_id} - Period: {self.calculation_period_start} to {self.calculation_period_end} - Status: {self.status.value}>'