import os

# Email Configuration
SMTP_SERVER = os.environ.get('SMTP_SERVER', 'smtp.gmail.com')
SMTP_PORT = int(os.environ.get('SMTP_PORT', 587))
SMTP_USERNAME = os.environ.get('SMTP_USERNAME', '<EMAIL>')
SMTP_PASSWORD = os.environ.get('SMTP_PASSWORD', 'your-app-password')
FROM_EMAIL = os.environ.get('FROM_EMAIL', '<EMAIL>')
FRONTEND_URL = os.environ.get('FRONTEND_URL', 'http://localhost:5173')

# Solana Configuration
SOLANA_NETWORK = os.environ.get('SOLANA_NETWORK', 'mainnet')
SOLANA_RPC_ENDPOINT = os.environ.get('SOLANA_RPC_ENDPOINT', 'https://api.mainnet-beta.solana.com')
SOLANA_TREASURY_WALLET = os.environ.get('SOLANA_TREASURY_WALLET')
SOLANA_USDT_MINT = os.environ.get('SOLANA_USDT_MINT', 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB')
SOLANA_USDC_MINT = os.environ.get('SOLANA_USDC_MINT', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')