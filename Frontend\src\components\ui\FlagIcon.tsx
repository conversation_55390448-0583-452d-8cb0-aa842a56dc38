/**
 * Flag Icon Component for DeepTrade
 *
 * Renders beautiful flag icons using PNG images from /icons/flags/ folder.
 * Supports all languages used in the language selector.
 */

import React from 'react';

interface FlagIconProps {
  countryCode: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const FlagIcon: React.FC<FlagIconProps> = ({
  countryCode,
  size = 'md',
  className = ''
}) => {
  const [imageError, setImageError] = React.useState(false);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'w-8 h-5'; // Further increased for mobile legibility
      case 'md': return 'w-9 h-6'; // Larger for better visibility
      case 'lg': return 'w-10 h-7'; // Even better aspect ratio
      default: return 'w-9 h-6';
    }
  };

  const getFlagImagePath = (code: string) => {
    return `/icons/flags/${code.toLowerCase()}.png`;
  };

  const getFallbackFlag = (code: string) => {
    // Fallback to country code with styling if image doesn't load
    return code.toUpperCase();
  };

  const handleImageError = () => {
    setImageError(true);
  };

  if (imageError) {
    // Fallback to styled country code if image fails to load
    return (
      <div
        className={`inline-flex items-center justify-center rounded border border-gray-300 dark:border-gray-600 bg-blue-50 dark:bg-blue-900/20 ${getSizeClasses()} ${className}`}
        role="img"
        aria-label={`${countryCode} flag`}
        title={`${countryCode} flag`}
      >
        <span className={`font-bold text-blue-600 dark:text-blue-400 leading-none select-none ${
          size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'
        }`}>
          {getFallbackFlag(countryCode)}
        </span>
      </div>
    );
  }

  return (
    <div className={`inline-flex items-center justify-center rounded overflow-hidden border border-gray-200 dark:border-gray-600 shadow-sm ${getSizeClasses()} ${className}`}>
      <img
        src={getFlagImagePath(countryCode)}
        alt={`${countryCode} flag`}
        className="w-full h-full object-cover object-center"
        style={{ imageRendering: 'crisp-edges' }}
        onError={handleImageError}
        loading="lazy"
        draggable={false}
      />
    </div>
  );
};

export default FlagIcon;
