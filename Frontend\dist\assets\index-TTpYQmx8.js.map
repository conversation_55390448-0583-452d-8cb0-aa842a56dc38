{"version": 3, "file": "index-TTpYQmx8.js", "sources": ["../../node_modules/@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/rng.js", "../../node_modules/@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/stringify.js", "../../node_modules/@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/native.js", "../../node_modules/@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/v4.js", "../../node_modules/@solflare-wallet/metamask-sdk/lib/esm/utils.js", "../../node_modules/@solflare-wallet/metamask-sdk/lib/esm/detectProvider.js", "../../node_modules/@solflare-wallet/metamask-sdk/lib/esm/standard/solana.js", "../../node_modules/@solflare-wallet/metamask-sdk/lib/esm/standard/account.js", "../../node_modules/@solflare-wallet/metamask-sdk/lib/esm/index.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;", "export function isLegacyTransactionInstance(transaction) {\n    return transaction.version === undefined;\n}\nexport function serializeTransaction(transaction) {\n    return isLegacyTransactionInstance(transaction)\n        ? transaction.serialize({\n            verifySignatures: false,\n            requireAllSignatures: false\n        })\n        : transaction.serialize();\n}\nexport function serializeTransactionMessage(transaction) {\n    return isLegacyTransactionInstance(transaction)\n        ? transaction.serializeMessage()\n        : transaction.message.serialize();\n}\nexport function addSignature(transaction, publicKey, signature) {\n    if (isLegacyTransactionInstance(transaction)) {\n        transaction.addSignature(publicKey, Buffer.from(signature));\n    }\n    else {\n        const signerPubkeys = transaction.message.staticAccountKeys.slice(0, transaction.message.header.numRequiredSignatures);\n        const signerIndex = signerPubkeys.findIndex((pubkey) => pubkey.equals(publicKey));\n        if (signerIndex >= 0) {\n            transaction.signatures[signerIndex] = signature;\n        }\n    }\n}\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nexport function isSnapSupported(provider) {\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            yield provider.request({ method: 'wallet_getSnaps' });\n            return true;\n        }\n        catch (error) {\n            return false;\n        }\n    });\n}\nexport function detectProvider() {\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            const provider = window.ethereum;\n            if (!provider) {\n                return null;\n            }\n            if (provider.providers && Array.isArray(provider.providers)) {\n                const providers = provider.providers;\n                for (const provider of providers) {\n                    if (yield isSnapSupported(provider)) {\n                        return provider;\n                    }\n                }\n            }\n            if (provider.detected && Array.isArray(provider.detected)) {\n                const providers = provider.detected;\n                for (const provider of providers) {\n                    if (yield isSnapSupported(provider)) {\n                        return provider;\n                    }\n                }\n            }\n            if (yield isSnapSupported(provider)) {\n                return provider;\n            }\n            return null;\n        }\n        catch (error) {\n            console.error(error);\n            return null;\n        }\n    });\n}\n", "// This is copied from @solana/wallet-standard-chains\n/** Solana Mainnet (beta) cluster, e.g. https://api.mainnet-beta.solana.com */\nexport const SOLANA_MAINNET_CHAIN = 'solana:mainnet';\n/** Solana Devnet cluster, e.g. https://api.devnet.solana.com */\nexport const SOLANA_DEVNET_CHAIN = 'solana:devnet';\n/** Solana Testnet cluster, e.g. https://api.testnet.solana.com */\nexport const SOLANA_TESTNET_CHAIN = 'solana:testnet';\n/** Solana Localnet cluster, e.g. http://localhost:8899 */\nexport const SOLANA_LOCALNET_CHAIN = 'solana:localnet';\n/** Array of all Solana clusters */\nexport const SOLANA_CHAINS = [\n    SOLANA_MAINNET_CHAIN,\n    SOLANA_DEVNET_CHAIN,\n    SOLANA_TESTNET_CHAIN,\n    SOLANA_LOCALNET_CHAIN\n];\n/**\n * Check if a chain corresponds with one of the Solana clusters.\n */\nexport function is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(chain) {\n    return SOLANA_CHAINS.includes(chain);\n}\n", "// This is copied with modification from @wallet-standard/wallet\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _StandardSolflareMetaMaskWalletAccount_address, _StandardSolflareMetaMaskWalletAccount_publicKey, _StandardSolflareMetaMaskWalletAccount_chains, _StandardSolflareMetaMaskWalletAccount_features, _StandardSolflareMetaMaskWalletAccount_label, _StandardSolflareMetaMaskWalletAccount_icon;\nimport { SolanaSignAndSendTransaction, SolanaSignMessage, SolanaSignTransaction } from '@solana/wallet-standard-features';\nimport { SOLANA_CHAINS } from './solana.js';\nconst chains = SOLANA_CHAINS;\nconst features = [SolanaSignAndSendTransaction, SolanaSignTransaction, SolanaSignMessage];\nexport class StandardSolflareMetaMaskWalletAccount {\n    get address() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_address, \"f\");\n    }\n    get publicKey() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_publicKey, \"f\").slice();\n    }\n    get chains() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_chains, \"f\").slice();\n    }\n    get features() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_features, \"f\").slice();\n    }\n    get label() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_label, \"f\");\n    }\n    get icon() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_icon, \"f\");\n    }\n    constructor({ address, publicKey, label, icon }) {\n        _StandardSolflareMetaMaskWalletAccount_address.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_publicKey.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_chains.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_features.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_label.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_icon.set(this, void 0);\n        if (new.target === StandardSolflareMetaMaskWalletAccount) {\n            Object.freeze(this);\n        }\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_address, address, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_publicKey, publicKey, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_chains, chains, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_features, features, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_label, label, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_icon, icon, \"f\");\n    }\n}\n_StandardSolflareMetaMaskWalletAccount_address = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_publicKey = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_chains = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_features = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_label = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_icon = new WeakMap();\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';\nimport EventEmitter from 'eventemitter3';\nimport bs58 from 'bs58';\nimport { v4 as uuidv4 } from 'uuid';\nimport { isLegacyTransactionInstance, serializeTransaction } from './utils';\nimport { detectProvider } from './detectProvider';\nimport { StandardSolflareMetaMaskWalletAccount } from './standard/account';\nimport { isSolanaChain } from './standard/solana';\nexport * from './types';\nexport * from './standard/account';\nclass SolflareMetaMask extends EventEmitter {\n    constructor(config) {\n        super();\n        this._network = 'mainnet-beta';\n        this._iframeParams = {};\n        this._element = null;\n        this._iframe = null;\n        this._publicKey = null;\n        this._account = null;\n        this._isConnected = false;\n        this._connectHandler = null;\n        this._messageHandlers = {};\n        this._handleEvent = (event) => {\n            var _a, _b;\n            switch (event.type) {\n                case 'connect': {\n                    this._collapseIframe();\n                    if ((_a = event.data) === null || _a === void 0 ? void 0 : _a.publicKey) {\n                        this._publicKey = event.data.publicKey;\n                        this._isConnected = true;\n                        if (this._connectHandler) {\n                            this._connectHandler.resolve();\n                            this._connectHandler = null;\n                        }\n                        this._connected();\n                    }\n                    else {\n                        if (this._connectHandler) {\n                            this._connectHandler.reject();\n                            this._connectHandler = null;\n                        }\n                        this._disconnected();\n                    }\n                    return;\n                }\n                case 'disconnect': {\n                    if (this._connectHandler) {\n                        this._connectHandler.reject();\n                        this._connectHandler = null;\n                    }\n                    this._disconnected();\n                    return;\n                }\n                case 'accountChanged': {\n                    if ((_b = event.data) === null || _b === void 0 ? void 0 : _b.publicKey) {\n                        this._publicKey = event.data.publicKey;\n                        this.emit('accountChanged', this.publicKey);\n                        this._standardConnected();\n                    }\n                    else {\n                        this.emit('accountChanged', undefined);\n                        this._standardDisconnected();\n                    }\n                    return;\n                }\n                default: {\n                    return;\n                }\n            }\n        };\n        this._handleResize = (data) => {\n            if (data.resizeMode === 'full') {\n                if (data.params.mode === 'fullscreen') {\n                    this._expandIframe();\n                }\n                else if (data.params.mode === 'hide') {\n                    this._collapseIframe();\n                }\n            }\n            else if (data.resizeMode === 'coordinates') {\n                this._resizeIframe(data.params);\n            }\n        };\n        this._handleMessage = (event) => {\n            var _a;\n            if (((_a = event.data) === null || _a === void 0 ? void 0 : _a.channel) !== 'solflareIframeToWalletAdapter') {\n                return;\n            }\n            const data = event.data.data || {};\n            if (data.type === 'event') {\n                this._handleEvent(data.event);\n            }\n            else if (data.type === 'resize') {\n                this._handleResize(data);\n            }\n            else if (data.type === 'response') {\n                if (this._messageHandlers[data.id]) {\n                    const { resolve, reject } = this._messageHandlers[data.id];\n                    delete this._messageHandlers[data.id];\n                    if (data.error) {\n                        reject(data.error);\n                    }\n                    else {\n                        resolve(data.result);\n                    }\n                }\n            }\n        };\n        this._removeElement = () => {\n            if (this._element) {\n                this._element.remove();\n                this._element = null;\n            }\n        };\n        this._removeDanglingElements = () => {\n            const elements = document.getElementsByClassName('solflare-metamask-wallet-adapter-iframe');\n            for (const element of elements) {\n                if (element.parentElement) {\n                    element.remove();\n                }\n            }\n        };\n        this._injectElement = () => {\n            this._removeElement();\n            this._removeDanglingElements();\n            const params = Object.assign(Object.assign({}, this._iframeParams), { mm: true, v: 1, cluster: this._network || 'mainnet-beta', origin: window.location.origin || '', title: document.title || '' });\n            const queryString = Object.keys(params)\n                .map((key) => `${key}=${encodeURIComponent(params[key])}`)\n                .join('&');\n            const iframeUrl = `${SolflareMetaMask.IFRAME_URL}?${queryString}`;\n            this._element = document.createElement('div');\n            this._element.className = 'solflare-metamask-wallet-adapter-iframe';\n            this._element.innerHTML = `\n      <iframe src='${iframeUrl}' style='position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; border: none; border-radius: 0; z-index: 99999; color-scheme: auto;' allowtransparency='true'></iframe>\n    `;\n            document.body.appendChild(this._element);\n            this._iframe = this._element.querySelector('iframe');\n            window.addEventListener('message', this._handleMessage, false);\n        };\n        this._collapseIframe = () => {\n            if (this._iframe) {\n                this._iframe.style.top = '';\n                this._iframe.style.right = '';\n                this._iframe.style.height = '2px';\n                this._iframe.style.width = '2px';\n            }\n        };\n        this._expandIframe = () => {\n            if (this._iframe) {\n                this._iframe.style.top = '0px';\n                this._iframe.style.bottom = '0px';\n                this._iframe.style.left = '0px';\n                this._iframe.style.right = '0px';\n                this._iframe.style.width = '100%';\n                this._iframe.style.height = '100%';\n            }\n        };\n        this._resizeIframe = (params) => {\n            if (!this._iframe) {\n                return;\n            }\n            this._iframe.style.top = isFinite(params.top) ? `${params.top}px` : '';\n            this._iframe.style.bottom = isFinite(params.bottom) ? `${params.bottom}px` : '';\n            this._iframe.style.left = isFinite(params.left) ? `${params.left}px` : '';\n            this._iframe.style.right = isFinite(params.right) ? `${params.right}px` : '';\n            this._iframe.style.width = isFinite(params.width)\n                ? `${params.width}px`\n                : params.width;\n            this._iframe.style.height = isFinite(params.height)\n                ? `${params.height}px`\n                : params.height;\n        };\n        this._sendIframeMessage = (data) => {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            return new Promise((resolve, reject) => {\n                var _a, _b;\n                const messageId = uuidv4();\n                this._messageHandlers[messageId] = { resolve, reject };\n                (_b = (_a = this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                    channel: 'solflareWalletAdapterToIframe',\n                    data: Object.assign({ id: messageId }, data)\n                }, '*');\n            });\n        };\n        this._connected = () => {\n            this._isConnected = true;\n            this.emit('connect', this.publicKey);\n            this._standardConnected();\n        };\n        this._disconnected = () => {\n            this._publicKey = null;\n            this._isConnected = false;\n            window.removeEventListener('message', this._handleMessage, false);\n            this._removeElement();\n            this.emit('disconnect');\n            this._standardDisconnected();\n        };\n        this._standardConnected = () => {\n            if (!this.publicKey) {\n                return;\n            }\n            const address = this.publicKey.toString();\n            if (!this._account || this._account.address !== address) {\n                this._account = new StandardSolflareMetaMaskWalletAccount({\n                    address,\n                    publicKey: this.publicKey.toBytes()\n                });\n                this.emit('standard_change', { accounts: this.standardAccounts });\n            }\n        };\n        this._standardDisconnected = () => {\n            if (this._account) {\n                this._account = null;\n                this.emit('standard_change', { accounts: this.standardAccounts });\n            }\n        };\n        if (config === null || config === void 0 ? void 0 : config.network) {\n            this._network = config === null || config === void 0 ? void 0 : config.network;\n        }\n        if (window.SolflareMetaMaskParams) {\n            this._iframeParams = Object.assign(Object.assign({}, this._iframeParams), window.SolflareMetaMaskParams);\n        }\n        if (config === null || config === void 0 ? void 0 : config.params) {\n            this._iframeParams = Object.assign(Object.assign({}, this._iframeParams), config === null || config === void 0 ? void 0 : config.params);\n        }\n    }\n    get publicKey() {\n        return this._publicKey ? new PublicKey(this._publicKey) : null;\n    }\n    get standardAccount() {\n        return this._account;\n    }\n    get standardAccounts() {\n        return this._account ? [this._account] : [];\n    }\n    get isConnected() {\n        return this._isConnected;\n    }\n    get connected() {\n        return this.isConnected;\n    }\n    get autoApprove() {\n        return false;\n    }\n    connect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.connected) {\n                return;\n            }\n            this._injectElement();\n            yield new Promise((resolve, reject) => {\n                this._connectHandler = { resolve, reject };\n            });\n        });\n    }\n    disconnect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            yield this._sendIframeMessage({\n                method: 'disconnect'\n            });\n            this._disconnected();\n        });\n    }\n    signTransaction(transaction) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const serializedTransaction = serializeTransaction(transaction);\n                const response = yield this._sendIframeMessage({\n                    method: 'signTransactionV2',\n                    params: {\n                        transaction: bs58.encode(serializedTransaction)\n                    }\n                });\n                const { transaction: signedTransaction } = response;\n                return isLegacyTransactionInstance(transaction) ? Transaction.from(bs58.decode(signedTransaction)) : VersionedTransaction.deserialize(bs58.decode(signedTransaction));\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign transaction');\n            }\n        });\n    }\n    signAllTransactions(transactions) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const serializedTransactions = transactions.map((transaction) => serializeTransaction(transaction));\n                const { transactions: signedTransactions } = yield this._sendIframeMessage({\n                    method: 'signAllTransactionsV2',\n                    params: {\n                        transactions: serializedTransactions.map((transaction) => bs58.encode(transaction))\n                    }\n                });\n                return signedTransactions.map((signedTransaction, index) => {\n                    return isLegacyTransactionInstance(transactions[index]) ? Transaction.from(bs58.decode(signedTransaction)) : VersionedTransaction.deserialize(bs58.decode(signedTransaction));\n                });\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign transactions');\n            }\n        });\n    }\n    signAndSendTransaction(transaction, options) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const serializedTransaction = serializeTransaction(transaction);\n                const { signature } = yield this._sendIframeMessage({\n                    method: 'signAndSendTransaction',\n                    params: {\n                        transaction: bs58.encode(serializedTransaction),\n                        options\n                    }\n                });\n                return signature;\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign and send transaction');\n            }\n        });\n    }\n    signMessage(data, display = 'utf8') {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const { signature } = yield this._sendIframeMessage({\n                    method: 'signMessage',\n                    params: {\n                        data: bs58.encode(data),\n                        display\n                    }\n                });\n                return Uint8Array.from(bs58.decode(signature));\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign message');\n            }\n        });\n    }\n    sign(data, display = 'utf8') {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield this.signMessage(data, display);\n        });\n    }\n    static isSupported() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const provider = yield detectProvider();\n            return !!provider;\n        });\n    }\n    standardSignAndSendTransaction(...inputs) {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected)\n                throw new Error('not connected');\n            const outputs = [];\n            if (inputs.length === 1) {\n                const { transaction, account, chain, options } = inputs[0];\n                const { minContextSlot, preflightCommitment, skipPreflight, maxRetries } = options || {};\n                if (account !== this._account)\n                    throw new Error('invalid account');\n                if (!isSolanaChain(chain))\n                    throw new Error('invalid chain');\n                const signature = yield this.signAndSendTransaction(VersionedTransaction.deserialize(transaction), {\n                    preflightCommitment,\n                    minContextSlot,\n                    maxRetries,\n                    skipPreflight\n                });\n                outputs.push({ signature: bs58.decode(signature) });\n            }\n            else if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(...(yield this.standardSignAndSendTransaction(input)));\n                }\n            }\n            return outputs;\n        });\n    }\n    standardSignTransaction(...inputs) {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected)\n                throw new Error('not connected');\n            const outputs = [];\n            if (inputs.length === 1) {\n                const { transaction, account, chain } = inputs[0];\n                if (account !== this._account)\n                    throw new Error('invalid account');\n                if (chain && !isSolanaChain(chain))\n                    throw new Error('invalid chain');\n                const signedTransaction = yield this.signTransaction(VersionedTransaction.deserialize(transaction));\n                outputs.push({ signedTransaction: signedTransaction.serialize() });\n            }\n            else if (inputs.length > 1) {\n                let chain;\n                for (const input of inputs) {\n                    if (input.account !== this._account)\n                        throw new Error('invalid account');\n                    if (input.chain) {\n                        if (!isSolanaChain(input.chain))\n                            throw new Error('invalid chain');\n                        if (chain) {\n                            if (input.chain !== chain)\n                                throw new Error('conflicting chain');\n                        }\n                        else {\n                            chain = input.chain;\n                        }\n                    }\n                }\n                const transactions = inputs.map(({ transaction }) => VersionedTransaction.deserialize(transaction));\n                const signedTransactions = yield this.signAllTransactions(transactions);\n                outputs.push(...signedTransactions.map((signedTransaction) => ({\n                    signedTransaction: signedTransaction.serialize()\n                })));\n            }\n            return outputs;\n        });\n    }\n    standardSignMessage(...inputs) {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected)\n                throw new Error('not connected');\n            const outputs = [];\n            if (inputs.length === 1) {\n                const { message, account } = inputs[0];\n                if (account !== this._account)\n                    throw new Error('invalid account');\n                const signature = yield this.signMessage(message);\n                outputs.push({ signedMessage: message, signature });\n            }\n            else if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(...(yield this.standardSignMessage(input)));\n                }\n            }\n            return outputs;\n        });\n    }\n}\nSolflareMetaMask.IFRAME_URL = 'https://widget.solflare.com/';\nexport default SolflareMetaMask;\n"], "names": ["getRandomValues", "rnds8", "rng", "byteToHex", "unsafeStringify", "arr", "offset", "randomUUID", "native", "v4", "options", "buf", "rnds", "isLegacyTransactionInstance", "transaction", "serializeTransaction", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "reject", "fulfilled", "step", "e", "rejected", "result", "isSnapSupported", "provider", "detectProvider", "providers", "error", "SOLANA_MAINNET_CHAIN", "SOLANA_DEVNET_CHAIN", "SOLANA_TESTNET_CHAIN", "SOLANA_LOCALNET_CHAIN", "SOLANA_CHAINS", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chain", "__classPrivateFieldGet", "receiver", "state", "kind", "f", "__classPrivateFieldSet", "_StandardSolflareMetaMaskWalletAccount_address", "_StandardSolflareMetaMaskWalletAccount_publicKey", "_StandardSolflareMetaMaskWalletAccount_chains", "_StandardSolflareMetaMaskWalletAccount_features", "_StandardSolflareMetaMaskWalletAccount_label", "_StandardSolflareMetaMaskWalletAccount_icon", "chains", "features", "SolanaSignAndSendTransaction", "SolanaSignTransaction", "SolanaSignMessage", "StandardSolflareMetaMaskWalletAccount", "address", "public<PERSON>ey", "label", "icon", "SolflareMetaMask", "EventEmitter", "config", "event", "_a", "_b", "data", "elements", "element", "params", "queryString", "key", "iframeUrl", "messageId", "uuidv4", "PublicKey", "serializedTransaction", "response", "bs58", "signedTransaction", "Transaction", "VersionedTransaction", "transactions", "serializedTransactions", "signedTransactions", "index", "signature", "display", "inputs", "outputs", "account", "minContextSlot", "preflightCommitment", "skipPreflight", "maxRetries", "input", "message"], "mappings": "wFAGA,IAAIA,EACJ,MAAMC,EAAQ,IAAI,WAAW,EAAE,EAChB,SAASC,GAAM,CAE5B,GAAI,CAACF,IAEHA,EAAkB,OAAO,OAAW,KAAe,OAAO,iBAAmB,OAAO,gBAAgB,KAAK,MAAM,EAE3G,CAACA,GACH,MAAM,IAAI,MAAM,0GAA0G,EAI9H,OAAOA,EAAgBC,CAAK,CAC9B,CCXA,MAAME,EAAY,CAAA,EAElB,QAAS,EAAI,EAAG,EAAI,IAAK,EAAE,EACzBA,EAAU,MAAM,EAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,EAG3C,SAASC,EAAgBC,EAAKC,EAAS,EAAG,CAG/C,OAAOH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAIH,EAAUE,EAAIC,EAAS,CAAC,CAAC,EAAI,IAAMH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,EAAIH,EAAUE,EAAIC,EAAS,EAAE,CAAC,CACnf,CChBA,MAAMC,EAAa,OAAO,OAAW,KAAe,OAAO,YAAc,OAAO,WAAW,KAAK,MAAM,EACtGC,EAAe,CACb,WAAAD,CACF,ECCA,SAASE,EAAGC,EAASC,EAAKL,EAAQ,CAChC,GAAIE,EAAO,YAAsB,CAACE,EAChC,OAAOF,EAAO,WAAU,EAG1BE,EAAUA,GAAW,CAAA,EACrB,MAAME,EAAOF,EAAQ,SAAWA,EAAQ,KAAOR,KAE/C,OAAAU,EAAK,CAAC,EAAIA,EAAK,CAAC,EAAI,GAAO,GAC3BA,EAAK,CAAC,EAAIA,EAAK,CAAC,EAAI,GAAO,IAYpBR,EAAgBQ,CAAI,CAC7B,CC1BO,SAASC,EAA4BC,EAAa,CACrD,OAAOA,EAAY,UAAY,MACnC,CACO,SAASC,EAAqBD,EAAa,CAC9C,OAAOD,EAA4BC,CAAW,EACxCA,EAAY,UAAU,CACpB,iBAAkB,GAClB,qBAAsB,EAClC,CAAS,EACCA,EAAY,UAAS,CAC/B,CCVA,IAAIE,EAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAG,CAAC,CAAG,CAC3G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC1F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC7F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAG,CAC7GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAA,CAAE,GAAG,MAAM,CACxE,CAAC,CACL,EACO,SAASY,EAAgBC,EAAU,CACtC,OAAOf,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CACA,aAAMe,EAAS,QAAQ,CAAE,OAAQ,iBAAiB,CAAE,EAC7C,EACX,MACc,CACV,MAAO,EACX,CACJ,CAAC,CACL,CACO,SAASC,GAAiB,CAC7B,OAAOhB,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CACA,MAAMe,EAAW,OAAO,SACxB,GAAI,CAACA,EACD,OAAO,KAEX,GAAIA,EAAS,WAAa,MAAM,QAAQA,EAAS,SAAS,EAAG,CACzD,MAAME,EAAYF,EAAS,UAC3B,UAAWA,KAAYE,EACnB,GAAI,MAAMH,EAAgBC,CAAQ,EAC9B,OAAOA,CAGnB,CACA,GAAIA,EAAS,UAAY,MAAM,QAAQA,EAAS,QAAQ,EAAG,CACvD,MAAME,EAAYF,EAAS,SAC3B,UAAWA,KAAYE,EACnB,GAAI,MAAMH,EAAgBC,CAAQ,EAC9B,OAAOA,CAGnB,CACA,OAAI,MAAMD,EAAgBC,CAAQ,GACvBA,EAEJ,IACX,OACOG,EAAO,CACV,eAAQ,MAAMA,CAAK,EACZ,IACX,CACJ,CAAC,CACL,CCnDO,MAAMC,EAAuB,iBAEvBC,EAAsB,gBAEtBC,EAAuB,iBAEvBC,EAAwB,kBAExBC,EAAgB,CACzBJ,EACAC,EACAC,EACAC,CACJ,EAIO,SAASE,EAAcC,EAAO,CACjC,OAAOF,EAAc,SAASE,CAAK,CACvC,CCpBA,IAAIC,EAAkE,SAAUC,EAAUC,EAAOC,EAAMC,EAAG,CACtG,GAAID,IAAS,KAAO,CAACC,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAOF,GAAU,WAAaD,IAAaC,GAAS,CAACE,EAAI,CAACF,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,0EAA0E,EACjL,OAAOE,IAAS,IAAMC,EAAID,IAAS,IAAMC,EAAE,KAAKH,CAAQ,EAAIG,EAAIA,EAAE,MAAQF,EAAM,IAAID,CAAQ,CAChG,EACII,EAAkE,SAAUJ,EAAUC,EAAOtB,EAAOuB,EAAMC,EAAG,CAC7G,GAAID,IAAS,IAAK,MAAM,IAAI,UAAU,gCAAgC,EACtE,GAAIA,IAAS,KAAO,CAACC,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAOF,GAAU,WAAaD,IAAaC,GAAS,CAACE,EAAI,CAACF,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,yEAAyE,EAChL,OAAQE,IAAS,IAAMC,EAAE,KAAKH,EAAUrB,CAAK,EAAIwB,EAAIA,EAAE,MAAQxB,EAAQsB,EAAM,IAAID,EAAUrB,CAAK,EAAIA,CACxG,EACI0B,EAAgDC,EAAkDC,EAA+CC,EAAiDC,EAA8CC,EAGpP,MAAMC,EAASf,EACTgB,EAAW,CAACC,EAA8BC,EAAuBC,CAAiB,EACjF,MAAMC,CAAsC,CAC/C,IAAI,SAAU,CACV,OAAOjB,EAAuB,KAAMM,EAAgD,GAAG,CAC3F,CACA,IAAI,WAAY,CACZ,OAAON,EAAuB,KAAMO,EAAkD,GAAG,EAAE,MAAK,CACpG,CACA,IAAI,QAAS,CACT,OAAOP,EAAuB,KAAMQ,EAA+C,GAAG,EAAE,MAAK,CACjG,CACA,IAAI,UAAW,CACX,OAAOR,EAAuB,KAAMS,EAAiD,GAAG,EAAE,MAAK,CACnG,CACA,IAAI,OAAQ,CACR,OAAOT,EAAuB,KAAMU,EAA8C,GAAG,CACzF,CACA,IAAI,MAAO,CACP,OAAOV,EAAuB,KAAMW,EAA6C,GAAG,CACxF,CACA,YAAY,CAAE,QAAAO,EAAS,UAAAC,EAAW,MAAAC,EAAO,KAAAC,CAAI,EAAI,CAC7Cf,EAA+C,IAAI,KAAM,MAAM,EAC/DC,EAAiD,IAAI,KAAM,MAAM,EACjEC,EAA8C,IAAI,KAAM,MAAM,EAC9DC,EAAgD,IAAI,KAAM,MAAM,EAChEC,EAA6C,IAAI,KAAM,MAAM,EAC7DC,EAA4C,IAAI,KAAM,MAAM,EACxD,aAAeM,GACf,OAAO,OAAO,IAAI,EAEtBZ,EAAuB,KAAMC,EAAgDY,EAAS,GAAG,EACzFb,EAAuB,KAAME,EAAkDY,EAAW,GAAG,EAC7Fd,EAAuB,KAAMG,EAA+CI,EAAQ,GAAG,EACvFP,EAAuB,KAAMI,EAAiDI,EAAU,GAAG,EAC3FR,EAAuB,KAAMK,EAA8CU,EAAO,GAAG,EACrFf,EAAuB,KAAMM,EAA6CU,EAAM,GAAG,CACvF,CACJ,CACAf,EAAiD,IAAI,QAAWC,EAAmD,IAAI,QAAWC,EAAgD,IAAI,QAAWC,EAAkD,IAAI,QAAWC,EAA+C,IAAI,QAAWC,EAA8C,IAAI,QCtDlX,IAAIrC,EAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAG,CAAC,CAAG,CAC3G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC1F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAG,OAASK,EAAG,CAAEH,EAAOG,CAAC,CAAG,CAAE,CAC7F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAG,CAC7GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAA,CAAE,GAAG,MAAM,CACxE,CAAC,CACL,EAWA,MAAM8C,UAAyBC,CAAa,CACxC,YAAYC,EAAQ,CAChB,MAAK,EACL,KAAK,SAAW,eAChB,KAAK,cAAgB,CAAA,EACrB,KAAK,SAAW,KAChB,KAAK,QAAU,KACf,KAAK,WAAa,KAClB,KAAK,SAAW,KAChB,KAAK,aAAe,GACpB,KAAK,gBAAkB,KACvB,KAAK,iBAAmB,CAAA,EACxB,KAAK,aAAgBC,GAAU,CAC3B,IAAIC,EAAIC,EACR,OAAQF,EAAM,KAAI,CACd,IAAK,UAAW,CACZ,KAAK,gBAAe,EACf,GAAAC,EAAKD,EAAM,QAAU,MAAQC,IAAO,SAAkBA,EAAG,WAC1D,KAAK,WAAaD,EAAM,KAAK,UAC7B,KAAK,aAAe,GAChB,KAAK,kBACL,KAAK,gBAAgB,QAAO,EAC5B,KAAK,gBAAkB,MAE3B,KAAK,WAAU,IAGX,KAAK,kBACL,KAAK,gBAAgB,OAAM,EAC3B,KAAK,gBAAkB,MAE3B,KAAK,cAAa,GAEtB,MACJ,CACA,IAAK,aAAc,CACX,KAAK,kBACL,KAAK,gBAAgB,OAAM,EAC3B,KAAK,gBAAkB,MAE3B,KAAK,cAAa,EAClB,MACJ,CACA,IAAK,iBAAkB,CACd,GAAAE,EAAKF,EAAM,QAAU,MAAQE,IAAO,SAAkBA,EAAG,WAC1D,KAAK,WAAaF,EAAM,KAAK,UAC7B,KAAK,KAAK,iBAAkB,KAAK,SAAS,EAC1C,KAAK,mBAAkB,IAGvB,KAAK,KAAK,iBAAkB,MAAS,EACrC,KAAK,sBAAqB,GAE9B,MACJ,CACA,QACI,MAEpB,CACQ,EACA,KAAK,cAAiBG,GAAS,CACvBA,EAAK,aAAe,OAChBA,EAAK,OAAO,OAAS,aACrB,KAAK,cAAa,EAEbA,EAAK,OAAO,OAAS,QAC1B,KAAK,gBAAe,EAGnBA,EAAK,aAAe,eACzB,KAAK,cAAcA,EAAK,MAAM,CAEtC,EACA,KAAK,eAAkBH,GAAU,CAC7B,IAAIC,EACJ,KAAMA,EAAKD,EAAM,QAAU,MAAQC,IAAO,OAAS,OAASA,EAAG,WAAa,gCACxE,OAEJ,MAAME,EAAOH,EAAM,KAAK,MAAQ,CAAA,EAChC,GAAIG,EAAK,OAAS,QACd,KAAK,aAAaA,EAAK,KAAK,UAEvBA,EAAK,OAAS,SACnB,KAAK,cAAcA,CAAI,UAElBA,EAAK,OAAS,YACf,KAAK,iBAAiBA,EAAK,EAAE,EAAG,CAChC,KAAM,CAAE,QAAA/C,EAAS,OAAAC,CAAM,EAAK,KAAK,iBAAiB8C,EAAK,EAAE,EACzD,OAAO,KAAK,iBAAiBA,EAAK,EAAE,EAChCA,EAAK,MACL9C,EAAO8C,EAAK,KAAK,EAGjB/C,EAAQ+C,EAAK,MAAM,CAE3B,CAER,EACA,KAAK,eAAiB,IAAM,CACpB,KAAK,WACL,KAAK,SAAS,OAAM,EACpB,KAAK,SAAW,KAExB,EACA,KAAK,wBAA0B,IAAM,CACjC,MAAMC,EAAW,SAAS,uBAAuB,yCAAyC,EAC1F,UAAWC,KAAWD,EACdC,EAAQ,eACRA,EAAQ,OAAM,CAG1B,EACA,KAAK,eAAiB,IAAM,CACxB,KAAK,eAAc,EACnB,KAAK,wBAAuB,EAC5B,MAAMC,EAAS,OAAO,OAAO,OAAO,OAAO,CAAA,EAAI,KAAK,aAAa,EAAG,CAAE,GAAI,GAAM,EAAG,EAAG,QAAS,KAAK,UAAY,eAAgB,OAAQ,OAAO,SAAS,QAAU,GAAI,MAAO,SAAS,OAAS,EAAE,CAAE,EAC7LC,EAAc,OAAO,KAAKD,CAAM,EACjC,IAAKE,GAAQ,GAAGA,CAAG,IAAI,mBAAmBF,EAAOE,CAAG,CAAC,CAAC,EAAE,EACxD,KAAK,GAAG,EACPC,EAAY,GAAGZ,EAAiB,UAAU,IAAIU,CAAW,GAC/D,KAAK,SAAW,SAAS,cAAc,KAAK,EAC5C,KAAK,SAAS,UAAY,0CAC1B,KAAK,SAAS,UAAY;AAAA,qBACjBE,CAAS;AAAA,MAElB,SAAS,KAAK,YAAY,KAAK,QAAQ,EACvC,KAAK,QAAU,KAAK,SAAS,cAAc,QAAQ,EACnD,OAAO,iBAAiB,UAAW,KAAK,eAAgB,EAAK,CACjE,EACA,KAAK,gBAAkB,IAAM,CACrB,KAAK,UACL,KAAK,QAAQ,MAAM,IAAM,GACzB,KAAK,QAAQ,MAAM,MAAQ,GAC3B,KAAK,QAAQ,MAAM,OAAS,MAC5B,KAAK,QAAQ,MAAM,MAAQ,MAEnC,EACA,KAAK,cAAgB,IAAM,CACnB,KAAK,UACL,KAAK,QAAQ,MAAM,IAAM,MACzB,KAAK,QAAQ,MAAM,OAAS,MAC5B,KAAK,QAAQ,MAAM,KAAO,MAC1B,KAAK,QAAQ,MAAM,MAAQ,MAC3B,KAAK,QAAQ,MAAM,MAAQ,OAC3B,KAAK,QAAQ,MAAM,OAAS,OAEpC,EACA,KAAK,cAAiBH,GAAW,CACxB,KAAK,UAGV,KAAK,QAAQ,MAAM,IAAM,SAASA,EAAO,GAAG,EAAI,GAAGA,EAAO,GAAG,KAAO,GACpE,KAAK,QAAQ,MAAM,OAAS,SAASA,EAAO,MAAM,EAAI,GAAGA,EAAO,MAAM,KAAO,GAC7E,KAAK,QAAQ,MAAM,KAAO,SAASA,EAAO,IAAI,EAAI,GAAGA,EAAO,IAAI,KAAO,GACvE,KAAK,QAAQ,MAAM,MAAQ,SAASA,EAAO,KAAK,EAAI,GAAGA,EAAO,KAAK,KAAO,GAC1E,KAAK,QAAQ,MAAM,MAAQ,SAASA,EAAO,KAAK,EAC1C,GAAGA,EAAO,KAAK,KACfA,EAAO,MACb,KAAK,QAAQ,MAAM,OAAS,SAASA,EAAO,MAAM,EAC5C,GAAGA,EAAO,MAAM,KAChBA,EAAO,OACjB,EACA,KAAK,mBAAsBH,GAAS,CAChC,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UACzB,MAAM,IAAI,MAAM,sBAAsB,EAE1C,OAAO,IAAI,QAAQ,CAAC/C,EAASC,IAAW,CACpC,IAAI4C,EAAIC,EACR,MAAMQ,EAAYC,EAAM,EACxB,KAAK,iBAAiBD,CAAS,EAAI,CAAE,QAAAtD,EAAS,OAAAC,CAAM,GACnD6C,GAAMD,EAAK,KAAK,WAAa,MAAQA,IAAO,OAAS,OAASA,EAAG,iBAAmB,MAAQC,IAAO,QAAkBA,EAAG,YAAY,CACjI,QAAS,gCACT,KAAM,OAAO,OAAO,CAAE,GAAIQ,CAAS,EAAIP,CAAI,CAC/D,EAAmB,GAAG,CACV,CAAC,CACL,EACA,KAAK,WAAa,IAAM,CACpB,KAAK,aAAe,GACpB,KAAK,KAAK,UAAW,KAAK,SAAS,EACnC,KAAK,mBAAkB,CAC3B,EACA,KAAK,cAAgB,IAAM,CACvB,KAAK,WAAa,KAClB,KAAK,aAAe,GACpB,OAAO,oBAAoB,UAAW,KAAK,eAAgB,EAAK,EAChE,KAAK,eAAc,EACnB,KAAK,KAAK,YAAY,EACtB,KAAK,sBAAqB,CAC9B,EACA,KAAK,mBAAqB,IAAM,CAC5B,GAAI,CAAC,KAAK,UACN,OAEJ,MAAMV,EAAU,KAAK,UAAU,SAAQ,GACnC,CAAC,KAAK,UAAY,KAAK,SAAS,UAAYA,KAC5C,KAAK,SAAW,IAAID,EAAsC,CACtD,QAAAC,EACA,UAAW,KAAK,UAAU,QAAO,CACrD,CAAiB,EACD,KAAK,KAAK,kBAAmB,CAAE,SAAU,KAAK,iBAAkB,EAExE,EACA,KAAK,sBAAwB,IAAM,CAC3B,KAAK,WACL,KAAK,SAAW,KAChB,KAAK,KAAK,kBAAmB,CAAE,SAAU,KAAK,iBAAkB,EAExE,EACoDM,GAAO,UACvD,KAAK,SAA2DA,GAAO,SAEvE,OAAO,yBACP,KAAK,cAAgB,OAAO,OAAO,OAAO,OAAO,CAAA,EAAI,KAAK,aAAa,EAAG,OAAO,sBAAsB,GAEvDA,GAAO,SACvD,KAAK,cAAgB,OAAO,OAAO,OAAO,OAAO,CAAA,EAAI,KAAK,aAAa,EAAmDA,GAAO,MAAM,EAE/I,CACA,IAAI,WAAY,CACZ,OAAO,KAAK,WAAa,IAAIa,EAAU,KAAK,UAAU,EAAI,IAC9D,CACA,IAAI,iBAAkB,CAClB,OAAO,KAAK,QAChB,CACA,IAAI,kBAAmB,CACnB,OAAO,KAAK,SAAW,CAAC,KAAK,QAAQ,EAAI,CAAA,CAC7C,CACA,IAAI,aAAc,CACd,OAAO,KAAK,YAChB,CACA,IAAI,WAAY,CACZ,OAAO,KAAK,WAChB,CACA,IAAI,aAAc,CACd,MAAO,EACX,CACA,SAAU,CACN,OAAO/D,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAC5C,KAAK,YAGT,KAAK,eAAc,EACnB,MAAM,IAAI,QAAQ,CAACO,EAASC,IAAW,CACnC,KAAK,gBAAkB,CAAE,QAAAD,EAAS,OAAAC,CAAM,CAC5C,CAAC,EACL,CAAC,CACL,CACA,YAAa,CACT,OAAOR,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,MAAM,KAAK,mBAAmB,CAC1B,OAAQ,YACxB,CAAa,EACD,KAAK,cAAa,CACtB,CAAC,CACL,CACA,gBAAgBF,EAAa,CACzB,IAAIsD,EACJ,OAAOpD,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UACzB,MAAM,IAAI,MAAM,sBAAsB,EAE1C,GAAI,CACA,MAAMgE,EAAwBjE,EAAqBD,CAAW,EACxDmE,EAAW,MAAM,KAAK,mBAAmB,CAC3C,OAAQ,oBACR,OAAQ,CACJ,YAAaC,EAAK,OAAOF,CAAqB,CACtE,CACA,CAAiB,EACK,CAAE,YAAaG,CAAiB,EAAKF,EAC3C,OAAOpE,EAA4BC,CAAW,EAAIsE,EAAY,KAAKF,EAAK,OAAOC,CAAiB,CAAC,EAAIE,EAAqB,YAAYH,EAAK,OAAOC,CAAiB,CAAC,CACxK,OACOxD,EAAG,CACN,MAAM,IAAI,QAAQyC,EAA2CzC,GAAE,YAAc,MAAQyC,IAAO,OAAS,OAASA,EAAG,KAAKzC,CAAC,IAAM,4BAA4B,CAC7J,CACJ,CAAC,CACL,CACA,oBAAoB2D,EAAc,CAC9B,IAAIlB,EACJ,OAAOpD,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UACzB,MAAM,IAAI,MAAM,sBAAsB,EAE1C,GAAI,CACA,MAAMuE,EAAyBD,EAAa,IAAKxE,GAAgBC,EAAqBD,CAAW,CAAC,EAC5F,CAAE,aAAc0E,CAAkB,EAAK,MAAM,KAAK,mBAAmB,CACvE,OAAQ,wBACR,OAAQ,CACJ,aAAcD,EAAuB,IAAKzE,GAAgBoE,EAAK,OAAOpE,CAAW,CAAC,CAC1G,CACA,CAAiB,EACD,OAAO0E,EAAmB,IAAI,CAACL,EAAmBM,IACvC5E,EAA4ByE,EAAaG,CAAK,CAAC,EAAIL,EAAY,KAAKF,EAAK,OAAOC,CAAiB,CAAC,EAAIE,EAAqB,YAAYH,EAAK,OAAOC,CAAiB,CAAC,CAC/K,CACL,OACOxD,EAAG,CACN,MAAM,IAAI,QAAQyC,EAA2CzC,GAAE,YAAc,MAAQyC,IAAO,OAAS,OAASA,EAAG,KAAKzC,CAAC,IAAM,6BAA6B,CAC9J,CACJ,CAAC,CACL,CACA,uBAAuBb,EAAaJ,EAAS,CACzC,IAAI0D,EACJ,OAAOpD,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UACzB,MAAM,IAAI,MAAM,sBAAsB,EAE1C,GAAI,CACA,MAAMgE,EAAwBjE,EAAqBD,CAAW,EACxD,CAAE,UAAA4E,CAAS,EAAK,MAAM,KAAK,mBAAmB,CAChD,OAAQ,yBACR,OAAQ,CACJ,YAAaR,EAAK,OAAOF,CAAqB,EAC9C,QAAAtE,CACxB,CACA,CAAiB,EACD,OAAOgF,CACX,OACO/D,EAAG,CACN,MAAM,IAAI,QAAQyC,EAA2CzC,GAAE,YAAc,MAAQyC,IAAO,OAAS,OAASA,EAAG,KAAKzC,CAAC,IAAM,qCAAqC,CACtK,CACJ,CAAC,CACL,CACA,YAAY2C,EAAMqB,EAAU,OAAQ,CAChC,IAAIvB,EACJ,OAAOpD,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UACzB,MAAM,IAAI,MAAM,sBAAsB,EAE1C,GAAI,CACA,KAAM,CAAE,UAAA0E,CAAS,EAAK,MAAM,KAAK,mBAAmB,CAChD,OAAQ,cACR,OAAQ,CACJ,KAAMR,EAAK,OAAOZ,CAAI,EACtB,QAAAqB,CACxB,CACA,CAAiB,EACD,OAAO,WAAW,KAAKT,EAAK,OAAOQ,CAAS,CAAC,CACjD,OACO/D,EAAG,CACN,MAAM,IAAI,QAAQyC,EAA2CzC,GAAE,YAAc,MAAQyC,IAAO,OAAS,OAASA,EAAG,KAAKzC,CAAC,IAAM,wBAAwB,CACzJ,CACJ,CAAC,CACL,CACA,KAAK2C,EAAMqB,EAAU,OAAQ,CACzB,OAAO3E,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,OAAO,MAAM,KAAK,YAAYsD,EAAMqB,CAAO,CAC/C,CAAC,CACL,CACA,OAAO,aAAc,CACjB,OAAO3E,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAEhD,MAAO,CAAC,EADS,MAAMgB,EAAc,EAEzC,CAAC,CACL,CACA,kCAAkC4D,EAAQ,CACtC,OAAO5E,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,eAAe,EACnC,MAAM6E,EAAU,CAAA,EAChB,GAAID,EAAO,SAAW,EAAG,CACrB,KAAM,CAAE,YAAA9E,EAAa,QAAAgF,EAAS,MAAArD,EAAO,QAAA/B,CAAO,EAAKkF,EAAO,CAAC,EACnD,CAAE,eAAAG,EAAgB,oBAAAC,EAAqB,cAAAC,EAAe,WAAAC,CAAU,EAAKxF,GAAW,CAAA,EACtF,GAAIoF,IAAY,KAAK,SACjB,MAAM,IAAI,MAAM,iBAAiB,EACrC,GAAI,CAACtD,EAAcC,CAAK,EACpB,MAAM,IAAI,MAAM,eAAe,EACnC,MAAMiD,EAAY,MAAM,KAAK,uBAAuBL,EAAqB,YAAYvE,CAAW,EAAG,CAC/F,oBAAAkF,EACA,eAAAD,EACA,WAAAG,EACA,cAAAD,CACpB,CAAiB,EACDJ,EAAQ,KAAK,CAAE,UAAWX,EAAK,OAAOQ,CAAS,EAAG,CACtD,SACSE,EAAO,OAAS,EACrB,UAAWO,KAASP,EAChBC,EAAQ,KAAK,GAAI,MAAM,KAAK,+BAA+BM,CAAK,CAAE,EAG1E,OAAON,CACX,CAAC,CACL,CACA,2BAA2BD,EAAQ,CAC/B,OAAO5E,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,eAAe,EACnC,MAAM6E,EAAU,CAAA,EAChB,GAAID,EAAO,SAAW,EAAG,CACrB,KAAM,CAAE,YAAA9E,EAAa,QAAAgF,EAAS,MAAArD,CAAK,EAAKmD,EAAO,CAAC,EAChD,GAAIE,IAAY,KAAK,SACjB,MAAM,IAAI,MAAM,iBAAiB,EACrC,GAAIrD,GAAS,CAACD,EAAcC,CAAK,EAC7B,MAAM,IAAI,MAAM,eAAe,EACnC,MAAM0C,EAAoB,MAAM,KAAK,gBAAgBE,EAAqB,YAAYvE,CAAW,CAAC,EAClG+E,EAAQ,KAAK,CAAE,kBAAmBV,EAAkB,UAAS,CAAE,CAAE,CACrE,SACSS,EAAO,OAAS,EAAG,CACxB,IAAInD,EACJ,UAAW0D,KAASP,EAAQ,CACxB,GAAIO,EAAM,UAAY,KAAK,SACvB,MAAM,IAAI,MAAM,iBAAiB,EACrC,GAAIA,EAAM,MAAO,CACb,GAAI,CAAC3D,EAAc2D,EAAM,KAAK,EAC1B,MAAM,IAAI,MAAM,eAAe,EACnC,GAAI1D,GACA,GAAI0D,EAAM,QAAU1D,EAChB,MAAM,IAAI,MAAM,mBAAmB,OAGvCA,EAAQ0D,EAAM,KAEtB,CACJ,CACA,MAAMb,EAAeM,EAAO,IAAI,CAAC,CAAE,YAAA9E,CAAW,IAAOuE,EAAqB,YAAYvE,CAAW,CAAC,EAC5F0E,EAAqB,MAAM,KAAK,oBAAoBF,CAAY,EACtEO,EAAQ,KAAK,GAAGL,EAAmB,IAAKL,IAAuB,CAC3D,kBAAmBA,EAAkB,UAAS,CAClE,EAAkB,CAAC,CACP,CACA,OAAOU,CACX,CAAC,CACL,CACA,uBAAuBD,EAAQ,CAC3B,OAAO5E,EAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,GAAI,CAAC,KAAK,UACN,MAAM,IAAI,MAAM,eAAe,EACnC,MAAM6E,EAAU,CAAA,EAChB,GAAID,EAAO,SAAW,EAAG,CACrB,KAAM,CAAE,QAAAQ,EAAS,QAAAN,GAAYF,EAAO,CAAC,EACrC,GAAIE,IAAY,KAAK,SACjB,MAAM,IAAI,MAAM,iBAAiB,EACrC,MAAMJ,EAAY,MAAM,KAAK,YAAYU,CAAO,EAChDP,EAAQ,KAAK,CAAE,cAAeO,EAAS,UAAAV,CAAS,CAAE,CACtD,SACSE,EAAO,OAAS,EACrB,UAAWO,KAASP,EAChBC,EAAQ,KAAK,GAAI,MAAM,KAAK,oBAAoBM,CAAK,CAAE,EAG/D,OAAON,CACX,CAAC,CACL,CACJ,CACA7B,EAAiB,WAAa", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}