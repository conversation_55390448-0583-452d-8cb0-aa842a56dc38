"""add_email_verification_fields

Revision ID: 06cece6e9417
Revises: update_subscription_status_case
Create Date: 2025-07-12 21:31:01.734135

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '06cece6e9417'
down_revision = 'update_subscription_status_case'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('email_verified', sa.<PERSON>an(), nullable=False, server_default='0'))
        batch_op.add_column(sa.Column('verification_token', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('verification_token_expires_at', sa.DateTime(), nullable=True))

    # Set existing Google OAuth users as verified
    op.execute("UPDATE users SET email_verified = 1 WHERE google_id IS NOT NULL")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('verification_token_expires_at')
        batch_op.drop_column('verification_token')
        batch_op.drop_column('email_verified')
    # ### end Alembic commands ###
