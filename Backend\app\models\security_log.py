import uuid
from datetime import datetime
from enum import Enum
from app import db
from cryptography.fernet import Fernet
import base64

class SecurityLog(db.Model):
    """Model to log security events for monitoring and auditing."""
    __tablename__ = 'security_logs'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON><PERSON>('users.id'), nullable=True)
    
    # Event details
    event_type = db.Column(db.String(100), nullable=False)  # login_success, failed_login, etc.
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.Text)
    
    # Additional event data
    details = db.Column(db.JSON)  # Store additional event-specific data
    risk_level = db.Column(db.String(20), default='low')  # low, medium, high, critical
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def __init__(self, user_id, event_type, ip_address, user_agent=None, details=None, risk_level='low'):
        self.user_id = user_id
        self.event_type = event_type
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.details = details or {}
        self.risk_level = risk_level
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'event_type': self.event_type,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'details': self.details,
            'risk_level': self.risk_level,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<SecurityLog {self.id} - User: {self.user_id} - Event: {self.event_type} - Risk: {self.risk_level}>'


class LoginAttempt(db.Model):
    """Model to track login attempts for security monitoring."""
    __tablename__ = 'login_attempts'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)  # Nullable for failed attempts
    
    # Request details
    ip_address = db.Column(db.String(45), nullable=False)  # IPv6 support
    user_agent = db.Column(db.Text)
    
    # Attempt details
    success = db.Column(db.Boolean, nullable=False)
    email_attempted = db.Column(db.String(255))  # For failed attempts
    failure_reason = db.Column(db.Text)
    
    # 2FA details
    two_fa_required = db.Column(db.Boolean, default=False)
    two_fa_success = db.Column(db.Boolean)
    
    # Timestamps
    attempted_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def __init__(self, ip_address, user_agent=None, user_id=None, email_attempted=None):
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.user_id = user_id
        self.email_attempted = email_attempted
        self.success = False  # Default to failed, set to True when successful
    
    def mark_success(self, two_fa_required=False, two_fa_success=None):
        """Mark login attempt as successful."""
        self.success = True
        self.two_fa_required = two_fa_required
        self.two_fa_success = two_fa_success
    
    def mark_failure(self, reason):
        """Mark login attempt as failed with reason."""
        self.success = False
        self.failure_reason = reason
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'success': self.success,
            'email_attempted': self.email_attempted,
            'failure_reason': self.failure_reason,
            'two_fa_required': self.two_fa_required,
            'two_fa_success': self.two_fa_success,
            'attempted_at': self.attempted_at.isoformat()
        }
    
    def __repr__(self):
        return f'<LoginAttempt {self.id} - IP: {self.ip_address} - Success: {self.success}>'


class ExchangeType(Enum):
    BINANCE = 'binance'
    BINANCE_US = 'binance_us'
    COINBASE = 'coinbase'
    KRAKEN = 'kraken'
    OKX = 'okx'
    KUCOIN = 'kucoin'
    BYBIT = 'bybit'
    HYPERLIQUID = 'hyperliquid'
    HTX = 'htx'
    HUOBI = 'huobi'

class APICredential(db.Model):
    """Model to store encrypted API credentials for different exchanges."""
    __tablename__ = 'api_credentials'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    
    # Exchange details
    exchange = db.Column(db.Enum(ExchangeType), nullable=False)
    
    # Encrypted credentials
    encrypted_api_key = db.Column(db.Text, nullable=False)
    encrypted_secret_key = db.Column(db.Text, nullable=False)
    encrypted_passphrase = db.Column(db.Text)  # For exchanges like Coinbase that require passphrase
    
    # Status and validation
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_valid = db.Column(db.Boolean, default=False, nullable=False)  # Validated by test API call
    last_validated_at = db.Column(db.DateTime)
    validation_error = db.Column(db.Text)
    
    # Permissions (what the API key can do)
    can_read = db.Column(db.Boolean, default=True, nullable=False)
    can_trade = db.Column(db.Boolean, default=False, nullable=False)
    can_withdraw = db.Column(db.Boolean, default=False, nullable=False)
    
    # Usage tracking
    last_used_at = db.Column(db.DateTime)
    total_requests = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __init__(self, user_id, exchange, api_key, secret_key, passphrase=None):
        self.user_id = user_id
        self.exchange = exchange
        self.set_credentials(api_key, secret_key, passphrase)
    
    def get_user_encryption_key(self):
        """Get user-specific encryption key."""
        from app.models.user import User
        user = User.query.get(self.user_id)
        if user:
            return user.get_encryption_key()
        raise ValueError("User not found for API credential")
    
    def set_credentials(self, api_key, secret_key, passphrase=None):
        """Encrypt and store API credentials."""
        key = self.get_user_encryption_key()
        f = Fernet(key)
        
        # Encrypt API key
        encrypted_api_key = f.encrypt(api_key.encode('utf-8'))
        self.encrypted_api_key = base64.b64encode(encrypted_api_key).decode('utf-8')
        
        # Encrypt secret key
        encrypted_secret_key = f.encrypt(secret_key.encode('utf-8'))
        self.encrypted_secret_key = base64.b64encode(encrypted_secret_key).decode('utf-8')
        
        # Encrypt passphrase if provided
        if passphrase:
            encrypted_passphrase = f.encrypt(passphrase.encode('utf-8'))
            self.encrypted_passphrase = base64.b64encode(encrypted_passphrase).decode('utf-8')
    
    def get_credentials(self):
        """Decrypt and return API credentials."""
        try:
            key = self.get_user_encryption_key()
            f = Fernet(key)
            
            # Decrypt API key
            encrypted_api_key = base64.b64decode(self.encrypted_api_key.encode('utf-8'))
            api_key = f.decrypt(encrypted_api_key).decode('utf-8')
            
            # Decrypt secret key
            encrypted_secret_key = base64.b64decode(self.encrypted_secret_key.encode('utf-8'))
            secret_key = f.decrypt(encrypted_secret_key).decode('utf-8')
            
            # Decrypt passphrase if exists
            passphrase = None
            if self.encrypted_passphrase:
                encrypted_passphrase = base64.b64decode(self.encrypted_passphrase.encode('utf-8'))
                passphrase = f.decrypt(encrypted_passphrase).decode('utf-8')
            
            return {
                'api_key': api_key,
                'secret_key': secret_key,
                'passphrase': passphrase
            }
        except Exception as e:
            # Log error and return None
            return None
    
    def validate_credentials(self):
        """Validate API credentials by making a test API call."""
        credentials = self.get_credentials()
        if not credentials:
            self.mark_invalid("Failed to decrypt credentials")
            return False
        
        try:
            # Import exchange-specific validation
            if self.exchange == ExchangeType.BINANCE:
                try:
                    from app.services.exchange_service import BinanceService
                    service = BinanceService(credentials['api_key'], credentials['secret_key'])
                    is_valid, permissions = service.validate_credentials()
                except ImportError:
                    is_valid, permissions = False, {}
            elif self.exchange == ExchangeType.KRAKEN:
                try:
                    from app.services.exchange_service import KrakenService
                    service = KrakenService(credentials['api_key'], credentials['secret_key'])
                    is_valid, permissions = service.validate_credentials()
                except ImportError:
                    is_valid, permissions = False, {}
            else:
                # Add other exchanges as needed
                is_valid, permissions = False, {}
            
            if is_valid:
                self.mark_valid(permissions)
                return True
            else:
                self.mark_invalid("API credentials validation failed")
                return False
                
        except Exception as e:
            self.mark_invalid(f"Validation error: {str(e)}")
            return False
    
    def mark_valid(self, permissions=None):
        """Mark credentials as valid."""
        self.is_valid = True
        self.last_validated_at = datetime.utcnow()
        self.validation_error = None
        
        if permissions:
            self.can_read = permissions.get('can_read', True)
            self.can_trade = permissions.get('can_trade', False)
            self.can_withdraw = permissions.get('can_withdraw', False)
        
        self.updated_at = datetime.utcnow()
    
    def mark_invalid(self, error_message):
        """Mark credentials as invalid."""
        self.is_valid = False
        self.validation_error = error_message
        self.updated_at = datetime.utcnow()
    
    def update_usage(self):
        """Update usage statistics."""
        self.last_used_at = datetime.utcnow()
        self.total_requests += 1
    
    def deactivate(self):
        """Deactivate API credentials."""
        self.is_active = False
        self.updated_at = datetime.utcnow()
    
    def to_dict(self, include_credentials=False):
        """Convert to dictionary."""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'exchange': self.exchange.value,
            'is_active': self.is_active,
            'is_valid': self.is_valid,
            'last_validated_at': self.last_validated_at.isoformat() if self.last_validated_at else None,
            'validation_error': self.validation_error,
            'can_read': self.can_read,
            'can_trade': self.can_trade,
            'can_withdraw': self.can_withdraw,
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None,
            'total_requests': self.total_requests,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_credentials:
            credentials = self.get_credentials()
            if credentials:
                data['api_key'] = credentials['api_key']
                data['secret_key'] = credentials['secret_key']
                data['passphrase'] = credentials['passphrase']
        
        return data
    
    def __repr__(self):
        return f'<APICredential {self.id} - User: {self.user_id} - Exchange: {self.exchange.value} - Valid: {self.is_valid}>'