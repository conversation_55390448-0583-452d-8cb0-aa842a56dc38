#!/usr/bin/env python3
"""
Migration script to add risk settings fields to users table
Run this script to add the new risk management fields to existing users table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from sqlalchemy import text

def run_migration():
    """Run the migration to add risk settings fields"""
    app = create_app()
    
    with app.app_context():
        try:
            print("Starting migration: Adding risk settings fields to users table...")
            
            # Check if columns already exist
            with db.engine.connect() as connection:
                result = connection.execute(text("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = 'users'
                    AND column_name IN ('investment_percentage', 'leverage_multiplier', 'risk_settings_configured', 'selected_exchange', 'account_type')
                """))

                existing_columns = [row[0] for row in result]
            
            if len(existing_columns) > 0:
                print(f"Some columns already exist: {existing_columns}")
                print("Skipping migration to avoid conflicts.")
                return
            
            # Add new columns
            migrations = [
                "ALTER TABLE users ADD COLUMN investment_percentage DECIMAL(5,2) DEFAULT 0.00 NOT NULL",
                "ALTER TABLE users ADD COLUMN leverage_multiplier DECIMAL(3,1) DEFAULT 1.0 NOT NULL", 
                "ALTER TABLE users ADD COLUMN risk_settings_configured BOOLEAN DEFAULT FALSE NOT NULL",
                "ALTER TABLE users ADD COLUMN selected_exchange VARCHAR(50) DEFAULT 'binance' NOT NULL",
                "ALTER TABLE users ADD COLUMN account_type VARCHAR(20) DEFAULT 'SPOT' NOT NULL"
            ]
            
            with db.engine.connect() as connection:
                for migration in migrations:
                    print(f"Executing: {migration}")
                    connection.execute(text(migration))

                # Add indexes
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_users_risk_configured ON users(risk_settings_configured)",
                    "CREATE INDEX IF NOT EXISTS idx_users_selected_exchange ON users(selected_exchange)"
                ]

                for index in indexes:
                    print(f"Executing: {index}")
                    connection.execute(text(index))

                connection.commit()
            

            print("✅ Migration completed successfully!")
            print("New fields added:")
            print("  - investment_percentage (DECIMAL(5,2), default 0.00)")
            print("  - leverage_multiplier (DECIMAL(3,1), default 1.0)")
            print("  - risk_settings_configured (BOOLEAN, default FALSE)")
            print("  - selected_exchange (VARCHAR(50), default 'binance')")
            print("  - account_type (VARCHAR(20), default 'SPOT')")
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    run_migration()
