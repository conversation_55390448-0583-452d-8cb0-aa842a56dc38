#!/usr/bin/env python3
"""
🚀 Startup Services for DeepTrade
Initializes ML prediction services and trading bot on application startup
"""

import os
import logging
import threading
from flask import current_app

logger = logging.getLogger(__name__)

def initialize_ml_services():
    """Initialize ML prediction services on startup"""
    logger.info("🤖 Initializing ML prediction services...")
    
    try:
        # Initialize Elite ML System
        try:
            from app.services.elite_ml_service import Elite96PercentPredictor
            elite_predictor = Elite96PercentPredictor()
            logger.info("✅ Elite ML system initialized")
        except Exception as e:
            logger.warning(f"Elite ML system initialization failed: {e}")

        # Initialize SL/TP ML System
        try:
            from app.services.sl_tp_ml_predictors import SLTPMLManager
            sl_tp_manager = SLTPMLManager()
            logger.info("✅ SL/TP ML system initialized")
        except Exception as e:
            logger.warning(f"SL/TP ML system initialization failed: {e}")

        # Initialize Hybrid Deep Learning System
        try:
            from app.services.hybrid_deep_ml import HybridDeepMLEnhancer
            hybrid_enhancer = HybridDeepMLEnhancer()
            logger.info("✅ Hybrid Deep Learning system initialized")
        except Exception as e:
            logger.info(f"ℹ️ Hybrid Deep Learning system not available: {e}")
        
        # Initialize ML Forecast Service (already started in __init__.py)
        logger.info("✅ ML forecast service already initialized")
        
        logger.info("🎉 ML prediction services initialization completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ ML services initialization failed: {e}")
        return False

def initialize_trading_services():
    """Initialize trading bot services on startup"""
    logger.info("🤖 Initializing trading bot services...")
    
    try:
        # Initialize Trading Container System
        try:
            from app.services.trading_container import UserTradingContainer
            logger.info("✅ Trading container system initialized")
        except Exception as e:
            logger.warning(f"Trading container system initialization failed: {e}")
        
        # Initialize Paper Trading Service
        try:
            from app.services.paper_trading_service import PaperTradingService
            logger.info("✅ Paper trading service initialized")
        except Exception as e:
            logger.warning(f"Paper trading service initialization failed: {e}")
        
        # Start background trading signal monitoring
        try:
            start_trading_signal_monitoring()
            logger.info("✅ Trading signal monitoring started")
        except Exception as e:
            logger.warning(f"Trading signal monitoring failed to start: {e}")
        
        logger.info("🎉 Trading bot services initialization completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Trading services initialization failed: {e}")
        return False

def start_trading_signal_monitoring():
    """Start background thread for trading signal monitoring"""
    def trading_signal_worker():
        """Background worker for trading signals"""
        logger.info("🔄 Trading signal monitoring worker started")
        
        # This worker will be responsible for:
        # 1. Monitoring market conditions
        # 2. Generating trading signals for active users
        # 3. Managing trading containers
        
        # For now, just log that it's running
        # The actual trading logic is triggered by user actions
        # and managed by individual trading containers
        
    # Start the worker thread
    worker_thread = threading.Thread(target=trading_signal_worker, daemon=True)
    worker_thread.start()
    
    return True

def create_demo_paper_trade():
    """Create a demo paper trade for UI testing"""
    logger.info("📊 Creating demo paper trade for UI testing...")
    
    try:
        from app import db
        from app.models.user import User
        from app.models.paper_trading import PaperTrade, PaperTradeSide, PaperTradeStatus
        from app.services.paper_trading_service import PaperTradingService
        from app.services.market_data import fetch_binance_data
        from decimal import Decimal
        import random
        
        # Find or create a test user
        test_user = User.query.filter_by(email='<EMAIL>').first()
        if not test_user:
            test_user = User(
                email='<EMAIL>',
                full_name='Demo User',
                password='demo_password'
            )
            test_user.is_active = True
            db.session.add(test_user)
            db.session.commit()
            logger.info(f"✅ Created demo user: {test_user.email}")
        
        # Get or create paper account
        paper_account = PaperTradingService.get_or_create_paper_account(str(test_user.id))
        
        # Get current BTC price
        try:
            market_data = fetch_binance_data('BTCUSDT', '1m', 1)
            if market_data is not None and len(market_data) > 0:
                current_price = float(market_data.iloc[-1]['close'])
            else:
                current_price = 67000.0  # Fallback price
        except:
            current_price = 67000.0  # Fallback price
        
        # Create a demo paper trade
        demo_trade = PaperTrade(
            user_id=str(test_user.id),
            paper_account_id=paper_account.id,
            symbol='BTCUSDT',
            side=PaperTradeSide.BUY,
            quantity=Decimal('0.01'),  # 0.01 BTC
            entry_price=Decimal(str(current_price)),
            stop_loss=Decimal(str(current_price * 0.98)),  # 2% stop loss
            take_profit=Decimal(str(current_price * 1.05)),  # 5% take profit
            source='demo'
        )
        
        db.session.add(demo_trade)
        db.session.commit()
        
        logger.info(f"✅ Created demo paper trade: {demo_trade.id}")
        logger.info(f"   Symbol: {demo_trade.symbol}")
        logger.info(f"   Side: {demo_trade.side.value}")
        logger.info(f"   Quantity: {demo_trade.quantity}")
        logger.info(f"   Entry Price: ${demo_trade.entry_price}")
        logger.info(f"   Stop Loss: ${demo_trade.stop_loss}")
        logger.info(f"   Take Profit: ${demo_trade.take_profit}")
        
        return demo_trade
        
    except Exception as e:
        logger.error(f"❌ Failed to create demo paper trade: {e}")
        return None

def initialize_demo_data():
    """Initialize demo data for testing"""
    logger.info("🎭 Initializing demo data...")
    
    try:
        # Create demo paper trade
        demo_trade = create_demo_paper_trade()
        
        if demo_trade:
            logger.info("✅ Demo data initialized successfully")
            return True
        else:
            logger.warning("⚠️ Demo data initialization completed with issues")
            return False
            
    except Exception as e:
        logger.error(f"❌ Demo data initialization failed: {e}")
        return False

def startup_health_check():
    """Perform startup health check"""
    logger.info("🏥 Performing startup health check...")
    
    health_status = {
        'ml_services': False,
        'trading_services': False,
        'database': False,
        'redis': False
    }
    
    # Check database connection
    try:
        from app import db
        db.session.execute('SELECT 1')
        health_status['database'] = True
        logger.info("✅ Database connection: OK")
    except Exception as e:
        logger.error(f"❌ Database connection: FAILED - {e}")
    
    # Check Redis connection
    try:
        from app.services.redis_service import redis_client
        redis_client.ping()
        health_status['redis'] = True
        logger.info("✅ Redis connection: OK")
    except Exception as e:
        logger.error(f"❌ Redis connection: FAILED - {e}")
    
    # Check ML services
    try:
        from app.services.market_data import ml_service
        health_status['ml_services'] = True
        logger.info("✅ ML services: OK")
    except Exception as e:
        logger.error(f"❌ ML services: FAILED - {e}")
    
    # Check trading services
    try:
        from app.services.trading_container import UserTradingContainer
        health_status['trading_services'] = True
        logger.info("✅ Trading services: OK")
    except Exception as e:
        logger.error(f"❌ Trading services: FAILED - {e}")
    
    # Overall health
    overall_health = all(health_status.values())
    if overall_health:
        logger.info("🎉 Startup health check: ALL SYSTEMS OPERATIONAL")
    else:
        logger.warning("⚠️ Startup health check: SOME SYSTEMS HAVE ISSUES")
        for service, status in health_status.items():
            if not status:
                logger.warning(f"   - {service}: NEEDS ATTENTION")
    
    return health_status

def run_full_startup_sequence():
    """Run the complete startup sequence"""
    logger.info("🚀 Starting DeepTrade full startup sequence...")
    
    try:
        # Step 1: Health check
        health_status = startup_health_check()
        
        # Step 2: Initialize ML services
        ml_success = initialize_ml_services()
        
        # Step 3: Initialize trading services
        trading_success = initialize_trading_services()
        
        # Step 4: Initialize demo data (optional)
        demo_success = initialize_demo_data()
        
        # Summary
        logger.info("📊 STARTUP SEQUENCE SUMMARY:")
        logger.info(f"   Health Check: {'✅ PASS' if all(health_status.values()) else '⚠️ ISSUES'}")
        logger.info(f"   ML Services: {'✅ PASS' if ml_success else '❌ FAIL'}")
        logger.info(f"   Trading Services: {'✅ PASS' if trading_success else '❌ FAIL'}")
        logger.info(f"   Demo Data: {'✅ PASS' if demo_success else '⚠️ ISSUES'}")
        
        overall_success = ml_success and trading_success
        if overall_success:
            logger.info("🎉 DEEPTRADE STARTUP COMPLETED SUCCESSFULLY!")
        else:
            logger.warning("⚠️ DeepTrade startup completed with some issues")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ Startup sequence failed: {e}")
        return False
