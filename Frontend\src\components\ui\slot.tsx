import * as React from 'react';

/* -----------------------------------------------------------------------------
 * Slot
 * ---------------------------------------------------------------------------*/

interface SlotProps extends React.HTMLAttributes<HTMLElement> {
  children?: React.ReactNode;
}

export const Slot = React.forwardRef<HTMLElement, SlotProps>((props, ref) => {
  const { children, ...slotProps } = props;
  const childrenArray = React.Children.toArray(children);
  const slottable = childrenArray.find(isSlottable);

  if (slottable && React.isValidElement(slottable)) {
    const slottableProps = slottable.props as any;
    const newElement = slottableProps.children as React.ReactElement;
    const newChildren = childrenArray.map((child) => {
      if (child === slottable) {
        if (React.isValidElement(newElement)) {
          return (newElement.props as any).children ?? null;
        }
        return null;
      }
      return child;
    });

    return (
      <SlotClone ref={ref} {...mergeProps(slotProps, React.isValidElement(newElement) ? (newElement.props as any) : {})}>
        {newChildren}
      </SlotClone>
    );
  }

  return (
    <SlotClone ref={ref} {...slotProps}>
      {children}
    </SlotClone>
  );
});

Slot.displayName = 'Slot';

/* -----------------------------------------------------------------------------
 * SlotClone
 * ---------------------------------------------------------------------------*/

interface SlotCloneProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
}

const SlotClone = React.forwardRef<HTMLElement, SlotCloneProps>((props, ref) => {
  const { children, ...slotProps } = props;

  if (React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<any>, {
      ...mergeProps(slotProps, (children.props as any) || {}),
      ref: ref ? composeRefs(ref, (children as any).ref) : (children as any).ref,
    });
  }

  return React.Children.count(children) > 1 ? React.Children.only(null) : null;
});

SlotClone.displayName = 'SlotClone';

/* -----------------------------------------------------------------------------
 * Slottable
 * ---------------------------------------------------------------------------*/

export const Slottable = ({
  children,
}: {
  children: React.ReactNode;
}): React.ReactElement => {
  return <>{children}</>;
};

/* -----------------------------------------------------------------------------
 * Utils
 * ---------------------------------------------------------------------------*/

function isSlottable(child: React.ReactNode): child is React.ReactElement {
  return React.isValidElement(child) && child.type === Slottable;
}

function mergeProps(
  ...objects: Record<string, any>[]
): Record<string, any> {
  const result: Record<string, any> = {};

  for (const object of objects) {
    for (const key in object) {
      const existingValue = result[key];
      const newValue = object[key];

      if (key === 'className') {
        result[key] = [existingValue, newValue].filter(Boolean).join(' ');
      } else if (key === 'style') {
        result[key] = { ...existingValue, ...newValue };
      } else if (key.startsWith('on') && typeof existingValue === 'function' && typeof newValue === 'function') {
        result[key] = (...args: any[]) => {
          existingValue(...args);
          newValue(...args);
        };
      } else {
        result[key] = newValue;
      }
    }
  }

  return result;
}

function composeRefs<T>(...refs: React.Ref<T>[]): React.RefCallback<T> {
  return (value) => {
    refs.forEach((ref) => {
      if (typeof ref === 'function') {
        ref(value);
      } else if (ref != null) {
        (ref as React.MutableRefObject<T | null>).current = value;
      }
    });
  };
}
