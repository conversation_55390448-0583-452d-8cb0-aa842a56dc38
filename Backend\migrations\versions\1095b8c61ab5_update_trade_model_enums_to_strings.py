"""Update Trade model enums to strings

Revision ID: 1095b8c61ab5
Revises: 
Create Date: 2025-07-09 10:50:10.726949

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '1095b8c61ab5'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('fee_calculations',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('calculation_period_start', sa.Date(), nullable=False),
    sa.Column('calculation_period_end', sa.Date(), nullable=False),
    sa.Column('total_profit', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('total_loss', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('net_profit', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('fee_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('fee_amount', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'CALCULATED', 'PROCESSED', 'FAILED', name='feecalculationstatus'), nullable=False),
    sa.Column('transaction_hash', sa.String(length=255), nullable=True),
    sa.Column('payment_reference', sa.String(length=255), nullable=True),
    sa.Column('processed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('trades_count', sa.Integer(), nullable=True),
    sa.Column('winning_trades_count', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('trading_sessions',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'PAUSED', 'STOPPED', 'COMPLETED', name='tradingsessionstatus'), nullable=False),
    sa.Column('initial_balance', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('current_balance', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('leverage', sa.Integer(), nullable=True),
    sa.Column('investment_percentage', sa.Integer(), nullable=True),
    sa.Column('total_trades', sa.Integer(), nullable=True),
    sa.Column('winning_trades', sa.Integer(), nullable=True),
    sa.Column('losing_trades', sa.Integer(), nullable=True),
    sa.Column('total_profit', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('total_loss', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=False),
    sa.Column('ended_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('trades',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('session_id', sa.String(length=36), nullable=True),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('side', sa.String(length=10), nullable=False),
    sa.Column('quantity', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('source', sa.String(length=20), nullable=False),
    sa.Column('entry_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('exit_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('stop_loss', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('take_profit', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('pnl', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('fee_charged', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('exchange_fee', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('exchange_order_id', sa.String(length=255), nullable=True),
    sa.Column('exit_reason', sa.String(length=100), nullable=True),
    sa.Column('entry_time', sa.DateTime(), nullable=False),
    sa.Column('exit_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['session_id'], ['trading_sessions.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('subscription')
    with op.batch_alter_table('api_credentials', schema=None) as batch_op:
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_server_default=sa.text("'1'"))
        batch_op.alter_column('is_valid',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('can_read',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_server_default=sa.text("'1'"))
        batch_op.alter_column('can_trade',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('can_withdraw',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_server_default=sa.text("'0'"))
        batch_op.drop_constraint('api_credentials_ibfk_1', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    with op.batch_alter_table('payments', schema=None) as batch_op:
        batch_op.drop_constraint('payments_ibfk_2', type_='foreignkey')
        batch_op.drop_constraint('payments_ibfk_1', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'subscriptions', ['subscription_id'], ['id'])

    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.drop_index('ix_subscriptions_tier_status_id')
        batch_op.drop_index('ix_subscriptions_user_id')
        batch_op.drop_constraint('subscriptions_ibfk_2', type_='foreignkey')
        batch_op.drop_constraint('subscriptions_ibfk_1', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'user_tier_status', ['tier'], ['tier'])
        batch_op.drop_column('tier_status_id')

    with op.batch_alter_table('user_2fa_backup_codes', schema=None) as batch_op:
        batch_op.drop_index('ix_user_2fa_backup_codes_user_id')
        batch_op.drop_constraint('user_2fa_backup_codes_ibfk_1', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])
        batch_op.drop_column('updated_at')

    with op.batch_alter_table('user_tier_status', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
        batch_op.drop_constraint('user_tier_status_ibfk_1', type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('two_fa_enabled',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_server_default=sa.text("'1'"))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_server_default=sa.text("'1'"))
        batch_op.alter_column('two_fa_enabled',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_server_default=sa.text("'0'"))

    with op.batch_alter_table('user_tier_status', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_tier_status_ibfk_1', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.alter_column('id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=36),
               existing_nullable=False,
               autoincrement=True)

    with op.batch_alter_table('user_2fa_backup_codes', schema=None) as batch_op:
        batch_op.add_column(sa.Column('updated_at', mysql.DATETIME(), nullable=False))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_2fa_backup_codes_ibfk_1', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_index('ix_user_2fa_backup_codes_user_id', ['user_id'], unique=False)

    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('tier_status_id', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=36), nullable=False))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('subscriptions_ibfk_1', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('subscriptions_ibfk_2', 'user_tier_status', ['tier_status_id'], ['id'], ondelete='CASCADE')
        batch_op.create_index('ix_subscriptions_user_id', ['user_id'], unique=False)
        batch_op.create_index('ix_subscriptions_tier_status_id', ['tier_status_id'], unique=False)

    with op.batch_alter_table('payments', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('payments_ibfk_1', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('payments_ibfk_2', 'subscriptions', ['subscription_id'], ['id'], ondelete='SET NULL')

    with op.batch_alter_table('api_credentials', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('api_credentials_ibfk_1', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.alter_column('can_withdraw',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('can_trade',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('can_read',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_server_default=sa.text("'1'"))
        batch_op.alter_column('is_valid',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_server_default=sa.text("'1'"))

    op.create_table('subscription',
    sa.Column('id', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=36), nullable=False),
    sa.Column('user_id', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=36), nullable=False),
    sa.Column('tier', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('status', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50), nullable=False),
    sa.Column('start_date', mysql.DATETIME(), nullable=False),
    sa.Column('end_date', mysql.DATETIME(), nullable=True),
    sa.Column('created_at', mysql.DATETIME(), nullable=False),
    sa.Column('updated_at', mysql.DATETIME(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='subscription_ibfk_1', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.drop_table('trades')
    op.drop_table('trading_sessions')
    op.drop_table('fee_calculations')
    # ### end Alembic commands ###
