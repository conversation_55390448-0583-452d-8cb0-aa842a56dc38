# DeepTrade Production Deployment Plan

## Overview
This plan covers setting up the DeepTrade application with:
- **Frontend**: Deployed on Vercel (https://deep-trade-frontend.vercel.app) → deeptrade.capitolchilax.com
- **Backend**: AWS EC2 Free Tier (************) with HTTPS support
- **Environment Configuration**: Replace hardcoded URLs with environment variables

## Current Status
- [x] **Backend Environment Configuration** ✅ COMPLETED
- [x] **Frontend Environment Configuration** ✅ COMPLETED
- [ ] **AWS EC2 HTTPS Setup**
- [ ] **Vercel Custom Domain Setup**
- [x] **CORS Configuration Update** ✅ COMPLETED
- [ ] **Testing & Validation**

---

## Phase 1: Backend Environment Configuration

### 1.1 Update Backend .env File ✅ COMPLETED
- [x] Create production .env file with FRONTEND_URL variable
- [x] Update FRONTEND_URL to point to production domain
- [x] Configure CORS origins for production

**Files modified:**
- `Backend/.env` (production configuration) ✅
- `Backend/app/__init__.py` (CORS settings) ✅

### 1.2 Replace Hardcoded Frontend URLs ✅ COMPLETED
- [x] Audit backend code for hardcoded frontend URLs
- [x] Replace with FRONTEND_URL environment variable
- [x] Update email templates and verification links

**Updated files:**
- `Backend/app/__init__.py` - Updated CORS origins ✅
- `Backend/app/api/support_routes.py` - Updated CORS origins ✅
- Email services already use FRONTEND_URL from config ✅

---

## Phase 2: Frontend Environment Configuration

### 2.1 Create Frontend .env File ✅ COMPLETED
- [x] Create `.env` file in Frontend directory
- [x] Add VITE_API_URL environment variable
- [x] Configure for production backend URL

### 2.2 Replace Hardcoded Backend URLs ✅ COMPLETED
- [x] Update `src/config.ts` to use environment variable
- [x] Replace hardcoded IPs in auth service
- [x] Update all API service files
- [x] Fix hardcoded URLs in component files

**Updated files:**
- `src/config.ts`: Already using VITE_API_URL ✅
- `src/api/auth.service.ts`: Updated to use environment variable ✅
- `src/services/api.ts`: Updated to use VITE_API_URL ✅
- `src/pages/SignUp.tsx`: Updated to use API_BASE_URL ✅
- `src/pages/ForgotPassword.tsx`: Updated to use API_BASE_URL ✅
- `src/pages/VerifyEmail.tsx`: Updated to use API_BASE_URL ✅
- `src/pages/TwoFAResetRequest.tsx`: Updated to use API_BASE_URL ✅
- `src/pages/Login.tsx`: Updated to use API_BASE_URL ✅

---

## Phase 3: AWS EC2 HTTPS Setup

### 3.1 SSL Certificate Setup
- [ ] Install Certbot for Let's Encrypt
- [ ] Generate SSL certificate for domain
- [ ] Configure automatic renewal

### 3.2 Nginx Configuration
- [ ] Install and configure Nginx as reverse proxy
- [ ] Set up SSL termination
- [ ] Configure proxy pass to Flask application
- [ ] Set up security headers

### 3.3 Flask Application Updates
- [ ] Update Flask to run behind proxy
- [ ] Configure for HTTPS in production
- [ ] Update CORS for HTTPS origins
- [ ] Test SSL connectivity

---

## Phase 4: Vercel Custom Domain Setup

### 4.1 Domain Configuration
- [ ] Add custom domain in Vercel dashboard
- [ ] Configure DNS records for deeptrade.capitolchilax.com
- [ ] Verify domain ownership
- [ ] Enable automatic HTTPS

### 4.2 Environment Variables
- [ ] Set VITE_API_URL in Vercel environment
- [ ] Configure production environment variables
- [ ] Test deployment with new domain

---

## Phase 5: CORS and Security Updates

### 5.1 Backend CORS Configuration
- [ ] Update allowed origins for production
- [ ] Add both Vercel URLs (temporary and custom domain)
- [ ] Configure secure cookie settings
- [ ] Test cross-origin requests

### 5.2 Security Headers
- [ ] Configure HTTPS-only cookies
- [ ] Set secure CORS policies
- [ ] Add security headers in Nginx
- [ ] Test security configuration

---

## Phase 6: Testing & Validation

### 6.1 Integration Testing
- [ ] Test frontend-backend communication
- [ ] Verify authentication flows
- [ ] Test API endpoints
- [ ] Validate email functionality

### 6.2 Production Validation
- [ ] Test with custom domain
- [ ] Verify HTTPS connectivity
- [ ] Test all user flows
- [ ] Monitor error logs

---

## Environment Variables Summary

### Backend (.env)
```bash
# Production Frontend URL
FRONTEND_URL=https://deeptrade.capitolchilax.com

# Flask Configuration
FLASK_ENV=production
HOST=0.0.0.0
PORT=5000

# SSL Configuration (if needed)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

### Frontend (.env)
```bash
# Production Backend URL
VITE_API_URL=https://************:5000
```

---

## Detailed Implementation Guide

### Phase 3: AWS EC2 HTTPS Setup - Step by Step

#### 3.1 Install Required Packages
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Nginx
sudo apt install nginx -y

# Install Certbot for Let's Encrypt SSL
sudo apt install certbot python3-certbot-nginx -y

# Install Python and pip if not already installed
sudo apt install python3 python3-pip -y
```

#### 3.2 Configure Nginx as Reverse Proxy
Create Nginx configuration file:
```bash
sudo nano /etc/nginx/sites-available/deeptrade
```

Add this configuration:
```nginx
server {
    listen 80;
    server_name ************;

    # Redirect all HTTP requests to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ************;

    # SSL Configuration (will be auto-configured by Certbot)
    ssl_certificate /etc/letsencrypt/live/************/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/************/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Proxy settings
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_redirect off;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

#### 3.3 Enable Nginx Configuration
```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/deeptrade /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 3.4 Generate SSL Certificate
```bash
# Generate SSL certificate for IP address (Note: Let's Encrypt doesn't support IP certificates)
# For IP-based SSL, you'll need to use a self-signed certificate or get a domain

# Option 1: Self-signed certificate (for testing)
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/deeptrade.key \
    -out /etc/ssl/certs/deeptrade.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=************"

# Update Nginx config to use self-signed certificate
sudo sed -i 's|/etc/letsencrypt/live/************/fullchain.pem|/etc/ssl/certs/deeptrade.crt|g' /etc/nginx/sites-available/deeptrade
sudo sed -i 's|/etc/letsencrypt/live/************/privkey.pem|/etc/ssl/private/deeptrade.key|g' /etc/nginx/sites-available/deeptrade

# Reload Nginx
sudo systemctl reload nginx
```

#### 3.5 Configure Flask Application for Production
Update the backend to run properly behind a proxy:

```bash
# Navigate to backend directory
cd /path/to/Backend

# Install gunicorn for production WSGI server
pip install gunicorn

# Create systemd service file
sudo nano /etc/systemd/system/deeptrade.service
```

Add this service configuration:
```ini
[Unit]
Description=DeepTrade Flask Application
After=network.target

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/DeepTrade/Production/Backend
Environment="PATH=/home/<USER>/DeepTrade/Production/Backend/venv/bin"
ExecStart=/home/<USER>/DeepTrade/Production/Backend/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:5000 run:app
Restart=always

[Install]
WantedBy=multi-user.target
```

#### 3.6 Start Services
```bash
# Reload systemd and start the service
sudo systemctl daemon-reload
sudo systemctl start deeptrade
sudo systemctl enable deeptrade

# Check service status
sudo systemctl status deeptrade

# Check Nginx status
sudo systemctl status nginx
```

### Commands Reference

#### Useful Commands for Troubleshooting
```bash
# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# Check Flask application logs
sudo journalctl -u deeptrade -f

# Test SSL certificate
openssl s_client -connect ************:443 -servername ************

# Restart services
sudo systemctl restart nginx
sudo systemctl restart deeptrade
```

### Phase 4: Vercel Custom Domain Setup - Step by Step

#### 4.1 Configure Custom Domain in Vercel
1. **Login to Vercel Dashboard**
   - Go to https://vercel.com/dashboard
   - Navigate to your `deep-trade-frontend` project

2. **Add Custom Domain**
   - Go to Settings → Domains
   - Add `deeptrade.capitolchilax.com`
   - Vercel will provide DNS configuration instructions

3. **Configure DNS Records**
   Add these DNS records to your domain provider:
   ```
   Type: CNAME
   Name: deeptrade
   Value: cname.vercel-dns.com
   ```

4. **Set Environment Variables in Vercel**
   - Go to Settings → Environment Variables
   - Add: `VITE_API_URL` = `https://************:5000`
   - Add: `VITE_GOOGLE_CLIENT_ID` = `65022818057-or6uhhh07v10uk2tfrmq7n7i8c0g1gou.apps.googleusercontent.com`

#### 4.2 Update Backend CORS for New Domain
Update the backend .env file:
```bash
# Update FRONTEND_URL to use custom domain
FRONTEND_URL=https://deeptrade.capitolchilax.com
```

### Phase 5: Final Testing and Validation

#### 5.1 Test All Endpoints
```bash
# Test backend health
curl -k https://************:5000/api/health

# Test CORS
curl -k -H "Origin: https://deeptrade.capitolchilax.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS https://************:5000/api/auth/login
```

#### 5.2 Test User Flows
1. **Registration Flow**
   - Sign up with new email
   - Check email verification
   - Verify email link works

2. **Authentication Flow**
   - Login with credentials
   - Test Google OAuth
   - Test 2FA if enabled

3. **API Integration**
   - Test trading endpoints
   - Test real-time data
   - Test payment flows

#### 5.3 Monitor and Debug
```bash
# Monitor backend logs
sudo journalctl -u deeptrade -f

# Monitor Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Check SSL certificate
curl -vI https://************:5000
```

---

## Troubleshooting Guide

### Common Issues and Solutions

1. **CORS Errors**
   - Check CORS origins in backend
   - Verify FRONTEND_URL environment variable
   - Check browser developer tools

2. **SSL Certificate Issues**
   - Verify certificate installation
   - Check certificate expiry
   - Test with `openssl s_client`

3. **502 Bad Gateway**
   - Check if Flask app is running
   - Verify Nginx proxy configuration
   - Check firewall settings

4. **Environment Variable Issues**
   - Verify .env files are loaded
   - Check environment variable names
   - Restart services after changes

---

## Security Checklist

- [ ] SSL/TLS certificate properly configured
- [ ] CORS origins restricted to known domains
- [ ] Security headers configured in Nginx
- [ ] Firewall configured (only ports 22, 80, 443 open)
- [ ] Database credentials secured
- [ ] API keys and secrets properly protected
- [ ] Regular security updates scheduled

---

## Next Steps After Completion
1. Monitor application performance
2. Set up automated backups
3. Configure monitoring and alerting
4. Implement CI/CD pipeline
5. Set up log aggregation
6. Configure domain-based SSL (recommended)
7. Set up database backups
8. Configure log rotation

---

## Notes
- Keep backup of current configuration
- Test each phase thoroughly before proceeding
- Monitor logs during deployment
- Have rollback plan ready
- Consider using a domain name for proper SSL certificates
- IP-based SSL certificates have limitations and browser warnings
