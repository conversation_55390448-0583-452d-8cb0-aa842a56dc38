"""Add admin models for administrative dashboard

Revision ID: 0482d3879ae2
Revises: 06cece6e9417
Create Date: 2025-07-15 16:18:17.932254

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0482d3879ae2'
down_revision = '06cece6e9417'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admin_user',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=80), nullable=False),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('is_super_admin', sa.<PERSON>(), nullable=False),
    sa.Column('is_active', sa.<PERSON>olean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['admin_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('admin_user', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_admin_user_username'), ['username'], unique=True)

    op.create_table('admin_action',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('admin_id', sa.Integer(), nullable=False),
    sa.Column('action_type', sa.String(length=50), nullable=False),
    sa.Column('target_type', sa.String(length=50), nullable=False),
    sa.Column('target_id', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('action_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.ForeignKeyConstraint(['admin_id'], ['admin_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('coupon_code',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('tier_level', sa.Integer(), nullable=False),
    sa.Column('expiration_date', sa.DateTime(), nullable=False),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('max_uses', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['admin_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('coupon_code', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_coupon_code_code'), ['code'], unique=True)

    op.create_table('coupon_usage',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('coupon_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('used_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['coupon_id'], ['coupon_code.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('user_2fa_email_codes', schema=None) as batch_op:
        batch_op.drop_index('idx_user_2fa_email_codes_expires_at')
        batch_op.drop_constraint('user_2fa_email_codes_ibfk_1', type_='foreignkey')
        batch_op.drop_index('idx_user_2fa_email_codes_user_id')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_2fa_email_codes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('user_2fa_email_codes_ibfk_1', 'users', ['user_id'], ['id'], ondelete='CASCADE')
        batch_op.create_index('idx_user_2fa_email_codes_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_user_2fa_email_codes_expires_at', ['expires_at'], unique=False)

    op.drop_table('coupon_usage')
    with op.batch_alter_table('coupon_code', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_coupon_code_code'))

    op.drop_table('coupon_code')
    op.drop_table('admin_action')
    with op.batch_alter_table('admin_user', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_admin_user_username'))

    op.drop_table('admin_user')
    # ### end Alembic commands ###
