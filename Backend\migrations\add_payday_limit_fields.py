"""
Database migration to add Saturday payday limit fields to user_tier_status table.
Run this script to add the new fields to existing database.
"""

import os
import sys

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from sqlalchemy import text

def add_payday_limit_fields():
    """Add payday limit fields to user_tier_status table."""
    app = create_app()
    
    with app.app_context():
        try:
            # Add new columns to user_tier_status table
            with db.engine.connect() as connection:
                # List of columns to add
                columns_to_add = [
                    ("next_payday_deadline", "DATETIME NULL"),
                    ("account_disabled", "BOOLEAN DEFAULT FALSE"),
                    ("disabled_at", "DATETIME NULL"),
                    ("payday_warning_sent", "BOOLEAN DEFAULT FALSE"),
                    ("email_warning_3_days_sent", "BOOLEAN DEFAULT FALSE"),
                    ("email_warning_2_days_sent", "BOOLEAN DEFAULT FALSE"),
                    ("email_warning_1_day_sent", "BOOLEAN DEFAULT FALSE"),
                    ("email_warning_3_days_sent_at", "DATETIME NULL"),
                    ("email_warning_2_days_sent_at", "DATETIME NULL"),
                    ("email_warning_1_day_sent_at", "DATETIME NULL")
                ]

                # Check existing columns first
                result = connection.execute(text("DESCRIBE user_tier_status"))
                existing_columns = [row[0] for row in result.fetchall()]

                added_count = 0
                for column_name, column_def in columns_to_add:
                    if column_name not in existing_columns:
                        try:
                            connection.execute(text(f"""
                                ALTER TABLE user_tier_status
                                ADD COLUMN {column_name} {column_def};
                            """))
                            print(f"Added column: {column_name}")
                            added_count += 1
                        except Exception as col_error:
                            print(f"Error adding column {column_name}: {str(col_error)}")
                    else:
                        print(f"Column {column_name} already exists, skipping")

                connection.commit()
                print(f"Successfully added {added_count} new columns to user_tier_status table")
            
            print("Successfully added payday limit fields to user_tier_status table")
            
            # Initialize payday deadlines for existing users
            from app.services.payday_service import PaydayService
            initialized_count = PaydayService.initialize_payday_deadlines()
            print(f"Initialized payday deadlines for {initialized_count} existing users")
            
        except Exception as e:
            print(f"Error adding payday limit fields: {str(e)}")
            db.session.rollback()

if __name__ == "__main__":
    add_payday_limit_fields()
