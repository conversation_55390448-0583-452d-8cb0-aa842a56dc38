{"root": ["../../src/app.tsx", "../../src/config.ts", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/api/auth.service.ts", "../../src/api/trading.service.ts", "../../src/components/deactivatedaccountalert.tsx", "../../src/components/forecastchart.tsx", "../../src/components/papertradinganalytics.tsx", "../../src/components/papertradingdashboard.tsx", "../../src/components/papertradinghelp.tsx", "../../src/components/papertradingreset.tsx", "../../src/components/solanawalletprovider.tsx", "../../src/components/theme-provider.tsx", "../../src/components/api/addapicredentialform.tsx", "../../src/components/api/apicredentialcard.tsx", "../../src/components/api/apicredentialslist.tsx", "../../src/components/api/index.ts", "../../src/components/auth/emailverificationrequired.tsx", "../../src/components/debug/mobilenavigationtest.tsx", "../../src/components/layout/responsivelayout.tsx", "../../src/components/mobile/mobileapicredentials.tsx", "../../src/components/mobile/mobiledashboard.tsx", "../../src/components/mobile/mobilenavigation.tsx", "../../src/components/mobile/mobilereferral.tsx", "../../src/components/mobile/mobilesettings.tsx", "../../src/components/mobile/mobilesidebar.tsx", "../../src/components/mobile/mobiletiersystem.tsx", "../../src/components/mobile/mobiletopbar.tsx", "../../src/components/mobile/mobiletradinginterface.tsx", "../../src/components/mobile/ui/mobilecard.tsx", "../../src/components/mobile/ui/mobileform.tsx", "../../src/components/mobile/ui/mobilelayout.tsx", "../../src/components/mobile/ui/mobilemodal.tsx", "../../src/components/modals/privacypolicymodal.tsx", "../../src/components/modals/termsofservicemodal.tsx", "../../src/components/modals/index.ts", "../../src/components/solana/navbarwalletbutton.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/confirmationmodal.tsx", "../../src/components/ui/flagicon.tsx", "../../src/components/ui/languageselector.tsx", "../../src/components/ui/legalmodal.tsx", "../../src/components/ui/passwordstrengthindicator.tsx", "../../src/components/ui/slider.tsx", "../../src/components/ui/solanabranding.tsx", "../../src/components/ui/solanalogo.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/ui/alert.tsx", "../../src/components/ui/avatar.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/slot.tsx", "../../src/components/ui/toast.tsx", "../../src/components/ui/toaster.tsx", "../../src/components/ui/use-toast.ts", "../../src/contexts/authcontext.tsx", "../../src/contexts/nftverificationcontext.tsx", "../../src/contexts/solanawalletprovider.tsx", "../../src/hooks/useautotradingnotifications.ts", "../../src/hooks/usenftverification.ts", "../../src/hooks/usepapertrading.ts", "../../src/hooks/useresponsivedesign.ts", "../../src/hooks/usescreensize.ts", "../../src/hooks/usesolanapayment.ts", "../../src/hooks/usetranslation.ts", "../../src/i18n/index.ts", "../../src/i18n/locales/de/common.ts", "../../src/i18n/locales/en/common.ts", "../../src/i18n/locales/es/common.ts", "../../src/i18n/locales/fr/common.ts", "../../src/i18n/locales/ja/common.ts", "../../src/i18n/locales/ko/common.ts", "../../src/i18n/locales/pt/common.ts", "../../src/i18n/locales/zh/common.ts", "../../src/i18n/utils/add-language.ts", "../../src/i18n/utils/translation-validator.ts", "../../src/layouts/authlayout.tsx", "../../src/layouts/dashboardlayout.tsx", "../../src/lib/utils.ts", "../../src/pages/accesssecurity.tsx", "../../src/pages/apicredentials.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/forgotpassword.tsx", "../../src/pages/help.tsx", "../../src/pages/login.tsx", "../../src/pages/oauthcallback.tsx", "../../src/pages/register.tsx", "../../src/pages/resetpassword.tsx", "../../src/pages/settings.tsx", "../../src/pages/signup.tsx", "../../src/pages/twofaresetrequest.tsx", "../../src/pages/verify2fa.tsx", "../../src/pages/verifyemail.tsx", "../../src/pages/verifyemailchange.tsx", "../../src/pages/redeem-coupon.tsx", "../../src/pages/referral.tsx", "../../src/pages/tier.tsx", "../../src/services/api.ts", "../../src/services/market.service.ts", "../../src/services/nft.service.ts", "../../src/services/papertradingapi.ts", "../../src/services/price.service.ts", "../../src/services/realtime.service.ts", "../../src/types/plotly.d.ts", "../../src/types/react-plotly.d.ts", "../../src/utils/clocksync.ts"], "version": "5.8.3"}