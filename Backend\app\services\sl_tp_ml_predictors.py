#!/usr/bin/env python3
"""
Independent ML Predictors for Stop Loss and Take Profit
Advanced AI system with specialized models for SL/TP optimization
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import logging
from typing import Dict, Tuple, Optional
from datetime import datetime, timedelta
import joblib
import os

class StopLossMLPredictor:
    """
    Independent ML model for predicting optimal stop loss levels
    Analyzes market volatility, support/resistance, and risk patterns
    """
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        self.accuracy = 0.0
        self.logger = logging.getLogger(__name__)
        
        # Model ensemble for better predictions
        self.models = {
            'rf': RandomForestRegressor(n_estimators=100, random_state=42),
            'gb': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'lr': LinearRegression()
        }
        self.model_weights = {'rf': 0.5, 'gb': 0.3, 'lr': 0.2}
        
    def extract_sl_features(self, market_data: pd.DataFrame, entry_price: float, signal: str) -> np.array:
        """
        Extract features specifically for stop loss prediction
        Focus on volatility, support/resistance, and risk indicators
        """
        try:
            if len(market_data) < 50:
                return None
                
            features = []
            
            # Current market state
            current_price = float(market_data['close'].iloc[-1])
            features.extend([current_price, entry_price])
            
            # Volatility indicators (critical for SL)
            returns = market_data['close'].pct_change().dropna()
            volatility_5 = returns.rolling(5).std().iloc[-1] * 100
            volatility_14 = returns.rolling(14).std().iloc[-1] * 100
            volatility_30 = returns.rolling(30).std().iloc[-1] * 100
            features.extend([volatility_5, volatility_14, volatility_30])
            
            # ATR for dynamic risk assessment
            high_low = market_data['high'] - market_data['low']
            high_close = abs(market_data['high'] - market_data['close'].shift(1))
            low_close = abs(market_data['low'] - market_data['close'].shift(1))
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr_14 = true_range.rolling(14).mean().iloc[-1]
            atr_30 = true_range.rolling(30).mean().iloc[-1]
            features.extend([atr_14, atr_30])
            
            # Support/Resistance levels
            recent_highs = market_data['high'].rolling(20).max().iloc[-1]
            recent_lows = market_data['low'].rolling(20).min().iloc[-1]
            support_distance = abs(current_price - recent_lows) / current_price * 100
            resistance_distance = abs(recent_highs - current_price) / current_price * 100
            features.extend([support_distance, resistance_distance])
            
            # Swing points (key levels for SL placement)
            swing_high_5 = market_data['high'].rolling(5).max().iloc[-1]
            swing_low_5 = market_data['low'].rolling(5).min().iloc[-1]
            swing_high_10 = market_data['high'].rolling(10).max().iloc[-1]
            swing_low_10 = market_data['low'].rolling(10).min().iloc[-1]
            features.extend([swing_high_5, swing_low_5, swing_high_10, swing_low_10])
            
            # Market momentum (affects SL placement)
            rsi = self._calculate_rsi(market_data['close'], 14)
            macd_line, macd_signal, _ = self._calculate_macd(market_data['close'])
            momentum = (current_price - market_data['close'].iloc[-10]) / market_data['close'].iloc[-10] * 100
            features.extend([rsi, macd_line, macd_signal, momentum])
            
            # Volume analysis (confirms breakouts/breakdowns)
            volume_avg = market_data['volume'].rolling(20).mean().iloc[-1]
            volume_current = market_data['volume'].iloc[-1]
            volume_ratio = volume_current / volume_avg if volume_avg > 0 else 1
            features.append(volume_ratio)
            
            # Time-based features
            hour = datetime.now().hour
            day_of_week = datetime.now().weekday()
            features.extend([hour, day_of_week])
            
            # Signal direction (affects SL calculation)
            signal_numeric = 1 if signal == 'BUY' else -1 if signal == 'SELL' else 0
            features.append(signal_numeric)
            
            # Market regime indicators
            price_change_1h = (current_price - market_data['close'].iloc[-2]) / market_data['close'].iloc[-2] * 100
            price_change_4h = (current_price - market_data['close'].iloc[-5]) / market_data['close'].iloc[-5] * 100
            price_change_24h = (current_price - market_data['close'].iloc[-25]) / market_data['close'].iloc[-25] * 100
            features.extend([price_change_1h, price_change_4h, price_change_24h])
            
            return np.array(features).reshape(1, -1)
            
        except Exception as e:
            self.logger.error(f"Error extracting SL features: {e}")
            return None
    
    def predict_optimal_sl(self, market_data: pd.DataFrame, entry_price: float, signal: str) -> Dict:
        """
        Predict optimal stop loss level using ML ensemble
        Returns SL price, confidence, and risk assessment
        """
        try:
            if not self.is_trained:
                self.logger.warning("SL ML model not trained, using fallback")
                return self._fallback_sl_calculation(market_data, entry_price, signal)
            
            features = self.extract_sl_features(market_data, entry_price, signal)
            if features is None:
                return self._fallback_sl_calculation(market_data, entry_price, signal)
            
            # Scale features
            features_scaled = self.scaler.transform(features)
            
            # Ensemble prediction
            predictions = []
            confidences = []
            
            for model_name, model in self.models.items():
                if hasattr(model, 'predict'):
                    pred = model.predict(features_scaled)[0]
                    predictions.append(pred)
                    
                    # Calculate confidence based on model type
                    if hasattr(model, 'predict_proba'):
                        conf = np.max(model.predict_proba(features_scaled)[0])
                    else:
                        # For regression models, use feature importance
                        conf = 0.8  # Default confidence
                    confidences.append(conf)
            
            # Weighted ensemble prediction
            if predictions:
                weights = [self.model_weights.get(name, 0.33) for name in self.models.keys()]
                sl_distance_pct = np.average(predictions, weights=weights)
                avg_confidence = np.average(confidences, weights=weights)
            else:
                return self._fallback_sl_calculation(market_data, entry_price, signal)
            
            # Convert percentage to actual price
            if signal == 'BUY':
                sl_price = entry_price * (1 - abs(sl_distance_pct) / 100)
            else:  # SELL
                sl_price = entry_price * (1 + abs(sl_distance_pct) / 100)
            
            # Apply safety bounds (0.3% to 2.0%)
            min_sl_distance = entry_price * 0.003  # 0.3%
            max_sl_distance = entry_price * 0.020  # 2.0%
            
            if signal == 'BUY':
                sl_price = max(sl_price, entry_price - max_sl_distance)
                sl_price = min(sl_price, entry_price - min_sl_distance)
            else:
                sl_price = min(sl_price, entry_price + max_sl_distance)
                sl_price = max(sl_price, entry_price + min_sl_distance)
            
            # Risk assessment
            actual_sl_distance = abs(entry_price - sl_price) / entry_price * 100
            if actual_sl_distance <= 0.5:
                risk_level = "TIGHT"
            elif actual_sl_distance <= 1.0:
                risk_level = "MODERATE"
            else:
                risk_level = "WIDE"
            
            return {
                'sl_price': round(sl_price, 2),
                'sl_distance_pct': round(actual_sl_distance, 3),
                'confidence': round(avg_confidence * 100, 1),
                'risk_level': risk_level,
                'method': 'ML_ENSEMBLE',
                'model_accuracy': round(self.accuracy * 100, 1)
            }
            
        except Exception as e:
            self.logger.error(f"Error predicting SL: {e}")
            return self._fallback_sl_calculation(market_data, entry_price, signal)
    
    def _fallback_sl_calculation(self, market_data: pd.DataFrame, entry_price: float, signal: str) -> Dict:
        """Fallback SL calculation when ML model fails"""
        try:
            # Simple ATR-based fallback
            high_low = market_data['high'] - market_data['low']
            high_close = abs(market_data['high'] - market_data['close'].shift(1))
            low_close = abs(market_data['low'] - market_data['close'].shift(1))
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(14).mean().iloc[-1]
            
            if signal == 'BUY':
                sl_price = entry_price - (atr * 1.5)
            else:
                sl_price = entry_price + (atr * 1.5)
            
            sl_distance_pct = abs(entry_price - sl_price) / entry_price * 100
            
            return {
                'sl_price': round(sl_price, 2),
                'sl_distance_pct': round(sl_distance_pct, 3),
                'confidence': 60.0,
                'risk_level': 'MODERATE',
                'method': 'ATR_FALLBACK',
                'model_accuracy': 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Fallback SL calculation failed: {e}")
            # Emergency fallback
            sl_distance = entry_price * 0.008  # 0.8%
            if signal == 'BUY':
                sl_price = entry_price - sl_distance
            else:
                sl_price = entry_price + sl_distance
                
            return {
                'sl_price': round(sl_price, 2),
                'sl_distance_pct': 0.8,
                'confidence': 50.0,
                'risk_level': 'MODERATE',
                'method': 'EMERGENCY_FALLBACK',
                'model_accuracy': 0.0
            }
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
        except:
            return 50.0
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[float, float, float]:
        """Calculate MACD indicator"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            macd_signal = macd_line.ewm(span=signal).mean()
            macd_histogram = macd_line - macd_signal
            
            return (
                macd_line.iloc[-1] if not pd.isna(macd_line.iloc[-1]) else 0.0,
                macd_signal.iloc[-1] if not pd.isna(macd_signal.iloc[-1]) else 0.0,
                macd_histogram.iloc[-1] if not pd.isna(macd_histogram.iloc[-1]) else 0.0
            )
        except:
            return 0.0, 0.0, 0.0


class TakeProfitMLPredictor:
    """
    Independent ML model for predicting optimal take profit levels
    Analyzes price targets, momentum, and profit optimization patterns
    """
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        self.accuracy = 0.0
        self.logger = logging.getLogger(__name__)
        
        # Model ensemble for better predictions
        self.models = {
            'rf': RandomForestRegressor(n_estimators=100, random_state=42),
            'gb': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'lr': LinearRegression()
        }
        self.model_weights = {'rf': 0.5, 'gb': 0.3, 'lr': 0.2}
    
    def extract_tp_features(self, market_data: pd.DataFrame, entry_price: float, signal: str, sl_price: float = None, chart_forecast: Dict = None) -> np.array:
        """
        Extract features specifically for take profit prediction
        Focus on momentum, resistance levels, profit potential, and CHART PREDICTION SYNC
        """
        try:
            if len(market_data) < 50:
                return None
                
            features = []
            
            # Current market state
            current_price = float(market_data['close'].iloc[-1])
            features.extend([current_price, entry_price])
            
            # Risk-reward context (if SL is known)
            if sl_price:
                risk_distance = abs(entry_price - sl_price) / entry_price * 100
                features.append(risk_distance)
            else:
                features.append(0.8)  # Default risk assumption

            # 🎯 CHART PREDICTION SYNC - NEW FEATURE
            if chart_forecast:
                # Extract chart prediction targets
                if signal == 'BUY':
                    chart_target = chart_forecast.get('highest_price', entry_price * 1.02)
                else:
                    chart_target = chart_forecast.get('lowest_price', entry_price * 0.98)

                chart_target_distance = abs(chart_target - entry_price) / entry_price * 100
                chart_confidence = chart_forecast.get('confidence', 0.5)  # Default 50%

                # Chart prediction alignment score
                if signal == 'BUY':
                    chart_alignment = 1.0 if chart_target > entry_price else -0.5
                else:
                    chart_alignment = 1.0 if chart_target < entry_price else -0.5

                features.extend([chart_target_distance, chart_confidence, chart_alignment])

                self.logger.info(f"🎯 Chart sync: Target=${chart_target:.2f}, Distance={chart_target_distance:.2f}%, Confidence={chart_confidence:.1f}")
            else:
                # No chart data available
                features.extend([2.0, 0.5, 0.0])  # Default: 2% target, 50% confidence, neutral alignment
            
            # Momentum indicators (critical for TP)
            returns = market_data['close'].pct_change().dropna()
            momentum_5 = returns.rolling(5).mean().iloc[-1] * 100
            momentum_14 = returns.rolling(14).mean().iloc[-1] * 100
            momentum_strength = abs(momentum_14)
            features.extend([momentum_5, momentum_14, momentum_strength])
            
            # Trend strength
            sma_20 = market_data['close'].rolling(20).mean().iloc[-1]
            sma_50 = market_data['close'].rolling(50).mean().iloc[-1]
            trend_strength = (sma_20 - sma_50) / sma_50 * 100
            price_vs_sma20 = (current_price - sma_20) / sma_20 * 100
            features.extend([trend_strength, price_vs_sma20])
            
            # Resistance/Target levels
            recent_highs = market_data['high'].rolling(20).max().iloc[-1]
            recent_lows = market_data['low'].rolling(20).min().iloc[-1]
            resistance_distance = abs(recent_highs - current_price) / current_price * 100
            support_distance = abs(current_price - recent_lows) / current_price * 100
            features.extend([resistance_distance, support_distance])
            
            # Fibonacci-like levels
            price_range = recent_highs - recent_lows
            fib_38 = recent_lows + (price_range * 0.382)
            fib_62 = recent_lows + (price_range * 0.618)
            fib_100 = recent_lows + price_range
            
            fib_38_distance = abs(fib_38 - current_price) / current_price * 100
            fib_62_distance = abs(fib_62 - current_price) / current_price * 100
            fib_100_distance = abs(fib_100 - current_price) / current_price * 100
            features.extend([fib_38_distance, fib_62_distance, fib_100_distance])
            
            # Volume profile (confirms breakouts)
            volume_avg = market_data['volume'].rolling(20).mean().iloc[-1]
            volume_current = market_data['volume'].iloc[-1]
            volume_ratio = volume_current / volume_avg if volume_avg > 0 else 1
            volume_trend = market_data['volume'].rolling(5).mean().iloc[-1] / market_data['volume'].rolling(20).mean().iloc[-1]
            features.extend([volume_ratio, volume_trend])
            
            # Technical indicators
            rsi = self._calculate_rsi(market_data['close'], 14)
            macd_line, macd_signal, macd_hist = self._calculate_macd(market_data['close'])
            features.extend([rsi, macd_line, macd_signal, macd_hist])
            
            # Bollinger Bands (volatility context)
            bb_middle = market_data['close'].rolling(20).mean().iloc[-1]
            bb_std = market_data['close'].rolling(20).std().iloc[-1]
            bb_upper = bb_middle + (bb_std * 2)
            bb_lower = bb_middle - (bb_std * 2)
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower) * 100
            features.append(bb_position)
            
            # Market timing
            hour = datetime.now().hour
            day_of_week = datetime.now().weekday()
            features.extend([hour, day_of_week])
            
            # Signal direction
            signal_numeric = 1 if signal == 'BUY' else -1 if signal == 'SELL' else 0
            features.append(signal_numeric)
            
            # Price velocity
            price_change_1h = (current_price - market_data['close'].iloc[-2]) / market_data['close'].iloc[-2] * 100
            price_change_4h = (current_price - market_data['close'].iloc[-5]) / market_data['close'].iloc[-5] * 100
            price_velocity = price_change_1h - price_change_4h  # Acceleration
            features.extend([price_change_1h, price_change_4h, price_velocity])
            
            return np.array(features).reshape(1, -1)
            
        except Exception as e:
            self.logger.error(f"Error extracting TP features: {e}")
            return None
    
    def predict_optimal_tp(self, market_data: pd.DataFrame, entry_price: float, signal: str, sl_price: float = None, chart_forecast: Dict = None) -> Dict:
        """
        Predict optimal take profit level using ML ensemble
        Returns TP price, confidence, and profit potential
        """
        try:
            if not self.is_trained:
                self.logger.warning("TP ML model not trained, using fallback")
                return self._fallback_tp_calculation(market_data, entry_price, signal, sl_price)
            
            features = self.extract_tp_features(market_data, entry_price, signal, sl_price, chart_forecast)
            if features is None:
                return self._fallback_tp_calculation(market_data, entry_price, signal, sl_price)
            
            # Scale features
            features_scaled = self.scaler.transform(features)
            
            # Ensemble prediction
            predictions = []
            confidences = []
            
            for model_name, model in self.models.items():
                if hasattr(model, 'predict'):
                    pred = model.predict(features_scaled)[0]
                    predictions.append(pred)
                    
                    # Calculate confidence
                    if hasattr(model, 'predict_proba'):
                        conf = np.max(model.predict_proba(features_scaled)[0])
                    else:
                        conf = 0.8  # Default confidence
                    confidences.append(conf)
            
            # Weighted ensemble prediction
            if predictions:
                weights = [self.model_weights.get(name, 0.33) for name in self.models.keys()]
                tp_distance_pct = np.average(predictions, weights=weights)
                avg_confidence = np.average(confidences, weights=weights)
            else:
                return self._fallback_tp_calculation(market_data, entry_price, signal, sl_price)
            
            # Convert percentage to actual price
            if signal == 'BUY':
                tp_price = entry_price * (1 + abs(tp_distance_pct) / 100)
            else:  # SELL
                tp_price = entry_price * (1 - abs(tp_distance_pct) / 100)
            
            # Apply safety bounds (0.8% to 5.0%)
            min_tp_distance = entry_price * 0.008  # 0.8%
            max_tp_distance = entry_price * 0.050  # 5.0%
            
            if signal == 'BUY':
                tp_price = max(tp_price, entry_price + min_tp_distance)
                tp_price = min(tp_price, entry_price + max_tp_distance)
            else:
                tp_price = min(tp_price, entry_price - min_tp_distance)
                tp_price = max(tp_price, entry_price - max_tp_distance)
            
            # Risk-reward ratio calculation
            if sl_price:
                risk = abs(entry_price - sl_price)
                reward = abs(tp_price - entry_price)
                risk_reward_ratio = reward / risk if risk > 0 else 0
            else:
                risk_reward_ratio = 2.0  # Default assumption
            
            # Profit potential assessment
            actual_tp_distance = abs(tp_price - entry_price) / entry_price * 100
            if actual_tp_distance >= 3.0:
                profit_potential = "HIGH"
            elif actual_tp_distance >= 1.5:
                profit_potential = "MODERATE"
            else:
                profit_potential = "LOW"
            
            return {
                'tp_price': round(tp_price, 2),
                'tp_distance_pct': round(actual_tp_distance, 3),
                'confidence': round(avg_confidence * 100, 1),
                'risk_reward_ratio': round(risk_reward_ratio, 2),
                'profit_potential': profit_potential,
                'method': 'ML_ENSEMBLE',
                'model_accuracy': round(self.accuracy * 100, 1)
            }
            
        except Exception as e:
            self.logger.error(f"Error predicting TP: {e}")
            return self._fallback_tp_calculation(market_data, entry_price, signal, sl_price)
    
    def _fallback_tp_calculation(self, market_data: pd.DataFrame, entry_price: float, signal: str, sl_price: float = None) -> Dict:
        """Fallback TP calculation when ML model fails"""
        try:
            # Risk-reward based fallback
            if sl_price:
                risk_distance = abs(entry_price - sl_price)
                reward_distance = risk_distance * 2.5  # 1:2.5 ratio
                
                if signal == 'BUY':
                    tp_price = entry_price + reward_distance
                else:
                    tp_price = entry_price - reward_distance
            else:
                # Default percentage-based
                tp_distance = entry_price * 0.015  # 1.5%
                if signal == 'BUY':
                    tp_price = entry_price + tp_distance
                else:
                    tp_price = entry_price - tp_distance
            
            tp_distance_pct = abs(tp_price - entry_price) / entry_price * 100
            risk_reward_ratio = 2.5 if sl_price else 2.0
            
            return {
                'tp_price': round(tp_price, 2),
                'tp_distance_pct': round(tp_distance_pct, 3),
                'confidence': 65.0,
                'risk_reward_ratio': risk_reward_ratio,
                'profit_potential': 'MODERATE',
                'method': 'RISK_REWARD_FALLBACK',
                'model_accuracy': 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Fallback TP calculation failed: {e}")
            # Emergency fallback
            tp_distance = entry_price * 0.012  # 1.2%
            if signal == 'BUY':
                tp_price = entry_price + tp_distance
            else:
                tp_price = entry_price - tp_distance
                
            return {
                'tp_price': round(tp_price, 2),
                'tp_distance_pct': 1.2,
                'confidence': 50.0,
                'risk_reward_ratio': 1.5,
                'profit_potential': 'LOW',
                'method': 'EMERGENCY_FALLBACK',
                'model_accuracy': 0.0
            }
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
        except:
            return 50.0
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[float, float, float]:
        """Calculate MACD indicator"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            macd_signal = macd_line.ewm(span=signal).mean()
            macd_histogram = macd_line - macd_signal
            
            return (
                macd_line.iloc[-1] if not pd.isna(macd_line.iloc[-1]) else 0.0,
                macd_signal.iloc[-1] if not pd.isna(macd_signal.iloc[-1]) else 0.0,
                macd_histogram.iloc[-1] if not pd.isna(macd_histogram.iloc[-1]) else 0.0
            )
        except:
            return 0.0, 0.0, 0.0


class SLTPMLManager:
    """
    Manager class for coordinating SL and TP ML predictions
    Ensures consistency and optimal risk-reward ratios
    """
    
    def __init__(self):
        self.sl_predictor = StopLossMLPredictor()
        self.tp_predictor = TakeProfitMLPredictor()
        self.logger = logging.getLogger(__name__)
    
    def get_optimal_sl_tp(self, market_data: pd.DataFrame, entry_price: float, signal: str) -> Dict:
        """
        Get coordinated SL and TP predictions with optimal risk-reward and CHART SYNC
        """
        try:
            # 🎯 Fetch chart forecast for TP sync
            chart_forecast = self._get_chart_forecast()

            # Get SL prediction first
            sl_result = self.sl_predictor.predict_optimal_sl(market_data, entry_price, signal)

            # Get TP prediction with SL context and CHART SYNC
            tp_result = self.tp_predictor.predict_optimal_tp(
                market_data, entry_price, signal, sl_result['sl_price'], chart_forecast
            )
            
            # Ensure minimum risk-reward ratio of 1:2
            risk = abs(entry_price - sl_result['sl_price'])
            reward = abs(tp_result['tp_price'] - entry_price)
            current_rr = reward / risk if risk > 0 else 0
            
            if current_rr < 2.0:
                # Adjust TP to maintain 1:2 ratio
                min_reward = risk * 2.0
                if signal == 'BUY':
                    tp_result['tp_price'] = entry_price + min_reward
                else:
                    tp_result['tp_price'] = entry_price - min_reward
                
                tp_result['tp_distance_pct'] = abs(tp_result['tp_price'] - entry_price) / entry_price * 100
                tp_result['risk_reward_ratio'] = 2.0
                tp_result['method'] += '_RR_ADJUSTED'
            
            # Combined confidence score
            combined_confidence = (sl_result['confidence'] + tp_result['confidence']) / 2
            
            return {
                'sl_result': sl_result,
                'tp_result': tp_result,
                'combined_confidence': round(combined_confidence, 1),
                'final_risk_reward': round(tp_result['risk_reward_ratio'], 2),
                'system_status': 'OPTIMAL' if combined_confidence >= 75 else 'MODERATE'
            }
            
        except Exception as e:
            self.logger.error(f"Error getting optimal SL/TP: {e}")
            return None

    def _get_chart_forecast(self) -> Dict:
        """
        Fetch current chart forecast for TP synchronization
        """
        try:
            from app.services.market_data import ml_service

            # Get latest forecast for BTC/USDT 1h
            forecast = ml_service.generate_ensemble_forecast(
                symbol='BTCUSDT',
                timeframe='1h',
                future_hours=24  # Next 24 hours for TP planning
            )

            if forecast and not forecast.get('is_mock', False):
                self.logger.info(f"📊 Chart forecast synced: High=${forecast.get('highest_price', 0):.2f}, Low=${forecast.get('lowest_price', 0):.2f}")
                return forecast
            else:
                self.logger.warning("⚠️ Chart forecast not available or is mock data")
                return None

        except Exception as e:
            self.logger.error(f"Error fetching chart forecast: {e}")
            return None
