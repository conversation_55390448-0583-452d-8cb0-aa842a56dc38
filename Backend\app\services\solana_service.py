"""
Solana Service
Handles USDT payments on Solana blockchain for profit sharing and membership fees.
Integrates with the treasury wallet configured via SOLANA_TREASURY_WALLET environment variable.
"""

import json
import requests
import os
from decimal import Decimal
from datetime import datetime
from typing import Optional, Dict, Any
from flask import current_app
from app import db
from app.models.solana_payment import SolanaPayment, SolanaPaymentStatus


class SolanaService:
    """Service for interacting with Solana blockchain for USDT payments."""

    # Solana mainnet USDT token mint address
    USDT_MINT = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"

    def __init__(self):
        try:
            self.rpc_endpoint = current_app.config.get('SOLANA_RPC_ENDPOINT', 'https://api.mainnet-beta.solana.com')
            self.network = current_app.config.get('SOLANA_NETWORK', 'mainnet')
            self.treasury_wallet = current_app.config.get('SOLANA_TREASURY_WALLET')
            current_app.logger.info(f"SolanaService initialized with treasury wallet: {self.treasury_wallet}")
        except RuntimeError:
            # Fallback when no app context is available
            self.rpc_endpoint = 'https://api.mainnet-beta.solana.com'
            self.network = 'mainnet'
            self.treasury_wallet = os.getenv('SOLANA_TREASURY_WALLET')
            print(f"SolanaService fallback - treasury wallet: {self.treasury_wallet}")

        # Validate treasury wallet is configured
        if not self.treasury_wallet:
            error_msg = "SOLANA_TREASURY_WALLET environment variable is required but not set. Please check your backend/.env file."
            try:
                current_app.logger.error(error_msg)
            except RuntimeError:
                print(f"ERROR: {error_msg}")
            raise ValueError(error_msg)
        
    def _make_rpc_request(self, method: str, params: list) -> Dict[Any, Any]:
        """Make RPC request to Solana node."""
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(self.rpc_endpoint, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"Solana RPC request failed: {str(e)}")
            raise Exception(f"Failed to communicate with Solana network: {str(e)}")
    
    def get_transaction_details(self, signature: str) -> Optional[Dict[Any, Any]]:
        """Get transaction details by signature."""
        try:
            response = self._make_rpc_request("getTransaction", [
                signature,
                {
                    "encoding": "json",
                    "maxSupportedTransactionVersion": 0
                }
            ])
            
            if response.get("result"):
                return response["result"]
            else:
                current_app.logger.warning(f"Transaction not found: {signature}")
                return None
                
        except Exception as e:
            current_app.logger.error(f"Error getting transaction details: {str(e)}")
            return None
    
    def verify_usdt_payment(self, signature: str, expected_amount: Decimal, expected_recipient: str) -> Dict[str, Any]:
        """
        Verify USDT payment transaction.
        
        Args:
            signature: Transaction signature
            expected_amount: Expected USDT amount (in USDT units, not lamports)
            expected_recipient: Expected recipient wallet address
            
        Returns:
            Dict with verification results
        """
        try:
            tx_details = self.get_transaction_details(signature)
            
            if not tx_details:
                return {
                    'verified': False,
                    'error': 'Transaction not found'
                }
            
            # Check if transaction was successful
            if tx_details.get('meta', {}).get('err'):
                return {
                    'verified': False,
                    'error': 'Transaction failed on blockchain'
                }
            
            # Parse transaction for USDT transfer
            verification_result = self._parse_usdt_transfer(tx_details, expected_amount, expected_recipient)
            
            # Add additional transaction info
            verification_result.update({
                'block_time': tx_details.get('blockTime'),
                'slot': tx_details.get('slot'),
                'confirmations': self._get_confirmation_count(signature)
            })
            
            return verification_result
            
        except Exception as e:
            current_app.logger.error(f"Error verifying USDT payment: {str(e)}")
            return {
                'verified': False,
                'error': f'Verification failed: {str(e)}'
            }
    
    def _parse_usdt_transfer(self, tx_details: Dict, expected_amount: Decimal, expected_recipient: str) -> Dict[str, Any]:
        """Parse transaction details to verify USDT transfer."""
        try:
            # Get pre and post token balances
            pre_balances = tx_details.get('meta', {}).get('preTokenBalances', [])
            post_balances = tx_details.get('meta', {}).get('postTokenBalances', [])
            
            # Find USDT token transfers
            usdt_transfers = []
            
            for pre_balance in pre_balances:
                if pre_balance.get('mint') == self.USDT_MINT:
                    account_index = pre_balance.get('accountIndex')
                    pre_amount = Decimal(pre_balance.get('uiTokenAmount', {}).get('uiAmount', 0))
                    
                    # Find corresponding post balance
                    post_balance = next(
                        (pb for pb in post_balances 
                         if pb.get('accountIndex') == account_index and pb.get('mint') == self.USDT_MINT),
                        None
                    )
                    
                    if post_balance:
                        post_amount = Decimal(post_balance.get('uiTokenAmount', {}).get('uiAmount', 0))
                        transfer_amount = post_amount - pre_amount
                        
                        if transfer_amount != 0:
                            # Get account address
                            accounts = tx_details.get('transaction', {}).get('message', {}).get('accountKeys', [])
                            if account_index < len(accounts):
                                account_address = accounts[account_index]
                                usdt_transfers.append({
                                    'account': account_address,
                                    'amount': transfer_amount
                                })
            
            # Verify the transfer
            for transfer in usdt_transfers:
                if (transfer['account'] == expected_recipient and 
                    abs(transfer['amount'] - expected_amount) < Decimal('0.01')):  # Allow small rounding differences
                    
                    return {
                        'verified': True,
                        'amount': float(transfer['amount']),
                        'recipient': transfer['account'],
                        'sender': self._get_sender_from_transaction(tx_details)
                    }
            
            return {
                'verified': False,
                'error': f'USDT transfer not found or amount mismatch. Expected: {expected_amount}, Found transfers: {usdt_transfers}'
            }
            
        except Exception as e:
            current_app.logger.error(f"Error parsing USDT transfer: {str(e)}")
            return {
                'verified': False,
                'error': f'Failed to parse transaction: {str(e)}'
            }
    
    def _get_sender_from_transaction(self, tx_details: Dict) -> Optional[str]:
        """Extract sender address from transaction."""
        try:
            accounts = tx_details.get('transaction', {}).get('message', {}).get('accountKeys', [])
            if accounts:
                return accounts[0]  # First account is usually the fee payer/sender
        except Exception:
            pass
        return None
    
    def _get_confirmation_count(self, signature: str) -> int:
        """Get confirmation count for transaction."""
        try:
            response = self._make_rpc_request("getSignatureStatuses", [[signature]])
            
            if response.get("result", {}).get("value"):
                status = response["result"]["value"][0]
                if status and status.get("confirmationStatus"):
                    # Map confirmation status to count
                    status_map = {
                        "processed": 1,
                        "confirmed": 12,
                        "finalized": 32
                    }
                    return status_map.get(status["confirmationStatus"], 0)
            
            return 0
            
        except Exception as e:
            current_app.logger.error(f"Error getting confirmation count: {str(e)}")
            return 0
    
    def monitor_payment(self, payment_id: str) -> Dict[str, Any]:
        """Monitor a Solana payment for confirmation."""
        payment = SolanaPayment.query.get(payment_id)
        
        if not payment:
            return {'error': 'Payment not found'}
        
        if not payment.transaction_signature:
            return {'error': 'No transaction signature available'}
        
        try:
            # Verify the payment
            verification_result = self.verify_usdt_payment(
                payment.transaction_signature,
                payment.amount,
                payment.to_address
            )
            
            if verification_result['verified']:
                # Update payment with blockchain details
                payment.update_confirmations(verification_result.get('confirmations', 0))
                
                if verification_result.get('block_time'):
                    payment.block_time = datetime.fromtimestamp(verification_result['block_time'])
                
                if verification_result.get('slot'):
                    payment.slot = verification_result['slot']
                
                if verification_result.get('sender'):
                    payment.from_address = verification_result['sender']
                
                db.session.commit()
                
                return {
                    'status': 'verified',
                    'payment': payment.to_dict(),
                    'verification': verification_result
                }
            else:
                # Payment verification failed
                if payment.retry_count < 3:
                    payment.increment_retry()
                else:
                    payment.mark_as_failed(verification_result.get('error'))
                
                db.session.commit()
                
                return {
                    'status': 'failed',
                    'error': verification_result.get('error'),
                    'payment': payment.to_dict()
                }
                
        except Exception as e:
            current_app.logger.error(f"Error monitoring payment {payment_id}: {str(e)}")
            payment.mark_as_failed(str(e))
            db.session.commit()
            
            return {
                'status': 'error',
                'error': str(e),
                'payment': payment.to_dict()
            }
    
    def create_payment_request(self, user_id: str, amount: Decimal, payment_type: str, **kwargs) -> Dict[str, Any]:
        """Create a payment request for Solana USDT payment."""
        try:
            from app.models.solana_payment import SolanaPaymentType

            current_app.logger.info(f"Creating payment request for user {user_id}, type: {payment_type}, amount: {amount}")

            # Create payment record
            payment = SolanaPayment(
                user_id=user_id,
                payment_type=SolanaPaymentType(payment_type),
                amount=amount,
                to_address=self.treasury_wallet,
                **kwargs
            )

            current_app.logger.info(f"Payment object created: {payment.id}")
            
            current_app.logger.info("Adding payment to database session")
            db.session.add(payment)
            current_app.logger.info("Committing payment to database")
            db.session.commit()
            current_app.logger.info(f"Payment {payment.id} successfully saved to database")
            
            # Generate payment instructions
            payment_instructions = {
                'payment_id': payment.id,
                'recipient_address': self.treasury_wallet,
                'token_mint': self.USDT_MINT,
                'amount': float(amount),
                'network': self.network,
                'instructions': {
                    'step1': 'Connect your Solana wallet (Phantom, Solflare, etc.)',
                    'step2': f'Send exactly {amount} USDT to address: {self.treasury_wallet}',
                    'step3': 'Copy the transaction signature and submit it for verification',
                    'step4': 'Wait for blockchain confirmation (usually 1-2 minutes)'
                },
                'important_notes': [
                    'Send only USDT tokens, not SOL or other tokens',
                    'Ensure you have enough SOL for transaction fees (~0.001 SOL)',
                    'Double-check the recipient address before sending',
                    'Keep your transaction signature for verification'
                ]
            }
            
            return {
                'success': True,
                'payment': payment.to_dict(),
                'payment_instructions': payment_instructions
            }
            
        except Exception as e:
            current_app.logger.error(f"Error creating payment request: {str(e)}")
            current_app.logger.error(f"Exception type: {type(e)}")
            import traceback
            current_app.logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def submit_payment_signature(self, payment_id: str, signature: str, sender_address: Optional[str] = None) -> Dict[str, Any]:
        """Submit transaction signature for payment verification."""
        payment = SolanaPayment.query.get(payment_id)
        
        if not payment:
            return {'success': False, 'error': 'Payment not found'}
        
        if payment.status != SolanaPaymentStatus.PENDING:
            return {'success': False, 'error': 'Payment is not in pending status'}
        
        try:
            # Update payment with transaction details
            payment.update_transaction_details(signature, sender_address)
            db.session.commit()
            
            # Start monitoring the payment
            monitoring_result = self.monitor_payment(payment_id)
            
            return {
                'success': True,
                'message': 'Transaction signature submitted successfully',
                'payment': payment.to_dict(),
                'monitoring_result': monitoring_result
            }
            
        except Exception as e:
            current_app.logger.error(f"Error submitting payment signature: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
