#!/usr/bin/env python3
"""
DeepTrade Application Entry Point
"""
import os
import sys
import traceback
from dotenv import load_dotenv
from flask_migrate import upgrade
from app import create_app, db

# Load environment variables from backend/.env file explicitly
backend_env_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(backend_env_path)

# Configure logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Create application instance
try:
    logger.info("Creating Flask application...")
    app = create_app(os.getenv('FLASK_ENV', 'development'))
    logger.info("Flask application created successfully")
except Exception as e:
    logger.error(f"Failed to create Flask application: {e}")
    logger.error(traceback.format_exc())
    sys.exit(1)

# Configure application logging
app.logger.setLevel(logging.INFO)
app.logger.info("Application logging configured")

def create_tables():
    """Create database tables and run migrations."""
    with app.app_context():
        try:
            app.logger.info("Running database migrations...")
            upgrade()
            app.logger.info("Database migrations completed successfully")
        except Exception as e:
            app.logger.error(f"Database migration failed: {e}")
            app.logger.error(traceback.format_exc())
            
            try:
                app.logger.info("Attempting to create tables directly...")
                db.create_all()
                app.logger.info("Database tables created successfully")
            except Exception as e2:
                app.logger.error(f"Failed to create database tables: {e2}")
                app.logger.error(traceback.format_exc())
                raise

# Initialize database tables
try:
    app.logger.info("Initializing database tables...")
    create_tables()
    app.logger.info("Database initialization completed")
except Exception as e:
    app.logger.error(f"Failed to initialize database: {e}")
    app.logger.error(traceback.format_exc())
    sys.exit(1)

@app.shell_context_processor
def make_shell_context():
    """Make shell context for flask shell command."""
    from app.models.user import User, User2FABackupCode
    from app.models.subscription import Subscription
    from app.models.payment import Payment
    from app.models.trade import Trade, TradingSession
    from app.models.fee_calculation import FeeCalculation
    from app.models.security_log import LoginAttempt, APICredential
    
    return {
        'db': db,
        'User': User,
        'User2FABackupCode': User2FABackupCode,
        'Subscription': Subscription,
        'Payment': Payment,
        'Trade': Trade,
        'TradingSession': TradingSession,
        'FeeCalculation': FeeCalculation,
        'LoginAttempt': LoginAttempt,
        'APICredential': APICredential
    }

@app.cli.command()
def init_db():
    """Initialize the database."""
    db.create_all()
    print("Database initialized!")

@app.cli.command()
def create_admin():
    """Create an admin user."""
    from app.models.user import User
    from app.models.subscription import Subscription, SubscriptionTier
    
    email = input("Enter admin email: ")
    name = input("Enter admin name: ")
    google_id = input("Enter Google ID (for OAuth): ")
    
    # Check if user already exists
    existing_user = User.query.filter_by(email=email).first()
    if existing_user:
        print(f"User with email {email} already exists!")
        return
    
    # Create admin user
    admin_user = User(
        google_id=google_id,
        email=email,
        full_name=name
    )
    
    db.session.add(admin_user)
    db.session.flush()
    
    # Create Tier 2 subscription for admin
    subscription = Subscription(admin_user.id, SubscriptionTier.TIER_2)
    db.session.add(subscription)
    
    db.session.commit()
    
    print(f"Admin user {email} created successfully!")

@app.cli.command()
def setup_dev_data():
    """Setup development data."""
    from app.models.user import User
    from app.models.subscription import Subscription, SubscriptionTier
    from app.models.security_log import APICredential, ExchangeType
    
    # Create test users
    test_users = [
        {
            'google_id': '123456789',
            'email': '<EMAIL>',
            'full_name': 'Test User 1',
            'tier': SubscriptionTier.TIER_1
        },
        {
            'google_id': '987654321', 
            'email': '<EMAIL>',
            'full_name': 'Test User 2',
            'tier': SubscriptionTier.TIER_2
        }
    ]
    
    for user_data in test_users:
        # Check if user exists
        existing = User.query.filter_by(email=user_data['email']).first()
        if existing:
            continue
            
        # Create user
        user = User(
            google_id=user_data['google_id'],
            email=user_data['email'],
            full_name=user_data['full_name']
        )
        db.session.add(user)
        db.session.flush()
        
        # Create subscription
        subscription = Subscription(user.id, user_data['tier'])
        db.session.add(subscription)
        
        # Add sample API credentials for tier 2 user
        if user_data['tier'] == SubscriptionTier.TIER_2:
            api_cred = APICredential(
                user_id=user.id,
                exchange=ExchangeType.BINANCE,
                api_key='sample_api_key',
                secret_key='sample_secret_key'
            )
            db.session.add(api_cred)
    
    db.session.commit()
    print("Development data created successfully!")

if __name__ == '__main__':
    try:
        host = os.getenv('HOST', '127.0.0.1')
        port = int(os.getenv('PORT', 5000))
        debug_mode = os.getenv('FLASK_DEBUG', 'true').lower() == 'true'

        app.logger.info("Starting Flask development server...")

        # Print server URL information (always visible)
        print(f" * Running on http://{host}:{port}/")
        print(f" * Debug mode: {'on' if debug_mode else 'off'}")
        if debug_mode:
            print(" * Restarting with reloader")

        # Run the application
        app.run(
            host=host,
            port=port,
            debug=debug_mode,
            use_reloader=True
        )
    except Exception as e:
        app.logger.error(f"Failed to start Flask application: {e}")
        app.logger.error(traceback.format_exc())
        sys.exit(1)