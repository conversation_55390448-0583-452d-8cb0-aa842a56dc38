"""
Scheduled task for managing Saturday payday limits and account enforcement.
This script should be run as a cron job every hour or daily.
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app import create_app
from app.services.payday_service import PaydayService
from app.auth.two_factor import TwoFactorAuth

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('payday_scheduler.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def run_payday_tasks():
    """Run all payday-related tasks."""
    logger.info("Starting payday scheduler tasks...")

    app = create_app()

    with app.app_context():
        try:
            # Run daily payday tasks
            results = PaydayService.run_daily_payday_tasks()

            # Run security cleanup tasks
            cleanup_results = run_security_cleanup()
            results.update(cleanup_results)

            logger.info(f"Payday tasks completed successfully:")
            logger.info(f"  - Initialized deadlines: {results['initialized_deadlines']}")
            logger.info(f"  - Email warnings sent: {results['email_warnings_sent']}")
            logger.info(f"  - In-app warnings sent: {results['warnings_sent']}")
            logger.info(f"  - Accounts disabled: {results['accounts_disabled']}")
            logger.info(f"  - Expired 2FA codes cleaned: {results.get('expired_2fa_codes_cleaned', 0)}")

            return results

        except Exception as e:
            logger.error(f"Error running payday tasks: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None

def run_initialization_only():
    """Run only the initialization task for new deployments."""
    logger.info("Running payday system initialization...")
    
    app = create_app()
    
    with app.app_context():
        try:
            initialized_count = PaydayService.initialize_payday_deadlines()
            logger.info(f"Initialized payday deadlines for {initialized_count} users")
            return initialized_count
            
        except Exception as e:
            logger.error(f"Error initializing payday system: {str(e)}")
            return 0

def run_email_warnings_only():
    """Run only the email warning notifications."""
    logger.info("Running payday email warning notifications...")

    app = create_app()

    with app.app_context():
        try:
            email_warnings_sent = PaydayService.send_email_warnings()
            logger.info(f"Sent email warnings to {email_warnings_sent} users")
            return email_warnings_sent

        except Exception as e:
            logger.error(f"Error sending email warnings: {str(e)}")
            return 0

def run_warnings_only():
    """Run only the in-app warning notifications."""
    logger.info("Running payday in-app warning notifications...")

    app = create_app()

    with app.app_context():
        try:
            warnings_sent = PaydayService.send_payday_warnings()
            logger.info(f"Sent in-app warnings to {warnings_sent} users")
            return warnings_sent

        except Exception as e:
            logger.error(f"Error sending in-app warnings: {str(e)}")
            return 0

def run_enforcement_only():
    """Run only the account enforcement."""
    logger.info("Running payday enforcement...")
    
    app = create_app()
    
    with app.app_context():
        try:
            accounts_disabled = PaydayService.enforce_payday_limits()
            logger.info(f"Disabled {accounts_disabled} accounts for unpaid fees")
            return accounts_disabled
            
        except Exception as e:
            logger.error(f"Error enforcing payday limits: {str(e)}")
            return 0

def run_security_cleanup():
    """Run security-related cleanup tasks."""
    logger.info("Running security cleanup tasks...")

    try:
        # Clean up expired 2FA email codes
        expired_codes_cleaned = TwoFactorAuth.cleanup_expired_email_codes()
        logger.info(f"Cleaned up {expired_codes_cleaned} expired 2FA email codes")

        return {
            'expired_2fa_codes_cleaned': expired_codes_cleaned
        }

    except Exception as e:
        logger.error(f"Error running security cleanup: {str(e)}")
        return {
            'expired_2fa_codes_cleaned': 0
        }

def get_status():
    """Get current payday system status."""
    logger.info("Getting payday system status...")
    
    app = create_app()
    
    with app.app_context():
        try:
            outstanding_debt = PaydayService.get_users_with_outstanding_debt()
            disabled_accounts = PaydayService.get_disabled_accounts()
            
            logger.info(f"Payday system status:")
            logger.info(f"  - Users with outstanding debt: {len(outstanding_debt)}")
            logger.info(f"  - Disabled accounts: {len(disabled_accounts)}")
            
            return {
                'users_with_debt': len(outstanding_debt),
                'disabled_accounts': len(disabled_accounts),
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting payday status: {str(e)}")
            return None

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='DeepTrade Payday Scheduler')
    parser.add_argument('--task', choices=['all', 'init', 'email-warnings', 'warnings', 'enforce', 'status', 'security-cleanup'],
                       default='all', help='Task to run')

    args = parser.parse_args()

    if args.task == 'all':
        run_payday_tasks()
    elif args.task == 'init':
        run_initialization_only()
    elif args.task == 'email-warnings':
        run_email_warnings_only()
    elif args.task == 'warnings':
        run_warnings_only()
    elif args.task == 'enforce':
        run_enforcement_only()
    elif args.task == 'status':
        get_status()
    elif args.task == 'security-cleanup':
        app = create_app()
        with app.app_context():
            results = run_security_cleanup()
            logger.info(f"Security cleanup completed: {results}")

    logger.info("Payday scheduler completed.")
